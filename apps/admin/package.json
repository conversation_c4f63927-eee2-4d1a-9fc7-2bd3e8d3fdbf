{"name": "@zid/zid-ui", "version": "1.0.6", "description": "Admin portal for one identity", "main": "dist/index.js", "module": "dist/index.js", "files": ["dist"], "publishConfig": {"registry": "https://nexus.corp.zscaler.com/repository/zuxp-npm-releases/"}, "scripts": {"build-dev": "webpack --mode development", "build": "webpack --mode production", "start": "webpack serve --mode development", "update": "ncu -u && pnpm i", "test": "jest", "coverage": "jest --coverage", "prepare": "husky", "build-lib": "webpack --config webpack.library.config.js --mode production", "prepublishOnly": "npm run build-lib"}, "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "peerDependencies": {"@zscaler/zui-component-library": "workspace:*", "i18next": ">=23.15.1", "lodash-es": ">=4.17.21", "react": ">=18.3.0", "react-dom": ">=18.3.0", "react-i18next": ">=15.0.2", "react-redux": ">=9.1.2", "react-router-dom": ">=6.26.2", "@reduxjs/toolkit": ">=2.6.0"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.8.2", "@zscaler/zui-component-library": "workspace:*", "axios": "^1.9.0", "dayjs": "^1.11.13", "i18next": "^25.1.3", "lodash-es": "^4.17.21", "node-forge": "^1.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-html-id": "^0.1.5", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@swc/core": "^1.11.24", "@swc/jest": "^0.2.38", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "dotenv": "^16.5.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.1.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-fixed-jsdom": "^0.0.9", "lint-staged": "^16.0.0", "mini-css-extract-plugin": "^2.9.2", "msw": "^2.8.2", "prettier": "^3.5.3", "prop-types": "^15.8.1", "resolve-url-loader": "^5.0.0", "sass": "^1.89.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "svg-url-loader": "^8.0.0", "swc-loader": "^0.2.6", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "lint-staged": {"*.{js,jsx}": ["eslint --cache --fix", "prettier --ignore-unknown --plugin-search-dir=. --write"], "*.{css,scss,md,json}": "prettier --ignore-unknown --plugin-search-dir=. --write"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 2 chrome version", "last 2 firefox version", "last 2 safari version"]}, "volta": {"node": "20.11.0", "pnpm": "8.15.1"}}