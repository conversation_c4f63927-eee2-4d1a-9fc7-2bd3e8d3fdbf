const path = require('path');

const webpack = require('webpack');
const dotenv = require('dotenv');

const HtmlWebpackPlugin = require('html-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const CopyPlugin = require('copy-webpack-plugin');

const TerserPlugin = require('terser-webpack-plugin');

module.exports = (env, argv) => {
  const isDevMode = argv.mode === 'development';
  const isProdMode = argv.mode === 'production';

  const dotEnv = dotenv.config().parsed;

  const ASSET_PATH = isDevMode ? '/' : process.env.ASSET_PATH || '/iam';

  const config = {
    entry: './src/index.js',
    mode: argv.mode || 'development',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].[contenthash].js',
      publicPath: ASSET_PATH,
      clean: true,
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          use: 'swc-loader',
          exclude: /node_modules/,
        },
        {
          test: /\.scss$/,
          use: [
            isProdMode && MiniCssExtractPlugin.loader,
            isDevMode && 'style-loader',
            'css-loader',
            'resolve-url-loader',
            {
              loader: 'sass-loader',
              options: {
                sourceMap: true,
              },
            },
          ].filter(Boolean),
        },
        {
          test: /\.svg$/,
          // use: 'svg-url-loader',
          type: 'asset/resource',
          generator: {
            publicPath: (ASSET_PATH + '/').replace(/\/+/g, '/'),
          },
        },
        {
          test: /\.(ttf|eot|woff|woff2|png|jpg|jpeg)$/,
          type: 'asset',
          generator: {
            publicPath: (ASSET_PATH + '/').replace(/\/+/g, '/'),
          },
        },
      ],
    },
    resolve: {
      extensions: ['.js', '.jsx', '...'],
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: path.resolve(__dirname, 'src/index.html'),
      }),
      new webpack.DefinePlugin({
        'process.env': JSON.stringify({
          ...process.env,
          ...dotEnv,
          ASSET_PATH,
        }),
      }),
      new CopyPlugin({
        patterns: ['public'],
      }),
      isProdMode &&
        new MiniCssExtractPlugin({
          filename: `[name].[contenthash].css`,
        }),
      isProdMode &&
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
        }),
    ].filter(Boolean),
  };

  if (isDevMode) {
    config.devtool = 'source-map';

    config.devServer = {
      historyApiFallback: true,
      compress: true,
      hot: true,
      server: 'http',
      open: false,
      host: 'oidp-test-admin.zslogin.net',
      // host: 'localhost',
      port: 8000,
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
      proxy: [
        {
          context: ['/admin/internal-api/'],
          target: dotEnv?.PROXY_HOST || 'http://oidp-test-admin.zslogin.net:8001',
          // target: dotEnv?.PROXY_HOST || 'http://**********:8001',
          secure: false,
        },
      ],
      headers: {
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
        'Access-Control-Allow-Origin':
          dotEnv?.PROXY_HOST || 'http://oidp-test-admin.zslogin.net:8001/',
        'Access-Control-Allow-Credentials': false,
      },
    };
  }

  if (isProdMode) {
    // use for prod debug
    //config.devtool = 'source-map';

    config.optimization = {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          minify: TerserPlugin.swcMinify,
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
          },
        }),
      ],
      runtimeChunk: 'single',
      splitChunks: {
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      },
    };
  }

  return config;
};
