const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const webpack = require('webpack');

module.exports = (env, argv) => {
  const isProdMode = argv.mode === 'production';

  return {
    mode: isProdMode ? 'production' : 'development',
    entry: {
      index: './src/lib-entry.js',
    },
    devtool: 'source-map',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      library: {
        name: 'Zid<PERSON>',
        type: 'umd',
      },
      globalObject: 'this',
      clean: true,
    },
    externals: [/^react($|\/)/, /^react-dom($|\/)/, /^i18next($|\/)/, /^react-i18next($|\/)/],
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          use: 'swc-loader',
          exclude: /node_modules/,
        },
        {
          test: /\.scss$/,
          use: [
            isProdMode && MiniCssExtractPlugin.loader,
            !isProdMode && 'style-loader',
            'css-loader',
            'resolve-url-loader',
            {
              loader: 'sass-loader',
              options: {
                sourceMap: true,
              },
            },
          ].filter(Boolean),
        },
        {
          test: /\.svg$/,
          type: 'asset/resource',
        },
        {
          test: /\.(ttf|eot|woff|woff2|png|jpg|jpeg)$/,
          type: 'asset/resource',
        },
      ],
    },
    resolve: {
      extensions: ['.js', '.jsx', '...'],
    },
    plugins: [
      new MiniCssExtractPlugin({
        filename: '[name].css',
      }),
      new webpack.DefinePlugin({
        'process.env.IS_ONEUI': true,
      }),
    ],
    optimization: {
      minimize: isProdMode,
      minimizer: [
        new TerserPlugin({
          minify: TerserPlugin.swcMinify,
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
          },
        }),
      ],
    },
  };
};
