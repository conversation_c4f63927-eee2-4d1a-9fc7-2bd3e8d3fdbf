.typography-paragraph1,
.typography-paragraph1-condensed {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-paragraph1-strong,
.typography-paragraph1-uppercase {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-paragraph1-uppercase {
  letter-spacing: var(--semantic-letterSpacing-uppercase);
}
.typography-paragraph2 {
  font-size: var(--semantic-fontSize-xs);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-paragraph2-strong,
.typography-paragraph2-uppercase {
  font-size: var(--semantic-fontSize-xs);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-paragraph2-uppercase {
  letter-spacing: var(--semantic-letterSpacing-uppercase);
}
.typography-paragraph3 {
  font-size: var(--semantic-fontSize-xxs);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-paragraph3-strong {
  font-size: var(--semantic-fontSize-xxs);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-monospace {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-monospace-strong {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-medium);
}

& {
  --semantic-spacing-default-0: 0;
  --semantic-spacing-default-1: 0.25rem;
  --semantic-spacing-default-2: 0.5rem;
  --semantic-spacing-default-3: 0.75rem;
  --semantic-spacing-default-4: 1rem;
  --semantic-spacing-default-5: 1.25rem;
  --semantic-spacing-default-6: 1.5rem;
  --semantic-spacing-default-8: 2rem;
  --semantic-spacing-default-10: 2.5rem;
  --semantic-spacing-default-12: 3rem;
  --semantic-spacing-default-14: 3.5rem;
  --semantic-spacing-default-16: 4rem;
  --semantic-spacing-default-18: 4.5rem;
  --semantic-spacing-default-20: 5rem;
  --semantic-spacing-default-px: 0.0625rem;
  --semantic-spacing-default-0_5: 0.125rem;
  --semantic-spacing-default-1_5: 0.375rem;
  --semantic-spacing-default-2_5: 0.625rem;
  --semantic-spacing-default-3_5: 0.875rem;
  --semantic-spacing-shrink-small-1: 0.125rem;
  --semantic-spacing-shrink-small-2: 0.375rem;
  --semantic-spacing-shrink-small-3: 0.625rem;
  --semantic-spacing-shrink-small-px: 0;
  --semantic-spacing-shrink-small-0_5: 0.0625rem;
  --semantic-spacing-shrink-small-1_5: 0.25rem;
  --semantic-spacing-shrink-small-2_5: 0.5rem;
  --semantic-spacing-shrink-medium-1: 0;
  --semantic-spacing-shrink-medium-2: 0.25rem;
  --semantic-spacing-shrink-medium-3: 0.5rem;
  --semantic-spacing-shrink-medium-4: 0.75rem;
  --semantic-spacing-shrink-medium-5: 1rem;
  --semantic-spacing-shrink-medium-6: 1.25rem;
  --semantic-spacing-shrink-medium-8: 1.75rem;
  --semantic-spacing-shrink-medium-10: 2.25rem;
  --semantic-spacing-shrink-medium-12: 2.75rem;
  --semantic-spacing-shrink-medium-14: 3.25rem;
  --semantic-spacing-shrink-medium-16: 3.75rem;
  --semantic-spacing-shrink-medium-18: 4.25rem;
  --semantic-spacing-shrink-medium-20: 4.75rem;
  --semantic-spacing-shrink-medium-1_5: 0.125rem;
  --semantic-spacing-shrink-medium-2_5: 0.375rem;
  --semantic-spacing-grow-small-0: 0.125rem;
  --semantic-spacing-grow-small-1: 0.375rem;
  --semantic-spacing-grow-small-2: 0.625rem;
  --semantic-spacing-grow-small-3: 0.875rem;
  --semantic-spacing-grow-small-0_5: 0.25rem;
  --semantic-spacing-grow-small-1_5: 0.5rem;
  --semantic-spacing-grow-small-2_5: 0.75rem;
  --semantic-spacing-grow-medium-0: 0.25rem;
  --semantic-spacing-grow-medium-1: 0.5rem;
  --semantic-spacing-grow-medium-2: 0.75rem;
  --semantic-spacing-grow-medium-3: 1rem;
  --semantic-spacing-grow-medium-4: 1.25rem;
  --semantic-spacing-grow-medium-5: 1.5rem;
  --semantic-spacing-grow-medium-6: 1.75rem;
  --semantic-spacing-grow-medium-8: 2.25rem;
  --semantic-spacing-grow-medium-10: 2.75rem;
  --semantic-spacing-grow-medium-12: 3.5rem;
  --semantic-spacing-grow-medium-14: 3.75rem;
  --semantic-spacing-grow-medium-16: 4.25rem;
  --semantic-spacing-grow-medium-18: 4.75rem;
  --semantic-spacing-grow-medium-19: 5rem;
  --semantic-spacing-grow-medium-20: 5.25rem;
  --semantic-spacing-grow-medium-0_5: 0.375rem;
  --semantic-spacing-grow-medium-1_5: 0.625rem;
  --semantic-spacing-grow-medium-2_5: 0.875rem;
}
@media (min-width: 1024px) {
  & {
    --semantic-spacing-default-0: 0;
    --semantic-spacing-default-1: 0.25rem;
    --semantic-spacing-default-2: 0.5rem;
    --semantic-spacing-default-3: 0.75rem;
    --semantic-spacing-default-4: 1rem;
    --semantic-spacing-default-5: 1.25rem;
    --semantic-spacing-default-6: 1.5rem;
    --semantic-spacing-default-8: 2rem;
    --semantic-spacing-default-10: 2.5rem;
    --semantic-spacing-default-12: 3rem;
    --semantic-spacing-default-14: 3.5rem;
    --semantic-spacing-default-16: 4rem;
    --semantic-spacing-default-18: 4.5rem;
    --semantic-spacing-default-20: 5rem;
    --semantic-spacing-default-px: 0.0625rem;
    --semantic-spacing-default-0_5: 0.125rem;
    --semantic-spacing-default-1_5: 0.375rem;
    --semantic-spacing-default-2_5: 0.625rem;
    --semantic-spacing-default-3_5: 0.875rem;
    --semantic-spacing-shrink-small-1: 0.25rem;
    --semantic-spacing-shrink-small-2: 0.5rem;
    --semantic-spacing-shrink-small-3: 0.75rem;
    --semantic-spacing-shrink-small-px: 0.0625rem;
    --semantic-spacing-shrink-small-0_5: 0.125rem;
    --semantic-spacing-shrink-small-1_5: 0.375rem;
    --semantic-spacing-shrink-small-2_5: 0.625rem;
    --semantic-spacing-shrink-medium-1: 0.25rem;
    --semantic-spacing-shrink-medium-2: 0.5rem;
    --semantic-spacing-shrink-medium-3: 0.75rem;
    --semantic-spacing-shrink-medium-4: 1rem;
    --semantic-spacing-shrink-medium-5: 1.25rem;
    --semantic-spacing-shrink-medium-6: 1.5rem;
    --semantic-spacing-shrink-medium-8: 2rem;
    --semantic-spacing-shrink-medium-10: 2.5rem;
    --semantic-spacing-shrink-medium-12: 3rem;
    --semantic-spacing-shrink-medium-14: 3.5rem;
    --semantic-spacing-shrink-medium-16: 4rem;
    --semantic-spacing-shrink-medium-18: 4.5rem;
    --semantic-spacing-shrink-medium-20: 5rem;
    --semantic-spacing-shrink-medium-1_5: 0.375rem;
    --semantic-spacing-shrink-medium-2_5: 0.625rem;
    --semantic-spacing-grow-small-0: 0;
    --semantic-spacing-grow-small-1: 0.25rem;
    --semantic-spacing-grow-small-2: 0.5rem;
    --semantic-spacing-grow-small-3: 0.75rem;
    --semantic-spacing-grow-small-0_5: 0.125rem;
    --semantic-spacing-grow-small-1_5: 0.375rem;
    --semantic-spacing-grow-small-2_5: 0.625rem;
    --semantic-spacing-grow-medium-0: 0;
    --semantic-spacing-grow-medium-1: 0.25rem;
    --semantic-spacing-grow-medium-2: 0.5rem;
    --semantic-spacing-grow-medium-3: 0.75rem;
    --semantic-spacing-grow-medium-4: 1rem;
    --semantic-spacing-grow-medium-5: 1.25rem;
    --semantic-spacing-grow-medium-6: 1.5rem;
    --semantic-spacing-grow-medium-8: 2rem;
    --semantic-spacing-grow-medium-10: 2.5rem;
    --semantic-spacing-grow-medium-12: 3rem;
    --semantic-spacing-grow-medium-14: 3.5rem;
    --semantic-spacing-grow-medium-16: 4rem;
    --semantic-spacing-grow-medium-18: 4.5rem;
    --semantic-spacing-grow-medium-19: 4.75rem;
    --semantic-spacing-grow-medium-20: 5rem;
    --semantic-spacing-grow-medium-0_5: 0.125rem;
    --semantic-spacing-grow-medium-1_5: 0.375rem;
    --semantic-spacing-grow-medium-2_5: 0.625rem;
  }
}
& {
  --semantic-sizes-element-xs: 1.25rem;
  --semantic-sizes-element-default: 1.5rem;
  --semantic-sizes-element-base: 1.75rem;
  --semantic-sizes-element-medium: 2rem;
  --semantic-sizes-element-large: 2.25rem;
  --semantic-sizes-element-xl: 2.5rem;
  --semantic-sizes-element-2xl: 2.75rem;
  --semantic-sizes-screenSize-horizontal-small: 22.5rem;
  --semantic-sizes-screenSize-horizontal-default: 27.5rem;
  --semantic-sizes-screenSize-horizontal-large: 48rem;
  --semantic-sizes-screenSize-vertical-small: 48.75rem;
  --semantic-sizes-screenSize-vertical-default: 59.75rem;
  --semantic-sizes-screenSize-vertical-large: 64rem;
}
@media (min-width: 1024px) {
  & {
    --semantic-sizes-element-xs: 1rem;
    --semantic-sizes-element-default: 1.25rem;
    --semantic-sizes-element-base: 1.5rem;
    --semantic-sizes-element-medium: 1.75rem;
    --semantic-sizes-element-large: 2rem;
    --semantic-sizes-element-xl: 2.25rem;
    --semantic-sizes-element-2xl: 2.5rem;
    --semantic-sizes-screenSize-horizontal-small: 64rem;
    --semantic-sizes-screenSize-horizontal-default: 90rem;
    --semantic-sizes-screenSize-horizontal-large: 120rem;
    --semantic-sizes-screenSize-vertical-small: 48rem;
    --semantic-sizes-screenSize-vertical-default: 64rem;
    --semantic-sizes-screenSize-vertical-large: 67.5rem;
  }
}
& {
  --semantic-cornerRadius-none: 0;
  --semantic-cornerRadius-small: 0.375rem;
  --semantic-cornerRadius-medium: 0.5rem;
  --semantic-cornerRadius-default: 0.75rem;
  --semantic-cornerRadius-large: 1rem;
  --semantic-cornerRadius-full: 50%;
}
@media (min-width: 1024px) {
  & {
    --semantic-cornerRadius-none: 0;
    --semantic-cornerRadius-small: 0.25rem;
    --semantic-cornerRadius-medium: 0.375rem;
    --semantic-cornerRadius-default: 0.5rem;
    --semantic-cornerRadius-large: 0.75rem;
    --semantic-cornerRadius-full: 50%;
  }
}
& {
  --semantic-typography-size-2xs: 0.75rem;
  --semantic-typography-size-xs: 0.875rem;
  --semantic-typography-size-default: 1rem;
  --semantic-typography-size-base: 1.25rem;
  --semantic-typography-size-medium: 1.5rem;
  --semantic-typography-size-large: 1.75rem;
  --semantic-typography-size-xl: 2rem;
  --semantic-typography-size-2xl: 2.25rem;
  --semantic-typography-lineHeight-2xs: 1rem;
  --semantic-typography-lineHeight-xs: 1.25rem;
  --semantic-typography-lineHeight-default: 1.5rem;
  --semantic-typography-lineHeight-defaultCondensed: 1.375rem;
  --semantic-typography-lineHeight-base: 1.75rem;
  --semantic-typography-lineHeight-medium: 2rem;
  --semantic-typography-lineHeight-large: 2.25rem;
  --semantic-typography-lineHeight-xl: 2.5rem;
  --semantic-typography-lineHeight-2xl: 2.75rem;
  --semantic-typography-letterSpacing-uppercaseXs: 0.16;
  --semantic-typography-letterSpacing-uppercaseDefault: 0.36;
  --semantic-typography-weight-font-regular: 400;
  --semantic-typography-weight-font-strong: 600;
  --semantic-typography-weight-icon-light: light;
  --semantic-typography-weight-icon-regular: regular;
  --semantic-typography-weight-icon-solid: solid;
}
@media (min-width: 1024px) {
  & {
    --semantic-typography-size-2xs: 0.625rem;
    --semantic-typography-size-xs: 0.75rem;
    --semantic-typography-size-default: 0.875rem;
    --semantic-typography-size-base: 1rem;
    --semantic-typography-size-medium: 1.25rem;
    --semantic-typography-size-large: 1.5rem;
    --semantic-typography-size-xl: 1.75rem;
    --semantic-typography-size-2xl: 2rem;
    --semantic-typography-lineHeight-2xs: 0.875rem;
    --semantic-typography-lineHeight-xs: 1rem;
    --semantic-typography-lineHeight-default: 1.25rem;
    --semantic-typography-lineHeight-defaultCondensed: 1.125rem;
    --semantic-typography-lineHeight-base: 1.5rem;
    --semantic-typography-lineHeight-medium: 1.75rem;
    --semantic-typography-lineHeight-large: 2rem;
    --semantic-typography-lineHeight-xl: 2.25rem;
    --semantic-typography-lineHeight-2xl: 2.5rem;
    --semantic-typography-letterSpacing-uppercaseXs: 0.06;
    --semantic-typography-letterSpacing-uppercaseDefault: 0.16;
    --semantic-typography-weight-font-regular: 400;
    --semantic-typography-weight-font-strong: 500;
    --semantic-typography-weight-icon-light: light;
    --semantic-typography-weight-icon-regular: regular;
    --semantic-typography-weight-icon-solid: solid;
  }
}
& {
  --semantic-fontFamily-default:
    'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  --semantic-fontFamily-monospace: 'Source Code Pro', monospace;
}
.typography-header1 {
  font-size: var(--semantic-typography-size-2xl);
  line-height: 1.25;
}
.typography-header1,
.typography-header2 {
  font-family: var(--semantic-fontFamily-default);
  font-weight: var(--semantic-typography-weight-font-strong);
}
.typography-header2 {
  font-size: var(--semantic-typography-size-xl);
  line-height: 1.3;
}
.typography-header3 {
  font-size: var(--semantic-typography-size-large);
  line-height: 1.32;
}
.typography-header3,
.typography-header4 {
  font-family: var(--semantic-fontFamily-default);
  font-weight: var(--semantic-typography-weight-font-strong);
}
.typography-header4 {
  font-size: var(--semantic-typography-size-medium);
  line-height: 1.2;
}
.typography-header5 {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-base);
  font-weight: var(--semantic-typography-weight-font-strong);
  line-height: 1.25;
}
.typography-paragraph1 {
  line-height: 1.4;
}
.typography-paragraph1,
.typography-paragraph1-condensed {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-default);
  font-weight: var(--semantic-typography-weight-font-regular);
}
.typography-paragraph1-condensed {
  line-height: 1.28;
}
.typography-paragraph1-strong,
.typography-paragraph1-uppercase {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-default);
  font-weight: var(--semantic-typography-weight-font-strong);
  line-height: 1.4;
}
.typography-paragraph1-uppercase {
  letter-spacing: var(--semantic-typography-letterSpacing-uppercaseDefault);
  text-transform: uppercase;
}
.typography-paragraph2 {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-xs);
  font-weight: var(--semantic-typography-weight-font-regular);
  line-height: 1.32;
}
.typography-paragraph2-strong,
.typography-paragraph2-uppercase {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-xs);
  font-weight: var(--semantic-typography-weight-font-strong);
  line-height: 1.32;
}
.typography-paragraph2-uppercase {
  letter-spacing: var(--semantic-typography-letterSpacing-uppercaseXs);
  text-transform: uppercase;
}
.typography-paragraph3 {
  font-weight: var(--semantic-typography-weight-font-regular);
}
.typography-paragraph3,
.typography-paragraph3-strong {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-2xs);
  line-height: 1.36;
}
.typography-paragraph3-strong {
  font-weight: var(--semantic-typography-weight-font-strong);
}
.typography-monospace {
  font-weight: var(--semantic-typography-weight-font-regular);
}
.typography-monospace,
.typography-monospace-strong {
  font-family: var(--semantic-fontFamily-monospace);
  font-size: var(--semantic-typography-size-default);
  line-height: 1.4;
}
.typography-monospace-strong {
  font-weight: var(--semantic-typography-weight-font-strong);
}
