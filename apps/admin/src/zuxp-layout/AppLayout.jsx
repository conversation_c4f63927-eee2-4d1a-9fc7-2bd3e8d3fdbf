import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { LoaderContainer, ToastContainer } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getSystemFeatures, getTenantFeatures } from '../ducks/features';
import {
  appSetupDone,
  getBuildVersion,
  hideLoader,
  hideNotification,
  showLoader,
} from '../ducks/global';
import {
  selectAppSetupPending,
  selectIsAppSetupDone,
  selectIsLoading,
  selectNotificationsList,
} from '../ducks/global/selectors';
import { getMigrationStatus } from '../ducks/migration';
import { getMyPermissions } from '../ducks/permissions';
import { getProfile } from '../ducks/profile';
import { getZDKfeatureFlag } from '../ducks/token-validators';

import BannerLayout from '../layout/BannerLayout';

import {
  setupApiMethodMessageOption,
  setupFloatingPortalRootId,
  setupUseApiCallFunctionsRegistry,
} from '../setup';

setupApiMethodMessageOption();
setupUseApiCallFunctionsRegistry();
setupFloatingPortalRootId();

const AppLayout = ({ children }) => {
  const dispatch = useDispatch();

  const appSetupPending = useSelector(selectAppSetupPending);
  const isAppSetupDone = useSelector(selectIsAppSetupDone);

  const isLoading = useSelector(selectIsLoading);
  const notificationList = useSelector(selectNotificationsList);

  const setupAuth = () => {
    dispatch(showLoader());

    dispatch(getMyPermissions())
      .catch(noop)
      .finally(() => {
        dispatch(getSystemFeatures()).catch(noop);
        dispatch(getTenantFeatures()).catch(noop);
        dispatch(getProfile()).catch(noop);
        dispatch(getBuildVersion()).catch(noop);
        dispatch(getMigrationStatus()).catch(noop);
        dispatch(appSetupDone());
        dispatch(hideLoader());
        dispatch(getZDKfeatureFlag()).catch(noop);
      });
  };

  useEffect(() => {
    if (appSetupPending) {
      setupAuth();
    }
  }, [appSetupPending]);

  const renderRoutesSection = () => {
    if (!isAppSetupDone) {
      return null;
    }

    return children;
  };

  const onNotificationClose = (id) => {
    dispatch(hideNotification(id));
  };

  return (
    <>
      <LoaderContainer isLoading={isLoading} />
      <ToastContainer list={notificationList} onClose={onNotificationClose} />
      <BannerLayout isAppSetupDone={isAppSetupDone} isAuthFreePage={false} />
      {renderRoutesSection()}
    </>
  );
};

AppLayout.propTypes = {
  children: PropTypes.any,
};
export default AppLayout;
