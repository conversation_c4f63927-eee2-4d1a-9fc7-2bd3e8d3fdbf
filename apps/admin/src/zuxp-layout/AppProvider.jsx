'use client';

import PropTypes from 'prop-types';

import StoreProvider from '../StoreProvider';
import { APP_ID, PORTAL_ROOT_ID } from '../config';
import AppLayout from './AppLayout';

const AppProvider = ({ children }) => (
  <>
    <div id={APP_ID}>
      <StoreProvider>
        <AppLayout>{children}</AppLayout>
      </StoreProvider>
    </div>
    <div id={PORTAL_ROOT_ID} />
  </>
);

AppProvider.propTypes = {
  children: PropTypes.any,
};
export default AppProvider;
