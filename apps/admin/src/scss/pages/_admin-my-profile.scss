#admin-my-profile {
  .typography-header3 {
    margin: 10px;
  }

  .field {
    margin-top: 0;
  }

  .card {
    .field {
      max-width: 400px;
    }
  }
}

.change-email-modal-container {
  .modal-content-container {
    width: 624px;
  }

  .typography-paragraph1-uppercase {
    margin-top: 24px;
    margin-bottom: 8px;

    &:first-of-type {
      margin-top: 0px;
    }
  }

  .field-group {
    width: 200px;
  }

  .actions-info-section {
    width: calc(100% - 240px);
    margin: 20px 0px 0 8px;

    .button + .info {
      padding-left: 12px;

      max-width: calc(100% - 80px);

      .button {
        padding-left: 4px;
      }
    }

    .has-color-success {
      font-size: 2rem;
    }
  }

  .info {
    max-width: 100%;
  }
}

.change-password-modal-container {
  .modal-content-container {
    width: 324px;

    .field {
      margin-top: 0;
      margin-bottom: 16px;
    }
  }
}

.email-address-container {
  line-height: 19px;
}

.update-status-warning-container {
  .modal-content-container {
    width: 400px;
  }
}
