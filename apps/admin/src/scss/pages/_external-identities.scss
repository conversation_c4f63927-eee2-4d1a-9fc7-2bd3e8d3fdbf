#external-identities-page {
  .primary-idp-table-container {
    height: auto;
    max-height: 200px;
  }

  .secondary-idp-table-container {
    max-height: calc(100vh - 500px);

    &.has-no-data {
      height: auto;
    }
  }
}

.external-identities-form-modal {
  .config-buttons {
    .button {
      width: auto;
    }
  }

  .provisioning-form-container {
    margin-top: 20px;

    .status-container {
      margin-top: 8px;

      &.enabled {
        // color: $color-standard-green-text;
      }

      &.disabled {
        // color: $color-standard-red-text;
      }
    }
  }
}

.attribute-mapping {
  .user-attribute-section {
    margin-left: 16px;
  }
}

.choose-identity-container {
  .card {
    box-shadow: none;
  }

  .heading {
    padding-top: 4px;

    padding-bottom: 28px;

    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
  }

  .radio {
    margin-bottom: 32px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .content-container {
    font-size: 13px;
    line-height: 14px;
  }

  .info-section {
    display: flex;

    margin: 16px 0;
    padding: 8px 12px;

    margin-left: 28px;

    border-radius: 5px;
    // background-color: $color-lighter-gray-background;
  }

  .field {
    margin-top: 0;
    margin-bottom: 24px;

    padding-left: 28px;
  }

  .buttons {
    padding-top: 8px;
    margin-bottom: 0;
  }
}

.radio-container {
  .radio-buttons-container.disabled {
    cursor: not-allowed;
    pointer-events: none;
    .button.primary {
      // background-color: $color-darker-gray-text;
    }
  }
}

.al-header-container {
  margin-top: 16px;

  border: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);

  .al-column {
    height: 100%;
    width: 100%;

    padding: 16px;
    background-color: var(--semantic-color-surface-base-secondary, #f7f8fa);

    align-self: center;
  }
}

.al-mapping {
  border: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
  border-top-color: transparent;

  .al,
  .context {
    height: 100%;
    width: 100%;

    padding: 16px;
  }

  .al {
    align-self: center;

    .name {
      padding-bottom: 4px;
    }

    .description {
      color: var(--semantic-color-content-base-tertiary, #677289);
    }
  }

  .context {
    border-left: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
  }
}
