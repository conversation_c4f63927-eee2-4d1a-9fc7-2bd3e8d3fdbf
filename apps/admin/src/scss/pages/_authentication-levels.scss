#authentication-levels {
  color: var(--semantic-color-content-base-primary, #131a2e);
  background-color: var(--semantic-color-surface-base-primary, #fff);

  .branch-indicator {
    border-left: 1px dashed var(--semantic-color-border-base-secondary, #bac2cf);
  }

  .partial-branch-indicator {
    position: relative;

    &::before {
      content: '';

      position: absolute;

      margin-top: 2px;
      height: 13px;
      width: 1px;

      border-left: 1px dashed var(--semantic-color-border-base-secondary, #bac2cf);
    }
  }

  .al-list-container {
    width: 230px;
    height: 100%;

    flex-shrink: 0;

    padding: 0 20px 0 0;
    overflow-y: scroll;

    border-right: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);

    .authentication-nesting-1 {
      margin-left: 34px;

      .level-name-container {
        &::before {
          margin-left: -14px;

          width: 12px;
        }
      }
    }

    .authentication-nesting-2 {
      margin-left: 22px;

      .level-name-container {
        &::before {
          margin-left: -14px;

          width: 12px;
        }
      }
    }

    .authentication-nesting-3 {
      margin-left: 22px;
    }
  }

  .authentication-level-overview {
    max-width: 100%;

    flex-grow: 0;

    color: var(--semantic-color-content-base-secondary, #4a5468);

    &.disabled {
      background-color: rgba(255, 255, 255, 0.75);
      opacity: 0.5;
    }

    &.is-selected {
      background-color: #eef8ff;

      .level-name-container.is-top-level {
        background-color: #d6efff;
      }
    }

    &.is-top-level {
      margin-top: 16px;
      border-left: none;
    }

    .can-select {
      .level-name {
        cursor: pointer;
      }
    }

    .level-name-container {
      width: 100%;

      padding-left: 16px;

      &::before {
        content: '';

        position: absolute;

        height: 1px;

        border-top: 1px dashed var(--semantic-color-border-base-secondary, #bac2cf);
      }
    }

    .level-name {
      padding: 8px;
      padding-left: 2px;
    }

    .is-top-level {
      padding-left: 0;

      .level-name {
        padding-left: 8px;
      }
    }
  }

  .al-form {
    width: 100%;

    height: 100%;

    overflow-y: scroll;

    padding: 24px;

    .authentication-nesting-0 {
      margin-left: 8px;
    }

    .authentication-nesting-1 {
      margin-left: 40px;

      .al-level-name-container {
        &::before {
          margin-left: -40px;

          width: 32px;
        }
      }
    }

    .authentication-nesting-2 {
      margin-left: 40px;
    }

    .authentication-nesting-3 {
      margin-left: 40px;
    }
  }

  .authentication-level {
    margin-bottom: 20px;
    margin-top: -10px;

    position: relative;

    .al-level-name-container {
      &::before {
        content: '';

        position: absolute;

        height: 1px;

        border-top: 1px dashed var(--semantic-color-border-base-secondary, #bac2cf);
      }
    }

    .level-name {
      align-self: flex-start;

      padding: 3px 8px;

      margin-left: -8px;

      background-color: var(--semantic-color-surface-base-primary, #fff);

      border-radius: 4px;
      border: 1px solid var(--semantic-color-border-base-secondary, #bac2cf);
    }

    .move-dd {
      .selected-items {
        padding: 0;
      }
    }

    .field-container {
      margin-left: 22px;
      margin-bottom: 28px;

      border-bottom: 2px solid var(--semantic-color-border-base-subdued, #e9eef3);
    }

    .validity-input-container {
      .input-container {
        display: flex;
        align-items: center;

        .dropdown-container {
          // border-left: 1px solid $color-medium-gray-disabled;

          .selected-items {
            border-top-left-radius: 0px;
            border-bottom-left-radius: 0px;
            // background-color: $color-lighter-gray-background;
            // border-color: $color-lighter-gray-background;
            align-items: center;
            height: 32px;
          }
        }

        .input {
          height: 32px;
          border-top-right-radius: 0px;
          border-bottom-right-radius: 0px;
        }
      }
    }

    .create-level-btn {
      margin-left: 22px;
    }
  }
}
