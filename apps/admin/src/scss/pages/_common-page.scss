.page-container {
  .page-title {
    margin-bottom: 15px;
  }

  .selected-items {
    padding: 8px 20px;

    min-width: 130px;
  }

  .table-container {
    max-height: calc(100vh - 240px);
    max-width: 100%;
  }
}

.crud-modal {
  .card.file-browser-form-container {
    padding-top: 20px;
    padding-right: 0px;
  }
}

.info-card-container {
  overflow-y: scroll;
}

.action-section {
  position: fixed;

  width: calc(100% - 36px);
  bottom: 32px;

  margin-left: -20px;

  @extend .shadow-medium;

  background-color: var(--semantic-color-surface-base-primary, #fff);

  &.buttons {
    padding: 12px 20px;
    margin-bottom: 0;
    justify-content: flex-start;
  }
}

.otp-input-container {
  display: flex;

  gap: var(--semantic-spacing-px-40, 4px);

  .field {
    margin: 0;
  }

  .otp-input {
    width: var(--semantic-spacing-px-320, 32px);
    height: var(--semantic-spacing-px-320, 32px);

    border: 1px solid var(--semantic-color-border-base-primary, #dfe3ec);
    border-radius: var(--semantic-cornerRadius-40, 4px);

    &.has-error {
      border-color: var(--semantic-color-border-status-danger-active, #dc362e);
    }
  }
}
