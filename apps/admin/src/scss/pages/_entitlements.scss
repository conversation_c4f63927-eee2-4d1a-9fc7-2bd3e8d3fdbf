#entitlements {
  #services-users-and-groups {
    .services-tabs {
      .services-tabs-table {
        max-height: calc(100vh - 292px);
      }
    }
  }
  .services-header {
    display: flex;
    justify-content: space-between;
  }

  .subtitle {
    display: flex;
    color: var(--semantic-color-content-base-subdued, #677289);

    .services-label::after,
    .cloud-label::after {
      content: '|';
      margin-left: 5px;
      margin-right: 5px;
    }
    .cloud-label,
    .org-label {
      display: flex;
      align-items: center;

      gap: 2px;

      .value::before {
        content: ':';
        margin-right: 2px;
      }
    }
    &.with-border-bottom {
      border-bottom: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
      margin-bottom: 16px;
      margin-top: 4px;
    }
  }
  .assign-entities {
    height: calc(100vh - 62px);

    .action-section {
      padding-right: 300px;

      .cancel-button {
        margin-right: auto;
        order: -1;
      }
    }

    .assign-view {
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
      flex-flow: row nowrap;
      height: calc(100vh - 166px);
      .left-panel {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 226px;
        border-right: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
        .select-view {
          .icon-container {
            .icon-bar {
              top: 13px;
            }
          }
        }
        .summary-view {
          .icon-container {
            .icon-bar {
              top: -17px;
            }
          }
        }
        .select-view,
        .summary-view {
          display: flex;
          align-items: center;
          gap: 8px;
          align-self: stretch;
          height: 44px;
          .icon-container {
            display: flex;
            flex-direction: column;
            position: relative;
            .current-state {
              color: var(--semantic-color-border-accent-green-active, #62ab57);
            }
            .end-state {
              color: var(--semantic-color-border-base-tertiary, #586378);
            }
            .icon-bar {
              background: var(--semantic-color-border-base-tertiary, #586378);
              position: absolute;
              width: 1px;
              height: 18px;
              left: 6px;
            }
          }
          &.active {
            border-right: 2px solid var(--semantic-color-border-accent-green-active, #62ab57);
            .current-state,
            .end-state {
              color: var(--semantic-color-border-accent-green-active, #62ab57);
            }
            .icon-bar {
              background: var(--semantic-color-border-accent-green-active, #62ab57);
            }
          }
        }
      }
      .right-panel {
        height: 100%;
        padding: 0 34px;
        width: calc(100% - 226px);
        .assign-role-selection-container {
          display: flex;
          height: 40px;
          margin: 20px 0;
          .role-selection-drop-down,
          .scope-selection-drop-down {
            margin-left: 20px;
            width: 200px;
          }
        }
        .assign-admin-entitlements {
          max-height: calc(100vh - 416px);
          &.review-mode {
            max-height: calc(100vh - 300px);
          }
        }
        .assign-service-entitlements {
          max-height: calc(100vh - 356px);
          &.review-mode {
            max-height: calc(100vh - 300px);
          }
        }
      }
    }
  }
}
.group-users-modal {
  .group-users-container {
    border-radius: 8px;
    width: 600px;
    height: 532px;
    padding: 8px;
    .group-users-search-container {
      margin: 12px 0;
    }
    .group-users-table {
      height: calc(100% - 90px);
    }
  }
}

.device-groups-restrictions-container {
  flex-flow: column;
  .device-group-retrictions-table {
    height: 310px;
  }
}

.entitlements-center-modal-container {
  .modal-content-container {
    width: 624px;
  }

  .card {
    padding-left: 0;
    padding-right: 0;
  }
}
