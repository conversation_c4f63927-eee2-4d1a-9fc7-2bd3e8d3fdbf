#admin-branding {
  .buttons {
    padding: 12px 20px;
    margin-bottom: 0;
    justify-content: flex-start;
    &.icon {
      padding-left: 0;
      .text-tooltip-container {
        display: none;
      }
    }
  }

  .card {
    margin-bottom: 12px;
  }

  .field {
    margin-bottom: 0;

    &.field-stacked {
      align-items: center;

      .label {
        margin-top: 8px;
        width: 500px;
      }
      .input-container {
        width: 400px;
      }
    }
  }

  .email-subject {
    border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

    border-radius: 8px;
    padding-bottom: 16px;

    .email-subject-header {
      height: 44px;

      border-radius: inherit;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;

      background-color: var(--semantic-color-surface-table-header-default, #f7f8fa);

      border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

      .field {
        margin-top: 0;
      }
      .label {
        align-self: center;
      }
    }

    .field-group {
      padding: 0 16px;
    }
  }

  .from-email {
    margin-bottom: 70px;
  }

  .typography-paragraph1-uppercase {
    margin-bottom: 12px;
  }
}
