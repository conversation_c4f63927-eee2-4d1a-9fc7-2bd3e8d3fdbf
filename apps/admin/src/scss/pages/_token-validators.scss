.modal-container-token-validators {
  .modal-body-container {
    .info-icon {
      // color: $color-standard-blue-text;
    }
  }
}

#token-validators {
  .test-token {
    .card {
      margin-bottom: 16px;
      min-width: 540px;
      padding-top: 0px;

      border: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
      border-radius: 0px;
      // background-color: $color-white-background;
      box-shadow: 1px 3px 12px 0 rgba(151, 152, 153, 0.1);
    }
    .decode-header {
      height: 44px;
      background-color: var(--semantic-color-border-base-subdued, #e9eef3);
      padding-left: 10px;
      align-content: center;
    }
    .decode-value-container {
      min-height: 140px;
      width: 540px;
      padding: 10px;
      color: '#A17664';
      overflow: auto;
    }

    .encode-token-container {
      height: calc(100vh - 142px);
      .header-section {
        position: absolute;
        .icon {
          color: var(--semantic-color-content-base-secondary, #bac2cf);
          font-size: 28px;
        }
      }
      .token-textarea {
        background: transparent;
        outline: none;
        height: 100%;
        width: 100%;
        padding: 8px;
        font-size: 14px;
        color: var(--semantic-color-content-accent-purple-secondary, #bf3ad9);
        word-break: break-word;
      }
    }
  }
}
