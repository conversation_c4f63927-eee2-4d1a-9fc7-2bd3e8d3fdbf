.view-access-policy-container {
  .detail-container {
    margin: 14px 0;
  }

  .label {
    min-width: 150px;
  }
}

.steps-wizard-container {
  display: flex;
  height: 100%;
  width: 100%;

  color: var(--semantic-color-content-base-primary, #131a2e);

  .steps-list-container {
    display: flex;
    flex-direction: column;

    height: 100%;
    width: 250px;

    border-right: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  }

  .content {
    width: 100%;

    padding-left: 24px;
  }

  .step {
    position: relative;

    display: flex;
    align-items: center;

    border-right: 2px solid transparent;

    padding: 16px;

    margin-right: -2px;

    cursor: pointer;

    &.selected {
      border-right-color: var(--semantic-color-border-interactive-primary-default, #2160e1);
      background-color: var(--semantic-color-surface-interactive-secondary-hover, #f2f7ff);
    }

    &:not(:last-of-type)::before {
      content: '';

      position: absolute;

      width: 1px;
      height: 70%;

      top: 65%;
      margin-left: 8px;

      z-index: 1;

      background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
    }
  }
}

.step-header-section {
  display: flex;
  flex-direction: column;
  gap: 8px;

  padding-bottom: 16px;

  margin-bottom: 16px;

  border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
}

.step-header {
  font-size: 15px;
  font-weight: 500;
  line-height: 24px;
}

.step-sub-header {
  font-size: 12px;
  font-weight: 400;
  line-height: 15.84px;
}

.criteria-section-container {
  padding-left: 16px;

  display: flex;
  flex-direction: column;
  gap: 16px;

  margin-top: 16px;

  border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
}

.time-section-container,
.scope-section-container {
  display: flex;
  gap: 16px;

  padding: 20px 0 16px 24px;
  margin-left: 16px;
}

.time-section-container {
  flex-direction: column;

  .date-picker-custom-input-container {
    margin: 0;
  }
}

.option-selector {
  position: relative;

  &::before {
    content: '';

    position: absolute;

    width: 1px;
    height: 100%;

    top: 0;
    left: 0;

    border-left: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  }

  &:last-of-type::before {
    height: 52%;
  }

  .horizontal-line {
    &::before {
      content: '';

      position: absolute;

      width: 24px;
      height: 50%;

      top: 0;
      left: -24px;

      border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
    }
  }
}

.expression-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  padding-bottom: 16px;
}

.summary-section-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.criteria-rule-column {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .label {
    width: 80px;
  }
}

.occurrences-day-of-week-section {
  display: flex;
  gap: 16px;
  align-items: center;

  margin-left: 320px;
}

.days-of-week-section {
  display: flex;
  gap: 16px;
}

.day-of-week {
  display: flex;
  justify-content: center;
  align-items: center;

  width: 24px;
  height: 24px;

  color: var(--semantic-color-content-base-tertiary, #677289);
  background-color: var(--semantic-color-surface-base-tertiary, #e9eef3);

  border-radius: 50%;

  cursor: pointer;

  &.is-selected {
    color: var(--semantic-color-content-inverted-base-primary, #fff);
    background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
  }
}

.access-policy-form-modal {
  // &.is-blocking {
  //   width: calc(100% - 210px);
  //   left: 210px;
  // }

  // &.nav-minimized {
  //   width: calc(100% - 80px);
  //   left: 80px;
  // }

  .modal-content-container {
    height: 100%;
  }

  .modal-body-container {
    border-top: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

    height: calc(100% - 115px);

    padding: 16px 16px 0 0;
  }

  .modal-footer-container {
    border-top: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  }
}

.criteria-label {
  .label {
    opacity: 0;
    margin-bottom: 0;
    min-width: 150px;
  }
}
