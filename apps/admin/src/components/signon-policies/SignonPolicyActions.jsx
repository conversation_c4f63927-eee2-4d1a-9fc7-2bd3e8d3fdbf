import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button } from '@zscaler/zui-component-library';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const SignonPolicyActions = () => {
  const { t } = useTranslation();

  const { setModalMode, privileges, defaultDetail, defaultModalMode, setDetail } =
    useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  return (
    <div className="is-flex has-jc-sb full-width">
      {hasFullAccess ? (
        <div className="buttons">
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_RULE')}</span>
          </Button>
        </div>
      ) : null}
    </div>
  );
};

export default SignonPolicyActions;
