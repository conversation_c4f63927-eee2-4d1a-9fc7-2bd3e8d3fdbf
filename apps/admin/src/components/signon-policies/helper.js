import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'SIGN_ON_POLICY',
  },
  add: {
    headerText: 'ADD_SIGN_ON_POLICY',
  },
  edit: {
    headerText: 'EDIT_SIGN_ON_POLICY',
  },
  delete: {
    headerText: 'DELETE_SIGN_ON_POLICY',
    confirmationMessage:
      'Are you sure you want to delete this Policy? The changes cannot be undone.',
  },
};

export const isConditionValid = (condition = {}) => {
  const { field, op, value } = condition;

  if (field[0]?.value && op[0]?.value && value[0]?.value) {
    return true;
  }

  return false;
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { ruleOrder, name, action, conditions } = formValues || {};

  if (!ruleOrder) {
    validationDetail.isValid = false;
    validationDetail.context = 'ruleOrder';
    validationDetail.type = 'error';
    validationDetail.message = 'Rule Order is Required';

    return validationDetail;
  }

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  if (!conditions || conditions?.length === 0) {
    validationDetail.isValid = false;
    validationDetail.context = 'conditions';
    validationDetail.type = 'error';
    validationDetail.message = <p style={{ textTransform: 'initial' }}> Criteria is Required </p>;

    return validationDetail;
  }

  if (!action) {
    validationDetail.isValid = false;
    validationDetail.context = 'action';
    validationDetail.type = 'error';
    validationDetail.message = 'Action is Required';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'ruleOrder') {
    tooltipDetail.content = `Select the rule order. The sign-on policy automatically assigns the Rule Order number. Policy rules are evaluated in ascending order. Rule Order reflects this rule's place in the order.`;
  }

  if (name === 'name') {
    tooltipDetail.content = `Enter a rule name for the policy`;
  }

  if (name === 'ruleStatus') {
    tooltipDetail.content = `An enabled rule is actively enforced. A disabled rule is not actively enforced but does not lose its place in the Rule Order. The service skips it and moves to the next rule.`;
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. The description cannot exceed 512 characters.`;
  }

  if (name === 'action') {
    tooltipDetail.content = `Choose the action the Zscaler service takes upon detecting access request that matches the rule criteria`;
  }

  return tooltipDetail;
};
