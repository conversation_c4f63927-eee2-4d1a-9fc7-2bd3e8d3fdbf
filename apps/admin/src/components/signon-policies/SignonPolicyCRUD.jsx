import { useContext } from 'react';
import { useSelector } from 'react-redux';

import {
  CRUDModal,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { add, remove, update } from '../../ducks/signon-policies';
import { selectTableDetail } from '../../ducks/signon-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import SignonPolicyForm from './SignonPolicyForm';
import { modalModeDetail } from './helper';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_POLICY_DETAIL = {};

const SignonPolicyCRUD = () => {
  const { apiCall } = useApiCall();

  const { modalMode, setModalMode, detail, setDetail, isFormReadOnly } =
    useContext(CRUDPageContext);

  const tableDetail = useSelector(selectTableDetail);

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setDetail(DEFAULT_POLICY_DETAIL);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add({ ruleOrder: tableDetail.data.length + 1, ...detail }), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(detail), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  return (
    <CRUDModal
      mode={modalMode}
      showSave={!isFormReadOnly}
      renderFormSection={(props) => <SignonPolicyForm {...props} />}
      cancelText={isFormReadOnly ? 'CLOSE' : 'CANCEL'}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default SignonPolicyCRUD;
