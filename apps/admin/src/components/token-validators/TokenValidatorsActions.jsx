import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/token-validators/index';

import { TokenValidatorsContext } from '../../pages/admin/TokenValidatorsPage';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { UI_VIEW_OPTIONS } from '../tokens/helper';

const TokenValidatorsActions = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    searchTerm,
    setSearchTerm,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext);

  const { isZdkCluster, setView } = useContext(TokenValidatorsContext);
  const { hasFullAccess } = privileges;

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  const onTestTokenLinkClick = () => {
    setView(UI_VIEW_OPTIONS.TEST_TOKEN_VIEW);
  };

  return (
    <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
      <div className="buttons has-jc-sb" style={{ flex: 1 }}>
        {hasFullAccess && (
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_TOKEN_VALIDATOR')}</span>
          </Button>
        )}
        {!isZdkCluster && (
          <Button type="tertiary" containerClass="right no-m-r" onClick={onTestTokenLinkClick}>
            {t('TEST_TOKEN')}
          </Button>
        )}
      </div>

      <Search
        onSearch={onSearchEnter}
        term={searchTerm}
        containerClass="no-m-r"
        containerStyle={{ maxWidth: '258px', marginBottom: '20px' }}
      />
    </div>
  );
};

export default TokenValidatorsActions;
