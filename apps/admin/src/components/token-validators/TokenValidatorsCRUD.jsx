import { useContext } from 'react';

import {
  CRUDModal,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { isEmpty, noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';

import {
  addNewTokenValidator,
  deleteTokenValidator,
  updateTokenValidator,
} from '../../ducks/token-validators/index';

import { TokenValidatorsContext } from '../../pages/admin/TokenValidatorsPage';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import TokenValidatorsForm from './TokenValidatorsForm';
import { modalModeDetail } from './helper';

const TokenValidatorsCRUD = () => {
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultModalMode,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const { hasFullAccess, noAccess } = privileges;
  const { isZdkCluster } = useContext(TokenValidatorsContext);

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
  };

  const transformCustomerKey = (formattedCustomerKey) => {
    if (isEmpty(formattedCustomerKey)) return [];

    return formattedCustomerKey.map((key) => {
      if (isZdkCluster) {
        return key.value;
      }
      return {
        fileName: key.fileName,
        value: key.value,
      };
    });
  };

  const transformTokenConfig = (tokenConfig) => {
    if (isZdkCluster) {
      return tokenConfig.claims.map((claim) => ({
        claim: claim.claim,
        value: claim.value,
      }));
    }
    return tokenConfig.map((claim) => ({
      claim: claim.claim,
      value: claim.value,
    }));
  };

  const onSaveClick = () => {
    const payload = { ...detail };

    payload.customerKey = transformCustomerKey(payload.formattedCustomerKey);

    if (isZdkCluster) {
      payload.tokenConfig.claims = transformTokenConfig(payload.tokenConfig);
    } else {
      payload.tokenConfig = transformTokenConfig(payload.tokenConfig);
    }

    delete payload.formattedCustomerKey;

    if (modalMode === 'add') {
      apiCall(addNewTokenValidator(payload, isZdkCluster), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(updateTokenValidator(payload, isZdkCluster), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(deleteTokenValidator(payload, isZdkCluster), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  if (noAccess) {
    return <NoAccess />;
  }

  const showSave = hasFullAccess && !detail.isSystemRole;
  const cancelText = hasFullAccess && !detail.isSystemRole ? 'CANCEL' : 'CLOSE';

  return (
    <CRUDModal
      mode={modalMode}
      containerClass={'modal-container-token-validators'}
      renderFormSection={(props) => <TokenValidatorsForm {...props} />}
      saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
      cancelText={cancelText}
      showSave={showSave}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default TokenValidatorsCRUD;
