import { useContext, useEffect, useState } from 'react';

import { Button } from '@zscaler/zui-component-library';

import { getAuthenticationLevelMapping } from '../../ducks/authentication-levels/helper';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';
import AuthenticationLevelImage from '../../images/authentication-level.svg';
import AuthenticationLevelsOverview from './AuthenticationLevelsOverview';
import { getAddNewLevelDetail } from './helper';

const AuthenticationLevelsListContainer = () => {
  const { levelsList, detail, setDetail, pageMode, setPageMode } = useContext(
    AuthenticationLevelsPageContext,
  );

  const [activeList, setActiveList] = useState(levelsList);

  useEffect(() => {
    setActiveList(levelsList);
  }, [levelsList, detail]);

  const onNewLevelClick = () => {
    setPageMode('add');

    const treeMapping = {};
    const levelDetailMapping = {};

    const detail = getAuthenticationLevelMapping({
      levelDetail: getAddNewLevelDetail(1),
      levelDetailMapping,
      treeMapping,
    });

    setDetail(detail);
  };

  if (levelsList.length === 0) {
    return (
      <>
        <img
          src={AuthenticationLevelImage}
          style={{ height: '230px', width: '230px' }}
          alt="brand icon"
        />

        <p className="text-center"> Saved Authentication levels will appear here</p>
      </>
    );
  }

  const renderOverviewListSection = () => {
    return activeList.map(({ treeMapping, ...rest }) => (
      <AuthenticationLevelsOverview
        key={`${treeMapping?.[1]?.id}`}
        treeMapping={treeMapping}
        {...rest}
        currentTree={treeMapping?.[1]?.id}
      />
    ));
  };

  return (
    <div className="al-list-container">
      <Button containerClass="full-width" onClick={onNewLevelClick} disabled={pageMode !== 'view'}>
        New Authentication Level
      </Button>

      {renderOverviewListSection()}
    </div>
  );
};

export default AuthenticationLevelsListContainer;
