import { useContext, useEffect, useMemo, useState } from 'react';

import { faChevronDown, faChevronUp, faShieldCheck } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { cloneDeep, findIndex, last } from 'lodash-es';
import PropTypes from 'prop-types';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';

const DEFAULT_PROPS = {
  level: '1',
  levelDetailMapping: {},
  treeMapping: {},
  currentTree: '',
  isLastElement: false,
};

const AuthenticationLevelsOverview = ({
  level = DEFAULT_PROPS.level,
  levelDetailMapping = DEFAULT_PROPS.levelDetailMapping,
  treeMapping = DEFAULT_PROPS.treeMapping,
  currentTree = DEFAULT_PROPS.currentTree,
  isLastElement = DEFAULT_PROPS.isLastElement,
}) => {
  const {
    pageMode,
    levelsList,
    setSelectedTree,
    selectedTree,
    detail: selectedLevelDetail,
    setDetail,
    setLevelsList,
    hideBranchMapping,
    setHideBranchMapping,
  } = useContext(AuthenticationLevelsPageContext);

  const { id, subLevels, nesting, isSubLevel } = treeMapping[level] || [];
  const { name } = levelDetailMapping[id] || {};

  const [currentSubLevels, setCurrentSubLevels] = useState(subLevels || []);

  const { treeMapping: currentSelectedTreeMapping } = selectedLevelDetail;

  const currentSelectedNodeCount = useMemo(
    () => Object.keys(currentSelectedTreeMapping).length,
    [Object.keys(currentSelectedTreeMapping).length, levelsList],
  );

  useEffect(() => {
    setCurrentSubLevels(cloneDeep(subLevels || []));
  }, [
    id,
    subLevels,
    nesting,
    treeMapping,
    currentSelectedNodeCount,
    level,
    currentSelectedTreeMapping,
    levelsList,
    hideBranchMapping,
  ]);

  const isLevelSelected = selectedTree === currentTree;

  const canSelect = () => {
    if (pageMode === 'add') {
      return false;
    }

    if (pageMode === 'edit') {
      return isLevelSelected;
    }

    return pageMode === 'view';
  };

  const onSelectedTreeClick = (evt) => {
    evt.stopPropagation();

    if (!canSelect()) {
      return;
    }

    if (selectedTree != currentTree) {
      if (pageMode === 'edit') {
        const updatedList = cloneDeep(levelsList);

        const selectedTreeIndex = findIndex(
          updatedList,
          (o) => o?.treeMapping?.[1]?.id === selectedTree,
        );

        updatedList.splice(selectedTreeIndex, 1, selectedLevelDetail);

        setLevelsList(updatedList);
      }

      const currentTreeIndex = findIndex(
        levelsList,
        (o) => o?.treeMapping?.[1]?.id === currentTree,
      );

      setDetail(levelsList[currentTreeIndex] || {});
      setSelectedTree(currentTree);
    }
  };

  const onChangeBranchVisibilityClick = () => {
    if (!canSelect()) {
      return;
    }

    setHideBranchMapping((prevState) => {
      const newState = cloneDeep(prevState || {});

      newState[id] = !newState[id];

      return newState || {};
    });
  };

  const getContainerClass = () => {
    let className = '';

    if (!isSubLevel) {
      className += 'is-top-level ';
    }

    if (isLevelSelected) {
      className += ' is-selected';
    }

    if (canSelect()) {
      className += ' can-select pointer';
    } else {
      className += ' disabled';
    }

    if (!isLastElement) {
      className += ' branch-indicator';
    }

    return className;
  };

  const hasSubLevels = currentSubLevels?.length > 0;

  const showSubLevelContent = !hideBranchMapping[id];

  return (
    <div
      className={`authentication-level-overview is-flex has-fd-c authentication-nesting-${nesting}  ${getContainerClass()}`}
      onClick={onSelectedTreeClick}
    >
      <div className={`${isLastElement ? 'partial-branch-indicator' : ''}`}>
        <div
          className={`is-flex has-ai-c level-name-container ${isSubLevel ? '' : 'is-top-level pointer'} `}
          onClick={onChangeBranchVisibilityClick}
        >
          {hasSubLevels && (
            <FontAwesomeIcon
              className={`icon left has-color-primary pointer ${!isSubLevel ? 'right' : ''}`}
              icon={showSubLevelContent ? faChevronDown : faChevronUp}
            />
          )}

          {!isSubLevel && (
            <FontAwesomeIcon
              className="icon has-color-primary"
              style={{ marginLeft: hasSubLevels ? 'initial' : '30px' }}
              icon={faShieldCheck}
            />
          )}

          <div className="level-name">{name || level} </div>
        </div>
      </div>

      {showSubLevelContent &&
        currentSubLevels.map((subLevel) => {
          const isLastElement = last(currentSubLevels) === subLevel;

          return (
            <AuthenticationLevelsOverview
              key={`${currentTree}.${id}.${subLevel}`}
              level={subLevel}
              levelDetailMapping={levelDetailMapping}
              treeMapping={treeMapping}
              currentTree={currentTree}
              isLastElement={isLastElement}
            />
          );
        })}
    </div>
  );
};

AuthenticationLevelsOverview.propTypes = {
  level: PropTypes.object,
  levelDetailMapping: PropTypes.object,
  treeMapping: PropTypes.object,
  currentTree: PropTypes.string,
  isLastElement: PropTypes.bool,
};

export default AuthenticationLevelsOverview;
