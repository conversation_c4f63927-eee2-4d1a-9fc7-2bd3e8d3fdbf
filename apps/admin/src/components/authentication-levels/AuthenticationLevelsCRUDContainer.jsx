import { useContext, useEffect } from 'react';
import { useSelector } from 'react-redux';

import { cloneDeep } from 'lodash-es';

import { getAuthenticationLevelMapping } from '../../ducks/authentication-levels/helper';
import { selectList } from '../../ducks/authentication-levels/selectors';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';
import AuthenticationLevelContainer from './AuthenticationLevelContainer';
import AuthenticationLevelsListContainer from './AuthenticationLevelsListContainer';

const AuthenticationLevelsCRUDContainer = () => {
  const { pageMode } = useContext(AuthenticationLevelsPageContext);

  const list = useSelector(selectList);

  const { setLevelsList, setSelectedTree, setDetail, setPageMode } = useContext(
    AuthenticationLevelsPageContext,
  );

  useEffect(() => {
    const levelsList = [];

    list.forEach((level) => {
      const treeMapping = {};
      const levelDetailMapping = {};

      const detail = getAuthenticationLevelMapping({
        levelDetail: cloneDeep(level),
        levelDetailMapping,
        treeMapping,
      });

      levelsList.push(detail);
    });

    const selectedTree = list?.[0]?.id || '';

    setLevelsList(levelsList);
    setDetail(levelsList[0] || {});
    setSelectedTree(selectedTree);

    if (list.length > 0) {
      setPageMode('view');
    }
  }, [list]);

  if (!pageMode) {
    return null;
  }

  return (
    <div className="is-flex" style={{ height: 'calc(100vh - 140px)' }}>
      <AuthenticationLevelsListContainer />

      <AuthenticationLevelContainer />
    </div>
  );
};

export default AuthenticationLevelsCRUDContainer;
