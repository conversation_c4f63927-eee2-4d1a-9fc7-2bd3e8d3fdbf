import { useContext, useEffect, useState } from 'react';

import { faArrowTurnDownRight } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, CRUDModal, useApiCall } from '@zscaler/zui-component-library';

import { cloneDeep, last, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { remove } from '../../ducks/authentication-levels';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';
import AuthenticationLevelForm from './AuthenticationLevelForm';
import { addNewLevel } from './helper';

const DEFAULT_PROPS = {
  level: '1',
  levelDetailMapping: {},
  treeMapping: {},
};

const AuthenticationLevel = ({
  level = DEFAULT_PROPS.level,
  levelDetailMapping = DEFAULT_PROPS.levelDetailMapping,
  treeMapping = DEFAULT_PROPS.treeMapping,
}) => {
  const { apiCall } = useApiCall();

  const { pageMode, selectedTree, setDetail, pageModeActionType, setPageModeActionType } =
    useContext(AuthenticationLevelsPageContext);

  const { subLevels, nesting, isNestingAllowed } = treeMapping[level] || {};

  const [modalMode, setModalMode] = useState('');

  const [subLevelList, setSubLevelList] = useState(subLevels || []);

  useEffect(() => {
    setSubLevelList(subLevels || []);
  }, [subLevels]);

  const hasSubLevels = subLevelList?.length > 0;

  const onAddSubLevelClick = () => {
    setPageModeActionType('add');

    const newLevel = addNewLevel({
      level,
      levelDetailMapping,
      treeMapping,
      subLevels: subLevelList,
      parentLevel: level,
      nesting: nesting + 1,
    });

    setSubLevelList((prevState) => {
      const newState = cloneDeep(prevState);

      newState.push(newLevel);

      treeMapping[level].subLevels = newState;

      return newState;
    });

    setDetail({ level: '1', levelDetailMapping, treeMapping });
  };

  const onSaveClick = () => {
    apiCall(remove({ id: selectedTree }))
      .then(() => {
        setModalMode('');
        setPageModeActionType('');
      })
      .catch(noop);
  };

  const onCloseClick = () => {
    setModalMode('');
  };

  const canAddSubLevel = pageModeActionType === '' || pageModeActionType !== 'move';

  return (
    <div className={`authentication-level is-flex has-fd-c authentication-nesting-${nesting}`}>
      <div className={`${hasSubLevels ? 'branch-indicator' : ''}`}>
        <AuthenticationLevelForm
          level={level}
          levelDetailMapping={levelDetailMapping}
          treeMapping={treeMapping}
          hasSubLevels={hasSubLevels}
          setModalMode={setModalMode}
        />
      </div>

      {subLevelList.map((subLevel) => {
        const isLastElement = last(subLevelList) === subLevel;

        return (
          <div key={subLevel} className={`${!isLastElement ? 'branch-indicator' : ''}`}>
            <AuthenticationLevel
              key={subLevel}
              level={subLevel}
              levelDetailMapping={levelDetailMapping}
              treeMapping={treeMapping}
              setDetail={setDetail}
            />
          </div>
        );
      })}

      <div>
        {isNestingAllowed && pageMode !== 'view' && (
          <Button
            type="tertiary"
            containerClass="create-level-btn content-width no-p"
            onClick={onAddSubLevelClick}
            disabled={!canAddSubLevel}
          >
            <FontAwesomeIcon icon={faArrowTurnDownRight} className="icon left" />
            Create level {`${level}.${subLevelList.length + 1}`}
          </Button>
        )}
      </div>

      {modalMode && (
        <CRUDModal
          mode={modalMode}
          onSaveClick={onSaveClick}
          onCloseClick={onCloseClick}
          headerText={modalModeDetail[modalMode].headerText}
          confirmationMessage={modalModeDetail[modalMode].confirmationMessage}
          saveText={'DELETE'}
        />
      )}
    </div>
  );
};

const modalModeDetail = {
  delete: {
    headerText: 'DELETE_AL',
    confirmationMessage: 'DELETE_AL_CONFIRM_MSG',
  },
};

AuthenticationLevel.propTypes = {
  level: PropTypes.string,
  levelDetailMapping: PropTypes.object,
  treeMapping: PropTypes.object,
  setDetail: PropTypes.func,
};

export default AuthenticationLevel;
