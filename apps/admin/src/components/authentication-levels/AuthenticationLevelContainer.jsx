import { useContext } from 'react';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';
import AuthenticationLevel from './AuthenticationLevel';

const AuthenticationLevelContainer = () => {
  const { detail } = useContext(AuthenticationLevelsPageContext);

  return (
    <div className="al-form is-flex has-fd-c" style={{ gap: '12px' }}>
      <div> Details </div>

      <div style={{ marginBottom: '24px' }}>Configure details for your authentication</div>

      <AuthenticationLevel {...detail} />
    </div>
  );
};

export default AuthenticationLevelContainer;
