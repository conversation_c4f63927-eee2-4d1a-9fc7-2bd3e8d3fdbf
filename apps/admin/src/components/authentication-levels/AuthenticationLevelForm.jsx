import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import { faFolderTree } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  DropDown,
  FieldGroup,
  Input,
  TextArea,
  mergeFormValues,
} from '@zscaler/zui-component-library';

import { find, reject, without } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  VALIDITY_TYPE_LIST,
  getAuthenticationLevelFlatMapping,
  getAuthenticationLevelMapping,
} from '../../ducks/authentication-levels/helper';
import { showErrorNotification } from '../../ducks/global';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';
import { getCanMoveList, getFormTooltipDetail, getFormValidationDetail } from './helper';

const DEFAULT_PROPS = {
  level: '1',
  levelDetailMapping: {},
  treeMapping: {},
};

const AuthenticationLevelForm = ({
  level = DEFAULT_PROPS.level,
  levelDetailMapping = DEFAULT_PROPS.levelDetailMapping,
  treeMapping = DEFAULT_PROPS.treeMapping,
  hasSubLevels,
  setModalMode,
}) => {
  const dispatch = useDispatch();

  const { isFormReadOnly, pageMode, detail, setDetail, pageModeActionType, setPageModeActionType } =
    useContext(AuthenticationLevelsPageContext);

  const { id, isSubLevel, parentLevel } = treeMapping[level] || {};

  const { name, timeout, validityType, description, userMessage, childAuthenticationLevels } =
    levelDetailMapping[id] || {};

  const { id: parentId } = treeMapping[parentLevel] || {};

  const { name: parentName } = levelDetailMapping[parentId] || {};

  const [formValues, setFormValues] = useState({
    name,
    timeout,
    description,
    userMessage,
    childAuthenticationLevels,
  });

  useEffect(() => {
    setFormValues((prevState) => ({
      ...prevState,
      name,
      timeout,
      validityType,
      description,
      userMessage,
      childAuthenticationLevels,
    }));
  }, [name, timeout, validityType, description, userMessage, childAuthenticationLevels]);

  const [validationDetail, setValidationDetail] = useState({});

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({ formValues });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const [selectedValidityTypeList, setSelectedValidityTypeList] = useState([validityType]);

  useEffect(() => {
    setSelectedValidityTypeList([validityType]);
  }, [validityType]);

  const onFormFieldChange = (evt) => {
    const newFormValues = mergeFormValues(evt)(formValues);

    const prevDetail = levelDetailMapping[id] || {};

    levelDetailMapping[id] = { ...prevDetail, ...newFormValues };

    setFormValues(newFormValues);
  };

  const getMoveList = () => {
    const list = [];

    const excludeLevelDetail = treeMapping[level];

    if (excludeLevelDetail) {
      getCanMoveList({ excludeLevelDetail, treeMapping, list });
    }

    return list;
  };

  const MOVE_LIST = getMoveList();

  const onMoveSelection = (selectedNode) => {
    const newLevel = selectedNode?.[0]?.value || '';

    if (newLevel) {
      const { treeMapping, levelDetail } = detail;

      const flatList = [];

      getAuthenticationLevelFlatMapping({
        levelDetail,
        flatList,
      });

      const newLevelToMoveDetail = find(flatList, { id: treeMapping[newLevel].id });
      const oldLevelDetail = find(flatList, { id: treeMapping[level].id });
      const oldParentLevelDetail = find(flatList, { id: treeMapping[parentLevel].id });

      if (newLevelToMoveDetail && oldParentLevelDetail) {
        newLevelToMoveDetail.childAuthenticationLevels.push(oldLevelDetail);

        oldParentLevelDetail.childAuthenticationLevels = reject(
          oldParentLevelDetail.childAuthenticationLevels,
          { id: treeMapping[level].id },
        );

        const newLevelDetail = getAuthenticationLevelMapping({ levelDetail });

        setDetail((prevState) => ({ ...prevState, ...newLevelDetail }));

        setPageModeActionType('move');
      }
    }
  };

  const onDeleteClick = () => {
    if (parentLevel) {
      const { subLevels: parentSubLevels = [] } = treeMapping[parentLevel] || {};

      const newState = without(parentSubLevels, level);

      treeMapping[parentLevel].subLevels = newState;

      delete treeMapping[level];
      delete levelDetailMapping[id];

      setDetail({ level: '1', levelDetailMapping, treeMapping });

      setPageModeActionType('delete');
    } else {
      if (pageMode !== 'add') {
        setModalMode('delete');
      } else {
        dispatch(
          showErrorNotification({
            message: 'Cannot delete a level that is not saved.',
          }),
        );
      }
    }
  };

  const onValidityTypeSelection = (detail) => {
    const newFormValues = { validityType: detail?.[0] };

    const prevDetail = levelDetailMapping[id] || {};

    levelDetailMapping[id] = { ...prevDetail, ...newFormValues };

    setFormValues((prevState) => ({ ...prevState, ...newFormValues }));

    setSelectedValidityTypeList(detail);
  };

  const canMove =
    MOVE_LIST.length !== 0 && (pageModeActionType === '' || pageModeActionType === 'move');

  const canDeleteLevel = pageModeActionType === '' || pageModeActionType !== 'move';

  return (
    <>
      <div className={`al-level-name-container is-flex has-jc-sb has-ai-c`}>
        <div className="level-name">{level}</div>

        <div className="is-flex has-jc-sb" style={{ gap: '12px' }}>
          {isSubLevel && pageMode === 'edit' && (
            <>
              <DropDown
                list={MOVE_LIST}
                onSelection={onMoveSelection}
                hasTextIcon
                hasActiveIcon={false}
                textIcon={faFolderTree}
                renderSelectedItemsText={() => 'Move'}
                kind="tertiary"
                containerClass="move-dd"
                disabled={!canMove}
              />
            </>
          )}

          {!hasSubLevels && (pageMode === 'edit' || pageMode === 'add') && (
            <Button
              type="tertiary"
              containerClass="content-width no-p delete-icon"
              onClick={onDeleteClick}
              disabled={!canDeleteLevel}
            >
              <FontAwesomeIcon icon={faTrashAlt} className="icon left 2x" />
            </Button>
          )}
        </div>
      </div>

      <div className="field-container">
        <FieldGroup>
          <Input
            label={!isSubLevel ? 'LEVEL_NAME' : 'SUB_LEVEL_NAME'}
            name="name"
            value={name}
            onChange={onFormFieldChange}
            maxLength="16"
            info={validationDetail}
            disabled={isFormReadOnly}
            containerStyle={{ maxWidth: '250px', minWidth: '250px' }}
          />

          <Input
            type="number"
            min="0"
            label="VALIDITY"
            name="timeout"
            value={timeout}
            onChange={onFormFieldChange}
            disabled={isFormReadOnly}
            info={validationDetail}
            containerClass={`validity-input-container`}
            containerStyle={{ maxWidth: '250px', minWidth: '250px' }}
            suffixSection={
              <DropDown
                list={VALIDITY_TYPE_LIST}
                selectedList={selectedValidityTypeList}
                onSelection={onValidityTypeSelection}
                disabled={isFormReadOnly}
              />
            }
          />

          {(parentName || parentLevel) && (
            <Input label="PARENT" name="parentName" value={parentName || parentLevel} disabled />
          )}
        </FieldGroup>

        <FieldGroup>
          <TextArea
            name="description"
            value={description}
            onChange={onFormFieldChange}
            label="DESCRIPTION"
            maxLength="250"
            rows="3"
            info={validationDetail}
            tooltip={isFormReadOnly ? {} : getFormTooltipDetail('description')}
            disabled={isFormReadOnly}
            containerStyle={{ maxWidth: '250px', minWidth: '250px' }}
          />

          <TextArea
            name="userMessage"
            value={userMessage}
            onChange={onFormFieldChange}
            label="MESSAGE_TO_USER"
            maxLength="250"
            rows="3"
            tooltip={isFormReadOnly ? {} : getFormTooltipDetail('userMessage')}
            disabled={isFormReadOnly}
            containerStyle={{ maxWidth: '250px', minWidth: '250px' }}
          />
        </FieldGroup>
      </div>
    </>
  );
};

AuthenticationLevelForm.propTypes = {
  level: PropTypes.string,
  levelDetailMapping: PropTypes.object,
  treeMapping: PropTypes.object,
  setDetail: PropTypes.func,
  hasSubLevels: PropTypes.bool,
  setModalMode: PropTypes.func,
};

export default AuthenticationLevelForm;
