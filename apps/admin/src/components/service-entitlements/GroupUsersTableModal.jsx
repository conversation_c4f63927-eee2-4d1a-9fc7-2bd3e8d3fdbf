import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Search, TableContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getGroupUsers } from '../../ducks/service-entitlements';
import {
  selectGroupUsersTableConfig,
  selectGroupUsersTableDetail,
} from '../../ducks/service-entitlements/selectors';

const GroupUsersTable = ({ detail }) => {
  // eslint-disable-next-line react/prop-types
  const { id, resource } = detail;
  let resourceId = null;

  if (resource) {
    resourceId = resource.id;
  }

  const { apiCall } = useApiCall();
  const tableConfig = useSelector(selectGroupUsersTableConfig);
  const tableDetail = useSelector(selectGroupUsersTableDetail);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    apiCall(getGroupUsers({ id: resourceId || id })).catch(noop);
  }, []);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(
      getGroupUsers({
        id: resourceId || id,
        requireTotal: false,
        pageOffset: pageOffset + pageSize,
        pageSize,
      }),
    ).catch(noop);
  };

  const onSearchEnter = (term) => {
    apiCall(getGroupUsers({ id: resourceId || id, name: term })).catch(noop);

    setSearchTerm(term);
  };

  return (
    <article>
      <div className="group-users-container">
        <div className="is-flex has-jc-e group-users-search-container">
          <div>
            <Search
              onSearch={onSearchEnter}
              term={searchTerm}
              containerClass="group-users-search"
              containerStyle={{ width: '260px' }}
            />
          </div>
        </div>
        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          containerClass={'group-users-table'}
          data={tableDetail.data}
          pagination={{ ...tableDetail, onLoadMoreClick }}
        />
      </div>
    </article>
  );
};
GroupUsersTable.propTypes = {
  detail: PropTypes.object,
};

export default GroupUsersTable;
