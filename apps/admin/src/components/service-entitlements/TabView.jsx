import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faPlus, faTrash, faUser } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Actions,
  Button,
  CRUDModal,
  Card,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Search,
  Selector,
  TableContainer,
  TextWithTooltip,
  getApiDELETENotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { modalModeDetail } from '../../components/service-entitlements/helper';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import {
  bulkDelete,
  getEntityList,
  remove as removeEntity,
} from '../../ducks/service-entitlements';
import {
  selectEntityTableConfig,
  selectEntityTableDetail,
} from '../../ducks/service-entitlements/selectors';

import { PERMISSIONS_KEY } from '../../config';
import AssignEntityView from './AssignEntityView';
import GroupUsersTableModal from './GroupUsersTableModal';

const defaultProps = {
  service: {},
  privileges: {
    hasFullAccess: false,
  },
};

const TabView = ({ service, onAssignEntityClick, onDetailsFetch, privileges, entityType }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall({});
  const labelText = entityType === 'GROUP' ? 'GROUPS' : 'USERS';
  const { hasFullAccess: serviceEntitlementFullAccess } = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY),
  );

  const { hasFullAccess, hasViewAccess } = privileges;

  useEffect(() => {
    apiCall(getEntityList({ id: service?.id, entityType })).catch(noop);
    setSearchTerm('');
  }, [entityType]);

  const tableConfig = useSelector(selectEntityTableConfig);
  const tableDetail = useSelector(selectEntityTableDetail);

  useEffect(() => {
    onDetailsFetch(tableDetail?.totalRecord);
  }, [tableDetail]);

  const [showAssignEntityView, setShowAssignEntityView] = useState(false);
  const [selectedRowDetail, setSelectedRowDetail] = useState([]);
  const [modalMode, setModalMode] = useState('');
  const [entityDetail, setEntityDetail] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [isBulkRowsSelected, setIsBulkRowsSelected] = useState(false);
  const [showGroupUsersTableModal, setShowGroupUsersTableModal] = useState(false);
  const [groupUsersTableModalData, setGroupUsersTableModalData] = useState(false);

  useEffect(() => {
    setIsBulkRowsSelected(selectedRowDetail.length > 1);
  }, [selectedRowDetail]);

  const onDeleteClick = (detail) => {
    if (detail) {
      setEntityDetail(detail);
      setModalMode('delete');
    }
  };

  const onCloseClick = () => {
    setModalMode('');
  };

  const onSaveClick = () => {
    if (modalMode === 'delete') {
      apiCall(
        removeEntity({ id: entityDetail?.id, tservice: entityDetail?.tservice, entityType }),
        {
          successNotificationPayload: { ...getApiDELETENotificationOptions() },
        },
      )
        .then(onCloseClick)
        .catch(noop);
    } else if (modalMode === 'bulkDelete') {
      const ids = selectedRowDetail.map(({ id }) => id);
      apiCall(bulkDelete({ ids, tservice: service, entityType }), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'userId') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { resource } = props?.row?.original || {};

          return <TextWithTooltip text={resource?.loginName} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          return (
            <Actions
              {...props}
              showEdit={false}
              onDeleteClick={onDeleteClick}
              showDelete={(hasFullAccess || hasViewAccess) && serviceEntitlementFullAccess}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }
      if (columnDetail.id === 'groupUsers') {
        const UsersCellComponent = (props) => {
          return (
            <Button
              type="tertiary"
              onClick={() => {
                // eslint-disable-next-line react/prop-types
                setGroupUsersTableModalData(props?.row?.original);
                setShowGroupUsersTableModal(true);
              }}
            >
              <FontAwesomeIcon icon={faUser} className="icon left" />
              {t('USERS')}
            </Button>
          );
        };

        columnDetail.cell = UsersCellComponent;
      }

      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { name, idp } = props?.row?.original?.resource || {};

          let derivedName = name;

          const idpName = idp?.name || '';

          if (idpName) {
            derivedName = `${name} (${idpName})`;
          }

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });
    let newColumns = [];

    if ((hasFullAccess || hasViewAccess) && serviceEntitlementFullAccess) {
      newColumns = [
        {
          id: 'selection',
          Header: '',
          cell: (props) => <Selector {...props} />,
          size: 50,
          minSize: 50,
          maxSize: 50,
          enableResizing: false,
          disableSortBy: true,
          defaultCanSort: false,
        },
        ...(tableConfig?.columns || []),
      ];
    } else {
      newColumns = [...(tableConfig?.columns || [])];
      remove(newColumns, { id: 'actions' });
    }

    if (entityType === 'GROUP') {
      remove(newColumns, { id: 'userId' });
    }

    if (entityType === 'USER') {
      remove(newColumns, { id: 'groupUsers' });
    }
    return newColumns;
  }, [tableConfig?.columns, hasFullAccess, entityType]);

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(
      getEntityList({
        id: service?.id,
        requireTotal: false,
        pageOffset: pageOffset + pageSize,
        pageSize,
        entityType,
      }),
    ).catch(noop);
  };

  const onAddClick = () => {
    setShowAssignEntityView(true);
    onAssignEntityClick(true);
  };

  const hideAssignEntityView = () => {
    onAssignEntityClick(false);
    setShowAssignEntityView(false);
    apiCall(getEntityList({ id: service?.id, entityType })).catch(noop);
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);
  };

  const removeAssignments = () => {
    setModalMode('bulkDelete');
  };

  const cancelRemoveAssignments = () => {
    setSelectedRowDetail([]);
  };

  const onSearchEnter = (term) => {
    apiCall(getEntityList({ id: service?.id, name: term, entityType })).catch(noop);

    setSearchTerm(term);
  };

  const onCloseGroupUsersModal = () => {
    setShowGroupUsersTableModal(false);
  };

  const renderBodySection = () => {
    return <GroupUsersTableModal detail={groupUsersTableModalData} />;
  };

  return (
    <>
      {showAssignEntityView ? (
        <AssignEntityView
          serviceDetail={service}
          onBackClick={hideAssignEntityView}
          entityType={entityType}
        />
      ) : (
        <Card contianerClass="services-tabs">
          <div
            className={`is-flex full-width ${
              (hasFullAccess || hasViewAccess) && serviceEntitlementFullAccess
                ? 'has-jc-sb'
                : 'has-jc-e'
            }`}
          >
            {(hasFullAccess || hasViewAccess) && serviceEntitlementFullAccess ? (
              <>
                {isBulkRowsSelected && (
                  <div className="buttons">
                    {isBulkRowsSelected && (
                      <>
                        <Button type="secondary" onClick={removeAssignments}>
                          <FontAwesomeIcon icon={faTrash} className="icon left" />
                          <span>{t('REMOVE_ASSIGNMENTS')}</span>
                        </Button>
                        <Button type="tertiary" onClick={cancelRemoveAssignments}>
                          <span>{t('CANCEL')}</span>
                        </Button>
                      </>
                    )}
                  </div>
                )}
                {!isBulkRowsSelected && (
                  <div className="buttons">
                    <Button onClick={onAddClick}>
                      <FontAwesomeIcon icon={faPlus} className="icon left" />
                      <span>{t(`ASSIGN_${labelText}`)}</span>
                    </Button>
                  </div>
                )}
              </>
            ) : null}

            <div className="buttons">
              <Search
                onSearch={onSearchEnter}
                term={searchTerm}
                containerStyle={{ width: '260px' }}
              />
            </div>
          </div>

          <TableContainer
            {...tableConfig}
            columns={tableColumnConfig}
            containerClass={'services-tabs-table'}
            data={tableDetail.data}
            onRowSelection={onRowSelection}
            pagination={{ ...tableDetail, onLoadMoreClick }}
          />
          {modalMode && (
            <CRUDModal
              mode={modalMode}
              cancelText={'CANCEL'}
              onSaveClick={onSaveClick}
              onCloseClick={onCloseClick}
              {...modalModeDetail(entityType, modalMode)}
            />
          )}
          {showGroupUsersTableModal && (
            <Modal
              show={showGroupUsersTableModal}
              onEscape={onCloseGroupUsersModal}
              containerClass="group-users-modal crud-modal"
            >
              <ModalHeader text="USERS" onClose={onCloseGroupUsersModal} />
              <ModalBody> {renderBodySection()}</ModalBody>
              <ModalFooter cancelText="OK" showSave={false} onCancel={onCloseGroupUsersModal} />
            </Modal>
          )}
        </Card>
      )}
    </>
  );
};

TabView.defaultProps = defaultProps;

TabView.propTypes = {
  onAssignEntityClick: PropTypes.func,
  onDetailsFetch: PropTypes.func,
  service: PropTypes.object,
  privileges: PropTypes.object,
  entityType: PropTypes.string,
};

export default TabView;
