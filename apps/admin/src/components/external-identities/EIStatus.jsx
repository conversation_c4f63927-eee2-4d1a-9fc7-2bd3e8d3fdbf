import { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { faCheckCircle, faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons';
import { Card, StatusTag, Tooltip } from '@zscaler/zui-component-library';

import PropTypes from 'prop-types';

const defaultProps = {
  row: {
    original: {},
  },
};

const EIStatus = ({ row }) => {
  const { t } = useTranslation();

  const elementRef = useRef();

  const { original: { alertMessage = {}, status } = {} } = row;

  const { code, detailed } = alertMessage;

  const canShowTooltip = code === 'IDP_CERTIFICATE_EXPIRED';

  const renderTooltipSection = () => {
    return (
      <Card>
        <span className="has-color-error">{t(detailed)}</span>
      </Card>
    );
  };

  const getTruthyIcon = () => {
    if (code === 'IDP_CERTIFICATE_EXPIRED') {
      return faExclamationTriangle;
    } else {
      return faCheckCircle;
    }
  };

  const getTruthyIconClass = () => {
    if (code === 'IDP_CERTIFICATE_EXPIRED') {
      return 'has-color-warning';
    } else {
      return 'has-color-success';
    }
  };

  return (
    <div ref={elementRef}>
      <StatusTag
        type="ENABLED_DISABLED"
        truthyIcon={getTruthyIcon()}
        truthyIconClass={getTruthyIconClass()}
        value={status}
      />
      {canShowTooltip && <Tooltip elementRef={elementRef}>{renderTooltipSection()}</Tooltip>}
    </div>
  );
};

EIStatus.defaultProps = defaultProps;

EIStatus.propTypes = {
  row: PropTypes.object,
};

export default EIStatus;
