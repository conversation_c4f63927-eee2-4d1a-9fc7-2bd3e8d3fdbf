import { useTranslation } from 'react-i18next';

import { Card, FieldGroup } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import ProvisioningSettingsForm from './ProvisioningSettingsForm';

const defaultProps = {
  isEditMode: false,
  formValues: {},
  onFormFieldChange: noop,
  setFormValues: noop,
  privileges: {
    hasFullAccess: false,
  },
};

const ProvisioningForm = ({
  isEditMode,
  formValues,
  setFormValues,
  privileges,
  validationDetail,
}) => {
  const { t } = useTranslation();

  const { scimProvisionEnabled } = formValues?.scimConfigInfo || {};

  return (
    <>
      <Card>
        <FieldGroup containerClass="has-jc-sb has-ai-c provisioning-form-container">
          <div className="is-flex has-fd-c">
            <div className="is-flex has-ai-c">
              <span> {t('SCIM_PROVISIONING_STATUS')} </span>
            </div>
            <div className={`status-container ${scimProvisionEnabled ? 'enabled' : 'disabled'}`}>
              {scimProvisionEnabled ? t('ENABLED') : t('DISABLED')}
            </div>
          </div>
        </FieldGroup>
      </Card>

      <ProvisioningSettingsForm
        onDetailChange={setFormValues}
        detail={formValues}
        mode={isEditMode ? 'edit' : 'add'}
        privileges={privileges}
        validationDetail={validationDetail}
      />
    </>
  );
};

ProvisioningForm.defaultProps = defaultProps;

ProvisioningForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
  privileges: PropTypes.object,
  validationDetail: PropTypes.object,
};

export default ProvisioningForm;
