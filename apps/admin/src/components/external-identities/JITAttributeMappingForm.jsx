import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  DropDown,
  Field,
  Input,
  defaultFormPropTypes,
  defaultFormProps,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { cloneDeep, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { showWarningNotification } from '../../ducks/global';
import { getList } from '../../ducks/user-attributes';
import { selectAttributeList } from '../../ducks/user-attributes/selectors';

import { getAttributeMappingValidationDetail, getFormTooltipDetail } from './helper';

const defaultProps = {
  ...defaultFormProps,
  privileges: {
    hasFullAccess: false,
  },
};

const defaultMapping = {
  attrName: '',
  user: [],
};

const JITAttributeMappingForm = ({ detail, onDetailChange, privileges }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { hasFullAccess } = privileges;

  const attributeList = useSelector(selectAttributeList);
  const [selectedAttributeMapping, setSelectedAtrributeMapping] = useState(() => {
    const attributes = [];

    const { jitUserAttrMappings } = detail?.jitConfigInfo || {};

    if (jitUserAttrMappings?.length > 0) {
      jitUserAttrMappings?.forEach(({ mappedAttrDisplayName, mappedAttrName, attrName }) => {
        attributes.push({
          user: [{ label: mappedAttrDisplayName || mappedAttrName, value: mappedAttrName }],
          attrName,
        });
      });

      return attributes;
    }

    return [{ ...defaultMapping }];
  });

  const { jitGroupAttrName, jitProvisionEnabled } = detail?.jitConfigInfo || {};

  const { isDropDownLoading, onDropDownOpen } = useDropDownActions({
    apiCallFunc: getList,
  });

  const updateFormValues = (newAttributeMapping) => {
    let jitUserAttrMappings = [];

    newAttributeMapping?.forEach(({ attrName, user }) => {
      if (attrName && user?.[0]?.value) {
        jitUserAttrMappings.push({
          mappedAttrName: user?.[0]?.value,
          attrName,
        });
      }
    });

    onDetailChange((prevState) => {
      const jitConfigInfo = {
        ...prevState.jitConfigInfo,
        jitUserAttrMappings,
      };

      return { ...prevState, jitConfigInfo };
    });
  };

  const getValue = (idx, type) => {
    return selectedAttributeMapping?.[idx]?.[type] || '';
  };

  const onChange = (evt, type, idx) => {
    const { value } = evt.target || {};

    const attributeMapping = selectedAttributeMapping[idx] || {};

    attributeMapping[type] = value;

    setSelectedAtrributeMapping((prevState) => {
      prevState[idx] = attributeMapping;

      updateFormValues(prevState);

      return [...prevState];
    });
  };

  const getSelectedUserAttribute = (idx) => {
    return selectedAttributeMapping?.[idx]?.user || [];
  };

  const onUserAttributeSelection = (detail, idx) => {
    const attributeMapping = cloneDeep(selectedAttributeMapping[idx]) || {};

    attributeMapping.user = detail;

    const selectedUserAttribute = {};

    selectedAttributeMapping.forEach(({ user = [] }) => {
      user.forEach(({ value = '' }) => {
        selectedUserAttribute[value] = true;
      });
    });

    if (!selectedUserAttribute[detail?.[0]?.value]) {
      setSelectedAtrributeMapping((prevState) => {
        prevState[idx] = attributeMapping;

        updateFormValues(prevState);

        return [...prevState];
      });
    } else {
      // TODO: show toast message

      const label = detail?.[0]?.label;

      dispatch(
        showWarningNotification({
          message: 'ATTRIBUTE_ALREADY_MAPPED',
          translationMapping: { value: label || '' },
        }),
      );
    }
  };

  const onDeleteMapping = (removeIdx) => {
    setSelectedAtrributeMapping((prevState) => {
      const newAttributeMappings = remove(prevState, (_, idx) => idx !== removeIdx);

      if (newAttributeMappings.length === 0) {
        newAttributeMappings.push({});
      }

      updateFormValues(newAttributeMappings);

      return newAttributeMappings;
    });
  };

  const onAddMapping = () => {
    setSelectedAtrributeMapping((prevState) => {
      const lastAttributeDetail = prevState[prevState.length - 1];

      if (lastAttributeDetail.attrName && lastAttributeDetail?.user?.length > 0) {
        return [...prevState, { ...defaultMapping }];
      } else {
        return [...prevState];
      }
    });
  };

  const renderAttributeMappingSection = () => {
    const showDelete = () => {
      const attrName = selectedAttributeMapping?.[0]?.attrName;
      const user = selectedAttributeMapping?.[0]?.user?.[0]?.value;

      return attrName && user;
    };

    return (
      <>
        {selectedAttributeMapping.map((_, idx) => {
          const attrName = getValue(idx, 'attrName');
          const user = getSelectedUserAttribute(idx);

          const validationDetail = getAttributeMappingValidationDetail({ idx, user, attrName });

          return (
            <div
              key={idx}
              className="is-flex full-width attribute-mapping"
              style={{ marginBottom: '-16px' }}
            >
              <Input
                label="JUST_IN_TIME_ATTRIBUTE"
                value={getValue(idx, 'attrName')}
                name={`attrName-${idx}`}
                onChange={(evt) => onChange(evt, 'attrName', idx)}
                tooltip={!hasFullAccess ? {} : getFormTooltipDetail('jitAttribute')}
                disabled={!hasFullAccess}
                maxLength="128"
                info={validationDetail}
              />

              <Field
                label="USER_ATTRIBUTE"
                containerClass="user-attribute-section"
                tooltip={!hasFullAccess ? {} : getFormTooltipDetail('jitUserAttribute')}
                htmlFor="user"
                info={validationDetail}
              >
                <DropDown
                  list={attributeList}
                  selectedList={getSelectedUserAttribute(idx)}
                  onOpen={onDropDownOpen}
                  loading={isDropDownLoading}
                  onSelection={(detail) => onUserAttributeSelection(detail, idx)}
                  containerClass="full-width"
                  disabled={!hasFullAccess}
                />
              </Field>

              {showDelete() && (
                <Button
                  type="tertiary"
                  containerClass="content-width no-p-r"
                  containerStyle={{ paddingTop: '28px' }}
                  onClick={() => onDeleteMapping(idx)}
                  disabled={!hasFullAccess}
                >
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              )}
            </div>
          );
        })}

        {hasFullAccess && (
          <div className="is-flex has-jc-e">
            <Button type="tertiary" containerClass="no-p-r" onClick={onAddMapping}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              {t('ADD_MORE')}
            </Button>
          </div>
        )}
      </>
    );
  };

  const onSamlGroupAttrNameChange = (evt) => {
    const { value } = evt.target;

    onDetailChange((prevState) => {
      const jitConfigInfo = {
        ...prevState.jitConfigInfo,
        jitGroupAttrName: value,
      };

      return { ...prevState, jitConfigInfo };
    });
  };

  if (!jitProvisionEnabled) {
    return null;
  }

  return (
    <>
      <section className="typography-paragraph1-uppercase">
        {t('JUST_IN_TIME_ATTRIBUTE_MAPPING')}
      </section>

      <Card>
        <Input
          name="jitGroupAttrName"
          label="JUST_IN_TIME_USER_GROUP_ATTRIBUTE"
          value={jitGroupAttrName}
          onChange={onSamlGroupAttrNameChange}
          containerStyle={{ maxWidth: '50%' }}
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('userGroupJITAttribute')}
          disabled={!hasFullAccess}
          maxLength="128"
        />

        {renderAttributeMappingSection()}
      </Card>
    </>
  );
};

JITAttributeMappingForm.defaultProps = defaultProps;

JITAttributeMappingForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.bool };

export default JITAttributeMappingForm;
