import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Card,
  DownloadFile,
  DropDown,
  Field,
  FieldGroup,
  ToggleButton,
  defaultFormPropTypes,
  defaultFormProps,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  downloadSPCertificate,
  getSPCertificates,
  getSigningAlgorithms,
} from '../../ducks/external-identities';
import {
  selectEncryptionCertificateList,
  selectSPCertificateTypes,
  selectSigningAlgorithmList,
  selectSigningCertificateList,
} from '../../ducks/external-identities/selectors';

import AuthenticationLevelsMappingSection from './AuthenticationLevelsMappingSection';
import SessionAttributeMappingForm from './SessionAttributeMappingForm';
import { getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = {
  ...defaultFormProps,
  privileges: {
    hasFullAccess: false,
  },
  showAuthenticationLevelsMapping: false,
};

const IdentitiesAdvancedSettingsForm = ({
  detail = DEFAULT_PROPS.detail,
  onDetailChange = DEFAULT_PROPS.onDetailChange,
  validationDetail = DEFAULT_PROPS.validationDetail,
  privileges = DEFAULT_PROPS.privileges,
  isEditMode = DEFAULT_PROPS.isEditMode,
  showAuthenticationLevelsMapping = DEFAULT_PROPS.showAuthenticationLevelsMapping,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    includeLoginHint: samlLoginHint,
    samlAssertionEncEnabled,
    samlRequestSignEnabled,
    samlRequestSignAlgorithm,
  } = detail?.samlConfigInfo || {};

  const { includeLoginHint: oidclLoginHint } = detail?.oidcConfigInfo || {};

  const { hasFullAccess } = privileges;

  const signingAlgorithmList = useSelector(selectSigningAlgorithmList);
  const [selectedAlgorithm, setSelectedAlgorithm] = useState(() => {
    if (samlRequestSignAlgorithm) {
      return [{ label: samlRequestSignAlgorithm, value: samlRequestSignAlgorithm }];
    }

    return [];
  });

  const spCertificateTypes = useSelector(selectSPCertificateTypes);

  const signingCertificateList = useSelector(selectSigningCertificateList);

  const encryptionCertificateList = useSelector(selectEncryptionCertificateList);

  useEffect(() => {
    apiCall(getSigningAlgorithms(), { hasLoader: false });
    apiCall(getSPCertificates(spCertificateTypes.SIGNING), { hasLoader: false });
    apiCall(getSPCertificates(spCertificateTypes.ENCRYPTION), { hasLoader: false });
  }, []);

  const onSigningToggle = () => {
    const samlRequestSignCertificate = {
      id: signingCertificateList?.[0]?.value,
    };

    onDetailChange((prevState) => {
      const samlConfigInfo = {
        ...prevState.samlConfigInfo,
        samlRequestSignEnabled: !prevState.samlConfigInfo?.samlRequestSignEnabled,
        samlRequestSignCertificate,
      };

      return { ...prevState, samlConfigInfo };
    });
  };

  const onSamlLoginHintToggle = () => {
    onDetailChange((prevState) => {
      const samlConfigInfo = {
        ...prevState.samlConfigInfo,
        includeLoginHint: !prevState.samlConfigInfo?.includeLoginHint,
      };

      return { ...prevState, samlConfigInfo };
    });
  };

  const onOidcLoginHintToggle = () => {
    onDetailChange((prevState) => {
      const oidcConfigInfo = {
        ...prevState.oidcConfigInfo,
        includeLoginHint: !prevState.oidcConfigInfo?.includeLoginHint,
      };

      return { ...prevState, oidcConfigInfo };
    });
  };

  const onAssertionToggle = () => {
    const samlAssertionEncCertificate = {
      id: signingCertificateList?.[0]?.value,
    };

    onDetailChange((prevState) => {
      const samlConfigInfo = {
        ...prevState.samlConfigInfo,
        samlAssertionEncEnabled: !prevState.samlConfigInfo?.samlAssertionEncEnabled,
        samlAssertionEncCertificate,
      };

      return { ...prevState, samlConfigInfo };
    });
  };

  const onAlgorithmSelection = (detail) => {
    setSelectedAlgorithm(detail);
    const samlRequestSignAlgorithm = detail?.[0]?.value;

    if (samlRequestSignAlgorithm) {
      onDetailChange((prevState) => {
        const samlConfigInfo = {
          ...prevState.samlConfigInfo,
          samlRequestSignAlgorithm,
        };

        return { ...prevState, samlConfigInfo };
      });
    }
  };

  const onDownloadSiginingCertificate = async () => {
    const value = signingCertificateList?.[0]?.value;

    if (value) {
      return await apiCall(downloadSPCertificate({ id: value })).catch(noop);
    }

    return null;
  };

  const onDownloadEncryptionCertificate = async () => {
    const value = encryptionCertificateList?.[0]?.value;

    if (value) {
      return await apiCall(downloadSPCertificate({ id: value })).catch(noop);
    }

    return null;
  };

  const renderSAMLAuthenticationRequestSection = () => {
    if (detail.type === 'SAML') {
      return (
        <>
          <FieldGroup>
            <Field
              label="SAML_REQUEST_SIGNING"
              tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlRequestSignEnabled')}
            >
              <ToggleButton
                type="success"
                showLabel={false}
                isOn={samlRequestSignEnabled}
                onToggleClick={onSigningToggle}
                disabled={!hasFullAccess}
              />
            </Field>
          </FieldGroup>

          <FieldGroup>
            <Field label="LOGIN_HINT" tooltip={getFormTooltipDetail('loginHint')}>
              <ToggleButton
                type="success"
                isOn={samlLoginHint}
                showLabel={false}
                onToggleClick={onSamlLoginHintToggle}
                disabled={!hasFullAccess}
              />
            </Field>
          </FieldGroup>

          {samlRequestSignEnabled && (
            <FieldGroup>
              <Field
                label="SIGNING_ALGORITHM"
                htmlFor="samlRequestSignAlgorithm"
                info={validationDetail}
                tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlRequestSignAlgorithm')}
              >
                <DropDown
                  list={signingAlgorithmList}
                  selectedList={selectedAlgorithm}
                  onSelection={onAlgorithmSelection}
                />
              </Field>

              {samlRequestSignEnabled && (
                <Field
                  label="SP_SAML_CERTIFICATE"
                  tooltip={!hasFullAccess ? {} : getFormTooltipDetail('spSAMLCertificate')}
                >
                  <DownloadFile
                    variantType="iconWithText"
                    label="DOWNLOAD_CERTIFICATE"
                    onDownloadClick={onDownloadSiginingCertificate}
                  />
                </Field>
              )}
            </FieldGroup>
          )}
        </>
      );
    }

    return null;
  };

  const renderOIDCAuthenticationRequestSection = () => {
    if (detail.type === 'OIDC') {
      return (
        <FieldGroup>
          <Field label="LOGIN_HINT" tooltip={getFormTooltipDetail('loginHint')}>
            <ToggleButton
              type="success"
              isOn={oidclLoginHint}
              showLabel={false}
              onToggleClick={onOidcLoginHintToggle}
              disabled={!hasFullAccess}
            />
          </Field>
        </FieldGroup>
      );
    }

    return null;
  };

  const renderAuthenticationRequestSection = () => {
    if (detail.type === 'SAML') {
      return (
        <>
          <section className="typography-paragraph1-uppercase">
            {t('AUTHENTICATION_REQUEST')}
          </section>

          <Card>{renderSAMLAuthenticationRequestSection()}</Card>
        </>
      );
    }

    if (detail.type === 'OIDC') {
      return (
        <>
          <section className="typography-paragraph1-uppercase">
            {t('AUTHENTICATION_REQUEST')}
          </section>

          <Card>{renderOIDCAuthenticationRequestSection()}</Card>
        </>
      );
    }

    return null;
  };

  const renderEncryptedResponseSection = () => {
    if (detail.type === 'SAML') {
      return (
        <>
          <section className="typography-paragraph1-uppercase">
            {t('ENCRYPTED_SAML_ASSERTION')}
          </section>

          <Card>
            <FieldGroup>
              <Field
                label="ENCRYPTED_SAML_ASSERTION"
                tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlAssertionEncEnabled')}
              >
                <ToggleButton
                  type="success"
                  showLabel={false}
                  isOn={samlAssertionEncEnabled}
                  onToggleClick={onAssertionToggle}
                  disabled={!hasFullAccess}
                />
              </Field>

              {samlAssertionEncEnabled && (
                <Field
                  label="SAML_ENCRYPTION_CERTIFICATE"
                  tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlEncryptionCertificate')}
                >
                  <DownloadFile
                    variantType="iconWithText"
                    label="DOWNLOAD_CERTIFICATE"
                    onDownloadClick={onDownloadEncryptionCertificate}
                  />
                </Field>
              )}
            </FieldGroup>
          </Card>
        </>
      );
    }
    return null;
  };

  const renderAuthenticationLevelsMapping = () => {
    if (detail.type === 'OIDC' && showAuthenticationLevelsMapping) {
      return (
        <AuthenticationLevelsMappingSection
          onDetailChange={onDetailChange}
          detail={detail}
          privileges={privileges}
          isEditMode={isEditMode}
        />
      );
    }

    return null;
  };

  return (
    <>
      {renderAuthenticationRequestSection()}
      {renderEncryptedResponseSection()}

      {renderAuthenticationLevelsMapping()}

      <SessionAttributeMappingForm
        onDetailChange={onDetailChange}
        detail={detail}
        privileges={privileges}
      />
    </>
  );
};

IdentitiesAdvancedSettingsForm.propTypes = {
  ...defaultFormPropTypes,
  privileges: PropTypes.object,
};

export default IdentitiesAdvancedSettingsForm;
