import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Label,
  Tab,
  Tabs,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getList } from '../../ducks/authentication-levels';
import { selectNewIdpDetail } from '../../ducks/external-identities/selectors';
import {
  selectIsStepupSupportSystemEnabled,
  selectIsStepupSupportTenantEnabled,
  selectIsZiaStepupSupportSystemEnabled,
  selectIsZiaStepupSupportTenantEnabled,
} from '../../ducks/features/selectors';

import GeneralSectionForm from './GeneralSectionForm';
import IdentitiesAdvancedSettingsForm from './IdentitiesAdvancedSettingsForm';
import OIDCConfigurationForm from './OIDCConfigurationForm';
import ProvisioningForm from './ProvisioningForm';
import SAMLConfigurationForm from './SAMLConfigurationForm';
import { CONFIGURATION_MODE, getFormValidationDetail } from './helper';

const TABS = {
  BASIC: 'BASIC',
  ADVANCED: 'ADVANCED',
  PROVISIONING: 'PROVISIONING',
};

const DEFAULT_PROPS = {
  ...defaultFormProps,
  privileges: {
    hasFullAccess: false,
  },
};

const defaultFormValues = {
  name: '',
  status: false,
  vendorName: '',
  type: 'OIDC',
  loginIdAttribute: '',
  domains: [],
  idpAuthnLevelMappings: [],
  scimConfigInfo: {
    bearerToken: '',
    endPointUrl: '',
    scimProvisionEnabled: false,
    scimUserAttrMappings: [],
  },
  jitConfigInfo: {
    jitProvisionEnabled: false,
    jitUserAttrMappings: [],
    jitGroupAttrName: '',
  },
  samlConfigInfo: {
    includeLoginHint: true,
    samlIdpEntityId: '',
    samlIdpSSOUrl: '',
    samlIdpCertificate: {},
    samlAssertionEncEnabled: false,
    samlAssertionEncCertificate: {},
    samlRequestSignEnabled: false,
    samlRequestSignCertificate: {},
    samlRequestSignAlgorithm: '',
    samlIdpMetaDataUrl: '',
    samlSpEntityId: '',
    samlSpAcsUrl: '',
  },
  oidcConfigInfo: {
    includeLoginHint: true,
    authorizationEndpoint: '',
    clientId: '',
    clientSecret: '',
    defaultAuthenticationContext: '',
    issuer: '',
    jwksEndpoint: '',
    oidcIdpMetaDataUrl: '',
    redirectURI: '',
    scopes: ['openid'],
    tokenEndpoint: '',
    tokenEndpointAuthenticationMethod: 'CLIENT_SECRET_BASIC',
    userinfoEndpoint: '',
  },
  isIdpPrimary: false,
  onConfigurationModeChange: noop,
};

const ExternalIdentitiesForm = ({
  onDetailChange = DEFAULT_PROPS.onDetailChange,
  detail = DEFAULT_PROPS.detail,
  mode = DEFAULT_PROPS.mode,
  validationDetail = DEFAULT_PROPS.validationDetail,
  setValidationDetail = DEFAULT_PROPS.setValidationDetail,
  isIdpPrimary = DEFAULT_PROPS.isIdpPrimary,
  privileges = DEFAULT_PROPS.privileges,
  onConfigurationModeChange = DEFAULT_PROPS.onConfigurationModeChange,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const newIdpDetail = useSelector(selectNewIdpDetail);

  const isStepupSupportSystemEnabled = useSelector(selectIsStepupSupportSystemEnabled);
  const isStepupSupportTenantEnabled = useSelector(selectIsStepupSupportTenantEnabled);

  const isZiaStepupSupportSystemEnabled = useSelector(selectIsZiaStepupSupportSystemEnabled);
  const isZiaStepupSupportTenantEnabled = useSelector(selectIsZiaStepupSupportTenantEnabled);

  const isEditMode = mode === 'edit';
  const isAddMode = mode === 'add';

  const [formValues, setFormValues] = useState(() => {
    let loginIdAttribute = 'sub';

    if (detail?.type === 'SAML') {
      loginIdAttribute = detail?.samlConfigInfo?.loginIdAttribute || 'NameID';
    }

    if (detail?.type === 'OIDC') {
      loginIdAttribute = detail?.oidcConfigInfo?.loginIdAttribute || 'sub';
    }

    return { ...cloneDeep(defaultFormValues), loginIdAttribute, ...detail };
  });

  const [activeConfigurationMode, setActiveConfigurationMode] = useState(CONFIGURATION_MODE.URL);
  const [isActionChoosen, setIsActionChoosen] = useState(false);

  const [selectedTab, setSelectedTab] = useState(TABS.BASIC);

  useEffect(() => {
    if (isStepupSupportSystemEnabled && isStepupSupportTenantEnabled) {
      apiCall(getList()).catch(noop);
    }
  }, [isStepupSupportSystemEnabled, isStepupSupportTenantEnabled]);

  useEffect(() => {
    onConfigurationModeChange(activeConfigurationMode);
  }, [activeConfigurationMode]);

  const resetProtocolConfigInfo = () => {
    const {
      id,
      samlConfigInfo = {
        includeLoginHint: true,
        samlAssertionEncEnabled: false,
        samlRequestSignEnabled: false,
        samlSpAcsUrl: '',
        samlSpEntityId: '',
      },
      oidcConfigInfo = { includeLoginHint: true, redirectURI: '' },
    } = newIdpDetail;

    setIsActionChoosen(false);

    setFormValues((prevState) => ({
      ...prevState,
      id,
      oidcConfigInfo: {
        ...cloneDeep(defaultFormValues.oidcConfigInfo),
        ...oidcConfigInfo,
      },
      samlConfigInfo: {
        ...cloneDeep(defaultFormValues.samlConfigInfo),
        ...samlConfigInfo,
      },
    }));
  };

  useEffect(() => {
    if (isAddMode) {
      resetProtocolConfigInfo();
    }
  }, [isAddMode, newIdpDetail]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    onDetailChange(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    if (isEditMode) {
      setIsActionChoosen(true);
    } else {
      if (activeConfigurationMode === CONFIGURATION_MODE.MANUAL) {
        setIsActionChoosen(true);
      } else {
        setIsActionChoosen(false);
      }
    }

    setFormValues((prevState) => ({ ...prevState, ...formValues }));
  }, [activeConfigurationMode]);

  const renderGeneralSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('GENERAL')}</section>

        <GeneralSectionForm
          isEditMode={isEditMode}
          formValues={formValues}
          validationDetail={validationDetail}
          onFormFieldChange={onFormFieldChange}
          setFormValues={setFormValues}
          isIdpPrimary={isIdpPrimary}
          setActiveConfigurationMode={setActiveConfigurationMode}
          resetProtocolConfigInfo={resetProtocolConfigInfo}
          privileges={privileges}
        />
      </>
    );
  };

  const renderSAMLConfigurationSection = () => {
    if (formValues.type === 'SAML') {
      const isConditionsValid =
        validationDetail.context === 'samlIdpEntityId' ||
        validationDetail.context === 'samlIdpSSOUrl';

      return (
        <>
          <section className="typography-paragraph1-uppercase">
            <Label
              text="SAML_CONFIGURATION"
              info={isConditionsValid ? validationDetail : {}}
              containerStyle={{ marginBottom: '0' }}
            />
          </section>

          <SAMLConfigurationForm
            isEditMode={isEditMode}
            formValues={formValues}
            onFormFieldChange={onFormFieldChange}
            setFormValues={setFormValues}
            isActionChoosen={isActionChoosen}
            setIsActionChoosen={setIsActionChoosen}
            activeConfigurationMode={activeConfigurationMode}
            setActiveConfigurationMode={setActiveConfigurationMode}
            privileges={privileges}
          />
        </>
      );
    }

    return null;
  };

  const renderOIDCConfigurationSection = () => {
    if (formValues.type === 'OIDC') {
      const oidcValidationFields = [
        'redirectURI',
        'clientId',
        'clientSecret',
        'scopes',
        'issuer',
        'authorizationEndpoint',
        'tokenEndpoint',
        'jwksEndpoint',
        'userinfoEndpoint',
      ];

      const isConditionsValid = oidcValidationFields.indexOf(validationDetail.context) != -1;

      return (
        <>
          <section className="typography-paragraph1-uppercase">
            <Label
              text="OIDC_CONFIGURATION"
              info={isConditionsValid ? validationDetail : {}}
              containerStyle={{ marginBottom: '0' }}
            />
          </section>

          <OIDCConfigurationForm
            isEditMode={isEditMode}
            formValues={formValues}
            onFormFieldChange={onFormFieldChange}
            setFormValues={setFormValues}
            isActionChoosen={isActionChoosen}
            setIsActionChoosen={setIsActionChoosen}
            activeConfigurationMode={activeConfigurationMode}
            setActiveConfigurationMode={setActiveConfigurationMode}
            validationDetail={validationDetail}
            privileges={privileges}
          />
        </>
      );
    }

    return null;
  };

  const renderBasicSection = () => {
    if (selectedTab === TABS.BASIC) {
      return (
        <>
          {renderGeneralSection()}

          {renderSAMLConfigurationSection()}

          {renderOIDCConfigurationSection()}
        </>
      );
    }

    return null;
  };

  const renderAdvancedSection = () => {
    if (selectedTab === TABS.ADVANCED) {
      return (
        <>
          <IdentitiesAdvancedSettingsForm
            detail={formValues}
            onDetailChange={setFormValues}
            mode={mode}
            isEditMode={isEditMode}
            validationDetail={validationDetail}
            setValidationDetail={setValidationDetail}
            privileges={privileges}
            showAuthenticationLevelsMapping={
              (isStepupSupportSystemEnabled && isStepupSupportTenantEnabled) ||
              (isZiaStepupSupportSystemEnabled && isZiaStepupSupportTenantEnabled)
            }
          />
        </>
      );
    }

    return null;
  };

  const renderProvisioningSection = () => {
    if (selectedTab === TABS.PROVISIONING) {
      return (
        <>
          <section className="typography-paragraph1-uppercase">{t('STATUS')}</section>

          <ProvisioningForm
            isEditMode={isEditMode}
            formValues={formValues}
            onFormFieldChange={onFormFieldChange}
            setFormValues={setFormValues}
            privileges={privileges}
            validationDetail={validationDetail}
          />
        </>
      );
    }

    return null;
  };

  const getAdvancedTabClassName = () => {
    const isConditionsValid = validationDetail.context === 'samlRequestSignAlgorithm';

    return isConditionsValid && !validationDetail.isValid ? 'has-color-error' : '';
  };

  const getProvisioningTabClassName = () => {
    const isConditionsValid = validationDetail.context === 'bearerToken';

    return isConditionsValid && !validationDetail.isValid ? 'has-color-error' : '';
  };

  return (
    <>
      <Tabs>
        <Tab
          label={TABS.BASIC}
          isActive={selectedTab === TABS.BASIC}
          onClick={() => {
            setSelectedTab(TABS.BASIC);
          }}
        />

        <Tab
          label={TABS.ADVANCED}
          isActive={selectedTab === TABS.ADVANCED}
          containerClass={getAdvancedTabClassName()}
          onClick={() => {
            setSelectedTab(TABS.ADVANCED);
          }}
        />

        <Tab
          label={TABS.PROVISIONING}
          isActive={selectedTab === TABS.PROVISIONING}
          containerClass={getProvisioningTabClassName()}
          onClick={() => {
            setSelectedTab(TABS.PROVISIONING);
          }}
        />
      </Tabs>

      {renderBasicSection()}

      {renderAdvancedSection()}

      {renderProvisioningSection()}
    </>
  );
};

ExternalIdentitiesForm.propTypes = {
  ...defaultFormPropTypes,
  isIdpPrimary: PropTypes.bool,
  privileges: PropTypes.object,
  onConfigurationModeChange: PropTypes.func,
};

export default ExternalIdentitiesForm;
