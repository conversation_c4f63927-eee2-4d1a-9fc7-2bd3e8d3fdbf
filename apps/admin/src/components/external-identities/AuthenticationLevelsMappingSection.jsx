import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, Input, mergeFormValues } from '@zscaler/zui-component-library';

import { find, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getAuthenticationFlatListDetail } from '../../ducks/authentication-levels/helper';
import { selectList } from '../../ducks/authentication-levels/selectors';

const AuthenticationLevelsMappingSection = ({
  detail = {},
  onDetailChange = noop,
  isEditMode = false,
}) => {
  const { t } = useTranslation();

  const levelsList = useSelector(selectList);

  const { idpAuthnLevelMappings = [] } = detail || {};

  const [formValues, setFormValues] = useState({});

  const activeMapping = useMemo(() => {
    const flatLevelsList = getAuthenticationFlatListDetail(levelsList);

    const newActiveMapping = [];

    flatLevelsList.forEach((level) => {
      const mapping = {
        authenticationLevel: { ...level },
        idpAuthnContext: '',
      };

      if (isEditMode) {
        const detail = find(idpAuthnLevelMappings, (o) => o.authenticationLevel.id === level.id);

        if (detail) {
          mapping.idpAuthnContext = detail.idpAuthnContext;
        }
      }

      newActiveMapping.push(mapping);
    });

    if (isEditMode) {
      idpAuthnLevelMappings;
    }

    const newFormValues = {};

    newActiveMapping.forEach(({ authenticationLevel, idpAuthnContext } = {}) => {
      newFormValues[authenticationLevel.id] = idpAuthnContext;
    });

    setFormValues(newFormValues);

    return newActiveMapping;
  }, [levelsList]);

  const updateDetail = () => {
    onDetailChange((prevState) => {
      const idpAuthnLevelMappings = [];

      activeMapping.forEach(({ authenticationLevel } = {}) => {
        const idpAuthnContext = formValues[authenticationLevel.id];
        if (idpAuthnContext) {
          const newMapping = {
            authenticationLevel,
            idpAuthnContext,
          };

          idpAuthnLevelMappings.push(newMapping);
        }
      });

      return { ...prevState, idpAuthnLevelMappings };
    });
  };

  useEffect(() => {
    updateDetail();
  }, [formValues]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  const renderMapping = (mapping) => {
    const { authenticationLevel } = mapping;

    const { id, name, description } = authenticationLevel;

    return (
      <div key={id} className="is-flex has-jc-sb full-width al-mapping">
        <div className="al">
          <div className="name">{name}</div>
          <div className="description">{description}</div>
        </div>
        <div className="context">
          <Input
            name={id}
            value={formValues[id]}
            onChange={onFormFieldChange}
            containerClass="no-m"
          />
        </div>
      </div>
    );
  };

  return (
    <>
      <section className="typography-paragraph1-uppercase">
        {t('LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING')}
      </section>

      <Card containerClass="al-mapping-container">
        <div className="is-flex has-jc-sb al-header-container full-width">
          <div className="al-column">{t('LEVELS')}</div>
          <div className="al-column">{t('AUTHENTICATION_CONTEXT')}</div>
        </div>
        <div className="al-body-container">
          {activeMapping.map((mapping) => renderMapping(mapping))}
        </div>
      </Card>
    </>
  );
};

AuthenticationLevelsMappingSection.propTypes = {
  detail: PropTypes.object,
  onDetailChange: PropTypes.func,
  privileges: PropTypes.object,
  isEditMode: PropTypes.bool,
};

export default AuthenticationLevelsMappingSection;
