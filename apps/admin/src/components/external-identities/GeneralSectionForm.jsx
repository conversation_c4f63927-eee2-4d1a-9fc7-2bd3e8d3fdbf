import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  MultiSelection,
  ToggleButton,
  useApiCall,
} from '@zscaler/zui-component-library';

import { filter, find, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getVendorList } from '../../ducks/external-identities';
import { selectVendorNameList } from '../../ducks/external-identities/selectors';
import { getList } from '../../ducks/tenant-domains';
import { selectDomainNameList } from '../../ducks/tenant-domains/selectors';

import { CONFIGURATION_MODE, PROTOCOL_LIST, getFormTooltipDetail } from './helper';

const defaultProps = {
  isEditMode: false,
  isIdpPrimary: false,
  formValues: {},
  validationDetail: {},
  onFormFieldChange: noop,
  setFormValues: noop,
  setActiveConfigurationMode: noop,
  resetProtocolConfigInfo: noop,
  privileges: {
    hasFullAccess: false,
  },
};

const GeneralSectionForm = ({
  isEditMode,
  formValues,
  validationDetail,
  onFormFieldChange,
  setFormValues,
  isIdpPrimary,
  setActiveConfigurationMode,
  resetProtocolConfigInfo,
  privileges,
}) => {
  const { apiCall } = useApiCall();
  const { hasFullAccess } = privileges;

  const vendorNameList = useSelector(selectVendorNameList);
  const [selectedVendorName, setSelectedVendorName] = useState(() => {
    if (formValues.vendorName) {
      return [{ label: formValues.vendorName, value: formValues.vendorName }];
    }

    return [];
  });

  const domainNameList = useSelector(selectDomainNameList);

  const getDomainNameList = () => {
    return filter(domainNameList, (domain) => {
      const { idp } = domain;

      if (isEditMode) {
        if (idp && idp?.id !== formValues.id) {
          return false;
        }
      }

      if (!isEditMode && idp?.id) {
        return false;
      }

      return true;
    });
  };

  const [selectedDomainName, setSelectedDomainName] = useState(
    formValues.domains.map(({ id, name }) => {
      return {
        label: name,
        value: id,
      };
    }),
  );

  const [selectedProtocol, setSelectedProtocol] = useState(() => {
    const protocol = find(PROTOCOL_LIST, { value: formValues.type });

    return protocol ? [protocol] : [];
  });

  useEffect(() => {
    apiCall(getVendorList(), { hasLoader: false }).catch(noop);
    apiCall(getList({ all: true }), { hasLoader: false }).catch(noop);
  }, []);

  const onVendorSelection = (detail) => {
    setSelectedVendorName(detail);
    setFormValues((prevState) => ({ ...prevState, vendorName: detail?.[0]?.value || '' }));
  };

  const onDomainSelection = (detail) => {
    setSelectedDomainName(detail);

    setFormValues((prevState) => ({
      ...prevState,
      domains: detail.map(({ label, value }) => ({ name: label, id: value })),
    }));
  };

  const onProtocolSelection = (detail) => {
    setSelectedProtocol(detail);

    setFormValues((prevState) => {
      let loginIdAttribute = 'NameID';

      if (detail[0]?.value === 'SAML') {
        loginIdAttribute = 'NameID';
      }

      if (detail[0]?.value === 'OIDC') {
        loginIdAttribute = 'sub';
      }

      return {
        ...prevState,
        type: detail[0]?.value,
        loginIdAttribute,
      };
    });

    resetProtocolConfigInfo?.();

    setActiveConfigurationMode(CONFIGURATION_MODE.URL);
  };

  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      status: !formValues?.status,
    }));
  };

  return (
    <Card>
      <FieldGroup>
        <Input
          label="NAME"
          name="name"
          onChange={onFormFieldChange}
          value={formValues.name}
          info={validationDetail}
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('name')}
          maxLength="128"
          disabled={!hasFullAccess}
        />

        <Field
          label="IDENTITY_VENDOR"
          htmlFor="vendorName"
          info={validationDetail}
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('vendorName')}
        >
          <DropDown
            list={vendorNameList}
            selectedList={selectedVendorName}
            onSelection={onVendorSelection}
            containerClass="full-width"
            disabled={!hasFullAccess}
          />
        </Field>
      </FieldGroup>

      <FieldGroup>
        {!isIdpPrimary && (
          <Field
            label="DOMAIN"
            htmlFor="domains"
            info={validationDetail}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('domains')}
          >
            <DropDown
              list={getDomainNameList()}
              selectedList={selectedDomainName}
              onSelection={onDomainSelection}
              containerClass="full-width"
              renderItemsSelection={(props) => (
                <MultiSelection
                  unselectedTitle="Unselected Domains"
                  selectedTitle="Selected Domains"
                  {...props}
                />
              )}
              hasSearch
              isMulti
              disabled={!hasFullAccess}
            />
          </Field>
        )}

        {isEditMode ? (
          <Input
            label="PROTOCOL"
            name="protocol"
            onChange={onFormFieldChange}
            value={formValues.type}
            readOnly
            disabled
          />
        ) : (
          <Field
            label="PROTOCOL"
            htmlFor="protocol"
            info={validationDetail}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('protocol')}
          >
            <DropDown
              list={PROTOCOL_LIST}
              selectedList={selectedProtocol}
              onSelection={onProtocolSelection}
              containerClass="full-width"
              disabled={isEditMode || !hasFullAccess}
              hasSearch={false}
            />
          </Field>
        )}
      </FieldGroup>

      <FieldGroup>
        <Field label="STATUS" tooltip={!hasFullAccess ? {} : getFormTooltipDetail('status')}>
          <ToggleButton
            type="success"
            isOn={formValues?.status}
            onToggleClick={onToggleStatusClick}
            onLabel="ENABLED"
            offLabel="DISABLED"
            showLabel={false}
            disabled={!hasFullAccess}
          />
        </Field>

        <Input
          label="LOGIN_ID_ATTRIBUTE"
          name="loginIdAttribute"
          onChange={onFormFieldChange}
          value={formValues.loginIdAttribute}
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('loginIdAttribute')}
          info={validationDetail}
          disabled={!hasFullAccess}
        />
      </FieldGroup>
    </Card>
  );
};

GeneralSectionForm.defaultProps = defaultProps;

GeneralSectionForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  validationDetail: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
  isIdpPrimary: PropTypes.bool,
  setActiveConfigurationMode: PropTypes.func,
  resetProtocolConfigInfo: PropTypes.func,
  privileges: PropTypes.object,
};

export default GeneralSectionForm;
