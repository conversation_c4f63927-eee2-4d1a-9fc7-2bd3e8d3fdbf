import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const PROTOCOL_LIST = [
  { label: 'SAML_PROTOCOL_LABEL', value: 'SAML' },
  { label: 'OIDC_PROTOCOL_LABEL', value: 'OIDC' },
];

export const CONFIGURATION_MODE = {
  URL: 'URL',
  UPLOAD: 'UPLOAD',
  MANUAL: 'MANUAL',
};

export const CONFIGURATION_LIST = [
  { label: 'FETCH_WITH_URL', value: CONFIGURATION_MODE.URL },
  { label: 'UPLOAD_METADATA', value: CONFIGURATION_MODE.UPLOAD },
  { label: 'MANUAL_ENTRY', value: CONFIGURATION_MODE.MANUAL },
];

export const OIDC_INPUT_METHOD_LIST = [
  { label: 'FETCH_WITH_URL', value: CONFIGURATION_MODE.URL },
  { label: 'MANUAL_ENTRY', value: CONFIGURATION_MODE.MANUAL },
];

export const TOKEN_ENDPOINT_AUTHENTICATION_METHOD_LIST = [
  { label: 'CLIENT_SECRET_BASIC', value: 'CLIENT_SECRET_BASIC' },
  { label: 'CLIENT_SECRET_POST', value: 'CLIENT_SECRET_POST' },
];

export const primaryModalModeDetail = {
  '': {},
  view: {
    headerText: 'PRIMARY_IDENTITY_PROVIDER',
  },
  add: {
    headerText: 'ADD_PRIMARY_IDENTITY_PROVIDER',
  },
  edit: {
    headerText: 'EDIT_PRIMARY_IDENTITY_PROVIDER',
  },
  delete: {
    headerText: 'DELETE_PRIMARY_IDENTITY_PROVIDER',
    confirmationMessage:
      'Are you sure you want to delete this Identity Provider? The changes cannot be undone.',
  },
};

export const secondaryModalModeDetail = {
  '': {},
  view: {
    headerText: 'SECONDARY_IDENTITY_PROVIDER',
  },
  add: {
    headerText: 'ADD_SECONDARY_IDENTITY_PROVIDER',
  },
  edit: {
    headerText: 'EDIT_SECONDARY_IDENTITY_PROVIDER',
  },
  delete: {
    headerText: 'DELETE_SECONDARY_IDENTITY_PROVIDER',
    confirmationMessage:
      'Are you sure you want to delete this Identity Provider? The changes cannot be undone.',
  },
};

export const getModalModeDetail = ({ defaultIdp }) => {
  if (defaultIdp) {
    return primaryModalModeDetail;
  }

  return secondaryModalModeDetail;
};

export const advancedSettingsModalModeDetail = {
  '': {},
  add: {
    headerText: 'IDP_ADVANCED_SETTINGS',
  },
  edit: {
    headerText: 'IDP_ADVANCED_SETTINGS',
  },
};

export const provisioningSettingsModalModeDetail = {
  '': {},
  add: {
    headerText: 'PROVISIONING_SETTINGS',
  },
  edit: {
    headerText: 'PROVISIONING_SETTINGS',
  },
};

export const jitAttributeModalModeDetail = {
  '': {},
  add: {
    headerText: 'JIT_ATTRIBUTE_MAPPING',
  },
  edit: {
    headerText: 'JIT_ATTRIBUTE_MAPPING',
  },
};

export const scimAttributeModalModeDetail = {
  '': {},
  add: {
    headerText: 'SCIM_ATTRIBUTE_MAPPING',
  },
  edit: {
    headerText: 'SCIM_ATTRIBUTE_MAPPING',
  },
};

export const getAttributeMappingValidationDetail = ({ idx, attrName, user }) => {
  const validationDetail = { ...defaultValidationDetail };

  if (!attrName && user.length > 0) {
    validationDetail.isValid = false;
    validationDetail.context = `attrName-${idx}`;
    validationDetail.type = 'error';
    validationDetail.message = "Mapping won't be saved until the Attribute is entered";

    return validationDetail;
  }

  if (attrName && user.length === 0) {
    validationDetail.isValid = false;
    validationDetail.context = `user`;
    validationDetail.type = 'error';
    validationDetail.message = "Mapping won't be saved until User Attribute is selected";

    return validationDetail;
  }

  return validationDetail;
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const {
    name,
    vendorName,
    domains,
    samlConfigInfo,
    oidcConfigInfo,
    scimConfigInfo,
    type,
    loginIdAttribute,
  } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  if (!vendorName) {
    validationDetail.isValid = false;
    validationDetail.context = 'vendorName';
    validationDetail.type = 'error';
    validationDetail.message = 'Vendor Name is Required';

    return validationDetail;
  }

  if (domains?.length === 0) {
    validationDetail.isValid = false;
    validationDetail.context = 'domains';
    validationDetail.type = 'error';
    validationDetail.message = 'Domain is Required';

    return validationDetail;
  }

  if (!loginIdAttribute) {
    validationDetail.isValid = false;
    validationDetail.context = 'loginIdAttribute';
    validationDetail.type = 'error';
    validationDetail.message = 'Login Id Atrtibute is Required';

    return validationDetail;
  }

  if (type === 'SAML') {
    if (!samlConfigInfo?.samlIdpEntityId) {
      validationDetail.isValid = false;
      validationDetail.context = 'samlIdpEntityId';
      validationDetail.type = 'error';
      validationDetail.message = (
        <p style={{ textTransform: 'initial' }}>SAML IDP Entity Id is Required</p>
      );

      return validationDetail;
    }

    if (!samlConfigInfo?.samlIdpSSOUrl) {
      validationDetail.isValid = false;
      validationDetail.context = 'samlIdpSSOUrl';
      validationDetail.type = 'error';
      validationDetail.message = (
        <p style={{ textTransform: 'initial' }}>SAML IDP SSO Url is Required</p>
      );

      return validationDetail;
    }
  }

  if (type === 'OIDC') {
    if (!oidcConfigInfo?.redirectURI) {
      validationDetail.isValid = false;
      validationDetail.context = 'redirectURI';
      validationDetail.type = 'error';
      validationDetail.message = 'Redirect URI is Required';

      return validationDetail;
    }

    if (!oidcConfigInfo?.clientId) {
      validationDetail.isValid = false;
      validationDetail.context = 'clientId';
      validationDetail.type = 'error';
      validationDetail.message = 'Client Id is Required';

      return validationDetail;
    }

    if (!oidcConfigInfo?.clientSecret) {
      validationDetail.isValid = false;
      validationDetail.context = 'clientSecret';
      validationDetail.type = 'error';
      validationDetail.message = 'Client Secret is Required';

      return validationDetail;
    }

    if (oidcConfigInfo?.scopes?.length === 0) {
      validationDetail.isValid = false;
      validationDetail.context = 'scopes';
      validationDetail.type = 'error';
      validationDetail.message = 'Scopes is Required';

      return validationDetail;
    }

    if (!oidcConfigInfo?.issuer) {
      validationDetail.isValid = false;
      validationDetail.context = 'issuer';
      validationDetail.type = 'error';
      validationDetail.message = 'Issuer is Required. Use Metadata URL or Enter Manually';

      return validationDetail;
    }

    if (!oidcConfigInfo?.authorizationEndpoint) {
      validationDetail.isValid = false;
      validationDetail.context = 'authorizationEndpoint';
      validationDetail.type = 'error';
      validationDetail.message = 'Authorization Endpoint is Required';

      return validationDetail;
    }

    if (!oidcConfigInfo?.tokenEndpoint) {
      validationDetail.isValid = false;
      validationDetail.context = 'tokenEndpoint';
      validationDetail.type = 'error';
      validationDetail.message = 'Token Endpoint is Required';

      return validationDetail;
    }

    if (!oidcConfigInfo?.jwksEndpoint) {
      validationDetail.isValid = false;
      validationDetail.context = 'jwksEndpoint';
      validationDetail.type = 'error';
      validationDetail.message = 'JWKS Endpoint is Required';

      return validationDetail;
    }

    if (!oidcConfigInfo?.userinfoEndpoint) {
      validationDetail.isValid = false;
      validationDetail.context = 'userinfoEndpoint';
      validationDetail.type = 'error';
      validationDetail.message = 'Userinfo Endpoint is Required';

      return validationDetail;
    }
  }

  const { samlRequestSignEnabled, samlRequestSignAlgorithm } = formValues?.samlConfigInfo || {
    samlConfigInfo: {},
  };

  if (samlRequestSignEnabled && !samlRequestSignAlgorithm) {
    validationDetail.isValid = false;
    validationDetail.context = 'samlRequestSignAlgorithm';
    validationDetail.type = 'error';
    validationDetail.message = 'Signing Algorithm is Required';

    return validationDetail;
  }

  if (scimConfigInfo?.scimProvisionEnabled && !scimConfigInfo?.bearerToken) {
    validationDetail.isValid = false;
    validationDetail.context = 'bearerToken';
    validationDetail.type = 'error';
    validationDetail.message = <p style={{ textTransform: 'initial' }}>Bearer Token is Required</p>;

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the IdP`;
  }

  if (name === 'vendorName') {
    tooltipDetail.content = `Choose the IdP vendor`;
  }

  if (name === 'domains') {
    tooltipDetail.content = `Select the IdP domain. This allows the Zscaler service to display the correct IdP to authenticate an incoming user.`;
  }

  if (name === 'protocol') {
    tooltipDetail.content = `Select the protocol in which you want to configure the identity provider`;
  }

  if (name === 'status') {
    tooltipDetail.content = `Enable or Disable the IdP`;
  }

  if (name === 'loginIdAttribute') {
    tooltipDetail.content = `Enter a unique ID for the login attribute.`;
  }

  if (name === 'inputMethod') {
    tooltipDetail.content = `Select from the input methods listed to procure the information required for SAML configuration`;
  }

  if (name === 'oidcInputMethod') {
    tooltipDetail.content = `Select from the input methods listed to procure the information required for OIDC configuration`;
  }

  if (name === 'idpMetadataUrl') {
    tooltipDetail.content = `Paste the link to the IdP's metadata file. You can find the link to the URL from the IdP.`;
  }

  if (name === 'oidcIdpMetaDataUrl') {
    tooltipDetail.content = (
      <p>
        Paste the link to the OpenID provider&apos;s metadata file and click{' '}
        <strong className="tooltip-bold">Fetch</strong>
      </p>
    );
  }

  if (name === 'idpMetadata') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Upload IdP Metadata</strong> to upload the metadata
        file of the IdP from your local machine
      </p>
    );
  }

  if (name === 'redirectURI') {
    tooltipDetail.content = `Copy the URI. You need this URI when configuring ZIdentity as the relying party on the OpenID provider's portal.`;
  }

  if (name === 'tokenAuthenticationMethod') {
    tooltipDetail.content = `Select the token authentication method to be used when ZIdentity authenticates itself to the OpenID provider`;
  }

  if (name === 'clientId') {
    tooltipDetail.content = `Enter the unique identifier used by the OpenID provider for ZIdentity`;
  }

  if (name === 'clientSecret') {
    tooltipDetail.content = `Enter the client secret. It's a confidential code used to authenticate ZIdentity to the OpenID provider.`;
  }

  if (name === 'requestedScope') {
    tooltipDetail.content = `Enter the scopes you want ZIdentity to include in the authentication request to the OpenID provider`;
  }

  if (name === 'defaultAuthenticationContext') {
    tooltipDetail.content = `Enter the authentication context class reference values to be passed in the authentication request to the OpenID provider`;
  }

  if (name === 'issuer') {
    tooltipDetail.content = `Enter the issuer URL of the OpenID provider`;
  }

  if (name === 'authorizationEndpoint') {
    tooltipDetail.content = `Enter the OpenID provider's URL where the user is redirected for authentication`;
  }

  if (name === 'tokenEndpoint') {
    tooltipDetail.content = `Enter the OpenID provider's URL where the ZIdentity service presents the authorization code in exchange for an ID token and access token`;
  }

  if (name === 'jwksEndpoint') {
    tooltipDetail.content = `Enter the JWKS URL where the JWKS is obtained containing public keys that are used to validate digital signatures on ID tokens and access tokens.`;
  }

  if (name === 'userinfoEndpoint') {
    tooltipDetail.content = `Enter the OpenID provider's URL from where the user information is received when an access token is provided`;
  }

  if (name === 'samlIdpEntityId') {
    tooltipDetail.content = `Enter the issuer URI of the IdP. This value is the entity ID in the IdP's metadata.`;
  }

  if (name === 'samlIdpSSOUrl') {
    tooltipDetail.content = `Enter the IdP's URL where the user is redirected for authentication`;
  }

  if (name === 'samlIdpCertificate') {
    tooltipDetail.content = `Upload the SAML certificate that is used to verify the digital signature of the IdP. This is the certificate you downloaded from your IdP. The certificate must be in base-64 encoded PEM format. The file extension must be .pem and have no other periods (.) in the file name.`;
  }

  if (name === 'samlRequestSignEnabled') {
    tooltipDetail.content = `Turn on the option to configure SAML request signing for user authentication`;
  }

  if (name === 'samlRequestSignAlgorithm') {
    tooltipDetail.content = `Select the signing algorithm`;
  }

  if (name === 'spSAMLCertificate') {
    tooltipDetail.content = `Download the SP SAML certificate. You need to upload this certificate to the IdP.`;
  }

  if (name === 'samlAssertionEncEnabled') {
    tooltipDetail.content = `Turn on the option to configure encrypted SAML response for authentication.`;
  }

  if (name === 'samlEncryptionCertificate') {
    tooltipDetail.content = `Download the SAML encryption certificate. You need to upload this certificate to the IdP.`;
  }

  if (name === 'jitProvisionEnabled') {
    tooltipDetail.content = `Turn on this option for JIT provisioning`;
  }

  if (name === 'userGroupSamlAttribute') {
    tooltipDetail.content = `Enter the SAML user group attribute`;
  }

  if (name === 'samlAttribute') {
    tooltipDetail.content = `Enter the SAML attribute that you want to map`;
  }

  if (name === 'samlUserAttribute') {
    tooltipDetail.content = (
      <p>
        Select the user attribute that you want to map to the{' '}
        <strong className="tooltip-bold">SAML Attribute</strong>. Click{' '}
        <strong className="tooltip-bold">Add More</strong> to map more attributes.
      </p>
    );
  }

  if (name === 'userGroupJITAttribute') {
    tooltipDetail.content = `Enter the JIT user group attribute`;
  }

  if (name === 'jitAttribute') {
    tooltipDetail.content = `Enter the JIT attribute that you want to map`;
  }

  if (name === 'jitUserAttribute') {
    tooltipDetail.content = (
      <p>
        Select the user attribute that you want to map to the{' '}
        <strong className="tooltip-bold">JIT Attribute</strong>. Click{' '}
        <strong className="tooltip-bold">Add More</strong> to map more attributes.
      </p>
    );
  }

  if (name === 'scimProvisionEnabled') {
    tooltipDetail.content = `Turn on to activate SCIM-based provisioning for users`;
  }

  if (name === 'authenticationMethod') {
    tooltipDetail.content = `Select the authentication method`;
  }

  if (name === 'bearerToken') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Generate Token</strong> for a new bearer token. Copy
        the bearer token because you need it when configuring your IdP for SCIM provisioning. If
        you&apos;re generating a new bearer token for an existing SCIM configuration, ensure you
        update the token to your IdP.
      </p>
    );
  }

  if (name === 'scimAttribute') {
    tooltipDetail.content = `Enter the SCIM attribute that you want to map`;
  }

  if (name === 'scimUserAttribute') {
    tooltipDetail.content = (
      <p>
        Select the user attribute that you want to map to the{' '}
        <strong className="tooltip-bold">SCIM Attribute</strong>. Click{' '}
        <strong className="tooltip-bold">Add More</strong> to map more attributes.
      </p>
    );
  }

  if (name === 'loginHint') {
    tooltipDetail.content = `Enable to include usernames in authentication requests sent to the external IdP`;
  }

  return tooltipDetail;
};
