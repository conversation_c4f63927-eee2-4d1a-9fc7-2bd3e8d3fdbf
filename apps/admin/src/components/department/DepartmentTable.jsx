import { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  RowNumber,
  Selector,
  TableContainer,
  TextWithTooltip,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/departments';
import { selectTableConfig, selectTableDetail } from '../../ducks/departments/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { defaultBulkActionOption, getSearchField } from './helper';

const DepartmentTable = () => {
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    setDetail,
    setSelectedRowDetail,
    setSelectedBulkAction,
    selectedSearchField,
    searchTerm,
    privileges,
  } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const { hasFullAccess, hasViewAccess } = privileges || {};

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'number') {
        columnDetail.cell = RowNumber;
      }

      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { name, idp } = props?.row?.original || {};

          let derivedName = name;

          const idpName = idp?.name || '';

          if (idpName) {
            derivedName = `${name} (${idpName})`;
          }

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'description') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { description } = props?.row?.original || {};

          return <TextWithTooltip> {description} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { dynamicGroup, readOnly } = props?.row?.original || {};

          if (dynamicGroup || readOnly) {
            return (
              <Actions {...props} editIcon={faEye} onEditClick={onViewClick} showDelete={false} />
            );
          }

          return (
            <Actions
              {...props}
              editIcon={hasViewAccess && !hasFullAccess ? faEye : faPencilAlt}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              showDelete={hasFullAccess}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    if (hasFullAccess) {
      return [
        {
          id: 'selection',
          Header: '',
          cell: (props) => {
            // eslint-disable-next-line react/prop-types
            const { dynamicGroup, readOnly } = props?.row?.original || {};

            if (dynamicGroup || readOnly) {
              return null;
            }

            return <Selector {...props} />;
          },
          size: 50,
          minSize: 50,
          maxSize: 50,
          enableResizing: false,
          disableSortBy: true,
          defaultCanSort: false,
        },
        ...(tableConfig?.columns || []),
      ];
    }

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, hasFullAccess, hasViewAccess]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(hasViewAccess && !hasFullAccess ? 'view' : 'edit');
    }
  };

  const onViewClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('view');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);

    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const searchField = getSearchField(selectedSearchField);

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchField) {
      payload[searchField] = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      onRowSelection={onRowSelection}
    />
  );
};

export default DepartmentTable;
