import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  FieldGroup,
  Input,
  TextArea,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getDetail } from '../../ducks/departments';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const defaultProps = {
  ...defaultFormProps,
};

const DepartmentForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const { detail, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const [formValues, setFormValues] = useState({
    name: '',
    description: '',
    disabled: false,
    source: 'UI',
    associatedUsers: [],
    ...detail,
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    if (mode === 'edit' || mode === 'view') {
      apiCall(getDetail({ id: detail.id })).catch(noop);
    }
  }, [mode]);

  useEffect(() => {
    setDetail(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const renderInformationSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('DEPARTMENT')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="DEPARTMENT_NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              info={validationDetail}
              maxLength="128"
              tooltip={isFormReadOnly ? '' : getFormTooltipDetail('name')}
              disabled={isFormReadOnly}
              containerStyle={{ maxWidth: '260px' }}
            />
          </FieldGroup>

          <FieldGroup>
            <TextArea
              name="description"
              value={formValues.description}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              containerClass="full-width"
              maxLength="256"
              rows="3"
              tooltip={isFormReadOnly ? '' : getFormTooltipDetail('description')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

DepartmentForm.defaultProps = defaultProps;

DepartmentForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.object };

export default DepartmentForm;
