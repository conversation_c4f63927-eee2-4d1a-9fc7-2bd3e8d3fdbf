import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  MultiSelection,
  TextArea,
  defaultFormPropTypes,
  defaultFormProps,
  getDropDownList,
  mergeFormValues,
  useApiCall,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { isEqual, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getDetail } from '../../ducks/groups';
import { selectGroupsDetail } from '../../ducks/groups/selectors';
import { getList } from '../../ducks/users';
import { getUsersLabel, selectTableDetail, selectUsersList } from '../../ducks/users/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const defaultProps = {
  ...defaultFormProps,
};

const GroupForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const { detail, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const usersListTableDetail = useSelector(selectTableDetail);
  const usersList = useSelector(selectUsersList);

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: usersListTableDetail,
    apiCallFunc: getList,
  });

  const groupsDetail = useSelector(selectGroupsDetail);

  const [selectedOption, setSelectedOption] = useState([]);

  const [formValues, setFormValues] = useState({
    name: '',
    description: '',
    disabled: false,
    source: 'UI',
    associatedUsers: [],
    ...detail,
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    if (mode === 'edit' || mode === 'view') {
      apiCall(getDetail({ id: detail.id })).catch(noop);
    }
  }, [mode]);

  useEffect(() => {
    setDetail(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    if (groupsDetail?.[detail.id]) {
      const fullGroupDetail = groupsDetail[detail.id];

      const newDetail = { ...formValues, ...fullGroupDetail };

      const selectedOptions = getDropDownList({
        list: newDetail.associatedUsers,
        getLabel: getUsersLabel,
      });

      setSelectedOption(selectedOptions);

      setDetail(newDetail);
    }
  }, [groupsDetail?.[detail.id]]);

  const isAssociatedUsersChanged = ({ selectedUserIds }) => {
    if (groupsDetail?.[detail.id]) {
      const fullGroupDetail = groupsDetail[detail.id];

      const { associatedUsers } = { ...fullGroupDetail };

      const associatedUsersId = associatedUsers?.map(({ id }) => id)?.sort();

      return !isEqual(
        associatedUsersId,
        [...selectedUserIds].sort((a, b) => a.localeCompare(b)),
      );
    }

    return false;
  };

  const onSelection = (detail) => {
    const selectedUserIds = detail?.map(({ value }) => value);

    const usersUpdated = isAssociatedUsersChanged({ selectedUserIds });

    setFormValues((prevState) => ({
      ...prevState,
      associatedUsers: detail.map(({ value }) => ({
        id: value,
      })),
      usersUpdated,
    }));

    setSelectedOption(detail);
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('INFORMATION')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="USER_GROUP_NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              info={validationDetail}
              maxLength="128"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('name')}
              disabled={isFormReadOnly}
            />
            <Field
              label="ASSIGN_USERS"
              containerClass="full-width"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('assignUsers')}
            >
              <DropDown
                list={usersList}
                selectedList={selectedOption}
                onOpen={onDropDownOpen}
                disabled={isFormReadOnly}
                onSelection={onSelection}
                renderItemsSelection={(props) => (
                  <MultiSelection
                    unselectedTitle="Unselected Users"
                    selectedTitle="Selected Users"
                    {...props}
                  />
                )}
                isMulti
                hasSearch
                containerClass="full-width"
                loadMoreDetail={{ ...usersListTableDetail, onLoadMoreClick }}
                loading={isDropDownLoading}
                containerStyle={{ maxWidth: '250px' }}
              />
            </Field>
          </FieldGroup>

          <FieldGroup>
            <Input
              label="SOURCE"
              name="source"
              onChange={onFormFieldChange}
              value={formValues.source}
              readOnly
              disabled
              style={{ color: 'black' }}
            />

            {formValues?.idp?.name && (
              <Input
                label="IDP"
                name="idp"
                onChange={onFormFieldChange}
                value={formValues?.idp?.name}
                readOnly
                disabled
                style={{ color: 'black' }}
              />
            )}
          </FieldGroup>

          <FieldGroup>
            <TextArea
              name="description"
              value={formValues.description}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              containerClass="full-width"
              maxLength="512"
              rows="3"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('description')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

GroupForm.defaultProps = defaultProps;

GroupForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.object };

export default GroupForm;
