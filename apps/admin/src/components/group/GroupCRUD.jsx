import { useContext, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import {
  CRUDModal,
  FileBrowserForm,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import GroupForm from '../../components/group/GroupForm';
import { getFormTooltipDetail, modalModeDetail } from '../../components/group/helper';

import { showErrorNotification } from '../../ducks/global';
import {
  add,
  bulkRemove,
  downloadTemplateCSV,
  importFromCsvPolling,
  remove,
  update,
} from '../../ducks/groups';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const GroupCRUD = () => {
  const dispatch = useDispatch();
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    csvImportDetail,
    setCsvImportDetail,
    csvImportResult,
    setCsvImportResult,
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultCsvImportDetail,
    defaultCsvImportResultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const [showUpdateStatusWarning, setShowUpdateStatusWarning] = useState(false);
  const [warningSummary, setWarningSummary] = useState('');

  const { hasFullAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setCsvImportDetail(defaultCsvImportDetail);
    setCsvImportResult(defaultCsvImportResultDetail);
    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(detail), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(detail), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch((response) => {
          if (response.apiErrorLevel === 'WARN') {
            setShowUpdateStatusWarning(true);
            setWarningSummary(response.errorSummary);
          } else {
            dispatch(showErrorNotification({ message: response?.errorSummary }));
          }
        });
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'bulkDelete') {
      onBulkActionSelection();
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsvPolling(csvImportDetail))
        .then((response = {}) => {
          setCsvImportResult({ ...response });
        })
        .catch(noop);
    }
  };

  const onSaveAuditorOverrideClick = () => {
    apiCall(update(detail, true), {
      successNotificationPayload: { ...getApiPUTNotificationOptions() },
    })
      .then(onCloseClick)
      .catch((response) => {
        if (response.apiErrorLevel === 'WARN') {
          setShowUpdateStatusWarning(true);
          setWarningSummary(response.errorSummary);
        } else {
          dispatch(showErrorNotification({ message: response?.errorSummary }));
        }
      });
  };

  const onCloseAuditorOverrideClick = () => {
    setShowUpdateStatusWarning(false);
    setWarningSummary('');
  };

  const onBulkActionSelection = () => {
    const { value } = selectedBulkAction;

    const ids = selectedRowDetail.map(({ id }) => id);

    if (value === 'DELETE') {
      apiCall(bulkRemove({ ids }), {
        successNotificationPayload: {
          ...getApiPUTNotificationOptions(),
          message: 'ITEMS_HAVE_BEEN_DELETED',
        },
      })
        .catch(noop)
        .finally(onCloseClick);
    }
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const isImportResultValid = !!csvImportResult;

  const showSave = useMemo(() => {
    if (modalMode === 'view') {
      return false;
    }

    return !isImportResultValid && hasFullAccess;
  }, [modalMode, isImportResultValid, hasFullAccess]);

  const cancelText = useMemo(() => {
    return isImportResultValid || modalMode === 'view' ? 'CLOSE' : 'CANCEL';
  }, [modalMode, isImportResultValid]);

  return (
    <>
      <CRUDModal
        mode={modalMode}
        renderFormSection={(props) =>
          modalMode === 'importFile' ? (
            <FileBrowserForm
              onDetailChange={setCsvImportDetail}
              detail={csvImportDetail}
              onDownloadClick={onDownloadClick}
              overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
              tooltip={getFormTooltipDetail('csvFile')}
              result={csvImportResult}
              {...props}
            />
          ) : (
            <GroupForm {...props} />
          )
        }
        saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
        showSave={showSave}
        cancelText={cancelText}
        onSaveClick={onSaveClick}
        onCloseClick={onCloseClick}
        {...modalModeDetail[modalMode]}
      />

      {showUpdateStatusWarning && (
        <Modal
          show={showUpdateStatusWarning}
          onEscape={onCloseClick}
          containerClass="update-status-warning-container"
        >
          <ModalHeader text="AUDITOR_OVERRIDE" onClose={onCloseClick} />
          <ModalBody>{warningSummary}</ModalBody>
          <ModalFooter
            saveText="REMOVE"
            onSave={onSaveAuditorOverrideClick}
            onCancel={onCloseAuditorOverrideClick}
          />
        </Modal>
      )}
    </>
  );
};

export default GroupCRUD;
