import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/roles/index';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const RolesActions = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    searchTerm,
    setSearchTerm,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  return (
    <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
      {hasFullAccess ? (
        <div className="buttons">
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_ROLE')}</span>
          </Button>
        </div>
      ) : null}
      <Search
        onSearch={onSearchEnter}
        term={searchTerm}
        containerClass="no-m-r"
        containerStyle={{ maxWidth: '258px', marginBottom: '20px' }}
      />
    </div>
  );
};

export default RolesActions;
