import { useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Field,
  FieldGroup,
  Input,
  Label,
  RadioButtons,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
} from '@zscaler/zui-component-library';

import { selectIsUserSSOEnabled } from '../../ducks/features/selectors';
import { selectPermissionLevels, selectPermissionsByKey } from '../../ducks/permissions/selectors';

import { PERMISSIONS_KEY, PERMISSION_LEVEL } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import {
  PERMISSIONS_LEVEL_DETAIL,
  PERMISSION_KEY_DETAILS,
  getFormValidationDetail,
} from './helper';

const defaultProps = {
  ...defaultFormProps,
};

const RolesForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();

  const { detail, setDetail } = useContext(CRUDPageContext);

  const getDefaultPermissions = () => {
    const defaultPermissions = {};

    for (let permissionKey of Object.keys(PERMISSION_KEY_DETAILS)) {
      const { defaultPermission } = PERMISSION_KEY_DETAILS[permissionKey];

      defaultPermissions[permissionKey] = defaultPermission;
    }

    return defaultPermissions;
  };

  const getPermissions = () => {
    const newPermissions = { ...getDefaultPermissions() };

    const { permissions = [] } = detail || {};

    for (let permissionDetail of permissions) {
      const [permissionKey, level] = permissionDetail.split('.');

      newPermissions[permissionKey] = level;
    }

    return newPermissions;
  };

  const [formValues, setFormValues] = useState({
    name: '',
    description: '',
    isSystemRole: false,
    comment: '',
    ...detail,
    permissions: getPermissions(),
  });

  const permissionLevels = useSelector(selectPermissionLevels);
  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.ROLES_POLICY));

  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);

  const { hasFullAccess } = privileges;

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  const getPermissionsForDetail = () => {
    const permissionForDetail = [];

    for (let permissionKey of Object.keys(formValues.permissions)) {
      const value = formValues.permissions[permissionKey];

      if (value != PERMISSION_LEVEL.NONE && value) {
        permissionForDetail.push(`${permissionKey}.${value}`);
      }
    }

    return permissionForDetail;
  };

  useEffect(() => {
    const permissions = getPermissionsForDetail();

    setDetail({ ...formValues, permissions });

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onRadioButtonsClick = (detail = {}) => {
    const { id, permissionKey } = detail;

    setFormValues((prevState) => {
      const permissions = { ...prevState.permissions, [permissionKey]: id };

      return { ...prevState, permissions };
    });
  };

  const isRadioButtonSelected = (detail = {}) => {
    const { id, permissionKey } = detail;

    return formValues?.permissions?.[permissionKey] == id;
  };

  const checkUpdatePermission = () => {
    return hasFullAccess && !formValues.isSystemRole;
  };

  const isUserIdentityDisabled = (pageKey) => {
    return (
      pageKey === PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY ||
      pageKey === PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY
    );
  };

  const isNotImplementedFeature = (pageKey) => {
    return pageKey === PERMISSIONS_KEY.AUTHENTICATION_EVENT_LOG_POLICY;
  };

  const radioButtonClasses = () => {
    if (!checkUpdatePermission()) {
      return 'disabled';
    }
    return '';
  };

  const permissionList = useMemo(() => {
    const list = [];

    Object.keys(permissionLevels).forEach((permissionKey) => {
      const level = permissionLevels[permissionKey] || [];

      const detail = PERMISSION_KEY_DETAILS[permissionKey];

      const levelList = level
        .map((level) => ({
          ...(PERMISSIONS_LEVEL_DETAIL[level] || {}),
          permissionKey,
        }))
        .sort((a, b) => a.order - b.order);

      list.push({ permissionKey, detail, levelList });
    });

    return list;
  }, [permissionLevels]);

  const renderAllPermissions = () => {
    return permissionList.map(({ permissionKey, detail, levelList }) => {
      // Hide all zaa permission in standalone zid portal only visibl in one-ui
      if (permissionKey.startsWith('zaa')) {
        return null;
      }

      if (
        !detail ||
        (!isUserSSOEnabled && isUserIdentityDisabled(permissionKey)) ||
        isNotImplementedFeature(permissionKey)
      ) {
        return null;
      }

      const radioBtnClass = radioButtonClasses();

      return (
        <Field key={permissionKey}>
          <Label
            tooltip={
              !hasFullAccess || !checkUpdatePermission()
                ? {}
                : {
                    content: detail.tooltip,
                    placement: 'right',
                  }
            }
          >
            {t(detail.label)}
          </Label>

          <RadioButtons
            containerClass={!hasFullAccess ? `disabled ${radioBtnClass}` : radioBtnClass}
            list={levelList}
            isSelected={isRadioButtonSelected}
            onClick={onRadioButtonsClick}
          />
        </Field>
      );
    });
  };

  const renderGeneralSection = () => {
    return (
      <FieldGroup>
        <Input
          name="name"
          label="NAME"
          onChange={onFormFieldChange}
          value={formValues.name}
          disabled={!hasFullAccess || !checkUpdatePermission()}
          maxLength="128"
          info={validationDetail}
        />
        <Input
          name="description"
          label="DESCRIPTION_OPTIONAL"
          onChange={onFormFieldChange}
          value={formValues.description}
          maxLength="256"
          disabled={!hasFullAccess || !checkUpdatePermission()}
          info={validationDetail}
        />
      </FieldGroup>
    );
  };

  return (
    <section className="roles-form-container">
      {renderGeneralSection()}
      <section className="permission-list-container">{renderAllPermissions()}</section>
    </section>
  );
};

RolesForm.defaultProps = defaultProps;

RolesForm.propTypes = { ...defaultFormPropTypes };

export default RolesForm;
