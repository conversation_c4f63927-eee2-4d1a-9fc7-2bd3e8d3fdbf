import { useContext, useMemo } from 'react';

import {
  CRUDModal,
  FileBrowserForm,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import {
  add,
  bulkRemove,
  downloadTemplateCSV,
  importFromCsvPolling,
  remove,
  update,
} from '../../../ducks/user-attributes';

import { CRUDPageContext } from '../../../contexts/CRUDPageContextProvider';
import { getFormTooltipDetail, getSanitizedApiPayload, modalModeDetail } from '../helper';
import UserAttributeForm from './UserAttributeForm';

const UserAttributeCRUD = () => {
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    selectedRowDetail,
    csvImportDetail,
    setCsvImportDetail,
    csvImportResult,
    setCsvImportResult,
    defaultModalMode,
    defaultDetail,
    defaultCsvImportDetail,
    defaultCsvImportResultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setCsvImportDetail(defaultCsvImportDetail);
    setCsvImportResult(defaultCsvImportResultDetail);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(getSanitizedApiPayload({ detail })), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(getSanitizedApiPayload({ detail })), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'bulkDelete') {
      const ids = selectedRowDetail.map(({ attrName }) => attrName);

      apiCall(bulkRemove({ ids }), {
        successNotificationPayload: {
          ...getApiPUTNotificationOptions(),
          message: 'ITEMS_HAVE_BEEN_DELETED',
        },
      })
        .catch(noop)
        .finally(onCloseClick);
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsvPolling(csvImportDetail))
        .then((response = {}) => {
          setCsvImportResult({ ...response });
        })
        .catch(noop);
    }
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const renderModalForm = (props) => {
    if (modalMode === 'importFile') {
      return (
        <FileBrowserForm
          onDetailChange={setCsvImportDetail}
          detail={csvImportDetail}
          onDownloadClick={onDownloadClick}
          overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
          tooltip={getFormTooltipDetail('csvFile')}
          result={csvImportResult}
          {...props}
        />
      );
    }

    return <UserAttributeForm {...props} />;
  };

  const isImportResultValid = !!csvImportResult;

  const cancelText = useMemo(() => {
    return isImportResultValid || modalMode === 'view' ? 'CLOSE' : 'CANCEL';
  }, [modalMode, isImportResultValid]);

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) => renderModalForm(props)}
      showSave={hasFullAccess && !isImportResultValid}
      cancelText={cancelText}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default UserAttributeCRUD;
