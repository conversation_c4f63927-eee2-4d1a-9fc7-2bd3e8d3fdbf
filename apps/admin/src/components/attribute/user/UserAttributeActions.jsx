import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faFileSpreadsheet, faPlus, faTrash } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../../ducks/user-attributes';

import { CRUDPageContext } from '../../../contexts/CRUDPageContextProvider';

const UserAttributeActions = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    selectedRowDetail,
    searchTerm,
    setSearchTerm,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onCSVImportClick = () => {
    setModalMode('importFile');
  };

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  const onBulkDelete = () => {
    setModalMode('bulkDelete');
  };

  return (
    <>
      <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
        {hasFullAccess ? (
          <div className="buttons">
            <Button onClick={onAddClick}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              <span>{t('ADD_ATTRIBUTE')}</span>
            </Button>

            <Button type="secondary" onClick={onCSVImportClick}>
              <FontAwesomeIcon icon={faFileSpreadsheet} className="icon left" />
              <span>{t('IMPORT_CSV')}</span>
            </Button>

            {isBulkActionEnabled && (
              <Button type="secondary" onClick={onBulkDelete}>
                <FontAwesomeIcon icon={faTrash} className="icon left" />
                <span>{t('DELETE')}</span>
              </Button>
            )}
          </div>
        ) : null}

        <Search
          onSearch={onSearchEnter}
          term={searchTerm}
          containerStyle={{ maxWidth: '260px', marginBottom: '20px' }}
        />
      </div>
    </>
  );
};

export default UserAttributeActions;
