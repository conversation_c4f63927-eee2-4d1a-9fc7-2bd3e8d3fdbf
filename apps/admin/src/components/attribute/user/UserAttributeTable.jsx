import { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  RowNumber,
  Selector,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../../ducks/user-attributes';
import { selectTableConfig, selectTableDetail } from '../../../ducks/user-attributes/selectors';

import { CRUDPageContext } from '../../../contexts/CRUDPageContextProvider';

const UserAttributeTable = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, setSelectedRowDetail, searchTerm, isFormReadOnly } =
    useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'number') {
        columnDetail.cell = RowNumber;
      }

      if (columnDetail.id === 'dataType') {
        columnDetail.cell = ({ cell }) => t(cell.getValue());
      }

      if (columnDetail.id === 'required') {
        columnDetail.cell = ({ cell }) => (cell.getValue() ? t('YES') : t('NO'));
      }

      if (columnDetail.id === 'origin') {
        columnDetail.cell = ({ cell }) =>
          cell.getValue() ? t('SYSTEM_DEFINED') : t('USER_DEFINED');
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              original: { systemDefined },
            },
          } = props;

          if (systemDefined) {
            return null;
          }

          return (
            <Actions
              {...props}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              showDelete={!isFormReadOnly}
              editIcon={isFormReadOnly ? faEye : faPencilAlt}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    if (isFormReadOnly) {
      return [...(tableConfig?.columns || [])];
    }

    return [
      {
        id: 'selection',
        Header: '',
        cell: (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              original: { systemDefined },
            },
          } = props;

          if (systemDefined) {
            return null;
          }

          return <Selector {...props} />;
        },
        size: 50,
        minSize: 50,
        maxSize: 50,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];
  }, [tableConfig?.columns, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      onRowSelection={onRowSelection}
    />
  );
};

export default UserAttributeTable;
