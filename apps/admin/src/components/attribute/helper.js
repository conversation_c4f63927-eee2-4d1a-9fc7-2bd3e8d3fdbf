import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'ATTRIBUTE',
  },
  add: {
    headerText: 'ADD_ATTRIBUTE',
  },
  edit: {
    headerText: 'EDIT_ATTRIBUTE',
  },
  delete: {
    headerText: 'DELETE_ATTRIBUTE',
    confirmationMessage:
      'Are you sure you want to delete this attribute? The changes cannot be undone.',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage:
      'Are you sure you want to bulk delete these user attributes? The changes cannot be undone.',
  },
  importFile: {
    headerText: 'IMPORT_ATTRIBUTE',
  },
};

export const modalModeSessionDetail = {
  '': {},
  view: {
    headerText: 'SESSION_ATTRIBUTE',
  },
  add: {
    headerText: 'ADD_SESSION_ATTRIBUTE',
  },
  edit: {
    headerText: 'EDIT_SESSION_ATTRIBUTE',
  },
  delete: {
    headerText: 'DELETE_SESSION_ATTRIBUTE',
    confirmationMessage:
      'Are you sure you want to delete this attribute? The changes cannot be undone.',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage:
      'Are you sure you want to bulk delete these session attributes? The changes cannot be undone.',
  },
  importFile: {
    headerText: 'IMPORT_ATTRIBUTE',
  },
};

export const getSanitizedApiPayload = ({ detail }) => {
  detail.attrName = detail.attrName?.trim?.() || '';

  for (const key in detail) {
    if (Object.hasOwnProperty.call(detail, key)) {
      const element = detail[key];

      if (typeof element === 'string') {
        detail[key] = element?.trim?.();
      }
    }
  }

  return detail;
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { attrName, displayName, dataType } = formValues || {};

  if (!attrName) {
    validationDetail.isValid = false;
    validationDetail.context = 'attrName';
    validationDetail.type = 'error';
    validationDetail.message = 'Attribute Name is Required';

    return validationDetail;
  }

  if (!displayName) {
    validationDetail.isValid = false;
    validationDetail.context = 'displayName';
    validationDetail.type = 'error';
    validationDetail.message = 'Display Name is Required';

    return validationDetail;
  }

  if (!dataType) {
    validationDetail.isValid = false;
    validationDetail.context = 'dataType';
    validationDetail.type = 'error';
    validationDetail.message = 'data type is the required attribute';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'attrName') {
    tooltipDetail.content = `Enter the SAML attribute name (e.g., Group or Email Address). To find all the available SAML attribute names, refer to the SAML JSON for your IdP.`;
  }

  if (name === 'displayName') {
    tooltipDetail.content = (
      <p>
        Enter a name for the SAML attribute. This name is only for your reference and doesn&apos;t
        need to be identical to the corresponding entry in{' '}
        <strong className="tooltip-bold">Attribute Name</strong> field.
      </p>
    );
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. Comments cannot exceed 512 characters.`;
  }

  if (name === 'dataType') {
    tooltipDetail.content = `Select the type of data the user needs to enter for this attribute`;
  }

  if (name === 'required') {
    tooltipDetail.content = (
      <p>
        Check the option to mandate the attribute information when{' '}
        <a href="https://help.zscaler.com/zslogin/adding-users">adding users</a>.
      </p>
    );
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Select if you want to update your existing attribute information, delete existing attribute
        , or add new attribute. If you only want to add new attributes, Zscaler doesn&apos;t
        recommend selecting this option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-attribute-information-csv-file">
          Importing Attribute Information from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};
