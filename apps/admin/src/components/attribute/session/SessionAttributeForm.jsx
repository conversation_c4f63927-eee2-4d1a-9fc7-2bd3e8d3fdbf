import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  TextArea,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { getDataTypes } from '../../../ducks/user-attributes';
import {
  selectDataTypesDetail,
  selectDataTypesList,
} from '../../../ducks/user-attributes/selectors';

import { CRUDPageContext } from '../../../contexts/CRUDPageContextProvider';
import { getFormTooltipDetail, getFormValidationDetail } from '../helper';

const defaultProps = defaultFormProps;

const SessionAttributeForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();

  const { detail, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const inEditMode = mode === 'edit';

  const dataTypes = useSelector(selectDataTypesDetail);
  const dataTypesList = useSelector(selectDataTypesList);

  const { isDropDownLoading, onDropDownOpen } = useDropDownActions({
    detail: dataTypes,
    apiCallFunc: getDataTypes,
  });

  const [selectedOption, setSelectedOption] = useState([
    { label: detail.dataType, value: detail.dataType },
  ]);

  const [formValues, setFormValues] = useState({
    attrName: '',
    displayName: '',
    comment: '',
    dataType: '',
    required: false,
    ...detail,
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    setDetail(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onSelection = (detail) => {
    if (detail && detail[0]) {
      setFormValues((prevState) => ({ ...prevState, dataType: detail?.[0]?.value }));
    }

    setSelectedOption(detail);
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('INFORMATION')}</section>

        <Card>
          <FieldGroup>
            <Input
              label="ATTRIBUTE_NAME"
              name="attrName"
              onChange={onFormFieldChange}
              value={formValues.attrName}
              info={validationDetail}
              readOnly={inEditMode}
              maxLength="128"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('attrName')}
              disabled={isFormReadOnly}
            />
            <Input
              label="DISPLAY_NAME"
              name="displayName"
              onChange={onFormFieldChange}
              value={formValues.displayName}
              info={validationDetail}
              maxLength="128"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('displayName')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>

          <div className="is-flex">
            <TextArea
              name="comment"
              value={formValues.comment}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              maxLength="512"
              rows="3"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('description')}
              disabled={isFormReadOnly}
            />
          </div>

          <FieldGroup>
            <Field
              label="DATA_TYPE"
              htmlFor="dataType"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('dataType')}
              containerStyle={{ maxWidth: '260px' }}
            >
              <DropDown
                list={dataTypesList}
                selectedList={selectedOption}
                onSelection={onSelection}
                containerClass="full-width"
                onOpen={onDropDownOpen}
                loading={isDropDownLoading}
                disabled={isFormReadOnly}
              />
            </Field>
          </FieldGroup>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

SessionAttributeForm.defaultProps = defaultProps;

SessionAttributeForm.propTypes = { ...defaultFormPropTypes };

export default SessionAttributeForm;
