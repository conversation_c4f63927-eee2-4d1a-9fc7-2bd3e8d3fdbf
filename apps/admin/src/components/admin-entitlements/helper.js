export const modalModeDetail = (entityType, modalMode) => {
  if (modalMode === 'delete') {
    return {
      headerText: 'DELETE_ASSIGNMENT',
      confirmationMessage: `Are you sure you want to delete this assignment? The changes cannot be undone.`,
    };
  } else if (modalMode === 'edit') {
    return {
      headerText: `EDIT_${entityType}`,
    };
  } else if (modalMode === 'bulkDelete') {
    return {
      headerText: 'BULK_DELETE',
      confirmationMessage:
        'Are you sure you want to bulk delete these assignments? The changes cannot be undone.',
    };
  } else if (modalMode === 'view') {
    return {
      headerText: `VIEW_${entityType}`,
    };
  }

  return {};
};

export const getAssignEntitiesApiPayload = ({
  selectedEntities,
  isZiamService,
  type,
  constraints,
}) => {
  // Define a function to generate the resource object
  const getResource = (entity) => {
    if (isZiamService) {
      return {
        displayName: entity.displayName,
        id: entity.id,
        name: entity.name,
        type,
      };
    }
    return { id: entity.id };
  };

  // Define a function to generate the roles array
  const getRoles = (entity) => {
    const roleAttribute = isZiamService ? 'roles' : 'tsRoles';
    return {
      [roleAttribute]: entity.selectedRole.map((role) => ({
        id: role.value,
        name: role.label,
        ...(isZiamService && { type: role.type }),
      })),
    };
  };

  // Define a function to generate the scope object
  const getScope = (entity) => {
    if (!constraints.scopeSupport) {
      return [];
    }
    const scope = entity.selectedScope;

    if (constraints?.multiScopeSupport) {
      return {
        tsScopes: scope.map((item) => ({ id: item?.value, name: item?.label })),
      };
    }

    return {
      tsScope: { id: scope[0]?.value, name: scope[0]?.label },
    };
  };

  // Generate the payload
  const payload = selectedEntities.map((entity) => ({
    resource: getResource(entity),
    ...getRoles(entity),
    ...(!isZiamService && { type }),
    ...getScope(entity),
  }));

  return payload;
};

export const getUpdateApiPayload = (userDetail) => {
  return userDetail;
};

export const manageOptions = [{ label: 'VIEW_ROLES', value: 'VIEW_ROLES' }];
