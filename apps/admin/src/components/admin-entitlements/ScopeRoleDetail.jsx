import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getServiceRoles } from '../../ducks/services';
import { selectScopeRoleDetail, selectScopesRoleDetail } from '../../ducks/services/selectors';

const defaultProps = {
  original: { id: '', tservice: { id: '' } },
};

const ScopeRoleDetail = ({ original }) => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall();

  const {
    id: scopeId,
    tservice: { id },
  } = original;

  const roles = selectScopeRoleDetail(useSelector(selectScopesRoleDetail), scopeId);

  useEffect(() => {
    apiCall(getServiceRoles({ id, scopeId })).catch(noop);
  }, [scopeId, id]);

  const roleName = roles?.map?.(({ name }) => name)?.join?.(', ');

  return <div className="scope-role-container">{roleName || t('NO_ITEMS_FOUND')}</div>;
};

ScopeRoleDetail.defaultProps = defaultProps;

ScopeRoleDetail.propTypes = {
  original: PropTypes.object,
};

export default ScopeRoleDetail;
