import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  MultiSelection,
  defaultFormPropTypes,
  defaultFormProps,
  getDropDownList,
  useApiCall,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { find, noop } from 'lodash-es';

import { getRoles, getScopes } from '../../ducks/admin-entitlements';
import {
  selectScopeRoleDetail,
  selectScopeRoleList,
  selectScopesRoleDetail,
  selectServiceConstraints,
  selectServiceRoleDetail,
  selectServiceRoleList,
  selectServiceScopeDetail,
  selectServiceScopeList,
  selectServicesRoleDetail,
  selectServicesScopeDetail,
} from '../../ducks/admin-entitlements/selectors';
import { getList } from '../../ducks/roles/index';
import {
  selectRolesList,
  selectTableDetail as selectRolesListTableDetail,
} from '../../ducks/roles/selectors';

const defaultProps = {
  ...defaultFormProps,
};

const EditForm = ({ onDetailChange, detail, serviceName, hasFullAccess }) => {
  const { apiCall } = useApiCall();
  const isZiamService = serviceName === 'ZIAM';

  const rolesListTableDetail = useSelector(selectRolesListTableDetail);
  const ziamRolesList = useSelector(selectRolesList);

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: rolesListTableDetail,
    apiCallFunc: getList,
  });

  const [selectedZiamRoles, setSelectedZiamRoles] = useState(() => {
    const { roles } = detail || [];

    if (roles) {
      return roles.map(({ id, name, type }) => ({ label: name, value: id, type }));
    }

    return [];
  });

  const serviceConstraints = find(useSelector(selectServiceConstraints), [
    'serviceName',
    detail?.tservice?.name || serviceName,
  ]);

  const serviceRolesDetail = selectServiceRoleDetail(
    useSelector(selectServicesRoleDetail),
    detail?.tservice?.id || detail?.id,
  );

  const serviceScopesDetail = selectServiceScopeDetail(
    useSelector(selectServicesScopeDetail),
    detail?.tservice?.id || detail?.id,
  );

  const [selectedRoleList, setSelectedRoleList] = useState(
    getDropDownList({ list: detail?.tsRoles || detail?.roles, lite: false }),
  );
  const [selectedScopeList, setSelectedScopeList] = useState(
    getDropDownList({ list: detail?.tsScopes || [], lite: false }),
  );

  const serviceRolesList = selectServiceRoleList(serviceRolesDetail);
  const serviceScopesList = selectServiceScopeList(serviceScopesDetail);

  const selectedScopeDetail = selectedScopeList[0];

  const selectedScopeRole = selectScopeRoleDetail(
    useSelector(selectScopesRoleDetail),
    selectedScopeDetail?.value,
  );

  const selectedScopeRoleList = selectScopeRoleList(selectedScopeRole);

  useEffect(() => {
    if (serviceConstraints?.scopeSupport) {
      apiCall(getScopes({ id: detail?.tservice?.id })).catch(noop);
      if (!serviceConstraints?.multiScopeSupport) {
        apiCall(
          getRoles({ id: detail?.tservice?.id, scopeId: detail?.tsScopes[0]?.id, isZiamService }),
        );
      }
    }

    if (!isZiamService && serviceConstraints?.multiScopeSupport) {
      apiCall(getRoles({ id: detail?.tservice?.id })).catch(noop);
    }
  }, []);

  const onRoleSelection = (payload) => {
    let roles = {};

    if (isZiamService) {
      setSelectedZiamRoles(payload);
      roles = {
        ...detail,
        roles: payload?.map?.(({ label, value, type, isSystemRole }) => ({
          id: value,
          name: label,
          type: type || (isSystemRole ? 'SYSTEM' : 'CUSTOM'),
        })),
      };
    } else {
      setSelectedRoleList(payload);
      roles = {
        ...detail,
        tsRoles: payload?.map?.(({ label, value }) => ({ id: value, name: label })),
      };
    }
    onDetailChange(roles);
  };

  const onScopeSelection = (payload) => {
    setSelectedScopeList(payload);

    const tsScopes = payload?.map?.(({ label, value }) => ({ id: value, name: label }));

    const nextDetail = {
      ...detail,
      tsScopes,
    };

    if (!serviceConstraints?.multiScopeSupport) {
      setSelectedRoleList([]);
      apiCall(
        getRoles({ id: detail?.tservice?.id, scopeId: tsScopes?.[0]?.id, isZiamService }),
      ).catch(noop);
      nextDetail.tsRoles = [];
    }

    onDetailChange(nextDetail);
  };

  let roleMultiSelectProps = {};
  if (serviceConstraints.multiRoleSupport || isZiamService) {
    roleMultiSelectProps.isMulti = true;
    roleMultiSelectProps.renderItemsSelection = (props) => {
      return (
        <MultiSelection
          unselectedTitle="Unselected Roles"
          selectedTitle="Selected Roles"
          {...props}
        />
      );
    };
  }

  let scopeMultiSelectProps = {};
  if (serviceConstraints.multiScopeSupport) {
    scopeMultiSelectProps.isMulti = true;
    scopeMultiSelectProps.renderItemsSelection = (props) => {
      return (
        <MultiSelection
          unselectedTitle="Unselected Scopes"
          selectedTitle="Selected Scopes"
          {...props}
        />
      );
    };
  }

  return (
    <Card>
      <FieldGroup>
        <Input
          label="NAME"
          name="name"
          value={detail?.resource?.name}
          maxLength="254"
          disabled={true}
        />
      </FieldGroup>
      <FieldGroup>
        {serviceConstraints.scopeSupport && (
          <Field label="ASSIGN_SCOPE">
            <DropDown
              list={serviceScopesList}
              selectedList={selectedScopeList}
              onSelection={onScopeSelection}
              hasSearch
              {...scopeMultiSelectProps}
              containerClass="full-width"
              containerStyle={{ maxWidth: '250px' }}
            />
          </Field>
        )}
        {isZiamService && (
          <Field label="ROLE" containerClass="full-width" htmlFor="role">
            <DropDown
              list={ziamRolesList}
              selectedList={selectedZiamRoles}
              onOpen={onDropDownOpen}
              onSelection={(detail) => {
                onRoleSelection(detail);
              }}
              {...roleMultiSelectProps}
              isMulti
              hasSearch
              containerClass="full-width"
              loadMoreDetail={{ ...rolesListTableDetail, onLoadMoreClick }}
              loading={isDropDownLoading}
              containerStyle={{ maxWidth: '250px' }}
              disabled={!hasFullAccess}
            />
          </Field>
        )}
        {!isZiamService && serviceConstraints.roleSupport && (
          <Field label="ROLE" containerStyle={{ maxWidth: '260px' }}>
            <DropDown
              list={serviceConstraints.scopeSupport ? selectedScopeRoleList : serviceRolesList}
              selectedList={selectedRoleList}
              onSelection={(detail) => {
                onRoleSelection(detail);
              }}
              {...roleMultiSelectProps}
              containerClass="full-width"
              containerStyle={{ maxWidth: '250px' }}
              disabled={!hasFullAccess}
            />
          </Field>
        )}
      </FieldGroup>
    </Card>
  );
};

EditForm.defaultProps = defaultProps;

EditForm.propTypes = { ...defaultFormPropTypes };

export default EditForm;
