import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faEye, faPencilAlt, faPlus, faTrash, faUser } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Actions,
  Button,
  CRUDModal,
  Card,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Search,
  Selector,
  TableContainer,
  TextWithTooltip,
  getApiDELETENotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { getUpdateApiPayload, modalModeDetail } from '../../components/admin-entitlements/helper';

import {
  bulkDelete,
  getEntityList,
  remove as removeEntity,
  update,
} from '../../ducks/admin-entitlements';
import {
  selectEntityTableConfig,
  selectEntityTableDetail,
  selectServiceConstraintDetail,
  selectServiceConstraints,
} from '../../ducks/admin-entitlements/selectors';
import { showErrorNotification } from '../../ducks/global';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import { PERMISSIONS_KEY } from '../../config';
import AssignEntityView from './AssignEntityView';
import EditForm from './EditForm';
import GroupUsersTableModal from './GroupUsersTableModal';

const defaultProps = {
  service: {},
  privileges: {
    hasFullAccess: false,
    hasViewAccess: false,
  },
  servicePermissions: {},
};

const TabView = ({
  service,
  onAssignEntityClick,
  onDetailsFetch,
  privileges,
  servicePermissions,
  entityType,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { apiCall } = useApiCall({});

  const adminEntitlementPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY),
  );

  const {
    hasFullAccess: hasAdminEntitlementFullAccess,
    hasRestrictedFullAccess: hasAdminEntitlementRestrictedFullAccess,
    noAccess: hasAdminEntitlementNoAccess,
  } = adminEntitlementPrivileges;

  const { hasFullAccess, hasViewAccess, noAccess: hasUserNoAccess } = privileges;
  const { hasFullAccess: hasServiceFullAccess = false, noAccess: hasServiceNoAccess } =
    servicePermissions;

  const [entityDetail, setEntityDetail] = useState({});
  const [groupUsersTableModalData, setGroupUsersTableModalData] = useState(false);
  const [isBulkRowsSelected, setIsBulkRowsSelected] = useState(false);
  const [modalMode, setModalMode] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRowDetail, setSelectedRowDetail] = useState([]);

  const [showAssignEntityView, setShowAssignEntityView] = useState(false);
  const [showGroupUsersTableModal, setShowGroupUsersTableModal] = useState(false);
  const isZiamService = service?.serviceName === 'ZIAM';
  const serviceConstraints = useSelector(selectServiceConstraints);

  const [showUpdateStatusWarning, setShowUpdateStatusWarning] = useState(false);
  const [warningSummary, setWarningSummary] = useState('');

  const { hasAdminAccess } = service || {};

  const constraints = selectServiceConstraintDetail(
    useSelector(selectServiceConstraints),
    service?.serviceName,
  );

  useEffect(() => {
    apiCall(getEntityList({ id: service?.id, isZiamService, type: entityType })).catch(noop);
    setSearchTerm('');
  }, [entityType]);

  const tableConfig = useSelector(selectEntityTableConfig);
  const tableDetail = useSelector(selectEntityTableDetail);

  const hasEditPermission =
    ((hasAdminEntitlementFullAccess ||
      (hasAdminEntitlementRestrictedFullAccess && hasAdminAccess)) &&
      (hasFullAccess || hasViewAccess)) ||
    (hasServiceFullAccess &&
      hasAdminEntitlementRestrictedFullAccess &&
      (hasFullAccess || hasViewAccess));

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'userId') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { resource } = props?.row?.original || {};

          return <TextWithTooltip text={resource?.loginName} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          return (
            <Actions
              {...props}
              editIcon={
                hasEditPermission
                  ? faPencilAlt
                  : !hasAdminEntitlementNoAccess || !hasUserNoAccess || !hasServiceNoAccess
                    ? faEye
                    : null
              }
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              showDelete={hasEditPermission}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'groupUsers') {
        const UsersCellComponent = (props) => {
          return (
            <Button
              type="tertiary"
              onClick={() => {
                // eslint-disable-next-line react/prop-types
                setGroupUsersTableModalData(props?.row?.original);
                setShowGroupUsersTableModal(true);
              }}
            >
              <FontAwesomeIcon icon={faUser} className="icon left" />
              {t('USERS')}
            </Button>
          );
        };

        columnDetail.cell = UsersCellComponent;
      }

      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { name, idp } = props?.row?.original?.resource || {};

          let derivedName = name;

          const idpName = idp?.name || '';

          if (idpName) {
            derivedName = `${name} (${idpName})`;
          }

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });

    let newColumns = [];

    if (hasEditPermission) {
      newColumns = [
        {
          id: 'selection',
          Header: '',
          cell: (props) => <Selector {...props} />,
          size: 50,
          minSize: 50,
          maxSize: 50,
          enableResizing: false,
          disableSortBy: true,
          defaultCanSort: false,
        },
        ...(tableConfig?.columns || []),
      ];
    } else {
      newColumns = [...(tableConfig?.columns || [])];
    }

    if (!constraints.scopeSupport) {
      remove(newColumns, { id: 'tsScopes' });
    }

    if (entityType === 'GROUP') {
      remove(newColumns, { id: 'userId' });
    }

    if (entityType === 'USER') {
      remove(newColumns, { id: 'groupUsers' });
    }

    return newColumns;
  }, [
    tableConfig?.columns,
    entityType,
    hasViewAccess,
    hasFullAccess,
    serviceConstraints,
    hasAdminEntitlementFullAccess,
    hasAdminEntitlementRestrictedFullAccess,
    hasAdminEntitlementNoAccess,
    hasServiceFullAccess,
    hasUserNoAccess,
    hasServiceNoAccess,
  ]);

  useEffect(() => {
    onDetailsFetch(tableDetail?.totalRecord);
  }, [tableDetail]);

  useEffect(() => {
    setIsBulkRowsSelected(selectedRowDetail.length > 1);
  }, [selectedRowDetail]);

  const cancelRemoveAssignments = () => {
    setSelectedRowDetail([]);
  };

  const hideAssignEntityView = () => {
    onAssignEntityClick(false);
    setShowAssignEntityView(false);
    apiCall(getEntityList({ id: service?.id, isZiamService, type: entityType })).catch(noop);
  };

  const onAddClick = () => {
    setShowAssignEntityView(true);
    onAssignEntityClick(true);
  };

  const onCloseClick = () => {
    setModalMode('');
  };

  const onCloseGroupUsersModal = () => {
    setShowGroupUsersTableModal(false);
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setEntityDetail(detail);
      setModalMode('delete');
    }
  };

  const onDetailChange = (detail) => {
    setEntityDetail(detail);
  };

  const onEditClick = (detail) => {
    let editDetail = { ...detail };
    if (editDetail) {
      if (!editDetail?.tService) {
        editDetail.tservice = service;
      }
      setEntityDetail(editDetail);
      setModalMode(hasEditPermission ? 'edit' : 'view');
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(
      getEntityList({
        id: service?.id,
        requireTotal: false,
        pageOffset: pageOffset + pageSize,
        pageSize,
        isZiamService,
        type: entityType,
      }),
    ).catch(noop);
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);
  };

  const onSaveClick = () => {
    if (modalMode === 'edit') {
      const updatePayload = getUpdateApiPayload(entityDetail);
      apiCall(
        update({
          id: entityDetail?.tservice?.id,
          assignedId: entityDetail.id,
          payload: updatePayload,
          isZiamService,
          type: entityType,
        }),
        {
          successNotificationPayload: { ...getApiPUTNotificationOptions() },
          hasNotification: false,
        },
      )
        .then(onCloseClick)
        .catch((response) => {
          if (response.apiErrorLevel === 'WARN') {
            setShowUpdateStatusWarning(true);
            setWarningSummary(response.errorSummary);
          } else {
            dispatch(showErrorNotification({ message: response?.errorSummary }));
          }
        });
    } else if (modalMode === 'delete') {
      apiCall(
        removeEntity({
          id: entityDetail?.id,
          tservice: entityDetail?.tservice,
          isZiamService,
          type: entityType,
        }),
        {
          successNotificationPayload: { ...getApiDELETENotificationOptions() },
        },
      )
        .then(onCloseClick)
        .catch(noop);
    } else if (modalMode === 'bulkDelete') {
      const ids = selectedRowDetail.map(({ id }) => id);
      apiCall(bulkDelete({ ids, tservice: service, isZiamService, type: entityType }), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const onSaveAuditorOverrideClick = () => {
    const updatePayload = getUpdateApiPayload(entityDetail);

    apiCall(
      update({
        id: entityDetail?.tservice?.id,
        assignedId: entityDetail.id,
        payload: updatePayload,
        isZiamService,
        type: entityType,
        auditorOverride: true,
      }),
      {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
        hasNotification: false,
      },
    )
      .then(onCloseClick)
      .catch((response) => {
        if (response.apiErrorLevel === 'WARN') {
          setShowUpdateStatusWarning(true);
          setWarningSummary(response.errorSummary);
        } else {
          dispatch(showErrorNotification({ message: response?.errorSummary }));
        }
      });
  };

  const onCloseAuditorOverrideClick = () => {
    setShowUpdateStatusWarning(false);
    setWarningSummary('');
  };

  const onSearchEnter = (term) => {
    apiCall(getEntityList({ id: service?.id, isZiamService, name: term, type: entityType })).catch(
      noop,
    );

    setSearchTerm(term);
  };

  const removeAssignments = () => {
    setModalMode('bulkDelete');
  };

  const renderBodySection = () => {
    return <GroupUsersTableModal detail={groupUsersTableModalData} />;
  };

  return (
    <>
      {showAssignEntityView ? (
        <AssignEntityView
          entityType={entityType}
          onBackClick={hideAssignEntityView}
          serviceDetail={service}
        />
      ) : (
        <Card containerClass="services-tabs">
          <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
            {((hasAdminEntitlementFullAccess ||
              (hasAdminEntitlementRestrictedFullAccess && hasAdminAccess)) &&
              (hasFullAccess || hasViewAccess)) ||
            (hasServiceFullAccess &&
              hasAdminEntitlementRestrictedFullAccess &&
              (hasFullAccess || hasViewAccess)) ? (
              <>
                {isBulkRowsSelected && (
                  <div className="buttons">
                    {isBulkRowsSelected && (
                      <>
                        <Button type="secondary" onClick={removeAssignments}>
                          <FontAwesomeIcon icon={faTrash} className="icon left" />
                          <span>{t('REMOVE_ASSIGNMENTS')}</span>
                        </Button>
                        <Button type="tertiary" onClick={cancelRemoveAssignments}>
                          <span>{t('CANCEL')}</span>
                        </Button>
                      </>
                    )}
                  </div>
                )}
                {!isBulkRowsSelected && constraints?.roleSupport && (
                  <div className="buttons">
                    <Button onClick={onAddClick}>
                      <FontAwesomeIcon icon={faPlus} className="icon left" />
                      <span>{entityType === 'GROUP' ? t('ASSIGN_GROUPS') : t('ASSIGN_USERS')}</span>
                    </Button>
                  </div>
                )}
              </>
            ) : null}
            <div className="buttons" style={{ marginLeft: 'auto' }}>
              <Search
                onSearch={onSearchEnter}
                term={searchTerm}
                containerStyle={{ width: '260px' }}
              />
            </div>
          </div>
          <TableContainer
            {...tableConfig}
            columns={tableColumnConfig}
            containerClass={'services-tabs-table'}
            data={tableDetail.data}
            onRowSelection={onRowSelection}
            pagination={{ ...tableDetail, onLoadMoreClick }}
          />
          {modalMode && (
            <CRUDModal
              mode={modalMode}
              renderFormSection={(props) => (
                <EditForm
                  {...props}
                  onDetailChange={onDetailChange}
                  detail={entityDetail}
                  serviceName={service?.serviceName}
                  hasFullAccess={hasEditPermission}
                />
              )}
              showSave={hasEditPermission}
              cancelText={hasEditPermission ? 'CANCEL' : 'CLOSE'}
              onSaveClick={onSaveClick}
              onCloseClick={onCloseClick}
              {...modalModeDetail(entityType, modalMode)}
            />
          )}
          {showGroupUsersTableModal && (
            <Modal
              show={showGroupUsersTableModal}
              onEscape={onCloseGroupUsersModal}
              containerClass="group-users-modal crud-modal"
            >
              <ModalHeader text="USERS" onClose={onCloseGroupUsersModal} />
              <ModalBody> {renderBodySection()}</ModalBody>
              <ModalFooter cancelText="OK" showSave={false} onCancel={onCloseGroupUsersModal} />
            </Modal>
          )}

          {showUpdateStatusWarning && (
            <Modal
              show={showUpdateStatusWarning}
              onEscape={onCloseClick}
              containerClass="update-status-warning-container"
            >
              <ModalHeader text="AUDITOR_OVERRIDE" onClose={onCloseClick} />
              <ModalBody>{warningSummary}</ModalBody>
              <ModalFooter
                saveText="REMOVE"
                onSave={onSaveAuditorOverrideClick}
                onCancel={onCloseAuditorOverrideClick}
              />
            </Modal>
          )}
        </Card>
      )}
    </>
  );
};

TabView.defaultProps = defaultProps;

TabView.propTypes = {
  onAssignEntityClick: PropTypes.func,
  onDetailsFetch: PropTypes.func,
  service: PropTypes.object,
  privileges: PropTypes.object,
  servicePermissions: PropTypes.object,
  entityType: PropTypes.string,
};

export default TabView;
