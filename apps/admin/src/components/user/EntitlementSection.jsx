import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { TableContainer, TextWithTooltip } from '@zscaler/zui-component-library';

import { cloneDeep, remove } from 'lodash-es';

import { selectIsUserSSOEnabled } from '../../ducks/features/selectors';
import { STATUS_ENUMS } from '../../ducks/migration/constants';
import { selectStatus } from '../../ducks/migration/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import {
  selectServiceEntitlementTableDetail,
  selectEntitlementTableConfig as selectTableConfig,
  selectEntitlementTableDetail as selectTableDetail,
} from '../../ducks/users/selectors';

import { getAppIcon } from '../../utils/generic';

import { PERMISSIONS_KEY } from '../../config';

function EntitlementSection() {
  const { t } = useTranslation();
  const tableConfig = useSelector(selectTableConfig);
  const adminTableDetails = useSelector(selectTableDetail);
  const serviceTableDetails = useSelector(selectServiceEntitlementTableDetail);

  const status = useSelector(selectStatus);
  const { currentStatus } = status || {};

  const adminEntitlementPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY),
  );

  const serviceEntitlementPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY),
  );

  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);

  const ServiceName = (props) => {
    // eslint-disable-next-line react/prop-types
    const { serviceDescription, serviceName } = props?.row?.original?.service || {};

    return (
      <>
        <div className="is-flex has-ai-c pointer">
          <span
            style={{
              width: '32px',
              height: '32px',
              margin: '0 1em',
              marginLeft: '0',
            }}
          >
            <img src={getAppIcon(serviceName)} style={{ height: '100%' }} alt="service name" />
          </span>
          <span> {serviceDescription}</span>
        </div>
      </>
    );
  };

  const adminTableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'roles') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { tsRoles } = props?.row?.original || {};

          return <TextWithTooltip text={tsRoles?.map((itm) => itm.name)?.join(',') || '-'} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'cloudName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { service } = props?.row?.original || {};

          return <TextWithTooltip text={service?.cloudDomainName || service?.cloudName || '-'} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'orgName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { service } = props?.row?.original || {};

          return <TextWithTooltip text={service?.orgName || '-'} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'serviceName') {
        columnDetail.cell = ServiceName;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  const serviceTableColumnConfig = useMemo(() => {
    const columnConfig = cloneDeep(tableConfig?.columns);

    columnConfig?.map((columnDetail) => {
      if (columnDetail.id === 'roles') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { tsRoles } = props?.row?.original || {};

          return <TextWithTooltip text={tsRoles?.map((itm) => itm.name)?.join(',') || '-'} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'cloudName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { service } = props?.row?.original || {};

          return <TextWithTooltip text={service?.cloudDomainName || service?.cloudName || '-'} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'orgName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { service } = props?.row?.original || {};

          return <TextWithTooltip text={service?.orgName || '-'} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'serviceName') {
        columnDetail.cell = ServiceName;
      }

      return columnDetail;
    });

    remove(columnConfig, { id: 'roles' });

    return [...(columnConfig || [])];
  }, [tableConfig?.columns]);

  return (
    <div
      style={{
        minHeight: '670px',
      }}
    >
      {!adminEntitlementPrivileges.noAccess && (
        <>
          {currentStatus === STATUS_ENUMS.PROVISIONED && (
            <div
              className="admin-entitlements-info-container is-flex"
              style={{ maxWidth: '778px' }}
            >
              <span className="has-color-primary has-as-fs">
                <FontAwesomeIcon icon={faInfoCircle} />
              </span>
              ZIdentity assigned administrative entitlements will override the underlying linked
              service (e.g. Internet Access, Private Access) admin entitlements.
            </div>
          )}
          <section className="typography-paragraph1-uppercase">
            {t('ADMINISTRATIVE_ENTITLEMENTS')}
          </section>
          {adminTableDetails.data.length ? (
            <TableContainer
              {...tableConfig}
              hidePagination={true}
              columns={adminTableColumnConfig}
              data={adminTableDetails.data}
            />
          ) : (
            <div
              className="text-gray is-flex has-jc-c has-ai-c "
              style={{
                marginTop: '20px',
              }}
            >
              No admin entitlements have been assigned
            </div>
          )}
        </>
      )}

      {isUserSSOEnabled && !serviceEntitlementPrivileges.noAccess && (
        <>
          <section className="typography-paragraph1-uppercase">
            {t('SERVICE_ENTITLEMENTS_TEXT')}
          </section>
          {serviceTableDetails.data?.length ? (
            <TableContainer
              {...tableConfig}
              hidePagination={true}
              columns={serviceTableColumnConfig}
              data={serviceTableDetails.data}
            />
          ) : (
            <div
              className="text-gray is-flex has-jc-c has-ai-c "
              style={{
                marginTop: '20px',
              }}
            >
              No service entitlements have been assigned
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default EntitlementSection;
