import { useContext, useEffect } from 'react';

import { CRUDModal, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { modalModeDetail } from '../../components/attribute/helper';

import { add } from '../../ducks/user-attributes';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import UserAttributeForm from '../attribute/user/UserAttributeForm';

const defaultProps = {
  showModal: '',
  setShowModal: noop,
};

const AddUserAttribute = ({ showModal, setShowModal }) => {
  const { apiCall } = useApiCall();

  const { modalMode, setModalMode, detail, setDetail, defaultModalMode, defaultDetail } =
    useContext(CRUDPageContext);

  useEffect(() => {
    setModalMode('add');
  }, [showModal]);

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(detail)).then(onCloseClick).catch(noop);
    }
  };

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setShowModal('');
  };

  if (!showModal) {
    return null;
  }

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) => <UserAttributeForm {...props} />}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

AddUserAttribute.defaultProps = defaultProps;

AddUserAttribute.propTypes = {
  showModal: PropTypes.string,
  setShowModal: PropTypes.func,
};

export default AddUserAttribute;
