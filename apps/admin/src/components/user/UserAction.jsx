import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faFileSpreadsheet, faPlus, faSync, faUnlockAlt } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, DropDown, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList } from '../../ducks/users';

import { PERMISSIONS_KEY } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { bulkActionOptions, searchOptions } from './helper';

const UserAction = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    selectedSearchField,
    setSelectedSearchField,
    searchTerm,
    setDetail,
    defaultDetail,
    setSearchTerm,
    defaultBulkActionOption,
    defaultSearchOption,
    privileges,
    defaultModalMode,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const userCredentialsPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_CREDENTIALS_POLICY),
  );

  const onCSVImportClick = () => {
    setModalMode('importFile');
  };

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onBulkActionSelectionClick = (payload) => {
    const newSelection = payload[0] || defaultBulkActionOption;

    setSelectedBulkAction(newSelection);

    if (selectedRowDetail?.length > 0) {
      const { value } = newSelection;

      let mode = '';

      if (value === 'ACTIVATE') {
        mode = 'bulkActivate';
      }

      if (value === 'DE_ACTIVATE') {
        mode = 'bulkDeactivate';
      }

      if (value === 'DELETE') {
        mode = 'bulkDelete';
      }

      setModalMode(mode);
    }
  };

  const onBulkResetPasswordClick = () => {
    setModalMode('actionConfirmation');
  };

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);

    setSelectedBulkAction(defaultBulkActionOption);
    setSelectedSearchField(defaultSearchOption);
    setSearchTerm('');
  };

  const getSearchField = () => {
    const { value } = selectedSearchField || {};

    let searchField = '';

    if (value === 'NAME') {
      searchField = 'name';
    }

    if (value === 'GROUP') {
      searchField = 'groupname';
    }

    return searchField;
  };

  const onSearchEnter = (term) => {
    const searchField = getSearchField();

    if (searchField) {
      apiCall(getList({ [searchField]: term })).catch(noop);

      setSearchTerm(term);
    }
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  return (
    <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
      {hasFullAccess ? (
        <div className="buttons">
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_USER')}</span>
          </Button>
          <Button type="secondary" onClick={onCSVImportClick}>
            <FontAwesomeIcon icon={faFileSpreadsheet} className="icon left" />
            <span>{t('IMPORT_CSV')}</span>
          </Button>

          {isBulkActionEnabled && !userCredentialsPrivileges.noAccess && (
            <>
              <Button type="secondary" onClick={onBulkResetPasswordClick}>
                <FontAwesomeIcon icon={faUnlockAlt} className="icon left" />
                <span>{t('RESET_PASSWORD')}</span>
              </Button>
            </>
          )}

          {isBulkActionEnabled && (
            <>
              <DropDown
                list={bulkActionOptions}
                selectedList={[selectedBulkAction]}
                onSelection={onBulkActionSelectionClick}
                selectedItemsProps={{
                  kind: 'secondary',
                  containerStyle: { justifyContent: 'center' },
                }}
                disabled={!isBulkActionEnabled}
              />
            </>
          )}
        </div>
      ) : null}
      <div className="buttons">
        <Button
          type="tertiary"
          onClick={onRefreshClick}
          style={{ minWidth: '0px', paddingRight: '0px' }}
        >
          <FontAwesomeIcon icon={faSync} />
        </Button>

        <div className="dropdown-with-search">
          <DropDown
            list={searchOptions}
            selectedList={[selectedSearchField]}
            onSelection={(payload) => {
              setSelectedSearchField(payload[0]);
            }}
            selectedItemsProps={{
              kind: 'tertiary',
              containerStyle: {
                justifyContent: 'center',
                paddingLeft: '4px',
                paddingRight: '4px',
                minWidth: '70px',
              },
            }}
            itemsSelectionProps={{
              containerStyle: {
                minWidth: '130px',
              },
            }}
          />

          <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ width: '260px' }} />
        </div>
      </div>
    </div>
  );
};

export default UserAction;
