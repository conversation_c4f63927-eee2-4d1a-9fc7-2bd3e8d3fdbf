import { defaultValidationDetail, validEmail } from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { find, isEmpty, isUndefined } from 'lodash-es';

export const domainNameValidationRegex =
  /^((?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+((?!-)[A-Za-z0-9-]{1,63}(?<!-))$/;

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'USER',
  },
  add: {
    headerText: 'ADD_USER',
  },
  edit: {
    headerText: 'EDIT_USER',
  },
  delete: {
    headerText: 'DELETE_USER',
    confirmationMessage: 'Are you sure you want to delete this user? The changes cannot be undone.',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage:
      'Are you sure you want to bulk delete these users? The changes cannot be undone.',
  },
  bulkActivate: {
    headerText: 'BULK_ACTIVATE',
    confirmationMessage: 'Are you sure you want to activate these user?',
  },
  bulkDeactivate: {
    headerText: 'BULK_DEACTIVATE',
    confirmationMessage: 'Are you sure you want to deactivate these user?',
  },
  actionConfirmation: {
    headerText: 'BULK_RESET',
    confirmationMessage: 'Are you sure you want to reset password for these user?',
  },
  importFile: {
    headerText: 'IMPORT_USERS',
  },
};

export const multifactorModalModeDetail = {
  '': {},
  delete: {
    headerText: 'DELETE_AUTHENTICATOR',
    confirmationMessage: 'Are you sure you want to delete this authenticator?',
  },
};

export const bulkActionOptions = [
  { label: 'ACTIVATE', value: 'ACTIVATE' },
  { label: 'DE_ACTIVATE', value: 'DE_ACTIVATE' },
  { label: 'DELETE', value: 'DELETE' },
];

export const searchOptions = [
  { label: 'NAME', value: 'NAME' },
  { label: 'GROUP', value: 'GROUP' },
];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const defaultSearchOption = {
  label: 'SELECT',
  value: 'SELECT',
};

export const getCustomAttributeApiPayload = ({ customAttrsInfo, attributeList }) => {
  Object.keys(customAttrsInfo).forEach((key) => {
    const { dataType, required } = find(attributeList, { attrName: key }) || {};

    if (dataType === 'DATE') {
      customAttrsInfo[key] = dayjs(customAttrsInfo[key]).format('MM/DD/YYYY');
    }

    if (dataType === 'BOOLEAN') {
      if (!required && isUndefined(customAttrsInfo[key])) {
        delete customAttrsInfo[key];
      }
    }
  });

  return customAttrsInfo;
};

export const getCustomAttributeValidationDetail = ({ customAttrsInfo, attributeList }) => {
  const validationDetail = { ...defaultValidationDetail };

  const requiredAttributes = attributeList.filter((list) => !list.systemDefined && list.required);

  for (let element of requiredAttributes) {
    // const isValueNumber = element.dataType === 'DECIMAL' || element.dataType === 'INTEGER';
    const isNotValid = isUndefined(customAttrsInfo[element.attrName]);

    // if (isValueNumber && isNotValid) {
    //   validationDetail.isValid = false;
    //   validationDetail.context = element.attrName;
    //   validationDetail.type = 'error';
    //   validationDetail.message = `${element.displayName} is the not valid ${element.dataType} value`;

    //   break;
    // }

    if (isNotValid) {
      validationDetail.isValid = false;
      validationDetail.context = element.attrName;
      validationDetail.type = 'error';
      validationDetail.message = `${element.displayName} is the required attribute`;

      break;
    }
  }

  return validationDetail;
};

export const getSecuritySettingsValidationDetail = ({
  formValues,
  allowAdminSetPasswords,
  selectedPasswordOption,
  passwordPolicyValidation,
}) => {
  const validationDetail = { ...defaultValidationDetail };
  const { password, confirmPassword, pwdConfig } = formValues || {};

  if (pwdConfig && allowAdminSetPasswords) {
    const { setByUser } = pwdConfig;

    if (!setByUser) {
      if (!password) {
        validationDetail.isValid = false;
        validationDetail.context = 'password';
        validationDetail.type = 'error';
        validationDetail.message = 'password is required';

        return validationDetail;
      }

      if (!passwordPolicyValidation.isValid) {
        validationDetail.isValid = false;
        validationDetail.context = 'password';
        validationDetail.type = 'error';
        validationDetail.message = "doesn't match with password policy";

        return validationDetail;
      }

      const isConfirmPasswordVisible = selectedPasswordOption.value === 'SET_BY_ADMIN';

      if (password && password !== confirmPassword && isConfirmPasswordVisible) {
        validationDetail.isValid = false;
        validationDetail.context = 'confirmPassword';
        validationDetail.type = 'error';
        validationDetail.message = "password and confirm password doesn't match";

        return validationDetail;
      }
    }
  }

  return validationDetail;
};

const getDomainName = ({ loginName }) => {
  const domainRegex = /@(.+)/gi;

  const loginNameDomain = domainRegex.exec(loginName)?.[1];

  return loginNameDomain;
};

export const getFormValidationDetail = ({
  formValues,
  attributeList,
  domainList,
  allowAdminSetPasswords,
  selectedPasswordOption,
  showSecuritySettings,
  passwordPolicyValidation,
  isArbitraryDomainConfigured,
  isSecurityVisible,
}) => {
  const validationDetail = { ...defaultValidationDetail };

  const {
    loginName,
    displayName,
    primaryEmail,
    secondaryEmail,
    customAttrsInfo,
    belongInternalDomain,
  } = formValues || {};

  if (!loginName) {
    validationDetail.isValid = false;
    validationDetail.context = 'loginName';
    validationDetail.type = 'error';
    validationDetail.message = 'Login ID is Required';

    return validationDetail;
  } else {
    const loginNameDomain = getDomainName({ loginName });

    if (
      !isArbitraryDomainConfigured &&
      domainList.indexOf(loginNameDomain) === -1 &&
      !belongInternalDomain
    ) {
      validationDetail.isValid = false;
      validationDetail.context = 'loginName';
      validationDetail.type = 'error';
      validationDetail.message = (
        <>
          <p>Allowed domains for Login ID - </p>
          <p>{domainList.join(',')}</p>
        </>
      );

      return validationDetail;
    } else if (!domainNameValidationRegex.test(loginNameDomain)) {
      validationDetail.isValid = false;
      validationDetail.context = 'loginName';
      validationDetail.type = 'error';
      validationDetail.message = 'Login ID should have a valid domain';

      return validationDetail;
    }

    if (validEmail(loginName)) {
      validationDetail.isValid = false;
      validationDetail.context = 'loginName';
      validationDetail.type = 'error';
      validationDetail.message = 'Login Name is not a valid';

      return validationDetail;
    }
  }

  if (!displayName) {
    validationDetail.isValid = false;
    validationDetail.context = 'displayName';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  if (!primaryEmail) {
    validationDetail.isValid = false;
    validationDetail.context = 'primaryEmail';
    validationDetail.type = 'error';
    validationDetail.message = 'Primary Email is Required';

    return validationDetail;
  }

  if (primaryEmail && validEmail(primaryEmail)) {
    validationDetail.isValid = false;
    validationDetail.context = 'primaryEmail';
    validationDetail.type = 'error';
    validationDetail.message = 'Primary Email is not valid';

    return validationDetail;
  }

  if (secondaryEmail) {
    if (validEmail(secondaryEmail)) {
      validationDetail.isValid = false;
      validationDetail.context = 'secondaryEmail';
      validationDetail.type = 'error';
      validationDetail.message = 'Secondary Email is not valid';

      return validationDetail;
    }

    if (primaryEmail === secondaryEmail) {
      validationDetail.isValid = false;
      validationDetail.context = 'secondaryEmail';
      validationDetail.type = 'error';
      validationDetail.message = `Primary and Secondary Email can't be same`;

      return validationDetail;
    }
  }

  if (customAttrsInfo) {
    const attributeValidationDetail = getCustomAttributeValidationDetail({
      customAttrsInfo,
      attributeList,
    });

    if (!attributeValidationDetail.isValid) {
      return attributeValidationDetail;
    }
  }

  if (isSecurityVisible) {
    const securityValidationDetail = getSecuritySettingsValidationDetail({
      formValues,
      allowAdminSetPasswords,
      selectedPasswordOption,
      showSecuritySettings,
      passwordPolicyValidation,
    });

    if (!securityValidationDetail.isValid) {
      return securityValidationDetail;
    }
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'loginName') {
    tooltipDetail.content = `Enter a login ID. The login ID consists for a username and domain name in email address format. The username must be unique, and its domain must belong to your organization.`;
  }

  if (name === 'firstName') {
    tooltipDetail.content = `Enter the first name of the user`;
  }

  if (name === 'lastName') {
    tooltipDetail.content = `Enter the last name of the user.`;
  }

  if (name === 'primaryEmail') {
    tooltipDetail.content = `Enter the primary email address of the user`;
  }

  if (name === 'department') {
    tooltipDetail.content = `Enter the department of the user`;
  }

  if (name === 'displayName') {
    tooltipDetail.content = (
      <p>
        The full name of the user is created based on what you enter in the{' '}
        <strong className="tooltip-bold">First Name</strong> and{' '}
        <strong className="tooltip-bold">Last Name</strong> fields.
      </p>
    );
  }

  if (name === 'sameAsUserId') {
    tooltipDetail.content = (
      <p>
        Enable this to autofill the <strong className="tooltip-bold">Primary Email</strong> field
        from the <strong className="tooltip-bold">Login ID</strong> field.
      </p>
    );
  }

  if (name === 'secondaryEmail') {
    tooltipDetail.content = `Enter the secondary email address of the user`;
  }

  if (name === 'status') {
    tooltipDetail.content = `Enable or disable the user`;
  }

  if (name === 'passwordOption') {
    tooltipDetail.content = `Choose from the options provided to configure the user's password`;
  }

  if (name === 'password') {
    tooltipDetail.content = `Enter a password for the user`;
  }

  if (name === 'canCopyPassword') {
    tooltipDetail.content = (
      <p>
        You can use the <strong className="tooltip-bold">Copy</strong> and{' '}
        <strong className="tooltip-bold">Eye</strong> icons to copy or view the auto-generated
        password
      </p>
    );
  }

  if (name === 'promptPasswordFirstLogin') {
    tooltipDetail.content = (
      <p>
        Check this option if you want to prompt the user to change their password after the initial
        log in. The user must follow the guidelines that you defined on the{' '}
        <a href="https://help.zscaler.com/zslogin/configuring-password-policy">Password Policy</a>{' '}
        page.
      </p>
    );
  }

  if (name === 'confirmPassword') {
    tooltipDetail.content = `Retype the user's password to confirm`;
  }

  if (name === 'assignGroups') {
    tooltipDetail.content = (
      <p>
        Select the group you want to add the user to. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/adding-user-groups">Adding Groups</a>.
      </p>
    );
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Select if you want to update your existing user information, delete existing users, or add
        new users. If you only want to add new users, Zscaler doesn&apos;t recommend selecting this
        option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-user-information-csv-file">
          Importing User Information from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};

export const securitySettingsInfo = ({
  loginName,
  idpDomains,
  tenantDomainList,
  isHostedIdpUser,
}) => {
  const idpDomainsNameList = idpDomains?.map?.(({ name }) => name);

  const loginNameDomain = getDomainName({ loginName });
  const arbitraryDomain = find(tenantDomainList, { arbitrary: true });
  const isArbitraryDomainConfigured = !isEmpty(arbitraryDomain);

  const isIdpConfigured = idpDomainsNameList?.indexOf(loginNameDomain) !== -1;

  if (isIdpConfigured && !isHostedIdpUser) {
    return {
      isArbitraryDomainConfigured,
      isSecurityVisible: false,
    };
  }

  const { guest, name } = find(tenantDomainList, { name: loginNameDomain }) || {};

  if (guest || (!name && isArbitraryDomainConfigured)) {
    return {
      isArbitraryDomainConfigured,
      isSecurityVisible: false,
      isEmailType: true,
    };
  }

  return {
    isArbitraryDomainConfigured,
    isSecurityVisible: true,
  };
};

export const getCustomFormValidationDetail = ({
  formValues,
  attributeList,
  allowAdminSetPasswords,
  selectedPasswordOption,
  showSecuritySettings,
  passwordPolicyValidation,
  isSecurityVisible,
}) => {
  const validationDetail = { ...defaultValidationDetail };

  const { customAttrsInfo } = formValues || {};

  if (isSecurityVisible) {
    const securityValidationDetail = getSecuritySettingsValidationDetail({
      formValues,
      allowAdminSetPasswords,
      selectedPasswordOption,
      showSecuritySettings,
      passwordPolicyValidation,
    });

    if (!securityValidationDetail.isValid) {
      return securityValidationDetail;
    }
  }

  if (customAttrsInfo) {
    const attributeValidationDetail = getCustomAttributeValidationDetail({
      customAttrsInfo,
      attributeList,
    });

    if (!attributeValidationDetail.isValid) {
      return attributeValidationDetail;
    }
  }

  return validationDetail;
};
