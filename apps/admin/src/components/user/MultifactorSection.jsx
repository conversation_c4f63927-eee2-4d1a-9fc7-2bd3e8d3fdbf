import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Actions,
  CRUDModal,
  Card,
  Label,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { multifactorModalModeDetail } from '../../components/user/helper';

import { deleteUserAuthenticator, getUserAuthenticator } from '../../ducks/user';
import { selectMfaTableConfig, selectMfaTableDetail } from '../../ducks/user/selectors';

const defaultProps = {
  userDetail: {},
  isEditMode: false,
};

const MultifactorSection = ({ userDetail, isEditMode }) => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall();

  const tableConfig = useSelector(selectMfaTableConfig);
  const tableDetail = useSelector(selectMfaTableDetail);

  const [modalMode, setModalMode] = useState('');
  const [deletingAuth, setDeletingAuth] = useState('');

  useEffect(() => {
    const { id } = userDetail;
    apiCall(getUserAuthenticator(id)).catch(noop);
  }, []);

  const onDeleteClick = (detail) => {
    if (detail) {
      setDeletingAuth(detail);
      setModalMode('delete');
    }
  };

  const onSaveClick = () => {
    if (modalMode === 'delete') {
      const { id: userId } = userDetail;
      const { id } = deletingAuth;
      apiCall(deleteUserAuthenticator(userId, id)).then(onCloseClick).catch(noop);
    }
  };

  const onCloseClick = () => {
    setModalMode('');
    setDeletingAuth('');
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { authenticatorType } = props?.row?.original || {};

          const showDelete = authenticatorType !== 'EMAIL';

          return (
            <Actions
              {...props}
              onDeleteClick={onDeleteClick}
              showEdit={false}
              showDelete={showDelete && isEditMode}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, isEditMode]);

  return (
    <>
      <section className=" typography-paragraph1-uppercase">{t('AUTHENTICATORS')}</section>
      <Card containerStyle={{ paddingTop: '20px', display: 'block' }}>
        <Label>{t('MFA_AUTHENTICATOR')}</Label>
        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          data={tableDetail.data || []}
          pagination={{ ...tableDetail }}
        />
      </Card>

      {modalMode && (
        <CRUDModal
          mode={modalMode}
          onSaveClick={onSaveClick}
          onCloseClick={onCloseClick}
          {...multifactorModalModeDetail[modalMode]}
        />
      )}
    </>
  );
};

MultifactorSection.defaultProps = defaultProps;

MultifactorSection.propTypes = {
  userDetail: PropTypes.object,
  isEditMode: PropTypes.bool,
};

export default MultifactorSection;
