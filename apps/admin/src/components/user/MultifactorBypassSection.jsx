import { useCallback, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Card,
  Field,
  FieldGroup,
  InlineDatePicker,
  ToggleButton,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import PropTypes from 'prop-types';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import { PERMISSIONS_KEY } from '../../config';
import { UserFormContext } from './UserForm';

const MultifactorBypassSection = () => {
  const { t } = useTranslation();
  const { setFormValues, formValues } = useContext(UserFormContext);
  const userCredentialsPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_CREDENTIALS_POLICY),
  );

  const initialSkipMfaUntil = formValues.skipMfaUntil > dayjs().unix();
  const [showDatePicker, setShowDatePicker] = useState(initialSkipMfaUntil);
  const [skipUntilTime, setSkipUntilTime] = useState(
    initialSkipMfaUntil
      ? dayjs.unix(formValues.skipMfaUntil).toDate()
      : dayjs().add(8, 'hours').toDate(),
  );

  const updateFormValues = useCallback(
    (skipMfaUntil) => {
      setFormValues((prevState) => ({
        ...prevState,
        skipMfaUntil,
      }));
    },
    [setFormValues],
  );

  useEffect(() => {
    if (showDatePicker) {
      const newSkipUntilTime = formValues.skipMfaUntil
        ? dayjs.unix(formValues.skipMfaUntil).toDate()
        : dayjs().add(8, 'hours').toDate();
      setSkipUntilTime(newSkipUntilTime);
      updateFormValues(dayjs(newSkipUntilTime).unix());
    } else {
      setSkipUntilTime(null);
      updateFormValues(0);
    }
  }, [showDatePicker, formValues.skipMfaUntil, updateFormValues]);

  const onValueChange = (evt) => {
    const newValue = evt?.target?.value;
    const newSkipUntilTime = newValue ? dayjs(newValue).toDate() : dayjs().add(8, 'hours').toDate();
    setSkipUntilTime(newSkipUntilTime);
    updateFormValues(dayjs(newSkipUntilTime).unix());
  };

  const onToggleStatusClick = () => {
    setShowDatePicker((prev) => !prev);
  };

  return (
    <>
      <section className="typography-paragraph1-uppercase">{t('MULTIFACTOR_BYPASS')}</section>
      <Card>
        <FieldGroup>
          <Field label="SKIP_SECOND_FACTOR_AUTHENTICATION">
            <ToggleButton
              type="success"
              isOn={showDatePicker}
              onToggleClick={onToggleStatusClick}
              showLabel={false}
              disabled={!userCredentialsPrivileges?.hasFullAccess}
            />
          </Field>

          {showDatePicker && (
            <InlineDatePicker
              label="SKIP_UNTIL"
              name="skipMfaUntil"
              selectedDate={skipUntilTime}
              onChange={onValueChange}
              elementProps={{
                containerStyle: { height: '32px' },
                disabled: !userCredentialsPrivileges?.hasFullAccess,
              }}
              format="MMM DD YYYY, hh:mm A"
              minDate={dayjs().add(1, 'minute').toDate()}
              maxDate={dayjs().add(5, 'days').toDate()}
              hideOnSelection={false}
              showTimePicker
              showHourMinuteOption={false}
              showHourOption
              showMinuteOption
            />
          )}
        </FieldGroup>
      </Card>
    </>
  );
};

MultifactorBypassSection.defaultProps = {
  userDetail: {},
  isEditMode: false,
};

MultifactorBypassSection.propTypes = {
  userDetail: PropTypes.object,
  isEditMode: PropTypes.bool,
};

export default MultifactorBypassSection;
