import { useContext, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  Items,
  MultiSelection,
  ToggleButton,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';

import { showErrorNotification } from '../../ducks/global';
import { getList as getGroupList } from '../../ducks/groups';
import { selectGroupsList, selectTableDetail } from '../../ducks/groups/selectors';

import DepartmentSection from './DepartmentSection';
import { UserFormContext } from './UserForm';
import { getFormTooltipDetail } from './helper';

const getSelectedGroupsDetail = (list) => {
  return list?.map(({ name, id, ...rest }) => {
    return { label: name, value: id, ...rest };
  });
};

const GeneralSection = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const {
    onFormFieldChange,
    formValues,
    setFormValues,
    validationDetail,
    isPrimaryEmailSameAsUserId,
    setIsPrimaryEmailSameAsUserId,
    isFormReadOnly,
    detail,
  } = useContext(UserFormContext);

  const groupListTableDetail = useSelector(selectTableDetail);
  const groupsList = useSelector(selectGroupsList);

  const prevGroupsAssignment = useRef(detail.groups?.map?.(({ id }) => id));

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: groupListTableDetail,
    apiCallFunc: getGroupList,
  });

  const [selectedGroupOptions, setSelectedGroupOptions] = useState(
    getSelectedGroupsDetail([...(detail.groups || [])]),
  );

  const onSamePrimaryAndUserIdClick = () => {
    if (!isPrimaryEmailSameAsUserId) {
      setFormValues((prevState) => ({ ...prevState, primaryEmail: prevState.loginName }));
    }

    setIsPrimaryEmailSameAsUserId((prevState) => !prevState);
  };

  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      status: !formValues?.status,
    }));
  };

  const onGroupSelection = (detail) => {
    const selectedGroupIds = detail?.map(({ value }) => value);

    let newSelectedGroupIds = [];
    let newGroupsAssignmentCount = 0;

    selectedGroupIds?.forEach?.((id) => {
      const prevGroupsAssignmentIndex = prevGroupsAssignment.current?.indexOf?.(id);

      if (prevGroupsAssignmentIndex === -1) {
        newGroupsAssignmentCount++;
      }

      newSelectedGroupIds.push({ id });
    });

    if (newGroupsAssignmentCount > 50) {
      dispatch(
        showErrorNotification({
          message: 'USER_GROUPS_ASSIGNMENT_LIMIT',
          translationMapping: { value: 50 },
        }),
      );

      setSelectedGroupOptions((prevState) => cloneDeep(prevState));
    } else {
      setFormValues((prevState) => ({
        ...prevState,
        groups: newSelectedGroupIds,
      }));

      setSelectedGroupOptions(detail);
    }
  };

  const renderUserInformationSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('USER_INFORMATION')}</section>

        <Card>
          <FieldGroup>
            <Input
              label="LOGIN_ID"
              name="loginName"
              onChange={onFormFieldChange}
              value={formValues.loginName}
              maxLength="254"
              info={validationDetail}
              tooltip={
                isFormReadOnly || formValues.belongInternalDomain
                  ? {}
                  : getFormTooltipDetail('loginName')
              }
              disabled={isFormReadOnly || formValues.belongInternalDomain}
            />
            <Input
              label="NAME"
              name="displayName"
              onChange={onFormFieldChange}
              value={formValues.displayName}
              maxLength="128"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('displayName')}
              tabIndex="-1"
              disabled={isFormReadOnly}
            />
          </FieldGroup>

          <FieldGroup>
            <Input
              label="FIRST_NAME"
              name="firstName"
              onChange={onFormFieldChange}
              value={formValues.firstName}
              maxLength="128"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('firstName')}
              disabled={isFormReadOnly}
            />
            <Input
              label="LAST_NAME"
              name="lastName"
              onChange={onFormFieldChange}
              value={formValues.lastName}
              maxLength="128"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('lastName')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>

          <FieldGroup>
            <Input
              label="PRIMARY_EMAIL"
              name="primaryEmail"
              onChange={onFormFieldChange}
              value={formValues.primaryEmail}
              maxLength="254"
              disabled={isPrimaryEmailSameAsUserId || isFormReadOnly}
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('primaryEmail')}
            />
            <Field
              label="SAME_AS_LOGIN_ID"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('sameAsUserId')}
            >
              <ToggleButton
                isOn={isPrimaryEmailSameAsUserId}
                onToggleClick={onSamePrimaryAndUserIdClick}
                showLabel={false}
                disabled={isFormReadOnly}
              />
            </Field>
          </FieldGroup>

          <FieldGroup>
            <Input
              label="SECONDARY_EMAIL"
              name="secondaryEmail"
              onChange={onFormFieldChange}
              value={formValues.secondaryEmail}
              maxLength="254"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('secondaryEmail')}
              disabled={isFormReadOnly}
            />

            <DepartmentSection />
          </FieldGroup>

          <FieldGroup>
            <Field label="STATUS" tooltip={isFormReadOnly ? {} : getFormTooltipDetail('status')}>
              <ToggleButton
                type="success"
                isOn={formValues?.status}
                onToggleClick={onToggleStatusClick}
                onLabel="ACTIVE"
                offLabel="INACTIVE"
                showLabel={false}
                disabled={isFormReadOnly}
              />
            </Field>
          </FieldGroup>
        </Card>
      </>
    );
  };

  const renderGroupAssignmentSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('ASSIGNMENT')}</section>
        <Card>
          <div className="is-flex full-width user-form-assignment-section">
            <Field
              label="ASSIGN_GROUPS"
              containerClass="full-width"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('assignGroups')}
            >
              <DropDown
                list={groupsList}
                selectedList={selectedGroupOptions}
                onSelection={onGroupSelection}
                onOpen={onDropDownOpen}
                disabled={isFormReadOnly}
                renderItem={(props) => {
                  // eslint-disable-next-line react/prop-types
                  const { dynamicGroup } = props.itemDetail || {};

                  if (dynamicGroup) {
                    return (
                      <Items
                        {...props}
                        checkboxClass="text-gray"
                        textIconClass="text-gray-darker"
                        hasRightIcon={false}
                        onClick={noop}
                      />
                    );
                  } else {
                    return <Items {...props} />;
                  }
                }}
                renderItemsSelection={(props) => (
                  <MultiSelection
                    unselectedTitle="Unselected User Groups"
                    selectedTitle="Selected User Groups"
                    {...props}
                  />
                )}
                isMulti
                hasSearch
                containerClass="full-width"
                loadMoreDetail={{ ...groupListTableDetail, onLoadMoreClick }}
                loading={isDropDownLoading}
              />
            </Field>
          </div>
        </Card>
      </>
    );
  };

  return (
    <>
      {renderUserInformationSection()}
      {renderGroupAssignmentSection()}
    </>
  );
};

export default GeneralSection;
