import { useTranslation } from 'react-i18next';

import { faChevronDown, faChevronUp, faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Card, Radio, TextWithTooltip, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { syncRoles } from '../../ducks/api-resources';

const ResourcesForm = ({
  resourcesList,
  showScopes,
  setShowScopes,
  radioButtonStatus,
  setRadioButtonStatus,
  isFormReadOnly,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const onRadioButtonsClick = (item, resourceId, serviceId) => {
    setRadioButtonStatus((prevState) => {
      const newState = { ...prevState };
      if (!item) {
        newState[resourceId][serviceId] = [];
        return newState;
      }
      if (item?.zpaScopeId) {
        newState[resourceId][serviceId] = [`${item?.zpaScopeId}${item?.id}`];
      } else {
        newState[resourceId][serviceId] = [item?.id];
      }
      return newState;
    });
  };

  const onSyncRolesClick = () => {
    apiCall(syncRoles()).catch(noop);
  };

  const isChecked = (item, resourceId, scope) => {
    const serviceId = scope?.service?.id;

    const radioButtonStatusForResource = radioButtonStatus[resourceId];

    if (!radioButtonStatusForResource || !radioButtonStatusForResource[serviceId]) return false;

    const itemId = item?.id;
    const zpaScopeId = item?.zpaScopeId;

    const fullItemId = zpaScopeId ? `${zpaScopeId}${itemId}` : itemId;

    return radioButtonStatusForResource[serviceId].includes(fullItemId);
  };

  return (
    <>
      {resourcesList?.map((resource, index) => {
        return (
          <div key={index}>
            <section className="is-flex has-jc-sb">
              <span className="typography-paragraph1-uppercase">{resource?.name}</span>
              <Button
                type="tertiary"
                onClick={onSyncRolesClick}
                containerClass="no-p-l no-p-r has-as-e"
              >
                <FontAwesomeIcon icon={faSync} className="icon left" />
                <span>{t('SYNC')}</span>
              </Button>
            </section>
            <Card containerStyle={{ paddingTop: '20px' }}>
              {resource?.serviceScopes?.map((scope, index) => {
                return (
                  <>
                    <section
                      key={index}
                      className="typography-paragraph1-uppercase collapsable"
                      onKeyDown={noop}
                      onClick={() => {
                        const newShowScopes = {
                          ...showScopes,
                          [resource?.id]: {
                            ...showScopes?.[resource?.id],
                            [scope?.service?.name]:
                              !showScopes?.[resource?.id]?.[scope?.service?.name],
                          },
                        };
                        setShowScopes(newShowScopes);
                      }}
                    >
                      <FontAwesomeIcon
                        className="icon left"
                        icon={
                          showScopes?.[resource?.id]?.[scope?.service?.name]
                            ? faChevronDown
                            : faChevronUp
                        }
                      />
                      <span>
                        {scope?.service?.name} {scope?.service?.orgName}
                      </span>
                    </section>
                    {showScopes?.[resource?.id]?.[scope?.service?.name] &&
                      scope?.scopes?.map((item, index) => {
                        return (
                          <div key={index}>
                            {isFormReadOnly ? (
                              <TextWithTooltip
                                containerStyle={{ maxWidth: '532px', padding: '4px' }}
                                text={item?.name}
                              >
                                {item?.name}
                              </TextWithTooltip>
                            ) : (
                              <Radio
                                id={item?.name}
                                name={'serviceScope'}
                                isLarge={false}
                                checked={isChecked(item, resource?.id, scope)}
                                onChange={() => {
                                  onRadioButtonsClick(item, resource?.id, scope?.service?.id);
                                }}
                              >
                                <TextWithTooltip
                                  containerStyle={{ maxWidth: '532px', padding: '4px' }}
                                  text={item?.name}
                                >
                                  {item?.name}
                                </TextWithTooltip>
                              </Radio>
                            )}
                          </div>
                        );
                      })}
                    {showScopes[resource?.id]?.[scope?.service?.name] &&
                      !isFormReadOnly &&
                      scope?.scopes?.length > 0 && (
                        <div key={scope?.service?.name + 'none'}>
                          <Radio
                            id={scope?.service?.name + 'none'}
                            name={'serviceScope'}
                            isLarge={false}
                            checked={
                              radioButtonStatus[resource?.id]?.[scope?.service?.id]?.length === 0
                            }
                            onChange={() => {
                              onRadioButtonsClick(null, resource?.id, scope?.service?.id);
                            }}
                          >
                            <TextWithTooltip text={t('NONE')}>{t('NONE')}</TextWithTooltip>
                          </Radio>
                        </div>
                      )}
                  </>
                );
              })}
            </Card>
          </div>
        );
      })}
    </>
  );
};

ResourcesForm.propTypes = {
  resourcesList: PropTypes.array,
  showScopes: PropTypes.object,
  setShowScopes: PropTypes.func,
  radioButtonStatus: PropTypes.object,
  setRadioButtonStatus: PropTypes.func,
  isFormReadOnly: PropTypes.bool,
};

export default ResourcesForm;
