import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  MultiSelection,
  TextArea,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { filter } from 'lodash-es';
import PropTypes from 'prop-types';

import { getList } from '../../ducks/ip-locations';
import { selectIpLocationsList, selectTableDetail } from '../../ducks/ip-locations/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const defaultProps = {
  ...defaultFormProps,
};

const IpLocationGroupsForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();

  const { detail, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const locationsListTableDetail = useSelector(selectTableDetail);
  const ipLocationsList = useSelector(selectIpLocationsList);

  const [selectedOption, setSelectedOption] = useState(() => {
    const { locations } = detail || {};

    if (locations) {
      return locations.map(({ id, name }) => ({ label: name, value: id }));
    }

    return [];
  });

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: locationsListTableDetail,
    apiCallFunc: getList,
  });

  const [formValues, setFormValues] = useState({
    name: '',
    locations: [],
    ...detail,
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    setDetail(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onSelection = (detail) => {
    const selectedLocationIds = detail?.map(({ value }) => value);

    setFormValues((prevState) => ({
      ...prevState,
      locations: filter(
        locationsListTableDetail.data,
        (o) => selectedLocationIds.indexOf(o.id) !== -1,
      ).map(({ id, name }) => ({ id, name })),
    }));

    setSelectedOption(detail);
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('INFORMATION')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              maxLength="128"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('name')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>
          <FieldGroup>
            <Field
              label="LOCATIONS"
              containerClass="full-width"
              htmlFor="locations"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('locations')}
            >
              <DropDown
                list={ipLocationsList}
                selectedList={selectedOption}
                onOpen={onDropDownOpen}
                onSelection={onSelection}
                renderItemsSelection={(props) => (
                  <MultiSelection
                    unselectedTitle="UNSELECTED_LOCATIONS"
                    selectedTitle="SELECTED_LOCATIONS"
                    {...props}
                  />
                )}
                isMulti
                hasSearch
                containerClass="full-width"
                loadMoreDetail={{ ...locationsListTableDetail, onLoadMoreClick }}
                loading={isDropDownLoading}
                containerStyle={{ maxWidth: '250px' }}
                disabled={isFormReadOnly}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <TextArea
              name="description"
              value={formValues.description}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              maxLength="512"
              rows="3"
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('description')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

IpLocationGroupsForm.defaultProps = defaultProps;

IpLocationGroupsForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.object };

export default IpLocationGroupsForm;
