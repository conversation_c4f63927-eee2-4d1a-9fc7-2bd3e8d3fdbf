import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faCheckCircle, faChevronDown, faChevronUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Card, Field, Label, Toast } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  selectIsPasswordPolicyWeak,
  selectRecommendedPasswordPolicyDetail,
} from '../../ducks/password-policy/selectors';

const defaultProps = {
  formValues: {},
  isRecommended: true,
  updateConfigurationType: noop,
  hasFullAccess: true,
};

const PasswordType = ({ formValues, isRecommended, updateConfigurationType, hasFullAccess }) => {
  const { t } = useTranslation();

  const [showConfigDetail, setConfigDetail] = useState(true);

  const recommendedPasswordPolicy = useSelector(selectRecommendedPasswordPolicyDetail);
  const isPasswordWeak = selectIsPasswordPolicyWeak({ formValues, recommendedPasswordPolicy });

  return (
    <>
      <section
        className="typography-paragraph1-uppercase collapsable"
        onKeyDown={noop}
        onClick={() => {
          setConfigDetail((prevState) => !prevState);
        }}
      >
        <FontAwesomeIcon
          className="icon left"
          icon={showConfigDetail ? faChevronDown : faChevronUp}
        />

        <span>{t('CONFIGURATION')}</span>
      </section>

      {showConfigDetail && (
        <Card containerClass="is-flex configuration-type">
          <Field label="CONFIGURATION_TYPE" containerStyle={{ width: 'auto' }}>
            <div className="is-flex config-buttons">
              <Label
                tooltip={
                  !hasFullAccess
                    ? {}
                    : {
                        content:
                          'Select this to enable Zscaler-default password policy configuration',
                      }
                }
              >
                <Button
                  type={isRecommended ? 'primary' : ''}
                  onClick={() => {
                    updateConfigurationType('RECOMMENDED');
                  }}
                  disabled={!hasFullAccess}
                >
                  {isRecommended && <FontAwesomeIcon className="icon left" icon={faCheckCircle} />}

                  <span>{t('DEFAULT')}</span>
                </Button>
              </Label>
              <Label
                tooltip={
                  !hasFullAccess
                    ? {}
                    : {
                        content: 'Select this to configure password policy for ZIdentity users',
                      }
                }
              >
                <Button
                  type={!isRecommended ? 'primary' : ''}
                  onClick={() => {
                    updateConfigurationType('CUSTOM');
                  }}
                  disabled={!hasFullAccess}
                >
                  {!isRecommended && <FontAwesomeIcon className="icon left" icon={faCheckCircle} />}

                  <span>{t('CUSTOM')}</span>
                </Button>
              </Label>
            </div>
          </Field>

          {isPasswordWeak && (
            <Toast type="warning" autoHide={false}>
              <span>
                {`The custom password policy is weaker than Zscaler's default password policy. We strongly advise to review the password policy with your Organisation's Security team.`}
              </span>
            </Toast>
          )}
        </Card>
      )}
    </>
  );
};

PasswordType.defaultProps = defaultProps;

PasswordType.propTypes = {
  formValues: PropTypes.object,
  isRecommended: PropTypes.bool,
  updateConfigurationType: PropTypes.func,
  hasFullAccess: PropTypes.bool,
};

export default PasswordType;
