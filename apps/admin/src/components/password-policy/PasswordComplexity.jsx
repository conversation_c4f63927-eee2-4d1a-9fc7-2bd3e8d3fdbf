import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faChevronDown, faChevronUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Card, DropDown, Field, FieldGroup } from '@zscaler/zui-component-library';

import { noop, range } from 'lodash-es';
import PropTypes from 'prop-types';

import { listGenerator } from '../../utils/generic';

const passwordLengthList = range(1, 21).map(listGenerator);
const passwordCaseList = range(0, 6).map(listGenerator);

const defaultProps = {
  selectedListsDetail: [],
  onSelection: noop,
  isRecommended: true,
  hasFullAccess: false,
};

const PasswordComplexity = ({ selectedListsDetail, onSelection, isRecommended, hasFullAccess }) => {
  const { t } = useTranslation();

  const [showComplexityDetail, setShowComplexityDetail] = useState(true);

  return (
    <>
      <section
        className="typography-paragraph1-uppercase collapsable"
        onKeyDown={noop}
        onClick={() => {
          setShowComplexityDetail((prevState) => !prevState);
        }}
      >
        <FontAwesomeIcon
          className="icon left"
          icon={showComplexityDetail ? faChevronDown : faChevronUp}
        />

        <span>{t('PASSWORD_COMPLEXITY')}</span>
      </section>

      {showComplexityDetail && (
        <Card>
          <FieldGroup containerClass="has-width-auto">
            <Field
              label="PASSWORD_LENGTH"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content: 'Set the minimum length of the password',
                    }
              }
            >
              <DropDown
                list={passwordLengthList}
                selectedList={selectedListsDetail.minLength}
                onSelection={(detail) => {
                  onSelection('minLength', detail);
                }}
                readOnly={isRecommended || !hasFullAccess}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field
              label="MIN_LWR_CASE"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Set the minimum number of lowercase letters required in the password',
                    }
              }
            >
              <DropDown
                list={passwordCaseList}
                selectedList={selectedListsDetail.minLowerCase}
                onSelection={(detail) => {
                  onSelection('minLowerCase', detail);
                }}
                readOnly={isRecommended || !hasFullAccess}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
            <Field
              label="MIN_UPPR_CASE"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Set the minimum number of uppercase letters required in the password',
                    }
              }
            >
              <DropDown
                list={passwordCaseList}
                selectedList={selectedListsDetail.minUpperCase}
                onSelection={(detail) => {
                  onSelection('minUpperCase', detail);
                }}
                readOnly={isRecommended || !hasFullAccess}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
            <Field
              label="MIN_NUMERIC"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Set the minimum number of numeric characters required in the password',
                    }
              }
            >
              <DropDown
                list={passwordCaseList}
                selectedList={selectedListsDetail.minNumeric}
                onSelection={(detail) => {
                  onSelection('minNumeric', detail);
                }}
                readOnly={isRecommended || !hasFullAccess}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
            <Field
              label="MIN_SPL_CHAR"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Set the minimum number of special characters required in the password',
                    }
              }
            >
              <DropDown
                list={passwordCaseList}
                selectedList={selectedListsDetail.minSpecialChar}
                onSelection={(detail) => {
                  onSelection('minSpecialChar', detail);
                }}
                readOnly={isRecommended || !hasFullAccess}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
        </Card>
      )}
    </>
  );
};

PasswordComplexity.defaultProps = defaultProps;

PasswordComplexity.propTypes = {
  selectedListsDetail: PropTypes.array,
  onSelection: PropTypes.func,
  isRecommended: PropTypes.bool,
  hasFullAccess: PropTypes.bool,
};

export default PasswordComplexity;
