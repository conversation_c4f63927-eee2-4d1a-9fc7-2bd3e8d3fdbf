import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { expiryAge } = formValues || {};

  if (expiryAge < 15) {
    validationDetail.isValid = false;
    validationDetail.context = 'expiryAge';
    validationDetail.type = 'error';
    validationDetail.message = 'Password expiration period should be minimum 15 days';

    return validationDetail;
  }

  if (expiryAge > 365) {
    validationDetail.isValid = false;
    validationDetail.context = 'expiryAge';
    validationDetail.type = 'error';
    validationDetail.message = 'Password expiration period should be less than 365 days';

    return validationDetail;
  }

  return validationDetail;
};
