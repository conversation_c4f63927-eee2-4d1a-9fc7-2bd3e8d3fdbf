import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { defaultFormPropTypes, defaultFormProps } from '@zscaler/zui-component-library';

import PropTypes from 'prop-types';

import {
  selectApiResourceDetailByID,
  selectTableDetail,
} from '../../ducks/api-resources/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import ResourcesForm from '../api-clients-resources/ResourcesForm';
import { getScopeAndRadioButtonStatus } from '../api-clients/helper';

const defaultProps = {
  ...defaultFormProps,
};

const ApiResourcesForm = () => {
  const { detail } = useContext(CRUDPageContext);

  const [showScopes, setShowScopes] = useState({});

  const resourcesList = selectApiResourceDetailByID(useSelector(selectTableDetail), detail?.id);

  useEffect(() => {
    const { showScopes } = getScopeAndRadioButtonStatus({ resourcesList });

    setShowScopes(showScopes);
  }, []);

  const renderBodySection = () => {
    return (
      <ResourcesForm
        resourcesList={resourcesList}
        showScopes={showScopes}
        setShowScopes={setShowScopes}
        isFormReadOnly={true}
      />
    );
  };

  return <>{renderBodySection()}</>;
};

ApiResourcesForm.defaultProps = defaultProps;

ApiResourcesForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.bool };

export default ApiResourcesForm;
