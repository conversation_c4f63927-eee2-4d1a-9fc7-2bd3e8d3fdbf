import { useContext } from 'react';

import { CRUDModal } from '@zscaler/zui-component-library';

import ApiResourcesForm from '../../components/api-resources/ApiResourcesForm';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiResourcesCRUD = () => {
  const { modalMode, setModalMode, setDetail, defaultModalMode, defaultDetail } =
    useContext(CRUDPageContext);

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
  };

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) => <ApiResourcesForm {...props} />}
      showSave={false}
      cancelText={'CLOSE'}
      onCloseClick={onCloseClick}
      headerText={'API_RESOURCE'}
    />
  );
};

export default ApiResourcesCRUD;
