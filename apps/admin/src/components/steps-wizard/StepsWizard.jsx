import { useTranslation } from 'react-i18next';

import { faCircle, faCircleDot } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  alignment: 'vertical',
  list: [],
  selectedItem: null,
  onSelection: noop,
  stateConfig: {
    disabled: { icon: faCircle, color: '#8590A6' },
    active: { icon: faCircleDot, color: '#2160E1' },
    unselected: { icon: faCircle, color: '#8590A6' },
    success: { icon: faCircle, color: 'green' },
    warning: { icon: faCircle, color: 'orange' },
    error: { icon: faCircle, color: 'red' },
  },
  containerClass: '',
  containerStyle: {},
  children: null,
};

const StepsWizard = ({
  alignment = DEFAULT_PROPS.alignment,
  list = DEFAULT_PROPS.list,
  selectedItem = DEFAULT_PROPS.selectedItem,
  onSelection = DEFAULT_PROPS.onSelection,
  stateConfig = DEFAULT_PROPS.stateConfig,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.container,
  children,
}) => {
  const { t } = useTranslation();

  return (
    <div className={`steps-wizard-container ${alignment} ${containerClass}`} style={containerStyle}>
      <div className="steps-list-container">
        {list.map((item, index) => {
          const isSelected = selectedItem?.value === item.value;

          return (
            <div
              key={`${item.value}-${index}`}
              className={`step ${isSelected ? 'selected' : ''}`}
              onClick={() => {
                onSelection(item);
              }}
            >
              <FontAwesomeIcon
                icon={isSelected ? stateConfig.active.icon : stateConfig.unselected.icon}
                className={`icon left ${isSelected ? 'selected' : ''}`}
                style={{
                  color: isSelected ? stateConfig.active.color : stateConfig.unselected.color,
                }}
              />

              {t(item.label)}
            </div>
          );
        })}
      </div>

      <div className="content">{children}</div>
    </div>
  );
};

StepsWizard.propTypes = {
  alignment: PropTypes.string,
  list: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
  selectedItem: PropTypes.shape({
    value: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
  }),
  onSelection: PropTypes.func,
  stateConfig: PropTypes.shape({
    disabled: PropTypes.shape({
      icon: PropTypes.object.isRequired,
      color: PropTypes.string.isRequired,
    }),
    active: PropTypes.shape({
      icon: PropTypes.object.isRequired,
      color: PropTypes.string.isRequired,
    }),
    unselected: PropTypes.shape({
      icon: PropTypes.object.isRequired,
      color: PropTypes.string.isRequired,
    }),
    success: PropTypes.shape({
      icon: PropTypes.object.isRequired,
      color: PropTypes.string.isRequired,
    }),
    warning: PropTypes.shape({
      icon: PropTypes.object.isRequired,
      color: PropTypes.string.isRequired,
    }),
    error: PropTypes.shape({
      icon: PropTypes.object.isRequired,
      color: PropTypes.string.isRequired,
    }),
  }),
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node,
};

export default StepsWizard;
