import { useTranslation } from 'react-i18next';

import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Modal, <PERSON>dalBody, Mo<PERSON>Footer, ModalHeader } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import StepsWizard from './StepsWizard';

const DEFAULT_PROPS = {
  headerText: '',
  onClose: noop,
  list: [],
  selectedItem: '',
  onSelection: noop,
  cancelText: 'CANCEL',
  showCancelButton: true,
  backText: 'BACK',
  showBackButton: true,
  isBackButtonDisabled: false,
  onBackClick: noop,
  nextText: 'NEXT',
  showNextButton: true,
  isNextButtonDisabled: false,
  onNextClick: noop,
};

const StepsWizardModal = ({
  headerText = DEFAULT_PROPS.headerText,
  onClose = DEFAULT_PROPS.onClose,
  list = DEFAULT_PROPS.list,
  selectedItem = DEFAULT_PROPS.selectedItem,
  onSelection = DEFAULT_PROPS.onSelection,
  cancelText = DEFAULT_PROPS.cancelText,
  showCancelButton = DEFAULT_PROPS.showCancelButton,
  backText = DEFAULT_PROPS.backText,
  showBackButton = DEFAULT_PROPS.showBackButton,
  isBackButtonDisabled = DEFAULT_PROPS.isBackButtonDisabled,
  onBackClick = DEFAULT_PROPS.onBackClick,
  nextText = DEFAULT_PROPS.nextText,
  showNextButton = DEFAULT_PROPS.showNextButton,
  isNextButtonDisabled = DEFAULT_PROPS.isNextButtonDisabled,
  onNextClick = noop,
  children,
  ...modalProps
}) => {
  const { t } = useTranslation();

  return (
    <Modal {...modalProps}>
      <ModalHeader>
        <div className="is-flex" style={{ gap: '12px' }}>
          <Button type="tertiary" containerClass="no-p content-width" onClick={onClose}>
            <FontAwesomeIcon icon={faArrowLeft} />
          </Button>

          <div className="section">{t(headerText)}</div>
        </div>
      </ModalHeader>
      <ModalBody>
        <StepsWizard list={list} selectedItem={selectedItem} onSelection={onSelection}>
          {children}
        </StepsWizard>
      </ModalBody>
      <ModalFooter containerClass="has-jc-sb">
        {showCancelButton && (
          <Button type="tertiary" onClick={onClose}>
            {t(cancelText)}
          </Button>
        )}
        <div className="is-flex has-jc-sb" style={{ gap: '8px' }}>
          {showBackButton && (
            <Button type="secondary" onClick={onBackClick} disabled={isBackButtonDisabled}>
              {t(backText)}
            </Button>
          )}
          {showNextButton && (
            <Button type="primary" onClick={onNextClick} disabled={isNextButtonDisabled}>
              {t(nextText)}
            </Button>
          )}
        </div>
      </ModalFooter>
    </Modal>
  );
};

StepsWizardModal.propTypes = {
  headerText: PropTypes.string,
  onClose: PropTypes.func,
  list: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
  selectedItem: PropTypes.shape({
    value: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
  }),
  onSelection: PropTypes.func,
  cancelText: PropTypes.string,
  showCancelButton: PropTypes.bool,
  backText: PropTypes.string,
  showBackButton: PropTypes.bool,
  isBackButtonDisabled: PropTypes.bool,
  onBackClick: PropTypes.func,
  showNextButton: PropTypes.bool,
  nextText: PropTypes.string,
  isNextButtonDisabled: PropTypes.bool,
  onNextClick: PropTypes.func,
  children: PropTypes.node,
};

export default StepsWizardModal;
