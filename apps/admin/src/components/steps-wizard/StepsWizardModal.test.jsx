import '@testing-library/jest-dom/extend-expect';
import { fireEvent, render, screen } from '@testing-library/react';

import StepsWizardModal from './StepsWizardModal';

describe('StepsWizardModal', () => {
  const defaultProps = {
    headerText: 'Header',
    onClose: jest.fn(),
    list: [],
    selectedItem: { value: '1', label: 'Item 1' },
    onSelection: jest.fn(),
    cancelText: 'CANCEL',
    showCancelButton: true,
    backText: 'BACK',
    showBackButton: true,
    isBackButtonDisabled: false,
    onBackClick: jest.fn(),
    nextText: 'NEXT',
    showNextButton: true,
    isNextButtonDisabled: false,
    onNextClick: jest.fn(),
  };

  it('should render the modal with default props', () => {
    render(<StepsWizardModal {...defaultProps} />);
    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('CANCEL')).toBeInTheDocument();
    expect(screen.getByText('BACK')).toBeInTheDocument();
    expect(screen.getByText('NEXT')).toBeInTheDocument();
  });

  it('should call onClose when cancel button is clicked', () => {
    render(<StepsWizardModal {...defaultProps} />);
    fireEvent.click(screen.getByText('CANCEL'));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('should call onBackClick when back button is clicked', () => {
    render(<StepsWizardModal {...defaultProps} />);
    fireEvent.click(screen.getByText('BACK'));
    expect(defaultProps.onBackClick).toHaveBeenCalled();
  });

  it('should call onNextClick when next button is clicked', () => {
    render(<StepsWizardModal {...defaultProps} />);
    fireEvent.click(screen.getByText('NEXT'));
    expect(defaultProps.onNextClick).toHaveBeenCalled();
  });

  it('should disable the back button when isBackButtonDisabled is true', () => {
    render(<StepsWizardModal {...defaultProps} isBackButtonDisabled={true} />);
    expect(screen.getByText('BACK')).toBeDisabled();
  });

  it('should disable the next button when isNextButtonDisabled is true', () => {
    render(<StepsWizardModal {...defaultProps} isNextButtonDisabled={true} />);
    expect(screen.getByText('NEXT')).toBeDisabled();
  });
});
