import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'LOCATION',
  },
  add: {
    headerText: 'ADD_LOCATION',
  },
  edit: {
    headerText: 'EDIT_LOCATION',
  },
  delete: {
    headerText: 'DELETE_LOCATION',
    confirmationMessage: 'DELETE_LOCATION_CONFIRMATION_MESSAGE',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage: 'BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE',
  },
  importFile: {
    headerText: 'IMPORT_LOCATION',
  },
};

export const bulkActionOptions = [{ label: 'DELETE', value: 'DELETE' }];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { country, name, ipInLocations } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'NAME_REQUIRED_MESSAGE';

    return validationDetail;
  }

  if (!country?.id) {
    validationDetail.isValid = false;
    validationDetail.context = 'country';
    validationDetail.type = 'error';
    validationDetail.message = 'COUNTRY_REQUIRED_MESSAGE';

    return validationDetail;
  }

  if (ipInLocations?.length === 0) {
    validationDetail.isValid = false;
    validationDetail.context = 'ipInLocations';
    validationDetail.type = 'error';
    validationDetail.message = 'IP_ADDRESS_REQUIRED_MESSAGE';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the location`;
  }

  if (name === 'country') {
    tooltipDetail.content = `Select the country of the IP location`;
  }

  if (name === 'ipAddress') {
    tooltipDetail.content = (
      <p>
        Enter the IP address of the location and click{' '}
        <strong className="tooltip-bold">Add Items</strong>. You can add mutiple IP addresses for a
        location. Click <strong className="tooltip-bold">X</strong> to remove a IP address or{' '}
        <strong className="tooltip-bold">Remove All</strong> to remove all selections.
      </p>
    );
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Enable this option if you want to update your existing locations, delete existing locations,
        or add new locations. If you only want to add new locations, Zscaler doesn&apos;t recommend
        selecting this option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-ip-locations-csv-file">
          Importing IP Locations from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};
