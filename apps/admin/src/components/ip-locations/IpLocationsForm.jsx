import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  ListBuilder,
  defaultFormPropTypes,
  defaultFormProps,
  ipAddressValidators,
  mergeFormValues,
  useApiCall,
} from '@zscaler/zui-component-library';

import PropTypes from 'prop-types';

import { getCountryCodes } from '../../ducks/countries';
import { selectCountryCodesList } from '../../ducks/countries/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const defaultProps = {
  ...defaultFormProps,
};

const IpLocationsForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { detail, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const [list, setList] = useState(detail?.ipInLocations || []);

  const [formValues, setFormValues] = useState({
    name: '',
    country: {},
    ipInLocations: [],
    ...detail,
  });

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ipInLocations: list }));
  }, [list]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    setDetail(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    apiCall(getCountryCodes());
  }, []);

  const countryCodesList = useSelector(selectCountryCodesList);

  const [selectedCountry, setSelectedCountry] = useState(() => {
    const { country } = detail;

    if (country?.id) {
      return [{ label: country.name, value: country.id }];
    }

    return [];
  });

  const onCountrySelection = (detail) => {
    setSelectedCountry(detail);

    if (detail?.[0]) {
      setFormValues((prevState) => ({ ...prevState, country: { id: detail?.[0]?.value } }));
    }
  };

  const getValidationMessage = ({ invalidItems }) => {
    return `Invalid ${t('IP_ADDRESS')} - ${invalidItems?.join?.(', ')}`;
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('INFORMATION')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              maxLength="128"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('name')}
              disabled={isFormReadOnly}
            />
          </FieldGroup>

          <FieldGroup>
            <Field
              label="COUNTRY"
              htmlFor="country"
              containerClass="full-width"
              info={validationDetail}
              tooltip={isFormReadOnly ? {} : getFormTooltipDetail('country')}
            >
              <DropDown
                disabled={isFormReadOnly}
                list={countryCodesList}
                selectedList={selectedCountry}
                onSelection={onCountrySelection}
                searchOnKeyPress
              />
            </Field>
          </FieldGroup>

          <Field
            label="IP_ADDRESS"
            htmlFor="ipInLocations"
            containerClass="full-width"
            info={validationDetail}
            tooltip={isFormReadOnly ? {} : getFormTooltipDetail('ipAddress')}
          >
            <ListBuilder
              list={list}
              setList={setList}
              validator={ipAddressValidators}
              separator={/[\r\n,]+/}
              getValidationMessage={getValidationMessage}
              disabled={isFormReadOnly}
            />
          </Field>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

IpLocationsForm.defaultProps = defaultProps;

IpLocationsForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.bool };

export default IpLocationsForm;
