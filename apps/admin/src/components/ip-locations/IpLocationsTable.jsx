import { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  InlineText,
  Selector,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { defaultBulkActionOption } from '../../components/ip-locations/helper';

import { getList } from '../../ducks/ip-locations';
import { selectTableConfig, selectTableDetail } from '../../ducks/ip-locations/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const IpLocationsTable = () => {
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    setDetail,
    setSelectedRowDetail,
    setSelectedBulkAction,
    searchTerm,
    isFormReadOnly,
  } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'locations') {
        const LocationCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const locations = props?.row?.original?.locations;

          return <InlineText>{locations.map(({ name }) => name).join(', ') || ''}</InlineText>;
        };

        columnDetail.cell = LocationCellComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            editIcon={isFormReadOnly ? faEye : faPencilAlt}
            showDelete={!isFormReadOnly}
            showMoreIcon={!isFormReadOnly}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    if (isFormReadOnly) {
      return [...(tableConfig?.columns || [])];
    }

    return [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 70,
        minSize: 70,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];
  }, [tableConfig?.columns, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);

    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      onRowSelection={onRowSelection}
    />
  );
};

export default IpLocationsTable;
