import { useContext, useMemo } from 'react';

import {
  CRUDModal,
  FileBrowserForm,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import IpLocationsForm from '../../components/ip-locations/IpLocationsForm';
import { getFormTooltipDetail, modalModeDetail } from '../../components/ip-locations/helper';

import {
  add,
  bulkRemove,
  downloadTemplateCSV,
  importFromCsvPolling,
  remove,
  update,
} from '../../ducks/ip-locations';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const IpLocationsCRUD = () => {
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    csvImportDetail,
    setCsvImportDetail,
    csvImportResult,
    setCsvImportResult,
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultCsvImportDetail,
    defaultCsvImportResultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setCsvImportDetail(defaultCsvImportDetail);
    setCsvImportResult(defaultCsvImportResultDetail);
    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(detail), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(detail), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'bulkDelete') {
      onBulkActionSelection();
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsvPolling(csvImportDetail))
        .then((response = {}) => {
          setCsvImportResult({ ...response });
        })
        .catch(noop);
    }
  };

  const onBulkActionSelection = () => {
    const { value } = selectedBulkAction;

    const ids = selectedRowDetail.map(({ id }) => id);

    if (value === 'DELETE') {
      apiCall(bulkRemove({ ids }), {
        successNotificationPayload: {
          ...getApiPUTNotificationOptions(),
          message: 'ITEMS_HAVE_BEEN_DELETED',
        },
      })
        .catch(noop)
        .finally(onCloseClick);
    }
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const isImportResultValid = !!csvImportResult;

  const cancelText = useMemo(() => {
    return isImportResultValid || modalMode === 'view' ? 'CLOSE' : 'CANCEL';
  }, [modalMode, isImportResultValid]);

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) =>
        modalMode === 'importFile' ? (
          <FileBrowserForm
            onDetailChange={setCsvImportDetail}
            detail={csvImportDetail}
            onDownloadClick={onDownloadClick}
            overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
            tooltip={getFormTooltipDetail('csvFile')}
            result={csvImportResult}
            {...props}
          />
        ) : (
          <IpLocationsForm {...props} />
        )
      }
      saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
      showSave={!isImportResultValid && hasFullAccess}
      cancelText={cancelText}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default IpLocationsCRUD;
