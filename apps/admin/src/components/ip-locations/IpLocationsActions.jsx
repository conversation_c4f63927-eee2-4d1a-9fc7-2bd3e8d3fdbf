import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faFileSpreadsheet, faPlus, faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, DropDown, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { bulkActionOptions } from '../../components/ip-locations/helper';

import { getList } from '../../ducks/ip-locations';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const IpLocationsAction = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    searchTerm,
    setSearchTerm,
    defaultBulkActionOption,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onCSVImportClick = () => {
    setModalMode('importFile');
  };

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onBulkActionSelectionClick = (payload) => {
    const newSelection = payload[0] || defaultBulkActionOption;

    setSelectedBulkAction(newSelection);

    if (selectedRowDetail?.length > 0) {
      const { value } = newSelection;

      let mode = '';

      if (value === 'DELETE') {
        mode = 'bulkDelete';
      }

      setModalMode(mode);
    }
  };

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);

    setSelectedBulkAction(defaultBulkActionOption);
    setSearchTerm('');
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  return (
    <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
      {hasFullAccess ? (
        <div className="buttons">
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_LOCATION')}</span>
          </Button>
          <Button type="secondary" onClick={onCSVImportClick}>
            <FontAwesomeIcon icon={faFileSpreadsheet} className="icon left" />
            <span>{t('IMPORT_CSV')}</span>
          </Button>

          {isBulkActionEnabled && (
            <DropDown
              list={bulkActionOptions}
              selectedList={[selectedBulkAction]}
              onSelection={onBulkActionSelectionClick}
              selectedItemsProps={{
                kind: 'secondary',
                containerStyle: { justifyContent: 'center' },
              }}
              disabled={!isBulkActionEnabled}
            />
          )}
        </div>
      ) : null}

      <div className="buttons">
        <Button
          type="tertiary"
          onClick={onRefreshClick}
          style={{ minWidth: '0px', paddingRight: '0px' }}
        >
          <FontAwesomeIcon icon={faSync} />
        </Button>

        <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ width: '260px' }} />
      </div>
    </div>
  );
};

export default IpLocationsAction;
