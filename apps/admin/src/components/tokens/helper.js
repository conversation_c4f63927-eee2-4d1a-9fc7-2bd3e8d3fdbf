import {
  faCircleCheck,
  faCircleXmark,
  faTriangleExclamation,
} from '@fortawesome/pro-solid-svg-icons';
import { getCalendarDDList } from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { find } from 'lodash-es';

export const UI_VIEW_OPTIONS = {
  TOKEN_VALIDATOR_VIEW: 'TOKEN_VALIDATOR_VIEW',
  TEST_TOKEN_VIEW: 'TEST_TOKEN_VIEW',
};

export const searchOptions = [
  { label: 'NAME', value: 'NAME' },
  { label: 'GROUP', value: 'GROUP' },
];

export const getIconClassAndLabel = (row) => {
  let truthyIcon = faCircleXmark;
  let truthyIconClass = 'text-gray';
  let truthyLabel = 'EXPIRED';

  if (row?.revoked) {
    truthyIcon = faTriangleExclamation;
    truthyIconClass = 'has-color-warning';
    truthyLabel = 'REVOKED';
  } else if (!row?.revoked && dayjs(row?.expiresAt).valueOf() > dayjs().valueOf()) {
    truthyIcon = faCircleCheck;
    truthyIconClass = 'has-color-success';
    truthyLabel = 'ACTIVE';
  }
  return { truthyIcon, truthyIconClass, truthyLabel };
};

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'JWT',
  },
  delete: {
    headerText: 'REVOKE_TOKEN',
    confirmationMessage: 'REVOKE_TOKEN_MESSAGE',
  },
};

export const getFilterApiPayload = ({ selectedFilter }) => {
  const filterApiPayload = {};

  filterApiPayload.isIssuedDateRangeValid = true;
  filterApiPayload.isExpiredDateRangeValid = true;

  Object.keys(selectedFilter).forEach((filterName) => {
    const filterDetail = selectedFilter[filterName];

    if (Array.isArray(filterDetail) && filterDetail.length > 0) {
      if (filterName == 'issuedAt' && !find(selectedFilter[filterName], ['value', 'ALL'])) {
        const { startTime, endTime } = selectedFilter[filterName]?.[0] || {};

        filterApiPayload['issuedAfter'] = dayjs(startTime).valueOf();
        filterApiPayload['issuedBefore'] = dayjs(endTime).valueOf();

        filterApiPayload.isIssuedDateRangeValid =
          filterApiPayload['issuedBefore'] > filterApiPayload['issuedAfter'];
      }

      if (filterName == 'expiresAt' && !find(selectedFilter[filterName], ['value', 'ALL'])) {
        const { startTime, endTime } = selectedFilter[filterName]?.[0] || {};

        filterApiPayload['expiresAfter'] = dayjs(startTime).valueOf();
        filterApiPayload['expiresBefore'] = dayjs(endTime).valueOf();

        filterApiPayload.isExpiredDateRangeValid =
          filterApiPayload['expiresBefore'] > filterApiPayload['expiresAfter'];
      }

      const statusDetail = selectedFilter['status'];

      if (Array.isArray(statusDetail) && statusDetail.length > 0) {
        filterApiPayload['status'] = statusDetail.map(({ value }) => value);
      }

      if (filterApiPayload['status']?.includes('ALL')) {
        delete filterApiPayload['status'];
      }
    }
  });

  return filterApiPayload;
};

export const calendarConfig = {
  minDate: dayjs().subtract(6, 'M'),
  maxDate: dayjs().add(1, 'M'),
  maxInterval: dayjs().diff(dayjs().subtract(6, 'M'), 'seconds'),
  message: { text: '' },
};

export const getDefaultTimeRange = (level) => {
  const newList = getCalendarDDList({ level });

  const defaultTimeRange = newList[0] ? [newList[0]] : [];

  return defaultTimeRange;
};
