import { CalendarDropDown, DropDown, Field, FieldGroup } from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { calendarConfig } from './helper';

const statusOptions = [
  { label: 'ALL', value: 'ALL' },
  { label: 'ACTIVE', value: 'active' },
  { label: 'REVOKED', value: 'revoked' },
  { label: 'EXPIRED', value: 'expired' },
];

const tooltipProps = {
  placement: 'right',
  offset: 30,
};

const defaultProps = {
  selectedFilter: {},
  setSelectedFilter: noop,
};

const Filters = ({ selectedFilter, setSelectedFilter }) => {
  const getSelectedList = (filterName) => {
    const filterDetail = selectedFilter[filterName];

    if (filterDetail?.length > 0) {
      return filterDetail;
    } else {
      return [];
    }
  };

  const onFilterSelection = (filterName) => (detail) =>
    setSelectedFilter((prevState) => {
      const selectedFilter = cloneDeep(prevState);

      return { ...selectedFilter, [filterName]: detail };
    });

  return (
    <FieldGroup containerClass="buttons">
      <Field label="ISSUED_AT" containerClass="no-m-b">
        <CalendarDropDown
          level="TOKEN"
          selectedList={getSelectedList('issuedAt')}
          setSelectedList={onFilterSelection('issuedAt')}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
          config={calendarConfig}
        />
      </Field>
      <Field label="EXPIRES_AT" containerClass="no-m-b">
        <CalendarDropDown
          level="EXPIRY"
          selectedList={getSelectedList('expiresAt')}
          setSelectedList={onFilterSelection('expiresAt')}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
          config={calendarConfig}
        />
      </Field>
      <Field label="STATUS" containerClass="no-m-b">
        <DropDown
          list={statusOptions}
          selectedList={getSelectedList('status')}
          onSelection={onFilterSelection('status')}
        />
      </Field>
    </FieldGroup>
  );
};

Filters.defaultProps = defaultProps;

Filters.propTypes = {
  selectedFilter: PropTypes.object,
  setSelectedFilter: PropTypes.func,
};

export default Filters;
