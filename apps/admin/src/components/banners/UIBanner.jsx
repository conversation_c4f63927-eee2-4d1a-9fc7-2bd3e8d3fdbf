import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { noop } from 'lodash-es';

import { getBanner } from '../../ducks/global';
import { selectBanner } from '../../ducks/global/selectors';

// to show read only mode in case of migration and maintainance
const UIBanner = () => {
  const dispatch = useDispatch();

  const uiBanners = useSelector(selectBanner);

  const { description, status } = uiBanners?.[0] || {};

  useEffect(() => {
    dispatch(getBanner()).catch(noop);
  }, []);

  if (!status) {
    return null;
  }

  return (
    <div className="ui-banner-container is-flex has-jc-c has-ai-c">
      <div className="description">{description}</div>
    </div>
  );
};

export default UIBanner;
