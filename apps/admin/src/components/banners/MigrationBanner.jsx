import { useSelector } from 'react-redux';

import { STATUS_ENUMS } from '../../ducks/migration/constants';
import { selectStatus } from '../../ducks/migration/selectors';

import MigrationAdminLinked from '../migration/MigrationAdminLinked';
import MigrationProvisioned from '../migration/MigrationProvisioned';

const MigrationBanner = () => {
  const status = useSelector(selectStatus);

  const { currentStatus, showBanner = false, extIdpAuthnSuccess = true } = status || {};

  if (!showBanner) {
    return null;
  }

  return (
    <div className="migration-banner-container is-flex has-jc-c has-ai-c">
      {currentStatus === STATUS_ENUMS.PROVISIONED && (
        <MigrationProvisioned status={status} extIdpAuthnSuccess={extIdpAuthnSuccess} />
      )}
      {currentStatus === STATUS_ENUMS.ADMIN_LINKED && <MigrationAdminLinked status={status} />}
    </div>
  );
};

export default MigrationBanner;
