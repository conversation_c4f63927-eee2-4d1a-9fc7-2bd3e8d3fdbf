import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  DropDown,
  InlineDatePicker,
  MultiSelection,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { find, uniq } from 'lodash-es';
import PropTypes from 'prop-types';

import { getResourceServerScopesList } from '../../ducks/access-policies';
import {
  selectDaysOfWeekEnums,
  selectFrequenciesEnums,
  selectOperationsEnums,
  selectResourceServerScopesList,
  selectResourceServerScopesTableDetail,
  selectTimeZonesEnums,
  selectWeeksOfMonthEnums,
} from '../../ducks/access-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import {
  ACCESS_POLICY_CRITERIA_DATE_FORMAT,
  DEFAULT_POLICY_CRITERIA,
  getPolicyCriteriaExplination,
} from './helper';

const TIME_OR_RESOURCES_OPTIONS = [
  { label: 'TIME', value: 'TIME' },
  { label: 'RESOURCES', value: 'RESOURCES' },
];

const API_DATE_FORMAT = 'YYYY-MM-DDTHH:mm:ss';

const CriteriaSection = ({ setShowTimeSection, setShowResourceSection }) => {
  const { t } = useTranslation();

  const { detail, setDetail } = useContext(CRUDPageContext);

  const { accessPolicyCriteria } = detail;

  const resourceServerScopesTableDetail = useSelector(selectResourceServerScopesTableDetail);

  const resourceServerScopesList = useSelector(selectResourceServerScopesList);
  const operationsList = useSelector(selectOperationsEnums);
  const frequenciesList = useSelector(selectFrequenciesEnums);
  const timeZonesList = useSelector(selectTimeZonesEnums);
  const daysOfWeekList = useSelector(selectDaysOfWeekEnums);
  const weeksOfMonthList = useSelector(selectWeeksOfMonthEnums);

  const [selectedOperation, setSelectedOperation] = useState([]);

  const [selectedFrequency, setSelectedFrequency] = useState([]);
  const [selectedWeeksOfMonth, setSelectedWeeksOfMonth] = useState([]);

  const [selectedFromDate, setSelectedFromDate] = useState();
  const [selectedToDate, setSelectedToDate] = useState();

  const [selectedTimeZone, setSelectedTimeZone] = useState([]);

  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState([]);

  const [selectedResourceCondition, setselectedResourceCondition] = useState([]);

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: resourceServerScopesTableDetail,
    apiCallFunc: getResourceServerScopesList,
  });

  useEffect(() => {
    const {
      operation = '',
      resourceConditions = [],
      timeCondition = {},
    } = accessPolicyCriteria || {};

    setSelectedOperation(operation ? [find(operationsList, { value: operation })] : []);

    setSelectedFrequency(
      timeCondition.frequency ? [find(frequenciesList, { value: timeCondition.frequency })] : [],
    );

    setSelectedFromDate(
      dayjs(
        timeCondition.fromDate ? dayjs(timeCondition.fromDate, API_DATE_FORMAT).toDate() : null,
      ),
    );

    setSelectedToDate(
      dayjs(timeCondition.toDate ? dayjs(timeCondition.toDate, API_DATE_FORMAT).toDate() : null),
    );

    setSelectedTimeZone(
      timeCondition.timeZone ? [find(timeZonesList, { value: timeCondition.timeZone })] : [],
    );

    setSelectedDaysOfWeek(timeCondition.daysOfWeek || []);

    setSelectedWeeksOfMonth(
      timeCondition.weeksOfMonth
        ? timeCondition.weeksOfMonth.map((value) => find(weeksOfMonthList, { value }))
        : [],
    );

    const selectedResource = resourceConditions.map(({ id, name, resourceId }) => ({
      label: name,
      value: id,
      resourceId,
    }));

    setselectedResourceCondition(selectedResource);
  }, [accessPolicyCriteria, operationsList, frequenciesList, timeZonesList, weeksOfMonthList]);

  const onWeeksOfMonthSelection = (selected) => {
    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        timeCondition: {
          ...prevState.accessPolicyCriteria.timeCondition,
          weeksOfMonth: selected.map((item) => item.value),
        },
      },
    }));
  };

  const renderWeeksOfMonthSection = () => {
    if (selectedFrequency[0]?.value !== 'REPEAT_MONTHLY') {
      return null;
    }

    return (
      <DropDown
        list={weeksOfMonthList}
        selectedList={selectedWeeksOfMonth}
        onSelection={onWeeksOfMonthSelection}
        isMulti
        containerStyle={{ width: '150px' }}
      />
    );
  };

  const onDaysOfWeekSelection = (day, isSelected) => {
    const newDaysOfWeek = isSelected
      ? selectedDaysOfWeek.filter((item) => item !== day)
      : uniq([...selectedDaysOfWeek, day]);

    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        timeCondition: {
          ...prevState.accessPolicyCriteria.timeCondition,
          daysOfWeek: newDaysOfWeek,
        },
      },
    }));
  };

  const renderDaysOfWeekSection = () => {
    if (
      selectedFrequency[0]?.value === 'REPEAT_WEEKLY' ||
      selectedFrequency[0]?.value === 'REPEAT_MONTHLY'
    ) {
      return (
        <div className="days-of-week-section">
          {daysOfWeekList.map((day) => {
            const isSelected = selectedDaysOfWeek.includes(day.value);

            return (
              <p
                key={day.value}
                className={`day-of-week ${isSelected ? 'is-selected' : ''}`}
                onClick={() => onDaysOfWeekSelection(day.value, isSelected)}
              >
                <span>{day.label.substring(0, 1)}</span>
              </p>
            );
          })}
        </div>
      );
    }

    return null;
  };

  const onFrequencySelection = (selected) => {
    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        timeCondition: {
          ...prevState.accessPolicyCriteria.timeCondition,
          frequency: selected[0].value,
        },
      },
    }));
  };

  const onFromDateChange = (evt) => {
    const newDate = evt.target.value;

    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        timeCondition: {
          ...prevState.accessPolicyCriteria.timeCondition,
          fromDate: dayjs(newDate).format(API_DATE_FORMAT),
        },
      },
    }));
  };

  const onToDateChange = (evt) => {
    const newDate = evt.target.value;

    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        timeCondition: {
          ...prevState.accessPolicyCriteria.timeCondition,
          toDate: dayjs(newDate).format(API_DATE_FORMAT),
        },
      },
    }));
  };

  const onTimeZoneSelection = (selected) => {
    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        timeCondition: {
          ...prevState.accessPolicyCriteria.timeCondition,
          timeZone: selected[0].value,
        },
      },
    }));
  };

  const onDeleteTimeCondition = () => {
    setShowTimeSection(false);

    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        operation: '',
        timeCondition: { ...DEFAULT_POLICY_CRITERIA.accessPolicyCriteria.timeCondition },
      },
    }));
  };

  const renderTimeSection = () => {
    const canShowWeeksOfMonth =
      selectedFrequency[0]?.value === 'REPEAT_WEEKLY' ||
      selectedFrequency[0]?.value === 'REPEAT_MONTHLY';

    return (
      <div className="time-section-container option-selector">
        <div className="is-flex has-jc-sb has-ai-c" style={{ gap: '8px' }}>
          <DropDown
            list={[TIME_OR_RESOURCES_OPTIONS[0]]}
            selectedList={[TIME_OR_RESOURCES_OPTIONS[0]]}
            containerClass="horizontal-line"
          />

          <span>that</span>

          <DropDown
            list={frequenciesList}
            selectedList={selectedFrequency}
            onSelection={onFrequencySelection}
            containerStyle={{ width: '135px' }}
          />

          <span>=</span>

          <InlineDatePicker
            format={ACCESS_POLICY_CRITERIA_DATE_FORMAT}
            label={detail.displayName}
            name={detail.attrName}
            selectedDate={selectedFromDate}
            onChange={onFromDateChange}
            elementProps={{ containerStyle: { height: '32px' } }}
            containerStyle={{ width: '130px' }}
            hideOnSelection={false}
            showTimePicker
            showHourMinuteOption={false}
            showHourOption
            showMinuteOption
            minDate={dayjs().toDate()}
            maxDate={selectedToDate}
          />

          <span>to</span>

          <InlineDatePicker
            format={ACCESS_POLICY_CRITERIA_DATE_FORMAT}
            label={detail.displayName}
            name={detail.attrName}
            selectedDate={selectedToDate}
            onChange={onToDateChange}
            elementProps={{ containerStyle: { height: '32px' } }}
            containerStyle={{ width: '130px' }}
            showTimePicker
            showHourMinuteOption={false}
            showHourOption
            showMinuteOption
            minDate={dayjs().toDate()}
          />

          <DropDown
            list={timeZonesList}
            selectedList={selectedTimeZone}
            onSelection={onTimeZoneSelection}
            containerStyle={{ width: '130px' }}
          />

          <Button type="tertiary" containerClass="content-width" onClick={onDeleteTimeCondition}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </div>

        {canShowWeeksOfMonth && (
          <div className="occurrences-day-of-week-section">
            <span> Every </span>

            {renderWeeksOfMonthSection()}

            {renderDaysOfWeekSection()}
          </div>
        )}
      </div>
    );
  };

  const onResourceSelection = (selected) => {
    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        resourceConditions:
          selected.map((detail) => ({
            name: detail.label,
            id: detail.value,
            resourceId: detail.resourceId,
          })) || [],
      },
    }));
  };

  const onDeleteResourceCondition = () => {
    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: {
        ...prevState.accessPolicyCriteria,
        operation: '',
        resourceConditions: [],
      },
    }));

    setShowResourceSection(false);
  };

  const renderResourcesSection = () => {
    return (
      <div className="scope-section-container option-selector has-ai-c">
        <DropDown
          list={[TIME_OR_RESOURCES_OPTIONS[1]]}
          selectedList={[TIME_OR_RESOURCES_OPTIONS[1]]}
          containerClass="horizontal-line"
        />

        <span>=</span>

        <DropDown
          list={resourceServerScopesList}
          selectedList={selectedResourceCondition}
          onSelection={onResourceSelection}
          onOpen={onDropDownOpen}
          renderItemsSelection={(props) => (
            <MultiSelection
              unselectedTitle="Unselected Scopes"
              selectedTitle="Selected Scopes"
              {...props}
            />
          )}
          isMulti
          hasSearch
          loadMoreDetail={{ ...resourceServerScopesTableDetail, onLoadMoreClick }}
          loading={isDropDownLoading}
          containerStyle={{ minWidth: '200px', maxWidth: '450px' }}
        />

        <Button type="tertiary" containerClass="content-width" onClick={onDeleteResourceCondition}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      </div>
    );
  };

  const onOperationSelection = (selected) => {
    setDetail((prevState) => ({
      ...prevState,
      accessPolicyCriteria: { ...prevState.accessPolicyCriteria, operation: selected[0].value },
    }));
  };

  const isTimeConditionSet = selectedFrequency.length > 0;
  const isResourceConditionSet = selectedResourceCondition.length > 0;

  return (
    <div className="criteria-section-container">
      <div>
        <DropDown
          list={operationsList}
          selectedList={selectedOperation}
          onSelection={onOperationSelection}
          selectedItemsProps={{ kind: 'tertiary' }}
          containerClass="full-width"
          containerStyle={{ maxWidth: '100px' }}
          disabled={!(isTimeConditionSet && isResourceConditionSet)}
        />

        {renderTimeSection()}
        {renderResourcesSection()}
      </div>

      <div className="expression-section">
        <p>Expression</p>
        <p>
          {getPolicyCriteriaExplination(accessPolicyCriteria, t, {
            operationsList,
            frequenciesList,
            timeZonesList,
            daysOfWeekList,
            weeksOfMonthList,
          })}
        </p>
      </div>
    </div>
  );
};
CriteriaSection.propTypes = {
  showTimeSection: PropTypes.bool.isRequired,
  setShowTimeSection: PropTypes.func.isRequired,
  setShowResourceSection: PropTypes.func.isRequired,
};

export default CriteriaSection;
