import { defaultValidationDetail } from '@zscaler/zui-component-library';

import dayjs from 'dayjs';

export const ACCESS_POLICY_CRITERIA_DATE_FORMAT = 'MM/DD/YYYY hh:mm A';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'VIEW_API_CLIENT_POLICY',
  },
  add: {
    headerText: 'ADD_POLICY_RULE',
  },
  edit: {
    headerText: 'EDIT_POLICY_RULE',
  },
  delete: {
    headerText: 'DELETE_POLICY_RULE',
    confirmationMessage:
      'Are you sure you want to delete this Policy? The changes cannot be undone.',
  },
};

export const DEFAULT_POLICY_CRITERIA = {
  name: '',
  apiClients: [],
  ruleAction: '',
  accessPolicyCriteria: {
    operation: '',
    timeCondition: {
      frequency: '',
      fromDate: '',
      toDate: '',
      timeZone: '',
      daysOfWeek: [],
      weeksOfMonth: [],
    },
    resourceConditions: [],
  },
};

export const getPolicyCriteriaExplination = (
  { operation = '', timeCondition = {}, resourceConditions = [] } = {},
  t = (str) => str,
  { operationsList, frequenciesList, timeZonesList, daysOfWeekList, weeksOfMonthList },
) => {
  const { frequency, fromDate, toDate, timeZone, daysOfWeek, weeksOfMonth } = timeCondition;

  let timeCriteriaExplained = '';

  if (frequency && fromDate && toDate && timeZone) {
    const frequencyLabel = frequenciesList.find((item) => item.value === frequency)?.label || '';
    const timezoneLabel = timeZonesList.find((item) => item.value === timeZone)?.label || '';
    const weeksOfMonthLabel = weeksOfMonth
      .map((occurrence) => weeksOfMonthList.find((item) => item.value === occurrence)?.label || '')
      .join(' and ');
    const daysOfWeekLabel = daysOfWeek
      .map((day) => daysOfWeekList.find((item) => item.value === day)?.label || '')
      .join(', ');

    timeCriteriaExplained = `(Time =  ${t(frequencyLabel ?? '')} ${weeksOfMonthLabel || daysOfWeekLabel ? 'every' : ''} 
    ${weeksOfMonthLabel} ${daysOfWeekLabel} ${weeksOfMonthLabel || daysOfWeekLabel ? 'between' : 'from'} 
    ${fromDate ? dayjs(fromDate).format(ACCESS_POLICY_CRITERIA_DATE_FORMAT) : ''} ${weeksOfMonthLabel || daysOfWeekLabel ? 'and' : 'to'} 
    ${toDate ? dayjs(toDate).format(ACCESS_POLICY_CRITERIA_DATE_FORMAT) : ''} ${timezoneLabel ?? ''})`;
  }

  const resourcesCriteriaExplained =
    resourceConditions.length > 0
      ? `(Resources = ${resourceConditions.map((resource) => resource.name).join(', ')})`
      : '';

  const operationLabel = operationsList.find((item) => item.value === operation)?.label || '';

  return `${operationLabel ? '(' : ''} ${timeCriteriaExplained} ${operationLabel} ${resourcesCriteriaExplained} ${operationLabel ? ')' : ''}`;
};

export const getFormValidationDetail = ({ formValues, activeStep }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name, ruleAction, accessPolicyCriteria } = formValues || {};

  const {
    operation = '',
    timeCondition = {},
    resourceConditions = [],
  } = accessPolicyCriteria || {};

  const { frequency, fromDate, toDate, timeZone, daysOfWeek, weeksOfMonth } = timeCondition;

  const isTimeConditionSet = !!frequency;
  const isResourceConditionSet = resourceConditions.length > 0;

  if (isTimeConditionSet && isResourceConditionSet) {
    if (!operation) {
      validationDetail.isValid = false;
      validationDetail.context = 'criteriaSection';
      validationDetail.type = 'error';
      validationDetail.message = 'Operation is Required';

      return validationDetail;
    }
  }

  if (fromDate || toDate || timeZone || frequency) {
    if (!frequency) {
      validationDetail.isValid = false;
      validationDetail.context = 'frequency';
      validationDetail.type = 'error';
      validationDetail.message = 'Frequency is Required';

      return validationDetail;
    }

    if (!fromDate) {
      validationDetail.isValid = false;
      validationDetail.context = 'criteriaSection';
      validationDetail.type = 'error';
      validationDetail.message = 'From Date is Required';

      return validationDetail;
    }

    if (!toDate) {
      validationDetail.isValid = false;
      validationDetail.context = 'criteriaSection';
      validationDetail.type = 'error';
      validationDetail.message = 'To Date is Required';

      return validationDetail;
    }

    if (!timeZone) {
      validationDetail.isValid = false;
      validationDetail.context = 'criteriaSection';
      validationDetail.type = 'error';
      validationDetail.message = 'Time Zone is Required';

      return validationDetail;
    }

    if (frequency === 'REPEAT_WEEKLY' && daysOfWeek.length === 0) {
      validationDetail.isValid = false;
      validationDetail.context = 'criteriaSection';
      validationDetail.type = 'error';
      validationDetail.message = 'Days of Week is Required';

      return validationDetail;
    }

    if (frequency === 'REPEAT_MONTHLY') {
      if (weeksOfMonth.length === 0) {
        validationDetail.isValid = false;
        validationDetail.context = 'criteriaSection';
        validationDetail.type = 'error';
        validationDetail.message = 'Week of Month is Required';

        return validationDetail;
      }

      if (weeksOfMonth.length === 0) {
        validationDetail.isValid = false;
        validationDetail.context = 'criteriaSection';
        validationDetail.type = 'error';
        validationDetail.message = 'Days of Week is Required';

        return validationDetail;
      }
    }
  }

  if (operation) {
    if (!isResourceConditionSet) {
      validationDetail.isValid = false;
      validationDetail.context = 'criteriaSection';
      validationDetail.type = 'error';
      validationDetail.message = 'Resources Condition is Required';

      return validationDetail;
    }
  }

  if (activeStep.value === 'DEFINE_CRITERIA' && !(isTimeConditionSet || isResourceConditionSet)) {
    validationDetail.isValid = false;
    validationDetail.context = 'criteriaSection';
    validationDetail.type = 'error';
    validationDetail.message = 'Criteria is Required';

    return validationDetail;
  }

  if (activeStep.value === 'DEFINE_CRITERIA' && !ruleAction) {
    validationDetail.isValid = false;
    validationDetail.context = 'ruleAction';
    validationDetail.type = 'error';
    validationDetail.message = 'Rule Action is Required';

    return validationDetail;
  }

  if (activeStep.value === 'SUMMARY' && !name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'ruleOrder') {
    tooltipDetail.content = `Select the rule order. The sign-on policy automatically assigns the Rule Order number. Policy rules are evaluated in ascending order. Rule Order reflects this rule's place in the order.`;
  }

  if (name === 'name') {
    tooltipDetail.content = `Enter a rule name for the policy`;
  }

  if (name === 'ruleStatus') {
    tooltipDetail.content = `An enabled rule is actively enforced. A disabled rule is not actively enforced but does not lose its place in the Rule Order. The service skips it and moves to the next rule.`;
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. The description cannot exceed 512 characters.`;
  }

  if (name === 'action') {
    tooltipDetail.content = `Choose the action the Zscaler service takes upon detecting access request that matches the rule criteria`;
  }

  return tooltipDetail;
};
