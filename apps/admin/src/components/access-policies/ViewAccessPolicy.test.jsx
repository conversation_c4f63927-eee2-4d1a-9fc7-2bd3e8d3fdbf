import { useSelector } from 'react-redux';

import { render } from '@testing-library/react';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import ViewAccessPolicy from './ViewAccessPolicy';
import { getPolicyCriteriaExplination } from './helper';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
}));

jest.mock('./helper', () => ({
  getPolicyCriteriaExplination: jest.fn(),
}));

const mockDetail = {
  name: 'Test Policy',
  apiClients: [{ name: 'Client1' }, { name: 'Client2' }],
  accessPolicyCriteria: {},
  ruleAction: 'Allow',
};

const mockSelectors = {
  selectOperationsEnums: jest.fn(),
  selectFrequenciesEnums: jest.fn(),
  selectTimeZonesEnums: jest.fn(),
  selectDaysOfWeekEnums: jest.fn(),
  selectWeeksOfMonthEnums: jest.fn(),
};

describe('ViewAccessPolicy', () => {
  beforeEach(() => {
    useSelector.mockImplementation((selector) => {
      switch (selector) {
        case mockSelectors.selectOperationsEnums:
          return ['Operation1', 'Operation2'];
        case mockSelectors.selectFrequenciesEnums:
          return ['Frequency1', 'Frequency2'];
        case mockSelectors.selectTimeZonesEnums:
          return ['TimeZone1', 'TimeZone2'];
        case mockSelectors.selectDaysOfWeekEnums:
          return ['Monday', 'Tuesday'];
        case mockSelectors.selectWeeksOfMonthEnums:
          return ['Occurrence1', 'Occurrence2'];
        default:
          return [];
      }
    });

    getPolicyCriteriaExplination.mockReturnValue('Criteria Explanation');
  });

  it('renders the ViewAccessPolicy component', () => {
    const { getByText } = render(
      <CRUDPageContext.Provider value={{ detail: mockDetail }}>
        <ViewAccessPolicy />
      </CRUDPageContext.Provider>,
    );

    expect(getByText('RULE_NAME')).toBeInTheDocument();
    expect(getByText('Test Policy')).toBeInTheDocument();
    expect(getByText('ASSIGNED_API_CLIENTS')).toBeInTheDocument();
    expect(getByText('Client1, Client2')).toBeInTheDocument();
    expect(getByText('CRITERIA_IF')).toBeInTheDocument();
    expect(getByText('Criteria Explanation')).toBeInTheDocument();
    expect(getByText('RULE_ACTION_THEN')).toBeInTheDocument();
    expect(getByText('Allow')).toBeInTheDocument();
  });
});
