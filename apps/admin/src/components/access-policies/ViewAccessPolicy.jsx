import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  selectDaysOfWeekEnums,
  selectFrequenciesEnums,
  selectOperationsEnums,
  selectTimeZonesEnums,
  selectWeeksOfMonthEnums,
} from '../../ducks/access-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getPolicyCriteriaExplination } from './helper';

const ViewAccessPolicy = () => {
  const { t } = useTranslation();

  const operationsList = useSelector(selectOperationsEnums);
  const frequenciesList = useSelector(selectFrequenciesEnums);
  const timeZonesList = useSelector(selectTimeZonesEnums);
  const daysOfWeekList = useSelector(selectDaysOfWeekEnums);
  const weeksOfMonthList = useSelector(selectWeeksOfMonthEnums);

  const { detail } = useContext(CRUDPageContext);

  const { name, apiClients, accessPolicyCriteria, ruleAction } = detail;

  return (
    <div className="view-access-policy-container">
      <div className="is-flex detail-container">
        <p className="label">{t('RULE_NAME')}</p>
        <p className="details">{name}</p>
      </div>

      <div className="is-flex detail-container">
        <p className="label">{t('ASSIGNED_API_CLIENTS')}</p>
        <p className="details">{apiClients?.map?.((clients) => clients.name).join?.(', ')}</p>
      </div>

      <div className="is-flex detail-container">
        <p className="label">{t('CRITERIA_IF')}</p>
        <p className="details">
          {getPolicyCriteriaExplination(accessPolicyCriteria, t, {
            operationsList,
            frequenciesList,
            timeZonesList,
            daysOfWeekList,
            weeksOfMonthList,
          })}
        </p>
      </div>

      <div className="is-flex detail-container">
        <p className="label">{t('RULE_ACTION_THEN')}</p>
        <p className="details">{ruleAction}</p>
      </div>
    </div>
  );
};

export default ViewAccessPolicy;
