import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Input, mergeFormValues } from '@zscaler/zui-component-library';

import {
  selectDaysOfWeekEnums,
  selectFrequenciesEnums,
  selectOperationsEnums,
  selectTimeZonesEnums,
  selectWeeksOfMonthEnums,
} from '../../ducks/access-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getPolicyCriteriaExplination } from './helper';

const SummarySection = () => {
  const { t } = useTranslation();

  const operationsList = useSelector(selectOperationsEnums);
  const frequenciesList = useSelector(selectFrequenciesEnums);
  const timeZonesList = useSelector(selectTimeZonesEnums);
  const daysOfWeekList = useSelector(selectDaysOfWeekEnums);
  const weeksOfMonthList = useSelector(selectWeeksOfMonthEnums);

  const { detail, setDetail } = useContext(CRUDPageContext);

  const { name, apiClients, accessPolicyCriteria, ruleAction } = detail || {};

  const onRuleChange = (evt) => {
    const newRuleName = mergeFormValues(evt)();

    setDetail((prevState) => ({ ...prevState, ...newRuleName }));
  };

  const renderBasicInformationSection = () => {
    return (
      <>
        <div>
          <h3>{t('BASIC_INFORMATION')}</h3>
        </div>
        <div style={{ marginLeft: '16px' }}>
          <Input
            label="RULE_NAME"
            name="name"
            onChange={onRuleChange}
            value={name}
            maxLength="128"
            containerStyle={{ maxWidth: '350px' }}
          />
        </div>
      </>
    );
  };

  const renderAssignApiClientsSection = () => {
    return (
      <>
        <div>
          <h3>{t('ASSIGNED_API_CLIENTS')}</h3>
        </div>
        <div>{apiClients?.map?.((clients) => clients.name).join?.(', ')}</div>
      </>
    );
  };

  const renderCriteriaSection = () => {
    return (
      <>
        <div>
          <h3>Criteria and Rule Action</h3>
        </div>

        <div className="criteria-rule-column">
          <div className="is-flex">
            <p className="label">{t('IF')}</p>
            <p className="details">
              {getPolicyCriteriaExplination(accessPolicyCriteria, t, {
                operationsList,
                frequenciesList,
                timeZonesList,
                daysOfWeekList,
                weeksOfMonthList,
              })}
            </p>
          </div>

          <div className="is-flex">
            <p className="label">{t('THEN')}</p>
            <p className="details">{ruleAction}</p>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="summary-section-container">
      {renderBasicInformationSection()}
      {renderAssignApiClientsSection()}
      {renderCriteriaSection()}
    </div>
  );
};

export default SummarySection;
