import { I18nextProvider } from 'react-i18next';
import { Provider } from 'react-redux';

import { render } from '@testing-library/react';

import configureStore from 'redux-mock-store';

// Assuming you have an i18n configuration file
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import i18n from '../../i18n';
import SummarySection from './SummarySection';

const mockStore = configureStore([]);

describe('SummarySection', () => {
  let store;
  let detail;
  let setDetail;

  beforeEach(() => {
    store = mockStore({
      accessPolicies: {
        operationsEnums: [],
        frequenciesEnums: [],
        timeZonesEnums: [],
        daysOfWeekEnums: [],
        occurrencesEnums: [],
      },
    });

    detail = {
      name: 'Test Rule',
      apiClients: [{ name: 'Client1' }, { name: 'Client2' }],
      accessPolicyCriteria: {},
      ruleAction: 'Allow',
    };

    setDetail = jest.fn();
  });

  const renderComponent = () =>
    render(
      <Provider store={store}>
        <I18nextProvider i18n={i18n}>
          <CRUDPageContext.Provider value={{ detail, setDetail }}>
            <SummarySection />
          </CRUDPageContext.Provider>
        </I18nextProvider>
      </Provider>,
    );

  it('should render basic information section', () => {
    const { getByText, getByLabelText } = renderComponent();
    expect(getByText('BASIC_INFORMATION')).toBeInTheDocument();
    expect(getByLabelText('RULE_NAME')).toBeInTheDocument();
    expect(getByLabelText('RULE_NAME').value).toBe('Test Rule');
  });

  it('should render assigned API clients section', () => {
    const { getByText } = renderComponent();
    expect(getByText('ASSIGNED_API_CLIENTS')).toBeInTheDocument();
    expect(getByText('Client1, Client2')).toBeInTheDocument();
  });

  it('should render criteria and rule action section', () => {
    const { getByText } = renderComponent();
    expect(getByText('Criteria and Rule Action')).toBeInTheDocument();
    expect(getByText('IF')).toBeInTheDocument();
    expect(getByText('THEN')).toBeInTheDocument();
    expect(getByText('Allow')).toBeInTheDocument();
  });
});
