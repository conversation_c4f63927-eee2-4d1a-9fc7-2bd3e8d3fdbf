import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { fireEvent, render, screen } from '@testing-library/react';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import AccessPolicyTable from './AccessPolicyTable';

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('@zscaler/zui-component-library', () => ({
  Actions: jest.fn(({ onEditClick, onDeleteClick, onAddUsersClick }) => (
    <div>
      <button onClick={() => onEditClick({ id: 1 })}>Edit</button>
      <button onClick={() => onDeleteClick({ id: 1 })}>Delete</button>
      <button onClick={() => onAddUsersClick({ id: 1 })}>View</button>
    </div>
  )),
  StatusTag: jest.fn(({ value }) => <div>{value ? 'ACTIVE' : 'EXPIRED'}</div>),
  TableContainer: jest.fn(({ columns, data, pagination }) => (
    <div>
      {columns.map((col) => (
        <div key={col.id}>{col.id}</div>
      ))}
      {data.map((row, index) => (
        <div key={index}>{row.name}</div>
      ))}
      <button onClick={pagination.onLoadMoreClick}>Load More</button>
    </div>
  )),
  TextWithTooltip: jest.fn(({ children }) => <div>{children}</div>),
  useApiCall: jest.fn(() => ({ apiCall: jest.fn() })),
}));

const mockContextValue = {
  setModalMode: jest.fn(),
  setDetail: jest.fn(),
  isFormReadOnly: false,
};

describe('AccessPolicyTable', () => {
  beforeEach(() => {
    useTranslation.mockReturnValue({ t: (key) => key });
    useSelector.mockImplementation((selector) => {
      switch (selector) {
        case expect.any(Function):
          return [];
        default:
          return {};
      }
    });
  });

  it('should render table columns', () => {
    render(
      <CRUDPageContext.Provider value={mockContextValue}>
        <AccessPolicyTable />
      </CRUDPageContext.Provider>,
    );

    expect(screen.getByText('criteria')).toBeInTheDocument();
    expect(screen.getByText('action')).toBeInTheDocument();
    expect(screen.getByText('status')).toBeInTheDocument();
    expect(screen.getByText('actions')).toBeInTheDocument();
  });

  it('should call setModalMode with "edit" when edit button is clicked', () => {
    render(
      <CRUDPageContext.Provider value={mockContextValue}>
        <AccessPolicyTable />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('Edit'));
    expect(mockContextValue.setModalMode).toHaveBeenCalledWith('edit');
  });

  it('should call setModalMode with "delete" when delete button is clicked', () => {
    render(
      <CRUDPageContext.Provider value={mockContextValue}>
        <AccessPolicyTable />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('Delete'));
    expect(mockContextValue.setModalMode).toHaveBeenCalledWith('delete');
  });

  it('should call setModalMode with "view" when view button is clicked', () => {
    render(
      <CRUDPageContext.Provider value={mockContextValue}>
        <AccessPolicyTable />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('View'));
    expect(mockContextValue.setModalMode).toHaveBeenCalledWith('view');
  });

  it('should call onLoadMoreClick when load more button is clicked', () => {
    render(
      <CRUDPageContext.Provider value={mockContextValue}>
        <AccessPolicyTable />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('Load More'));
    expect(screen.getByText('Load More')).toBeInTheDocument();
  });
});
