import { render, screen } from '@testing-library/react';

import AccessPolicyForm from './AccessPolicyForm';

describe('AccessPolicyForm', () => {
  test('should render Define Criteria step header', () => {
    render(<AccessPolicyForm mode="add" />);

    const stepHeader = screen.getByText('Define Criteria');
    expect(stepHeader).toBeInTheDocument();
  });

  test('should render Assign API Clients step header', () => {
    render(<AccessPolicyForm mode="add" />);

    const nextButton = screen.getByText('NEXT');
    nextButton.click();

    const stepHeader = screen.getByText('Assign API Clients');
    expect(stepHeader).toBeInTheDocument();
  });

  test('should render Summary step header', () => {
    render(<AccessPolicyForm mode="add" />);

    const nextButton = screen.getByText('NEXT');
    nextButton.click();
    nextButton.click();

    const stepHeader = screen.getByText('Summary');
    expect(stepHeader).toBeInTheDocument();
  });
});
