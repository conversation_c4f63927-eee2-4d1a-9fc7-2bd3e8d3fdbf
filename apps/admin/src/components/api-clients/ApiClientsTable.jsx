import { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import { Actions, StatusTag, TableContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/api-clients';
import { selectTableConfig, selectTableDetail } from '../../ducks/api-clients/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiClientsTable = () => {
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, searchTerm, isFormReadOnly } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            editIcon={isFormReadOnly ? faEye : faPencilAlt}
            showDelete={!isFormReadOnly}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { status } = props?.row?.original || {};

          return (
            <StatusTag
              value={status}
              truthyLabel="ACTIVE"
              falsyLabel="INACTIVE"
              type="ENABLED_DISABLED"
            />
          );
        };

        columnDetail.cell = StatusCellComponent;
      }

      return columnDetail;
    });

    if (isFormReadOnly) {
      return [...(tableConfig?.columns || [])];
    }

    return [
      // {
      //   id: 'selection',
      //   Header: '',
      //   cell: (props) => <Selector {...props} />,
      //   size: 70,
      //   minSize: 70,
      //   enableResizing: false,
      //   disableSortBy: true,
      //   defaultCanSort: false,
      // },
      ...(tableConfig?.columns || []),
    ];
  }, [tableConfig?.columns, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default ApiClientsTable;
