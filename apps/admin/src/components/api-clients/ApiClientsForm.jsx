import { createContext, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Tab,
  Tabs,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
} from '@zscaler/zui-component-library';

import PropTypes from 'prop-types';

import { selectDefaultApiResourceDetail } from '../../ducks/api-resources/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import ResourcesForm from '../api-clients-resources/ResourcesForm';
import ApiClientsInfoSection from './ApiCientsInfoSection';
import ApiClientsAuthSection from './ApiClientsAuthSection';
import TABS from './constants';
import {
  STATUS,
  VALIDATION_TYPE,
  getFormValidationDetail,
  getScopeAndRadioButtonStatus,
} from './helper';

const defaultProps = {
  ...defaultFormProps,
};

export const ApiClientsFormContext = createContext({});

const ApiClientsForm = ({
  mode,
  validationDetail,
  setValidationDetail,
  updateClientSecretMapping,
  updateRadioButtonStatus,
}) => {
  const { t } = useTranslation();

  const { detail, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const [formValues, setFormValues] = useState({
    accessTokenLifeTimeParsedValue: detail?.accessTokenLifeTime
      ? parseInt(detail?.accessTokenLifeTime) / 60
      : '',
    clientName: '',
    clientAuthentication: {
      authType: '',
      clientSecrets: [],
      clientJWKsUrl: '',
      clientCertificates: [],
      publicKeys: {},
    },
    clientCertificates: detail?.clientAuthentication?.clientCertificates
      ? detail?.clientAuthentication.clientCertificates
      : [],
    publicKeys: detail?.clientAuthentication?.publicKeys
      ? detail?.clientAuthentication?.publicKeys
      : [],
    statusValue: detail?.status ? STATUS.ENABLED : STATUS.DISABLED,
    validationType: detail?.clientAuthentication?.authType
      ? detail?.clientAuthentication?.authType
      : VALIDATION_TYPE.CLIENT_JWK,
    jwksUrl: detail?.clientAuthentication?.clientJWKsUrl
      ? detail?.clientAuthentication?.clientJWKsUrl
      : '',
    clientSecrets: detail?.clientAuthentication?.clientSecrets
      ? detail?.clientAuthentication?.clientSecrets
      : [],
    ...detail,
  });

  const [selectedTab, setSelectedTab] = useState(TABS.CLIENT);
  const [showScopes, setShowScopes] = useState({});
  const [radioButtonStatus, setRadioButtonStatus] = useState({});
  const [selectedClientSecretMapping, setSelectedClientSecretMapping] = useState(() => {
    const clientSecrets = [];

    if (formValues?.clientSecrets?.length > 0) {
      formValues?.clientSecrets?.forEach((secret, idx) => {
        secret = {
          ...secret,
          createdId: 'secret' + idx,
        };
        clientSecrets.push(secret);
      });
    }

    return clientSecrets;
  });
  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  const resourcesList = useSelector(selectDefaultApiResourceDetail);

  useEffect(() => {
    const { radioButtonStatus, showScopes } = getScopeAndRadioButtonStatus({
      clientResources: detail?.clientResources || [],
      resourcesList,
    });

    setRadioButtonStatus(radioButtonStatus);
    setShowScopes(showScopes);
  }, []);

  useEffect(() => {
    setDetail(formValues);

    const formValidationDetail = getFormValidationDetail({
      formValues,
      mode,
      selectedClientSecretMapping,
    });

    setValidationDetail(formValidationDetail);
    updateClientSecretMapping(selectedClientSecretMapping);
    updateRadioButtonStatus(radioButtonStatus);
  }, [formValues, selectedClientSecretMapping, radioButtonStatus]);

  const clientInformationSection = () => {
    return (
      <ApiClientsInfoSection
        formValues={formValues}
        setFormValues={setFormValues}
        onFormFieldChange={onFormFieldChange}
        isFormReadOnly={isFormReadOnly}
        validationDetail={validationDetail}
      />
    );
  };

  const clientAuthenticationSection = () => {
    return (
      <ApiClientsAuthSection
        formValues={formValues}
        setFormValues={setFormValues}
        onFormFieldChange={onFormFieldChange}
        validationDetail={validationDetail}
        isFormReadOnly={isFormReadOnly}
        selectedClientSecretMapping={selectedClientSecretMapping}
        setSelectedClientSecretMapping={setSelectedClientSecretMapping}
      />
    );
  };

  const renderClientSection = () => {
    if (selectedTab === TABS.CLIENT) {
      return (
        <>
          <section className="typography-paragraph1-uppercase">{t('CLIENT_INFORMATION')}</section>
          {clientInformationSection()}
          <section className="typography-paragraph1-uppercase">
            {t('CLIENT_AUTHENTICATION')}
          </section>
          {clientAuthenticationSection()}
        </>
      );
    }
    return null;
  };

  const renderResourcesSection = () => {
    if (selectedTab === 'RESOURCES') {
      return (
        <ResourcesForm
          resourcesList={resourcesList}
          showScopes={showScopes}
          setShowScopes={setShowScopes}
          radioButtonStatus={radioButtonStatus}
          setRadioButtonStatus={setRadioButtonStatus}
          isFormReadOnly={isFormReadOnly}
        />
      );
    }
  };

  return (
    <>
      <Tabs>
        <Tab
          label={TABS.CLIENT}
          isActive={selectedTab === TABS.CLIENT}
          onClick={() => {
            setSelectedTab(TABS.CLIENT);
          }}
        />
        <Tab
          label={TABS.RESOURCES}
          isActive={selectedTab === TABS.RESOURCES}
          onClick={() => {
            setSelectedTab(TABS.RESOURCES);
          }}
        />
      </Tabs>
      {renderClientSection()}
      {renderResourcesSection()}
    </>
  );
};

ApiClientsForm.defaultProps = defaultProps;

ApiClientsForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.bool };

export default ApiClientsForm;
