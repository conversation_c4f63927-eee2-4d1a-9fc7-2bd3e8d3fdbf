import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

import { faTriangleExclamation, faUpload } from '@fortawesome/free-solid-svg-icons';
import { faPlus, faTrash } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  DropDown,
  Field,
  FieldGroup,
  Input,
  InputFile,
  RadioButtons,
  TextWithTooltip,
  useApiCall,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { cloneDeep, isEmpty, noop, remove, some } from 'lodash-es';
import PropTypes from 'prop-types';

import { getCertDetails } from '../../ducks/api-clients';
import { showErrorNotification } from '../../ducks/global';

import { getFormTooltipDetail } from './helper';
import {
  CLIENT_SECRET_EXPIRATION_DATES_LIST,
  VALIDATION_TYPE,
  VALIDATION_TYPE_LIST,
  generateRandomID,
  generateRandomString,
  validateCert,
} from './helper';

const ApiClientsAuthSection = ({
  formValues,
  setFormValues,
  onFormFieldChange,
  validationDetail,
  isFormReadOnly,
  selectedClientSecretMapping,
  setSelectedClientSecretMapping,
}) => {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const getValue = (idx, type) => {
    return selectedClientSecretMapping?.[idx]?.[type] || '';
  };
  const isCertificateLimitReached = () => {
    const { clientCertificates, publicKeys } = formValues;
    const clientCertificatesLength = clientCertificates.length;
    const publicKeysLength = publicKeys.length;

    return (
      clientCertificatesLength > 1 ||
      publicKeysLength > 1 ||
      (clientCertificatesLength === 1 && publicKeysLength === 1)
    );
  };

  const isValidationTypeRadioButtonSelected = (detail = {}) => {
    const { value } = detail;

    return formValues?.validationType === value;
  };

  const onAddMapping = () => {
    const secretDetail = {
      id: generateRandomID(),
      createdAt: dayjs().valueOf(),
      secretValue: generateRandomString(),
      expiresAt: dayjs().add(1, 'M').valueOf(),
      expirationLimit: [CLIENT_SECRET_EXPIRATION_DATES_LIST[0]],
    };
    setSelectedClientSecretMapping((prevState) => {
      return [...prevState, { ...secretDetail }];
    });
  };

  const onCertUpload = (detail) => {
    if (detail?.length > 0) {
      if (validateCert(detail[0])) {
        const reader = new FileReader();
        reader.onload = function (event) {
          const fileContent = event.target.result;
          if (fileContent.includes('CERTIFICATE')) {
            apiCall(getCertDetails(detail[0]))
              .then((data) => {
                setFormValues((prevState) => {
                  return {
                    ...prevState,
                    clientCertificates: [...(formValues?.clientCertificates || []), data],
                  };
                });
              })
              .catch(() => {
                dispatch(showErrorNotification({ message: 'FETCHING_CERTIFICATE_DETAILS_FAILED' }));
              });
          } else if (!fileContent.includes('PRIVATE')) {
            setFormValues((prevState) => {
              return {
                ...prevState,
                publicKeys: [
                  ...(formValues?.publicKeys || []),
                  {
                    keyName: detail[0].name,
                    keyValue: fileContent,
                  },
                ],
              };
            });
          } else {
            dispatch(
              showErrorNotification({
                message: 'Uploaded cert must not be a private key',
              }),
            );
          }
        };
        reader.readAsText(detail[0]);
      }
    } else {
      dispatch(
        showErrorNotification({
          message: 'Must be a valid cert or a key file',
        }),
      );
    }
  };

  const onDeleteCertificate = (removeIdx) => {
    setFormValues((prevState) => {
      return {
        ...prevState,
        clientCertificates: [
          ...remove(formValues?.clientCertificates, (_, idx) => idx !== removeIdx),
        ],
      };
    });
  };
  const onDeleteMapping = (removeIdx) => {
    setSelectedClientSecretMapping((prevState) => {
      const newAttributeMappings = remove(prevState, (_, idx) => idx !== removeIdx);

      return newAttributeMappings;
    });
  };

  const onDeletePublicKey = (removeIdx) => {
    setFormValues((prevState) => {
      return {
        ...prevState,
        publicKeys: [...remove(formValues?.publicKeys, (_, idx) => idx !== removeIdx)],
      };
    });
  };

  const onExpirationSelection = (detail, idx) => {
    const attributeMapping = cloneDeep(selectedClientSecretMapping[idx]) || {};

    attributeMapping.expirationLimit = detail;
    attributeMapping.expiresAt = dayjs(attributeMapping?.createdAt)
      .add(detail[0]?.value || 2592000, 'second')
      .valueOf();

    setSelectedClientSecretMapping((prevState) => {
      prevState[idx] = attributeMapping;
      return [...prevState];
    });
  };

  const onValidationTypeButtonClick = (detail = {}) => {
    const { value } = detail;
    setFormValues((prevState) => {
      const validationType = value;

      return { ...prevState, validationType };
    });
  };

  return (
    <Card containerClass="client-authentication-section">
      <Field
        label="VALIDATION_TYPE"
        containerClass="radio-container"
        tooltip={isFormReadOnly ? {} : getFormTooltipDetail('validationType')}
      >
        <RadioButtons
          list={VALIDATION_TYPE_LIST}
          isSelected={isValidationTypeRadioButtonSelected}
          onClick={onValidationTypeButtonClick}
          containerClass={isFormReadOnly ? 'disabled' : ''}
        />
      </Field>
      {formValues?.validationType === VALIDATION_TYPE.CLIENT_JWK && (
        <FieldGroup>
          <Input
            label="CLIENT_JWK"
            name="jwksUrl"
            onChange={onFormFieldChange}
            value={formValues.jwksUrl}
            maxLength="128"
            info={validationDetail}
            tooltip={isFormReadOnly ? {} : getFormTooltipDetail('jwksUrl')}
            disabled={isFormReadOnly}
          />
        </FieldGroup>
      )}
      {formValues?.validationType === VALIDATION_TYPE.CERTIFICATE_PUBLIC_KEY && (
        <>
          <>
            {(!isEmpty(formValues?.clientCertificates) || !isEmpty(formValues?.publicKeys)) && (
              <div className="is-flex full-width" style={{ marginBottom: '-16px' }}>
                <Input
                  containerClass="certificate-input"
                  value={t('CERTIFICATES_AND_PUBLIC_KEYS')}
                  disabled
                />
                <Input containerClass="certificate-input" value={t('EXPIRES')} disabled />
              </div>
            )}
            {formValues?.clientCertificates.map((certificate, idx) => {
              return (
                <div className="is-flex full-width" style={{ marginBottom: '-3px' }} key={idx}>
                  <Input
                    containerClass="certificate-input certificate-name"
                    value={certificate.fileName}
                    name={`attr-${certificate.fileName}`}
                    disabled
                  />
                  <Field containerClass="certificate-input certificate-value">
                    <Input
                      value={certificate.expiresOn}
                      name={`attr-${certificate.expiresOn}`}
                      disabled
                    />
                    <Button
                      type="tertiary"
                      containerClass="content-width no-p-r"
                      containerStyle={{ padding: '0', marginLeft: '5px' }}
                      onClick={() => onDeleteCertificate(idx)}
                      disabled={isFormReadOnly}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  </Field>
                </div>
              );
            })}
            {formValues?.publicKeys.map((publicKey, idx) => {
              return (
                <div className="is-flex full-width" style={{ marginBottom: '-3px' }} key={idx}>
                  <Input
                    containerClass="certificate-input certificate-name"
                    value={publicKey.keyName}
                    name={`attr-${publicKey.keyName}`}
                    disabled
                  />
                  <Field containerClass="certificate-input certificate-value">
                    <Input value={t('NEVER')} name={`attr-expireson`} disabled />
                    <Button
                      type="tertiary"
                      containerClass="content-width no-p-r"
                      containerStyle={{ padding: '0', marginLeft: '5px' }}
                      onClick={() => onDeletePublicKey(idx)}
                      disabled={isFormReadOnly}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  </Field>
                </div>
              );
            })}
          </>
          {!isCertificateLimitReached() && !isFormReadOnly && (
            <FieldGroup>
              <Field containerClass="certificate-upload-button">
                <InputFile
                  name="certificates"
                  onChange={isCertificateLimitReached() ? noop : onCertUpload}
                  info={validationDetail}
                  leftIcon={faUpload}
                  hasLeftIcon
                  buttonLabel="UPLOAD"
                  buttonType="primary"
                  fileInfoPosition={'right'}
                />
              </Field>
            </FieldGroup>
          )}
        </>
      )}
      {formValues?.validationType === VALIDATION_TYPE.SECRET && (
        <>
          {selectedClientSecretMapping?.length > 0 && (
            <div className="is-flex full-width secret-mapping" style={{ marginBottom: '-16px' }}>
              <Input
                containerClass="certificate-input secret-id"
                value={t('TABLE_NUMBER')}
                disabled
              />
              <Input
                containerClass="certificate-input secret-created-at"
                value={t('CREATED_ON')}
                disabled
              />
              <Field containerClass="certificate-input secret-value">
                <TextWithTooltip text={t('SECRET')}>
                  <span> {t('SECRET')} </span>
                </TextWithTooltip>
              </Field>
              <Input
                containerClass="certificate-input secret-expires-at"
                value={t('EXPIRES_ON')}
                disabled
              />
            </div>
          )}
          {selectedClientSecretMapping.map((_, idx) => {
            let expiryDate = getValue(idx, 'expiresAt') || 0;
            expiryDate = dayjs(expiryDate).format('MMMM DD, YYYY');

            let createdDate = getValue(idx, 'createdAt') || 0;
            createdDate = dayjs(createdDate).format('MMMM DD, YYYY');

            const createdId = getValue(idx, 'createdId');
            return (
              <div
                key={idx}
                className="is-flex full-width secret-mapping"
                style={{ marginBottom: '-3px' }}
              >
                <Input
                  containerClass="certificate-input secret-id"
                  value={idx + 1}
                  name={`attrId-${idx}`}
                  tooltip={isFormReadOnly ? {} : getFormTooltipDetail('id')}
                  disabled
                />
                <Input
                  containerClass="certificate-input secret-created-at"
                  value={createdDate}
                  name={`attrCreatedAt-${idx}`}
                  tooltip={isFormReadOnly ? {} : getFormTooltipDetail('createdAt')}
                  disabled
                />
                {createdId && (
                  <Input
                    containerClass="certificate-input secret-value"
                    type={'password'}
                    value={'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'}
                    name={`attrSecret-${idx}`}
                    tooltip={isFormReadOnly ? {} : getFormTooltipDetail('secret')}
                    disabled
                  />
                )}
                {!createdId && (
                  <Field containerClass="certificate-input secret-value">
                    <TextWithTooltip text={getValue(idx, 'secretValue')} canCopy showCopy>
                      {getValue(idx, 'secretValue')}
                    </TextWithTooltip>
                  </Field>
                )}
                <Field containerClass="certificate-input secret-expires-at">
                  {!createdId ? (
                    <DropDown
                      list={CLIENT_SECRET_EXPIRATION_DATES_LIST}
                      selectedList={getValue(idx, 'expirationLimit')}
                      onSelection={(detail) => onExpirationSelection(detail, idx)}
                    />
                  ) : (
                    <Input
                      value={expiryDate}
                      name={`attrExpiresAt-${idx}`}
                      tooltip={isFormReadOnly ? {} : getFormTooltipDetail('expiresAt')}
                      disabled
                    />
                  )}

                  <Button
                    type="tertiary"
                    containerClass="content-width no-p-r"
                    containerStyle={{ padding: '0', marginLeft: '5px' }}
                    onClick={() => onDeleteMapping(idx)}
                    disabled={isFormReadOnly}
                  >
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                </Field>
              </div>
            );
          })}

          {some(selectedClientSecretMapping, (secret) => isEmpty(secret.createdId)) && (
            <div
              className="is-flex full-width secret-mapping has-color-warning"
              style={{ marginBottom: '-3px' }}
            >
              <TextWithTooltip text={t('CLIENT_SECRET_WARNING')}>
                <FontAwesomeIcon
                  icon={faTriangleExclamation}
                  className="icon right has-color-warning"
                />
                <span className="has-color-warning"> {t('CLIENT_SECRET_WARNING')} </span>
              </TextWithTooltip>
            </div>
          )}

          {!isFormReadOnly && selectedClientSecretMapping?.length < 2 && (
            <div className="is-flex has-jc-e">
              <Button
                type="tertiary"
                containerClass="no-p-r"
                containerStyle={{ marginTop: '10px' }}
                onClick={onAddMapping}
              >
                <FontAwesomeIcon icon={faPlus} className="icon left" />
                {t('ADD')}
              </Button>
            </div>
          )}
        </>
      )}
    </Card>
  );
};

ApiClientsAuthSection.propTypes = {
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  isFormReadOnly: PropTypes.bool,
  validationDetail: PropTypes.object,
  setFormValues: PropTypes.func,
  selectedClientSecretMapping: PropTypes.object,
  setSelectedClientSecretMapping: PropTypes.func,
};
export default ApiClientsAuthSection;
