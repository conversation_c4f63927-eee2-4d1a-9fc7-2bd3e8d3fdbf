import { useContext } from 'react';

import { HelpContainer } from '@zscaler/zui-component-library';

import { HELP_ARTICLES } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiClientsHelpContainer = () => {
  const { modalMode } = useContext(CRUDPageContext);

  let src = HELP_ARTICLES.API_CLIENTS;

  if (modalMode === 'add') {
    src = HELP_ARTICLES.API_CLIENTS_ADD;
  } else if (modalMode === 'edit' || modalMode === 'delete') {
    src = HELP_ARTICLES.API_CLIENTS_EDIT_OR_DELETE;
  } else {
    src = HELP_ARTICLES.API_CLIENTS;
  }

  return <HelpContainer src={src} />;
};

export default ApiClientsHelpContainer;
