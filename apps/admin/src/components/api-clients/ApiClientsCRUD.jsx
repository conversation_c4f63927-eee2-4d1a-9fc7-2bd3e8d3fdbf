import { useContext, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import {
  Button,
  CRUDModal,
  getApiDELETENotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import ApiClientsForm from '../../components/api-clients/ApiClientsForm';
import { getSanitizedApiPayload, modalModeDetail } from '../../components/api-clients/helper';

import { add, remove, update } from '../../ducks/api-clients';
import { selectResourceScopeMap } from '../../ducks/api-resources/selectors';
import { showSuccessNotification } from '../../ducks/global';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiClientsCRUD = () => {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultModalMode,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const [clientSecretMapping, setClientSecretMapping] = useState([]);
  const [radioButtonStatus, setRadioButtonStatus] = useState({});
  const resourceScopeMap = useSelector(selectResourceScopeMap);

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(
        add(
          getSanitizedApiPayload({
            detail,
            clientSecretMapping,
            radioButtonStatus,
            resourceScopeMap,
          }),
        ),
      )
        .then((response) => {
          dispatch(onCloseClick);
          if (response?.clientId) {
            dispatch(
              showSuccessNotification({
                message: `API Client created successfully with the client ID - ${response?.clientId}`,
                autoHide: false,
              }),
            );
          }
        })
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(
        update(
          getSanitizedApiPayload({
            detail,
            clientSecretMapping,
            radioButtonStatus,
            resourceScopeMap,
          }),
        ),
        {
          successNotificationPayload: { ...getApiPUTNotificationOptions() },
        },
      )
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const cancelText = useMemo(() => {
    return modalMode === 'view' ? 'CLOSE' : 'CANCEL';
  }, [modalMode]);

  const viewTokens = () => {
    navigate('/admin/tokens', { state: { clientId: detail?.clientId } });
  };

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) => (
        <>
          <ApiClientsForm
            {...props}
            updateClientSecretMapping={setClientSecretMapping}
            updateRadioButtonStatus={setRadioButtonStatus}
          />
          {detail?.clientId && (
            <Button
              containerStyle={{ position: 'absolute', top: '11px', right: '30px' }}
              type="tertiary"
              onClick={() => {
                viewTokens();
              }}
            >
              {t('VIEW_TOKENS')}
            </Button>
          )}
        </>
      )}
      saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
      showSave={hasFullAccess}
      cancelText={cancelText}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default ApiClientsCRUD;
