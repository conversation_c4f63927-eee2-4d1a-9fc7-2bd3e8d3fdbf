import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faCheckCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  FieldGroup,
  Input,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  TextWithTooltip,
  mergeFormValues,
  useApiCall,
  validEmail,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { changeEmail, verifyEmail } from '../../ducks/profile';

const defaultProps = {
  show: false,
  detail: {},
  onCLoseClick: noop,
};

const ChangeEmailModal = ({ show, detail, onCloseClick }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const [formStep, setFormStep] = useState(1);

  const [formValues, setFormValues] = useState({ ...detail, code: '' });

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...detail }));
  }, [detail]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  const onValidateEmailClick = () => {
    apiCall(changeEmail(formValues.primaryEmail))
      ?.then?.(() => {
        setFormStep(2);
      })
      ?.catch?.(noop);
  };

  const onVerifyClick = () => {
    apiCall(verifyEmail(formValues.code))
      ?.then?.(() => {
        setFormStep(3);
      })
      ?.catch?.(noop);
  };

  const onResendCodeClick = () => {
    apiCall(changeEmail(formValues.primaryEmail))?.catch?.(noop);
  };

  const renderBodySection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('UPDATE_EMAIL')}</section>

        <Card>
          <div className="is-flex full-width">
            <FieldGroup containerClass="has-width-auto">
              <Input
                label="NEW_EMAIL"
                name="primaryEmail"
                onChange={onFormFieldChange}
                value={formValues.primaryEmail}
                disabled={formStep !== 1}
              />
            </FieldGroup>
            <div className="is-flex has-ai-c actions-info-section">
              {formStep === 1 ? (
                <Button
                  onClick={onValidateEmailClick}
                  disabled={validEmail(formValues.primaryEmail)}
                  containerStyle={{ minWidth: 'unset' }}
                >
                  {t('VALIDATE_EMAIL')}
                </Button>
              ) : null}
              {formStep === 2 ? (
                <div className="info">
                  We have sent the validation code to{' '}
                  <TextWithTooltip>{formValues.primaryEmail}.</TextWithTooltip>
                </div>
              ) : null}
            </div>
          </div>

          {formStep !== 1 ? (
            <div className="is-flex full-width">
              <FieldGroup containerClass="has-width-auto">
                <Input
                  label="VALIDATION_CODE"
                  name="code"
                  onChange={onFormFieldChange}
                  value={formValues.code}
                  disabled={formStep === 3}
                />
              </FieldGroup>
              <div className="is-flex has-ai-c actions-info-section">
                {formStep === 2 ? (
                  <>
                    <Button onClick={onVerifyClick} disabled={!formValues.code}>
                      {t('VERIFY')}
                    </Button>
                    <div className="is-flex has-ai-c info">
                      <div>Did not receive?</div>
                      <Button type="tertiary" containerClass="no-p-l" onClick={onResendCodeClick}>
                        {t('RESEND_CODE')}
                      </Button>
                    </div>
                  </>
                ) : null}
                {formStep === 3 ? (
                  <div className="is-flex has-ai-c info">
                    <span className="text success">
                      <FontAwesomeIcon
                        icon={faCheckCircle}
                        className="icon left has-color-success"
                      />
                    </span>
                    <div className="info">
                      <TextWithTooltip>{formValues.primaryEmail}</TextWithTooltip> has been verified
                      successfully.
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          ) : null}
        </Card>
      </>
    );
  };

  return (
    <Modal show={show} onEscape={onCloseClick} containerClass="change-email-modal-container">
      <ModalHeader text="EDIT_EMAIL" onClose={onCloseClick} />
      <ModalBody>{renderBodySection()}</ModalBody>
      <ModalFooter
        saveText="DONE"
        isSaveDisabled={formStep !== 3}
        onSave={onCloseClick}
        onCancel={onCloseClick}
      />
    </Modal>
  );
};

ChangeEmailModal.defaultProps = defaultProps;

ChangeEmailModal.propTypes = {
  show: PropTypes.bool,
  detail: PropTypes.object,
  onCloseClick: PropTypes.func,
};

export default ChangeEmailModal;
