import { useSelector } from 'react-redux';

import PropTypes from 'prop-types';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import Menu from './Menu';

const defaultProps = {
  navGroup: [],
};

const NavContainer = ({ navGroup }) => {
  return (
    <>
      {navGroup?.map((navs, idx) => (
        <div key={idx} className="nav-group">
          {navs?.map((nav, idx) => {
            if (nav.page) {
              const { noAccess } = useSelector(selectPermissionsByKey(nav.page));

              if (noAccess) {
                return null;
              }
            }
            return <Menu key={`${idx}-${nav.linkTo}`} {...nav} />;
          })}
        </div>
      ))}
    </>
  );
};

NavContainer.defaultProps = defaultProps;

NavContainer.propTypes = {
  navGroup: PropTypes.arrayOf(Object),
};

export default NavContainer;
