import { createContext, useEffect, useMemo, useState } from 'react';

import { getCalendarDDList } from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import PropTypes from 'prop-types';

export const DashboardContext = createContext({});

export const CALENDAR_DD_LEVEL = 'ZIDENTITY_DASHBOARD';

const getDefaultTimeRange = ({ formatPattern }) => {
  const newList = getCalendarDDList({ formatPattern, level: CALENDAR_DD_LEVEL });

  const defaultTimeRange = newList[0] ? [newList[0]] : [];

  return defaultTimeRange;
};

const CALENDAR_CONFIG = {
  minDate: dayjs().subtract(6, 'M'),
  maxInterval: dayjs().diff(dayjs().subtract(6, 'M'), 'seconds'),
  message: { text: 'Data are avaiable for the last 6 Months' },
};

const DEFAULT_PROPS = {
  defaultCalendarConfig: {},
  formatPattern: 'MMM DD YYYY',
};

const DashboardContextProvider = ({
  defaultCalendarConfig = DEFAULT_PROPS.defaultCalendarConfig,
  formatPattern = DEFAULT_PROPS.formatPattern,
  children,
}) => {
  const calendarConfig = { ...CALENDAR_CONFIG, ...defaultCalendarConfig };

  const [selectedTenant, setSelectedTenant] = useState('');

  const [selectedTimeRange, setSelectedTimeRange] = useState(
    getDefaultTimeRange({ formatPattern }),
  );

  const getTimePayload = (selectedTimeRange) => {
    let { startTime, endTime, ...rest } = selectedTimeRange?.[0] || {};

    let selectedTime = { startTime, endTime, ...rest };

    if (startTime && endTime) {
      selectedTime.startTime = dayjs(startTime).unix();
      selectedTime.endTime = dayjs(endTime).unix();
    }

    return selectedTime;
  };

  const [timePayload, setTimePayload] = useState(getTimePayload(selectedTimeRange));

  useEffect(() => {
    setTimePayload(getTimePayload(selectedTimeRange));
  }, [selectedTimeRange]);

  const contextValue = useMemo(() => {
    return {
      selectedTenant,
      setSelectedTenant,
      selectedTimeRange,
      setSelectedTimeRange,
      calendarConfig,
      formatPattern,
      timePayload,
      setTimePayload,
    };
  }, [timePayload, selectedTenant]);

  return <DashboardContext.Provider value={contextValue}>{children}</DashboardContext.Provider>;
};

DashboardContextProvider.propTypes = {
  defaultCalendarConfig: PropTypes.object,
  formatPattern: PropTypes.string,
  children: PropTypes.node,
};

export default DashboardContextProvider;
