import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getGraphFlags } from '../../ducks/metrics';
import { selectGraphFlags } from '../../ducks/metrics/selectors';

import AuthenticationChartContainer from './AuthenticationChartContainer';
import DashboardCalendar from './DashboardCalendar';
import DeviceRegistrationChartContainer from './DeviceRegistrationChartContainer';
import ServiceAssignmentChartContainer from './ServiceAssignmentChartContainer';
import UserManangementChartContainer from './UserManangementChartContainer';
import UserManangementFailuresChartContainer from './UserManangementFailuresChartContainer';

const DashboardChartsContainer = () => {
  const { apiCall } = useApiCall();

  const {
    authentication_event,
    user_creation_event_success,
    user_creation_event_failure,
    service_assignment_event,
    device_registration_event,
  } = useSelector(selectGraphFlags);

  useEffect(() => {
    apiCall(getGraphFlags({})).catch(noop);
  }, []);

  return (
    <div className="is-flex has-fd-c" style={{ margin: '2rem 0', gap: '12px' }}>
      <div className="has-as-c">
        <DashboardCalendar />
      </div>

      {authentication_event && (
        <div className="is-flex">
          <AuthenticationChartContainer />
        </div>
      )}

      {(user_creation_event_success || user_creation_event_failure) && (
        <div className="is-flex has-jc-sa" style={{ gap: '12px' }}>
          {user_creation_event_success && <UserManangementChartContainer />}
          {user_creation_event_failure && <UserManangementFailuresChartContainer />}
        </div>
      )}

      {(service_assignment_event || device_registration_event) && (
        <div className="is-flex has-jc-sa" style={{ gap: '12px' }}>
          {service_assignment_event && <ServiceAssignmentChartContainer />}
          {device_registration_event && <DeviceRegistrationChartContainer />}
        </div>
      )}
    </div>
  );
};

export default DashboardChartsContainer;
