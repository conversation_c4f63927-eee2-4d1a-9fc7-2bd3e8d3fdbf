import React, { useEffect, useState } from 'react';

import PropTypes from 'prop-types';

import { OTP_LETTERS_LIMIT } from '../../config';

const inputsDefault = [...Array(OTP_LETTERS_LIMIT)]
  .map((x, index) => `otp${index + 1}`)
  .reduce((a, b) => Object.assign(a, { [b]: '' }), {});

const InputOTP = ({ submitEmailOTP, isInvalidOTP }) => {
  const [otpValue, setOTPValue] = useState(inputsDefault);
  const [showOTPMsg, setShowOTPMsg] = useState(true);

  const elemRefs = [];

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowOTPMsg(false);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // set focus to first input on render
    if (!showOTPMsg) {
      elemRefs[0]?.current?.focus();
    }
  }, [showOTPMsg]);

  const onInputChange = (event) => {
    if (!/^[0-9\b]+$/.test(event.target.value)) {
      event.target.value = '';
    }
  };

  const onInputOnPaste = (event) => {
    event.preventDefault();

    const pastedValue = event.clipboardData
      .getData('text/plain')
      .slice(0, OTP_LETTERS_LIMIT)
      .split('');
    // check pasted value length and it is only numbers
    const isValidValues =
      !pastedValue.filter((i) => {
        return !/[0-9]/gi.test(i);
      }).length > 0;

    if (pastedValue.length === OTP_LETTERS_LIMIT && isValidValues) {
      const pastedOTP = pastedValue.reduce((a, v, i) => ({ ...a, [`otp${i + 1}`]: v }), {});

      elemRefs.forEach((ref, index) => {
        elemRefs[index].current.value = pastedValue[index];
        elemRefs[index]?.current?.focus();
      });

      setOTPValue(pastedOTP);
    }
  };

  const onInputKeyUp = (event, index) => {
    const inputValue = event.target.value;
    const isValidInput = /[0-9]/gi.test(inputValue);
    const newOTPValue = {
      ...otpValue,
      [`otp${index + 1}`]: inputValue,
    };
    setOTPValue(newOTPValue);
    if (index < OTP_LETTERS_LIMIT - 1 && isValidInput) {
      elemRefs[index + 1].current.focus();
    }
    if (event.key === 'Backspace' && index > 0) {
      elemRefs[index - 1].current.focus();
    }
    if (index == OTP_LETTERS_LIMIT - 1 && isValidInput) {
      const OTP = Object.values(newOTPValue).join('');
      submitEmailOTP(OTP);
    }
  };

  const renderInput = (index) => {
    const ref = React.createRef();
    elemRefs.push(ref);

    return (
      <div key={`${index + 2}`} className="is-flex">
        <input
          type="text"
          className={`otp-input border ${isInvalidOTP ? 'has-error' : ''} text-center`}
          data-index={index}
          name={`otp${index + 1}`}
          ref={ref}
          maxLength={1}
          autoComplete="off"
          placeholder=""
          key={index}
          onKeyUp={(e) => onInputKeyUp(e, index)}
          onChange={(e) => onInputChange(e)}
          onPaste={(e) => onInputOnPaste(e)}
        />
      </div>
    );
  };

  return (
    <div className="otp-input-container">
      {Array.from({ length: OTP_LETTERS_LIMIT }, (element, index) => renderInput(index))}
    </div>
  );
};

InputOTP.propTypes = {
  isEmailOTPSent: PropTypes.bool,
  isEmailVerified: PropTypes.bool,
  primaryEmail: PropTypes.string,
  submitEmailOTP: PropTypes.func,
  isInvalidOTP: PropTypes.bool,
};

export default InputOTP;
