export const APP_ID = 'iam-admin-app';

export const PORTAL_ROOT_ID = 'iam-admin-app-portal';

export const BASE_URL = '';
// export const BASE_URL = 'http://10.65.59.5:7001';

export const HELP_PORTAL_URL = 'https://help.zscaler.com/zslogin';
export const ZSCALER_FAQ = 'https://www.zscaler.com/privacy/faq';

// Help portal query params setup

const origin = window.location.origin + '' || 'admin.zslogin.net';

const zsLoginIndex = origin.indexOf('zslogin');

const referer = zsLoginIndex != -1 ? origin.substring(zsLoginIndex) : 'zslogin.net';

const queryParamsForHelp = `?source=admin-ui&referer=https://admin.${referer}`;

export const HELP_ARTICLES = {
  ADMINISTRATIVE_ENTITLEMENTS: `${HELP_PORTAL_URL}/about-administrative-entitlements${queryParamsForHelp}`,
  ADMINISTRATIVE_ENTITLEMENTS_ADMINISTRATIVE: `${HELP_PORTAL_URL}/about-administrative-entitlements-administrative${queryParamsForHelp}`,
  API_CLIENTS: `${HELP_PORTAL_URL}/about-api-clients${queryParamsForHelp}`,
  API_CLIENTS_ADD: `${HELP_PORTAL_URL}/configuring-api-client${queryParamsForHelp}`,
  API_CLIENTS_EDIT_OR_DELETE: `${HELP_PORTAL_URL}/editing-or-deleting-api-client${queryParamsForHelp}`,
  API_RESOURCES: `${HELP_PORTAL_URL}/about-api-resources${queryParamsForHelp}`,
  SERVICE_ENTITLEMENTS: `${HELP_PORTAL_URL}/about-service-entitlements${queryParamsForHelp}`,
  SERVICE_ENTITLEMENTS_SERVICE: `${HELP_PORTAL_URL}/about-service-entitlements-service${queryParamsForHelp}`,
  DASHBOARD: `${HELP_PORTAL_URL}/about-dashboard${queryParamsForHelp}`,
  SIGNON_POLICY: `${HELP_PORTAL_URL}/about-sign-policies${queryParamsForHelp}`,
  ACCESS_POLICY: `${HELP_PORTAL_URL}/about-access-policies${queryParamsForHelp}`,
  PASSWORD_POLICY: `${HELP_PORTAL_URL}/configuring-password-policy${queryParamsForHelp}`,
  USER_PROFILE: `${HELP_PORTAL_URL}/managing-your-user-profile-zslogin-admin-portal${queryParamsForHelp}`,
  USERS: `${HELP_PORTAL_URL}/about-users${queryParamsForHelp}`,
  USER_GROUPS: `${HELP_PORTAL_URL}/about-user-groups${queryParamsForHelp}`,
  PRIMARY_IDP_PROVIDER: `${HELP_PORTAL_URL}/about-primary-identity-provider${queryParamsForHelp}`,
  SECONDAY_IDP_PROVIDER: `${HELP_PORTAL_URL}/about-secondary-identity-providers${queryParamsForHelp}`,
  ADDING_SAML_IDENTITY: `${HELP_PORTAL_URL}/adding-saml-identity-providers${queryParamsForHelp}`,
  ADDING_OIDC_IDENTITY: `${HELP_PORTAL_URL}/adding-openid-providers${queryParamsForHelp}`,
  IP_LOCATIONS: `${HELP_PORTAL_URL}/about-ip-locations${queryParamsForHelp}`,
  IP_LOCATION_GROUPS: `${HELP_PORTAL_URL}/about-ip-location-groups${queryParamsForHelp}`,
  LINKED_SERVICES: `${HELP_PORTAL_URL}/about-linked-services${queryParamsForHelp}`,
  USER_ATTRIBUTES: `${HELP_PORTAL_URL}/about-attributes${queryParamsForHelp}`,
  ADVANCED_SETTINGS: `${HELP_PORTAL_URL}/configuring-authentication-session${queryParamsForHelp}`,
  ZSCALER_SERVICES: `${HELP_PORTAL_URL}/about-zscaler-services${queryParamsForHelp}`,
  AUDIT_LOGS: `${HELP_PORTAL_URL}/about-audit-logs${queryParamsForHelp}`,
  AUTHENTICATION_METHODS: `${HELP_PORTAL_URL}/configuring-authentication-settings `,
  REMOTE_ASSISTANCE: `${HELP_PORTAL_URL}/enabling-remote-assistance`,
  TOKEN_VALIDATORS: `${HELP_PORTAL_URL}/adding-token-validators-assistance`,
  DEVICE_TOKEN: `${HELP_PORTAL_URL}/managing-device-tokens`,
  BRANDING: `${HELP_PORTAL_URL}/customizing-branding`,
  SESSION_ATTRIBUTE: `${HELP_PORTAL_URL}/adding-session-attributes`,
  DEPARTMENTS: `${HELP_PORTAL_URL}/adding-departments`,
  DOMAINS: `${HELP_PORTAL_URL}/adding-guest-domains `,
  MANAGE_DEVICE_GROUPS: `${HELP_PORTAL_URL}/managing-device-groups${queryParamsForHelp}`,
  ROLES: `${HELP_PORTAL_URL}/about-zidentity-admin-roles${queryParamsForHelp}`,
};

export const OTP_LETTERS_LIMIT = 6;

// Priority - 1 'full',
// Priority - 2 'restrictedfull',
// Priority - 3 'view',
// Priority - 4 'restrictedview',
// Priority - 5 'none',
// Note: Lowest priority overrides higher priority e.g. full will override view permission
export const PERMISSION_LEVEL = {
  FULL: 'full',
  RESTRICTED_FULL: 'restrictedfull',
  VIEW: 'view',
  RESTRICTED_VIEW: 'restrictedview',
  NONE: 'none',
};

export const PERMISSIONS_KEY = {
  ADMINISTRATIVE_ENTITLEMENTS_POLICY: 'administrative-entitlement',
  AUDIT_LOGS_POLICY: 'audit-Log',
  API_CLIENTS_AND_RESOURCES: 'api-clients-and-resources',
  AUTHENTICATION_EVENT_LOG_POLICY: 'authentication-event-log',
  AUTHENTICATION_METHODS: 'authentication-methods',
  AUTHENTICATION_SESSION_POLICY: 'authentication-session',
  BRANDING_POLICY: 'branding',
  CXO_INSIGHT: 'cxo-insight',
  DEVICE_TOKEN: 'device-token',
  GUEST_DOMAIN_POLICY: 'guest-domain',
  EXTERNAL_IDENTITIES_POLICY: 'external-identity',
  IP_LOCATION_POLICY: 'ip-location',
  LINKED_TENATS_POLICY: 'linked-tenant',
  REMOTE_ASSISTANCE_MANAGEMENT: 'remote-assistance-management',
  ROLES_POLICY: 'role',
  TOKEN_VALIDATOR_POLICY: 'token-validator',
  SERVICE_ENTITLEMENTS_POLICY: 'service-entitlement',
  SIGN_ON_POLICY: 'sign-on-policy',
  USERS_AND_GROUPS_POLICY: 'user-and-group',
  USERS_CREDENTIALS_POLICY: 'user-credential',
  ZAA_PROFILES: 'zaa-profiles',
  ZAA_CONTEXT_NUGGET_OVERRIDES: 'zaa-context-nugget-overrides',
  ZAA_INTEGRATIONS: 'zaa-integrations',
  ZAA_SIGNAL_HISTORY: 'zaa-signal-history',
};
