import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  CONFIGURATION_TYPES,
  DATA_ACTIVE_PASSWORD_POLICY,
  DATA_PASSWORD_POLICIES,
  DEFAULT_ACTIVE_CONFIG_DETAIL,
  DEFAULT_STATE,
  REDUCER_KEY,
} from './constants';
import {
  selectCustomPasswordPolicyDetail,
  selectRecommendedPasswordPolicyDetail,
} from './selectors';

const passwordPolicySlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateActivePolicy(state, { payload }) {
      state[DATA_ACTIVE_PASSWORD_POLICY] = payload;

      const passwordPolicies = state[DATA_PASSWORD_POLICIES];

      const { configurationType, config = {} } = payload || {};

      const { config: recommendedConfig = {} } =
        passwordPolicies[CONFIGURATION_TYPES.RECOMMENDED] || {};

      passwordPolicies[payload.configurationType] = {
        config: { ...DEFAULT_ACTIVE_CONFIG_DETAIL, ...recommendedConfig, ...config },
        configurationType,
      };

      state[DATA_PASSWORD_POLICIES] = passwordPolicies;
    },
    updatePolicy(state, { payload }) {
      const passwordPolicies = state[DATA_PASSWORD_POLICIES];

      const { configurationType, config = {} } = payload || {};

      const { config: recommendedConfig = {} } =
        passwordPolicies[CONFIGURATION_TYPES.RECOMMENDED] || {};

      passwordPolicies[payload.configurationType] = {
        config: { ...DEFAULT_ACTIVE_CONFIG_DETAIL, ...recommendedConfig, ...config },
        configurationType,
      };

      state[DATA_PASSWORD_POLICIES] = passwordPolicies;
    },
  },
});

const { updateActivePolicy, updatePolicy } = passwordPolicySlice.actions;

export const getPasswordPolicy =
  ({ configurationType } = {}) =>
  async (dispatch, getState) => {
    let url = API_ENDPOINT;

    if (configurationType) {
      url += `?configurationType=${configurationType}`;

      if (configurationType === CONFIGURATION_TYPES.RECOMMENDED) {
        const detail = selectRecommendedPasswordPolicyDetail(getState());

        if (detail.minLength) {
          return;
        }
      }

      if (configurationType === CONFIGURATION_TYPES.CUSTOM) {
        const detail = selectCustomPasswordPolicyDetail(getState());

        if (detail.minLength) {
          return;
        }
      }
    }

    const response = await http.get(url);

    if (response?.data) {
      if (configurationType) {
        dispatch(updatePolicy(response.data));
      } else {
        dispatch(updateActivePolicy(response.data));
      }
    }
  };

export const createPasswordPolicy = (newDetail) => async (dispatch) => {
  const url = API_ENDPOINT;

  const response = await http.post(url, newDetail);

  if (response?.data) {
    dispatch(updateActivePolicy(response.data));
  }
};

export default passwordPolicySlice;
