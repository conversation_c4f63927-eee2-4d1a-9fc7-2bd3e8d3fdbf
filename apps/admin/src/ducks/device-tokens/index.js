import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_SETTINGS, DEFAULT_STATE, REDUCER_KEY } from './constants';

const deviceTokensSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateSettings(state, { payload }) {
      state[DATA_SETTINGS] = payload;
    },
  },
});

const { updateSettings } = deviceTokensSlice.actions;

export const getSettings = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT + '/settings');

  if (response?.data) {
    dispatch(updateSettings(response.data));
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/settings';
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getSettings());
  }
};

export default deviceTokensSlice;
