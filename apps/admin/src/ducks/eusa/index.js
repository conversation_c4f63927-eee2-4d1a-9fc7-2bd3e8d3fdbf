import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_EUSA_CONTENT,
  DATA_EUSA_STATUS,
  DEFAULT_EUSA_CONTENT,
  DEFAULT_EUSA_STATUS,
  DEFAULT_STATE,
  REDUCER_KEY,
} from './constants';

const eusaSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateStatus(state, { payload }) {
      state[DATA_EUSA_STATUS] = payload || DEFAULT_EUSA_STATUS;
    },
    updateContent(state, { payload }) {
      state[DATA_EUSA_CONTENT] = payload || DEFAULT_EUSA_CONTENT;
    },
  },
});

const { updateStatus, updateContent } = eusaSlice.actions;

export const getEUSAStatus = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT + '/status');

  if (response?.data) {
    dispatch(updateStatus(response.data || {}));

    return response.data || {};
  }
};

export const getEUSAContent = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT + '/content');

  if (response?.data) {
    dispatch(updateContent(response.data || {}));
  }
};

export const updateEUSAStatus = () => async (dispatch) => {
  const response = await http.post(API_ENDPOINT + '/status', {});

  if (response?.data) {
    dispatch(updateStatus(response.data));
  }
};

export default eusaSlice;
