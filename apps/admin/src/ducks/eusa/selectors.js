import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_EUSA_CONTENT,
  DATA_EUSA_STATUS,
  DEFAULT_EUSA_CONTENT,
  DEFAULT_EUSA_STATUS,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectEUSAStatus = createSelector(
  baseSelector,
  (state) => state[DATA_EUSA_STATUS] || DEFAULT_EUSA_STATUS,
);

export const selectIsEUSAAccepted = createSelector(
  selectEUSAStatus,
  ({ accepted } = {}) => accepted || false,
);

export const selectEUSAContent = createSelector(
  baseSelector,
  (state) => state[DATA_EUSA_CONTENT] || DEFAULT_EUSA_CONTENT,
);
