import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
  ZDK_FEATURE_FLAG,
  ZDK_IDS_DETAIL,
  ZDK_MATCHING_TOKEN_VALIDATOR,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectZdkfeatureFlag = createSelector(
  baseSelector,
  (state) => state[ZDK_FEATURE_FLAG],
);

export const selectMatchingTokenValidator = createSelector(
  baseSelector,
  (state) => state[ZDK_MATCHING_TOKEN_VALIDATOR],
);

export const selectZDKIds = createSelector(baseSelector, (state) => {
  const ZDKIdsData = state[ZDK_IDS_DETAIL] || DEFAULT_TABLE_DETAIL;
  const ZDKIdsList = ZDKIdsData.data;
  return ZDKIdsList.map(({ zdkId, name }) => ({ label: name, value: zdkId }));
});
