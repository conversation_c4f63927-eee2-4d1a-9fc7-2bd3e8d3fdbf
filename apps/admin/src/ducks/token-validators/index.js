import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT_ZDK_FEATURE_FLAG,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
  TOKENS_API_ENDPOINT,
  ZDK_API_ENDPOINT,
  ZDK_FEATURE_FLAG,
  ZDK_IDS_DETAIL,
  ZDK_MATCHING_TOKEN_VALIDATOR,
} from './constants';

const tokenValidatorsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateZdkConfigList(state, { payload: { tokenConfigs } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];
      tableDetail.data = [...tokenConfigs];
      state[DATA_TABLE_DETAIL] = tableDetail;
    },

    updateTokenvalidatorsList(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },

    updateZDKIdsList(state, { payload }) {
      const tableDetail = state[ZDK_IDS_DETAIL];
      tableDetail.data = [...payload];
      state[ZDK_IDS_DETAIL] = tableDetail;
    },

    updateZDKfeatureFlag(state, { payload }) {
      state[ZDK_FEATURE_FLAG] = { ...payload };
    },

    updateMatchingTokenValidator(state, { payload }) {
      state[ZDK_MATCHING_TOKEN_VALIDATOR] = { ...payload };
    },
  },
});

const {
  updateTokenvalidatorsList,
  updateZDKIdsList,
  updateZDKfeatureFlag,
  updateZdkConfigList,
  updateMatchingTokenValidator,
} = tokenValidatorsSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    isZdkCluster,
  } = {}) =>
  async (dispatch) => {
    let requestUrl = isZdkCluster
      ? ZDK_API_ENDPOINT
      : TOKENS_API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      if (isZdkCluster) {
        dispatch(updateZdkConfigList(response.data || []));
      } else {
        dispatch(updateTokenvalidatorsList(response.data || []));
      }
    }
  };

export const getZDKIds =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
  } = {}) =>
  async (dispatch) => {
    let requestUrl = ZDK_API_ENDPOINT + '/zdkId' + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateZDKIdsList(response?.data?.zdkIds));
    }
  };

export const getZDKfeatureFlag = () => async (dispatch) => {
  let requestUrl = API_ENDPOINT_ZDK_FEATURE_FLAG;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateZDKfeatureFlag(response?.data));
  }
};

export const updateTokenValidator = (payload, isZdkCluster) => async (dispatch) => {
  let API_ENDPOINT = `${TOKENS_API_ENDPOINT}/${payload.validatorId}`;

  if (isZdkCluster) {
    API_ENDPOINT = `${ZDK_API_ENDPOINT}/${payload.configId}`;
  }
  const response = await http.put(API_ENDPOINT, payload);

  if (response?.data) {
    dispatch(getList({ isZdkCluster }));
  }
};

export const addNewTokenValidator = (payload, isZdkCluster) => async (dispatch) => {
  let API_ENDPOINT = TOKENS_API_ENDPOINT;
  if (isZdkCluster) {
    API_ENDPOINT = ZDK_API_ENDPOINT;
  }

  const response = await http.post(API_ENDPOINT, payload);

  if (response?.data) {
    dispatch(getList({ isZdkCluster }));
  }
};

export const deleteTokenValidator = (payload, isZdkCluster) => async (dispatch) => {
  let API_ENDPOINT = `${TOKENS_API_ENDPOINT}/${payload.validatorId}`;

  if (isZdkCluster) {
    API_ENDPOINT = `${ZDK_API_ENDPOINT}/${payload.configId}`;
  }

  await http.delete(API_ENDPOINT);

  dispatch(getList({ isZdkCluster }));
};

export const getMatchingTokenValidator = (token) => async (dispatch) => {
  let requestUrl = TOKENS_API_ENDPOINT + '/validate';

  const payload = {
    token,
  };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(updateMatchingTokenValidator(response?.data));
  }
};

export default tokenValidatorsSlice;
