export const REDUCER_KEY = 'api-resources';

export const API_ENDPOINT = '/admin/internal-api/v1/resource-servers';

export const DATA_TABLE_DETAIL = 'tableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.displayName || row.name || '---',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'name' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
