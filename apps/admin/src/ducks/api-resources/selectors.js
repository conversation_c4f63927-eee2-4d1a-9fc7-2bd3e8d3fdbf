import { createSelector } from '@reduxjs/toolkit';

import { filter } from 'lodash-es';

import {
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectDefaultApiResourceDetail = createSelector(selectTableDetail, ({ data }) => {
  return filter(data, ['defaultApi', true]);
});

export const selectApiResourceDetailByID = ({ data }, id) => {
  return filter(data, ['id', id]);
};

export const selectResourceScopeMap = createSelector(selectTableDetail, ({ data }) => {
  const resourceScopeMap = {};
  data.forEach((resource) => {
    resourceScopeMap[resource.id] = {
      resource,
    };
    resource?.serviceScopes?.forEach((serviceScope) => {
      resourceScopeMap[resource.id][serviceScope?.service?.id] = {};
      serviceScope?.scopes?.forEach((scope) => {
        if (scope?.zpaScopeId) {
          resourceScopeMap[resource.id][serviceScope?.service?.id][
            `${scope?.zpaScopeId}${scope?.id}`
          ] = scope;
        } else {
          resourceScopeMap[resource.id][serviceScope?.service?.id][scope?.id] = scope;
        }
      });
    });
  });
  return resourceScopeMap;
});
