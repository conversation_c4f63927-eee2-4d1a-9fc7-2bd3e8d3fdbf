import { HttpResponse, delay, http } from 'msw';

import {
  API_ENDPOINT,
  CHANGE_EMAIL_ENDPOINT,
  PWD_CHANGE_ENDPOINT,
  VERIFY_EMAIL_ENDPOINT,
} from './constants';

export const MOCK_MY_PROFILE_DATA = {
  id: 'g400000000081',
  tenant: {
    id: 'gg00000000081',
    name: 'OIDP Test Company',
  },
  timeZone: '(UTC+00:00) GMT',
  language: 'English (US)',
  loginName: '<EMAIL>',
  primaryEmail: '<EMAIL>',
  displayName: 'admin',
};

export const profileMockHandler = [
  http.get(API_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(MOCK_MY_PROFILE_DATA);
  }),

  http.put(API_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(MOCK_MY_PROFILE_DATA);
  }),

  http.put(CHANGE_EMAIL_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(MOCK_MY_PROFILE_DATA);
  }),

  http.post(VERIFY_EMAIL_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(MOCK_MY_PROFILE_DATA);
  }),

  http.post(PWD_CHANGE_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(MOCK_MY_PROFILE_DATA);
  }),
];
