export const REDUCER_KEY = 'sessionAttributes';

export const API_ENDPOINT = '/admin/internal-api/v1/custom-session-attributes';

export const DATA_TABLE_DETAIL = 'tableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'number',
    Header: 'TABLE_NUMBER',
    minSize: 50,
    size: 50,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'displayName',
    accessorFn: (row) => row.displayName || 'NONE',
    Header: 'DISPLAY_NAME',
    minSize: 300,
    size: 300,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'attributeName',
    accessorFn: (row) => row.attrName || 'NONE',
    Header: 'ATTRIBUTE_NAME',
    minSize: 300,
    size: 300,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'dataType',
    accessorFn: (row) => row.dataType || 'string',
    Header: 'DATA_TYPE',
    minSize: 100,
    size: 100,
    enableResizing: false,
    defaultCanSort: true,
  },
  {
    id: 'origin',
    accessorFn: (row) => row.systemDefined || false,
    Header: 'ORIGIN',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'basic',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'number' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 150,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DATA_TYPES = 'dataTypes';

export const DEFAULT_DATA_TYPES = {
  totalRecord: -1,
  pageSize: 150,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_TYPES]: DEFAULT_DATA_TYPES,
};
