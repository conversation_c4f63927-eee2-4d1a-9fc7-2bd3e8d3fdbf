import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_BRANDING_DETAILS, DEFAULT_STATE, REDUCER_KEY } from './constants';

const brandingSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateBrandingInfo(state, { payload }) {
      if (payload) {
        state[DATA_BRANDING_DETAILS] = payload;
      }
    },
  },
});

const { updateBrandingInfo } = brandingSlice.actions;

export const getBrandingInfo = () => async (dispatch) => {
  const url = API_ENDPOINT;

  const response = await http.get(url);

  if (response?.data) {
    dispatch(updateBrandingInfo(response?.data));
  }
};

export const update = (payload) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(updateBrandingInfo(response?.data));
  }
};

export default brandingSlice;
