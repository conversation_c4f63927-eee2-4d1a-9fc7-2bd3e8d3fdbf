import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_BRANDING_DETAILS,
  DEFAULT_BRANDING_DETAILS,
  DEFAULT_TABLE_CONFIG,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectBrandingDetails = createSelector(
  baseSelector,
  (state) => state[DATA_BRANDING_DETAILS] || DEFAULT_BRANDING_DETAILS,
);
