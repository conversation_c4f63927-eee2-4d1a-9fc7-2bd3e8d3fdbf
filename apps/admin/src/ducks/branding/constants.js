export const REDUCER_KEY = 'branding';

export const API_ENDPOINT = '/admin/internal-api/v1/branding';

export const DATA_BRANDING_DETAILS = 'brandingDetails';

export const DEFAULT_BRANDING_DETAILS = {};

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'subject',
    accessorFn: (row) => row.subject || '---',
    Header: 'EMAIL_SUBJECT',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'description',
    accessorFn: (row) => row.description || '---',
    Header: 'DESCRIPTION',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_STATE = {
  [DATA_BRANDING_DETAILS]: DEFAULT_BRANDING_DETAILS,
};
