export const REDUCER_KEY = 'roles';

export const API_ENDPOINT = '/admin/internal-api/v1/roles';

export const DATA_TABLE_DETAIL = 'tableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'description',
    accessorFn: (row) => row.description || null,
    Header: 'DESCRIPTION',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortingFn: (rowA, rowB, columnId) => {
      const valueA = rowA.getValue(columnId);
      const valueB = rowB.getValue(columnId);
      if (!valueA || valueA === '' || valueA === null) return 1;
      if (!valueB || valueB === '' || valueB === null) return -1;
      const lowerValueA = String(valueA).toLowerCase();
      const lowerValueB = String(valueB).toLowerCase();

      return lowerValueA < lowerValueB ? -1 : lowerValueA > lowerValueB ? 1 : 0;
    },
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
