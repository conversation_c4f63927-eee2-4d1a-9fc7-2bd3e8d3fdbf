import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_ADMIN_ENTITLEMENT_PERMISSIONS,
  DATA_ALL_PERMISSIONS,
  DATA_ALL_PERMISSION_LEVELS,
  DATA_MY_PERMISSIONS_LEVEL,
  DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS,
  DEFAULT_ALL_PERMISSIONS,
  DEFAULT_ALL_PERMISSION_LEVELS,
  DEFAULT_MY_PERMISSIONS_LEVEL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectAllPermissions = createSelector(
  baseSelector,
  (state) => state[DATA_ALL_PERMISSIONS] || DEFAULT_ALL_PERMISSIONS,
);

export const selectPermissionLevels = createSelector(
  baseSelector,
  (state) => state[DATA_ALL_PERMISSION_LEVELS] || DEFAULT_ALL_PERMISSION_LEVELS,
);

export const selectPermissionLevelByKey = (key) =>
  createSelector(selectPermissionLevels, (state) => state[key] || []);

export const selectMyPermissionsLevel = createSelector(
  baseSelector,
  (state) => state[DATA_MY_PERMISSIONS_LEVEL] || DEFAULT_MY_PERMISSIONS_LEVEL,
);

// permission not in the key are defaulted to view access
export const selectPermissionsByKey = (key) =>
  createSelector(
    selectMyPermissionsLevel,
    (state) =>
      state[key] || {
        hasFullAccess: false,
        hasRestrictedFullAccess: false,
        hasViewAccess: false,
        hasRestrictedViewAccess: false,
        noAccess: true,
      },
  );

export const selectAdminEntitlementServicePermissions = createSelector(
  baseSelector,
  (state) => state[DATA_ADMIN_ENTITLEMENT_PERMISSIONS] || DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS,
);
