import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import {
  DATA_GROUPS,
  DATA_TABLE_DETAIL,
  DEFAULT_GROUPS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ totalRecord, data }) => data?.length === totalRecord,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectGroupsDetail = createSelector(
  baseSelector,
  (state) => state[DATA_GROUPS] || DEFAULT_GROUPS,
);

export const selectGroupsList = createSelector(selectTableDetail, ({ data }) => {
  const getLabel = ({ detail: { name, idp } }) => {
    if (idp?.name) {
      return `${name} (${idp?.name})`;
    }

    return name;
  };

  return getDropDownList({ list: data, getLabel, lite: false });
});
