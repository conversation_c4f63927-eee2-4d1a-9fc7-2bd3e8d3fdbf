import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_TABLE_DETAIL, DEFAULT_STATE, REDUCER_KEY } from './constants';

const userSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateMfaTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];
      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
  },
});

const { updateMfaTableDetail } = userSlice.actions;

export const getUserAuthenticator = (userId) => async (dispatch) => {
  let requestUrl = API_ENDPOINT + `/users/${userId}/authenticators`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    const payload = {
      totalRecord: response.data.length,
      pageOffset: 0,
      pageSize: 100,
      records: response.data,
    };
    dispatch(updateMfaTableDetail(payload || []));
  }
};

export const deleteUserAuthenticator = (userId, authenticatorId) => async (dispatch) => {
  let requestUrl = API_ENDPOINT + `/users/${userId}/authenticators/${authenticatorId}`;

  await http.delete(requestUrl);

  dispatch(getUserAuthenticator(userId));
};

export default userSlice;
