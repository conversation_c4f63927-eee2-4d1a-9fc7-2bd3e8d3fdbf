import dayjs from 'dayjs';

export const REDUCER_KEY = 'user';

export const API_ENDPOINT = '/admin/internal-api/v1';

export const DATA_TABLE_DETAIL = 'mfaTableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'NAME',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'authenticatorType',
    accessorFn: (row) => row.authenticatorType || 'NONE',
    Header: 'TYPE',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'lastUsed',
    accessorFn: (row) =>
      row.lastUsed ? dayjs(row.lastUsed * 1000).format('MMMM DD, YYYY - hh:mm A') : 'NONE',
    Header: 'LAST_USED',
    minSize: 190,
    size: 190,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 70,
    size: 70,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
  hidePagination: true,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
