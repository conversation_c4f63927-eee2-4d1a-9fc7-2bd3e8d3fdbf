import { createSlice } from '@reduxjs/toolkit';
import { getEntriesDetail } from '@zscaler/zui-component-library';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_AUTHENTICATION,
  DATA_DASHBOARD_CARDS,
  DATA_DEVICE_REGISTRATION,
  DATA_EVENTS,
  DATA_GRAPH_FLAGS,
  DATA_SERVICE_ASSIGNMENT,
  DATA_SSO,
  DATA_USER_CREATION_FAILURE,
  DATA_USER_CREATION_SUCCESS,
  DATA_USER_DELETION_FAILURE,
  DATA_USER_DELETION_SUCCESS,
  DATA_USER_MODIFICATION_FAILURE,
  DATA_USER_MODIFICATION_SUCCESS,
  DEFAULT_AUTHENTICATION,
  DEFAULT_DASHBOARD_CARDS,
  DEFAULT_DEVICE_REGISTRATION,
  DEFAULT_EVENTS,
  DEFAULT_GRAPH_FLAGS,
  DEFAULT_SERVICE_ASSIGNMENT,
  DEFAULT_SSO,
  DEFAULT_STATE,
  DEFAULT_USER_CREATION_FAILURE,
  DEFAULT_USER_CREATION_SUCCESS,
  DEFAULT_USER_DELETION_FAILURE,
  DEFAULT_USER_DELETION_SUCCESS,
  DEFAULT_USER_MODIFICATION_FAILURE,
  DEFAULT_USER_MODIFICATION_SUCCESS,
  METRIC_RESULT,
  REDUCER_KEY,
} from './constants';

const metricsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateEvents(state, { payload }) {
      state[DATA_EVENTS] = payload || DEFAULT_EVENTS;
    },
    updateDashboardCards(state, { payload }) {
      state[DATA_DASHBOARD_CARDS] = payload || DEFAULT_DASHBOARD_CARDS;
    },
    updateGraphFlags(state, { payload }) {
      state[DATA_GRAPH_FLAGS] = payload || DEFAULT_GRAPH_FLAGS;
    },
    updateAuthenticationEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_AUTHENTICATION] = entriesDetail || DEFAULT_AUTHENTICATION;
    },
    updateUserCreationSuccessEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_USER_CREATION_SUCCESS] = entriesDetail || DEFAULT_USER_CREATION_SUCCESS;
    },
    updateUserCreationFailureEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_USER_CREATION_FAILURE] = entriesDetail || DEFAULT_USER_CREATION_FAILURE;
    },
    updateUserModificationSuccessEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_USER_MODIFICATION_SUCCESS] = entriesDetail || DEFAULT_USER_MODIFICATION_SUCCESS;
    },
    updateUserModificationFailureEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_USER_MODIFICATION_FAILURE] = entriesDetail || DEFAULT_USER_MODIFICATION_FAILURE;
    },
    updateUserDeletionSuccessEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_USER_DELETION_SUCCESS] = entriesDetail || DEFAULT_USER_DELETION_SUCCESS;
    },
    updateUserDeletionFailureEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_USER_DELETION_FAILURE] = entriesDetail || DEFAULT_USER_DELETION_FAILURE;
    },
    updateServiceAssignmentEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_SERVICE_ASSIGNMENT] = entriesDetail || DEFAULT_SERVICE_ASSIGNMENT;
    },
    updateDeviceRegistrationEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_DEVICE_REGISTRATION] = entriesDetail || DEFAULT_DEVICE_REGISTRATION;
    },
    updateSSOEvent(state, { payload }) {
      const { entries } = payload || {};

      const entriesDetail = getEntriesDetail({ entries });

      state[DATA_SSO] = entriesDetail || DEFAULT_SSO;
    },
  },
});

const {
  updateEvents,
  updateDashboardCards,
  updateGraphFlags,
  updateAuthenticationEvent,
  updateUserCreationSuccessEvent,
  updateUserCreationFailureEvent,
  updateUserModificationSuccessEvent,
  updateUserModificationFailureEvent,
  updateUserDeletionSuccessEvent,
  updateUserDeletionFailureEvent,
  updateServiceAssignmentEvent,
  updateDeviceRegistrationEvent,
  updateSSOEvent,
} = metricsSlice.actions;

export const getDashboardCards =
  ({ tenantPseudoDomain } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + '/dashboards';

    if (tenantPseudoDomain) {
      requestUrl += `?tenantPseudoDomain=${tenantPseudoDomain}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateDashboardCards(response.data || []));
    }
  };

export const getGraphFlags =
  ({ tenantPseudoDomain }) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + '/graph-flags';

    if (tenantPseudoDomain) {
      requestUrl += `?tenantPseudoDomain=${tenantPseudoDomain}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateGraphFlags(response.data));
    }
  };

export const getAuthenticationEvents = (detail, idpType) => async (dispatch) => {
  let requestUrl = API_ENDPOINT;

  const payload = {
    eventName: 'AUTHENTICATION_EVENT',
    includeTotal: true,
    includeTrend: true,
    idpType: idpType,
    ...detail,
  };

  if (!payload.tenantPseudoDomain) {
    delete payload.tenantPseudoDomain;
  }

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(updateAuthenticationEvent(response.data || []));
  }
};

export const getUserCreationEvents =
  (detail = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT;

    const payload = {
      eventName: 'USER_CREATION_EVENT',
      includeTotal: true,
      includeTrend: true,
      trendInterval: 864,
      ...detail,
    };

    if (!payload.tenantPseudoDomain) {
      delete payload.tenantPseudoDomain;
    }

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      if (payload.result === METRIC_RESULT.SUCCESS) {
        dispatch(updateUserCreationSuccessEvent(response.data || []));
      }

      if (payload.result === METRIC_RESULT.FAILURE) {
        dispatch(updateUserCreationFailureEvent(response.data || []));
      }
    }
  };

export const getUserModificationEvents =
  (detail = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT;

    const payload = {
      eventName: 'USER_MODIFICATION_EVENT',
      includeTotal: true,
      includeTrend: true,
      trendInterval: 864,
      ...detail,
    };

    if (!payload.tenantPseudoDomain) {
      delete payload.tenantPseudoDomain;
    }

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      if (payload.result === METRIC_RESULT.SUCCESS) {
        dispatch(updateUserModificationSuccessEvent(response.data || []));
      }

      if (payload.result === METRIC_RESULT.FAILURE) {
        dispatch(updateUserModificationFailureEvent(response.data || []));
      }
    }
  };

export const getUserDeletionEvents =
  (detail = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT;

    const payload = {
      eventName: 'USER_DELETION_EVENT',
      includeTotal: true,
      includeTrend: true,
      trendInterval: 864,
      ...detail,
    };

    if (!payload.tenantPseudoDomain) {
      delete payload.tenantPseudoDomain;
    }

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      if (payload.result === METRIC_RESULT.SUCCESS) {
        dispatch(updateUserDeletionSuccessEvent(response.data || []));
      }

      if (payload.result === METRIC_RESULT.FAILURE) {
        dispatch(updateUserDeletionFailureEvent(response.data || []));
      }
    }
  };

export const getDeviceRegistrationEvents =
  (detail = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT;

    const payload = {
      eventName: 'DEVICE_REGISTRATION_EVENT',
      includeTrend: false,
      ...detail,
    };

    if (!payload.tenantPseudoDomain) {
      delete payload.tenantPseudoDomain;
    }

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      dispatch(updateDeviceRegistrationEvent(response.data || []));
    }
  };

export const getSSOEvents =
  (detail = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT;

    const payload = {
      eventName: 'SSO_EVENT',
      includeTrend: false,
      ...detail,
    };

    if (!payload.tenantPseudoDomain) {
      delete payload.tenantPseudoDomain;
    }

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      dispatch(updateSSOEvent(response.data || []));
    }
  };

export const getServiceAssignmentEvents =
  (detail = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT;

    const payload = {
      eventName: 'SERVICE_ASSIGNMENT_EVENT',
      includeTrend: false,
      ...detail,
    };

    if (!payload.tenantPseudoDomain) {
      delete payload.tenantPseudoDomain;
    }

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      dispatch(updateServiceAssignmentEvent(response.data || []));
    }
  };

export const getEvents = () => async (dispatch) => {
  let requestUrl = API_ENDPOINT + `/events`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateEvents([]));
  }
};

export default metricsSlice;
