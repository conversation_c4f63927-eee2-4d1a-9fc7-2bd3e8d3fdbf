export const REDUCER_KEY = 'signonPolicies';

export const API_ENDPOINT = '/admin/internal-api/v1/sign-on-policies';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const DATA_ACTIONS = 'actions';

export const DATA_FIELDS = 'fields';

export const DATA_OPERATIONS = 'operations';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'ruleOrder',
    accessorFn: (row) => row.ruleOrder || 'NONE',
    Header: 'RULE_ORDER',
    size: 100,
    minSize: 100,
    maxSize: 100,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'RULE_NAME',
    size: 100,
    minSize: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'criteria',
    Header: 'CRITERIA',
    size: 300,
    minSize: 300,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'action',
    accessorFn: (row) => row.action || '',
    Header: 'RULE_ACTION',
    size: 100,
    minSize: 100,
    maxSize: 100,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'status',
    accessorFn: (row) => row.disabled || '',
    Header: 'RULE_STATUS',
    size: 100,
    minSize: 100,
    maxSize: 100,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'basic',
  },
  {
    id: 'description',
    accessorFn: (row) => row.description || '---',
    Header: 'DESCRIPTION',
    size: 300,
    minSize: 300,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'ruleOrder' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_ACTIONS = [];

export const DEFAULT_FIELDS = [];

export const DEFAULT_OPERATIONS = [];

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_ACTIONS]: DEFAULT_ACTIONS,
  [DATA_FIELDS]: DEFAULT_FIELDS,
  [DATA_OPERATIONS]: DEFAULT_OPERATIONS,
};
