import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_API_CLIENTS_TABLE_DETAIL,
  DATA_CRITERIA_VALUES,
  DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DATA_TABLE_DETAIL,
  DEFAULT_API_CLIENTS_TABLE_DETAIL,
  DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const accessPoliciesSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateApiClientsTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_API_CLIENTS_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_API_CLIENTS_TABLE_DETAIL] = tableDetail;
      }
    },
    updateResourceServerScopesTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL] = tableDetail;
      }
    },
    updateCriteriaValues(state, { payload }) {
      state[DATA_CRITERIA_VALUES] = payload;
    },
  },
});

const {
  updateTableDetail,
  updateApiClientsTableDetail,
  updateResourceServerScopesTableDetail,
  updateCriteriaValues,
} = accessPoliciesSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }

    // dispatch(updateTableDetail(MOCK_LIST));
  };

export const getApiCLientsList =
  ({
    pageSize = DEFAULT_API_CLIENTS_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_API_CLIENTS_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `/api-clients?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateApiClientsTableDetail(response.data || []));
    }
  };

export const getResourceServerScopesList =
  ({
    pageSize = DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT + `/resource-server-scopes?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&scopeName=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateResourceServerScopesTableDetail(response.data || []));
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.id}`;
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export const getCriteriaValues = () => async (dispatch) => {
  const url = API_ENDPOINT + `/criteria-values`;

  const response = await http.get(url);

  if (response?.data) {
    dispatch(updateCriteriaValues(response.data));
  }
};

export default accessPoliciesSlice;
