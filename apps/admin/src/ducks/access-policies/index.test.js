import { configureStore } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DEFAULT_STATE, accessPoliciesSlice } from './constants';
import {
  add,
  getApiCLientsList,
  getCriteriaValues,
  getList,
  getResourceServerScopesList,
  remove,
  update,
} from './index';

jest.mock('../../utils/http');

const store = configureStore({
  reducer: {
    accessPolicies: accessPoliciesSlice.reducer,
  },
  preloadedState: {
    accessPolicies: DEFAULT_STATE,
  },
});

describe('accessPoliciesSlice', () => {
  beforeEach(() => {
    store.dispatch = jest.fn();
  });

  it('should handle getList', async () => {
    const mockData = { records: [], pageSize: 10, pageOffset: 0, totalRecord: 0 };
    http.get.mockResolvedValue({ data: mockData });

    await store.dispatch(getList());

    expect(http.get).toHaveBeenCalledWith(expect.stringContaining('limit=10&offset=0'));
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle getApiCLientsList', async () => {
    const mockData = { records: [], pageSize: 10, pageOffset: 0, totalRecord: 0 };
    http.get.mockResolvedValue({ data: mockData });

    await store.dispatch(getApiCLientsList());

    expect(http.get).toHaveBeenCalledWith(
      expect.stringContaining('/api-clients?limit=10&offset=0'),
    );
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle getResourceServerScopesList', async () => {
    const mockData = { records: [], pageSize: 10, pageOffset: 0, totalRecord: 0 };
    http.get.mockResolvedValue({ data: mockData });

    await store.dispatch(getResourceServerScopesList());

    expect(http.get).toHaveBeenCalledWith(
      expect.stringContaining('/resource-server-scopes?limit=10&offset=0'),
    );
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle add', async () => {
    const mockDetail = { name: 'test' };
    http.post.mockResolvedValue({ data: mockDetail });

    await store.dispatch(add(mockDetail));

    expect(http.post).toHaveBeenCalledWith(expect.stringContaining(API_ENDPOINT), mockDetail);
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle update', async () => {
    const mockDetail = { id: 1, name: 'test' };
    http.put.mockResolvedValue({ data: mockDetail });

    await store.dispatch(update(mockDetail));

    expect(http.put).toHaveBeenCalledWith(expect.stringContaining(`${API_ENDPOINT}/1`), mockDetail);
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle remove', async () => {
    const mockId = 1;
    http.delete.mockResolvedValue({});

    await store.dispatch(remove({ id: mockId }));

    expect(http.delete).toHaveBeenCalledWith(expect.stringContaining(`${API_ENDPOINT}/1`));
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });

  it('should handle getCriteriaValues', async () => {
    const mockData = { criteria: [] };
    http.get.mockResolvedValue({ data: mockData });

    await store.dispatch(getCriteriaValues());

    expect(http.get).toHaveBeenCalledWith(expect.stringContaining('/criteria-values'));
    expect(store.dispatch).toHaveBeenCalledWith(expect.any(Function));
  });
});
