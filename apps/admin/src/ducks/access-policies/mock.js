export const MOCK_LIST = {
  pageOffset: 0,
  pageSize: 100,
  records: [
    {
      id: 'g9vig89nt02j4',
      name: 'Test',
      apiClients: [],
      ruleAction: 'BLOCK',
      accessPolicyCriteria: {
        operation: 'OR',
        timeCondition: {
          frequency: 'REPEAT_WEEKLY',
          fromDate: '2025-06-11T11:29:03',
          toDate: '2025-06-25T11:29:08',
          timeZone: 'Africa/Bujumbura',
          daysOfWeek: ['FRIDAY', 'TUESDAY'],
          weeksOfMonth: [],
        },
        resourceConditions: [
          {
            id: '9hvifnjc4g20r',
            name: 'zs:config:ziam:0:config:9hvifnjc4g20r:Super Admin',
            resourceId: 'hhvifnl1ig2i9::9hvifnjc4g20r',
          },
          {
            id: '9hvifnjung28k',
            name: 'zs:config:ziam:0:config:9hvifnjung28k:Users Admin',
            resourceId: 'hhvifnl1ig2i9::9hvifnjung28k',
          },
          {
            id: 'hpvifnl1l02ij',
            name: 'zs:config:zcc.zscaler.net:65579:config:1234:Super Admin',
            resourceId: 'hhvifnl1l02ii::hpvifnl1l02ij',
          },
          {
            id: 'hpvifnl1lg2ip',
            name: 'zs:config:cloud_connector.zscaler.net:356453:config:491:Super Admin',
            resourceId: 'hhvifnl1lg2io::hpvifnl1lg2ip',
          },
        ],
      },
      ruleStatus: 'ACTIVE',
      activeConditions: ['TIME', 'RESOURCES'],
    },
    {
      id: 'g9vig89nt02j5',
      name: 'Test 1',
      apiClients: [],
      ruleAction: 'BLOCK',
      accessPolicyCriteria: {
        operation: '',
        timeCondition: {
          frequency: 'REPEAT_WEEKLY',
          fromDate: '2025-06-11T11:29:03',
          toDate: '2025-06-25T11:29:08',
          timeZone: 'Africa/Bujumbura',
          daysOfWeek: ['FRIDAY', 'TUESDAY'],
          weeksOfMonth: [],
        },
        resourceConditions: [],
      },
      ruleStatus: 'ACTIVE',
      activeConditions: ['TIME', 'RESOURCES'],
    },
    {
      id: 'g9vig89nt02j6',
      name: 'Test 2',
      apiClients: [],
      ruleAction: 'BLOCK',
      accessPolicyCriteria: {
        operation: 'OR',
        timeCondition: {
          frequency: 'REPEAT_MONTHLY',
          fromDate: '2025-06-11T11:29:03',
          toDate: '2025-06-25T11:29:08',
          timeZone: 'Africa/Bujumbura',
          daysOfWeek: ['FRIDAY', 'TUESDAY'],
          weeksOfMonth: ['FOURTH', 'FIRST'],
        },
        resourceConditions: [
          {
            id: 'hpvifnl1l02ij',
            name: 'zs:config:zcc.zscaler.net:65579:config:1234:Super Admin',
            resourceId: 'hhvifnl1l02ii::hpvifnl1l02ij',
          },
          {
            id: 'hpvifnl1lg2ip',
            name: 'zs:config:cloud_connector.zscaler.net:356453:config:491:Super Admin',
            resourceId: 'hhvifnl1lg2io::hpvifnl1lg2ip',
          },
        ],
      },
      ruleStatus: 'ACTIVE',
      activeConditions: ['TIME', 'RESOURCES'],
    },
  ],
};
