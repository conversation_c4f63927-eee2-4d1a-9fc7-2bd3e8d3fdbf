import {
  DATA_API_CLIENTS_TABLE_DETAIL,
  DATA_CRITERIA_VALUES,
  DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  REDUCER_KEY,
} from './constants';
import {
  selectApiClientsList,
  selectApiClientsTableDetail,
  selectCriteriaValues,
  selectDaysOfWeekEnums,
  selectFrequenciesEnums,
  selectIsApiClientsTotalRecordsFetched,
  selectIsResourceServerScopesTotalRecordsFetched,
  selectIsTotalRecordsFetched,
  selectOperationsEnums,
  selectResourceServerScopesList,
  selectResourceServerScopesTableDetail,
  selectRuleActionsEnums,
  selectTableConfig,
  selectTableDetail,
  selectTimeZonesEnums,
  selectWeeksOfMonthEnums,
} from './selectors';

describe('Access Policies Selectors', () => {
  const state = {
    [REDUCER_KEY]: {
      [DATA_TABLE_DETAIL]: { hasFetchedAllRecords: true },
      [DATA_API_CLIENTS_TABLE_DETAIL]: {
        data: [{ clientName: 'Client1', clientId: '1' }],
        hasFetchedAllRecords: true,
      },
      [DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL]: {
        data: [{ displayName: 'Scope1' }],
        hasFetchedAllRecords: true,
      },
      [DATA_CRITERIA_VALUES]: {
        daysOfWeek: [{ displayName: 'Monday', name: 'MON' }],
        frequencies: [{ displayName: 'Daily', name: 'DAILY' }],
        operation: [{ displayName: 'Read', name: 'READ' }],
        timezones: [{ displayName: 'UTC', name: 'UTC' }],
        occurrences: [{ displayName: 'Once', name: 'ONCE' }],
        ruleActions: [{ displayName: 'Allow', name: 'ALLOW' }],
      },
    },
  };

  it('should select table detail', () => {
    expect(selectTableDetail(state)).toEqual({ hasFetchedAllRecords: true });
  });

  it('should select if total records are fetched', () => {
    expect(selectIsTotalRecordsFetched(state)).toBe(true);
  });

  it('should select table config', () => {
    expect(selectTableConfig(state)).toEqual(DEFAULT_TABLE_CONFIG);
  });

  it('should select API clients table detail', () => {
    expect(selectApiClientsTableDetail(state)).toEqual({
      data: [{ clientName: 'Client1', clientId: '1' }],
      hasFetchedAllRecords: true,
    });
  });

  it('should select API clients list', () => {
    expect(selectApiClientsList(state)).toEqual([{ label: 'Client1', value: '1' }]);
  });

  it('should select if API clients total records are fetched', () => {
    expect(selectIsApiClientsTotalRecordsFetched(state)).toBe(true);
  });

  it('should select resource server scopes table detail', () => {
    expect(selectResourceServerScopesTableDetail(state)).toEqual({
      data: [{ displayName: 'Scope1' }],
      hasFetchedAllRecords: true,
    });
  });

  it('should select resource server scopes list', () => {
    expect(selectResourceServerScopesList(state)).toEqual([{ label: 'Scope1', value: 'Scope1' }]);
  });

  it('should select if resource server scopes total records are fetched', () => {
    expect(selectIsResourceServerScopesTotalRecordsFetched(state)).toBe(true);
  });

  it('should select criteria values', () => {
    expect(selectCriteriaValues(state)).toEqual(state[REDUCER_KEY][DATA_CRITERIA_VALUES]);
  });

  it('should select days of week enums', () => {
    expect(selectDaysOfWeekEnums(state)).toEqual([{ label: 'Monday', value: 'MON' }]);
  });

  it('should select frequencies enums', () => {
    expect(selectFrequenciesEnums(state)).toEqual([{ label: 'Daily', value: 'DAILY' }]);
  });

  it('should select operations enums', () => {
    expect(selectOperationsEnums(state)).toEqual([{ label: 'Read', value: 'READ' }]);
  });

  it('should select time zones enums', () => {
    expect(selectTimeZonesEnums(state)).toEqual([{ label: 'UTC', value: 'UTC' }]);
  });

  it('should select occurrences enums', () => {
    expect(selectWeeksOfMonthEnums(state)).toEqual([{ label: 'Once', value: 'ONCE' }]);
  });

  it('should select rule actions enums', () => {
    expect(selectRuleActionsEnums(state)).toEqual([{ label: 'Allow', value: 'ALLOW' }]);
  });
});
