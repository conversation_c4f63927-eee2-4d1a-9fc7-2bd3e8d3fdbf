export const ENDPOINT = '/admin/internal-api/v1/';

export const API_ENDPOINT = '/admin/internal-api/v1/services';

export const USERS_API_ENDPOINT = '/admin/internal-api/v1/users';

export const ROLES_API_ENDPOINT = '/admin/internal-api/v1/roles';

export const REDUCER_KEY = 'adminEntitlements';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const DATA_ENTITY_TABLE_DETAIL = 'entityTableDetail';

export const DATA_GROUP_USERS_TABLE_DETAIL = 'groupUsersTableDetail';

export const DATA_ROLES_DETAIL = 'roles';

export const DATA_SCOPES_DETAIL = 'scopes';

export const DATA_ASSIGN_ENTITIES_TABLE_DETAIL = 'assignEntitiesTableDetail';

export const DATA_SELECTED_ENTITIES_TABLE_DETAIL = 'selectedEntitiesTableDetail';

export const DATA_SERVICE_CONSTRAINTS = 'serviceConstraints';

export const DATA_SERVICES_SCOPE_DETAIL = 'servicesScopeDetail';

export const DATA_SERVICES_ROLE_DETAIL = 'servicesRoleDetail';

export const DATA_SCOPE_ROLES_DETAIL = 'scopeRolesDetail';

export const DEFAULT_SERVICE_CONSTRAINTS = [
  {
    serviceName: 'ZIA',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZPA',
    scopeSupport: true,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZCC',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZDX',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZBI',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'DECEPTION',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: true,
    linkUnlink: true,
  },
  {
    serviceName: 'ZIAM',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: false,
  },
  {
    serviceName: 'TRUST',
    scopeSupport: false,
    roleSupport: false,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'DSPM',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: false,
  },
  {
    serviceName: 'MPP',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: false,
  },
];

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'serviceName',
    accessorFn: (row) => row.displayName || '---',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'cloudName',
    accessorFn: (row) => row.cloudDomainName || '---',
    Header: 'CLOUD_NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'orgName',
    accessorFn: (row) => row.orgName || '---',
    Header: 'ORG_NAME',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
  },
];

const DEFAULT_ENTITY_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row?.resource?.name || 'NONE',
    Header: 'NAME',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'userId',
    accessorFn: (row) => row?.resource?.loginName || '--',
    Header: 'USER_ID',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
  },
  {
    id: 'tsRoles',
    accessorFn: (row) =>
      row?.tsRoles?.map((role) => role.name).join(', ') ||
      row?.roles?.map((role) => role.name).join(', '),
    Header: 'ROLE',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
  },
  {
    id: 'tsScopes',
    accessorFn: (row) => row?.tsScopes?.map((scope) => scope.name).join(', '),
    Header: 'MICROTENANT',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
  },
  {
    id: 'groupUsers',
    Header: 'USERS',
    minSize: 200,
    size: 200,
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

const DEFAULT_GROUP_USERS_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row?.displayName || row?.name || '-',
    Header: 'NAME',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'userId',
    accessorFn: (row) => row?.loginName || row?.primaryEmail || 'NONE',
    Header: 'USER_ID',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
];

const DEFAULT_ASSIGN_ENTITIES_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row?.displayName || row?.name || 'NONE',
    Header: 'NAME',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'loginName',
    accessorFn: (row) => row?.loginName || 'NONE',
    Header: 'USER_ID',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'groupUsers',
    Header: 'USERS',
    minSize: 200,
    size: 200,
  },
  {
    id: 'scope',
    accessorFn: (row) => row?.selectedScope?.map((scope) => scope.label).join(', ') || 'NONE',
    Header: 'MICROTENANT',
    minSize: 200,
    size: 200,
  },
  {
    id: 'role',
    Header: 'ROLE',
    minSize: 200,
    size: 200,
  },
];

export const DEFAULT_ROLES_DETAIL = {
  pageSize: 100,
  pageOffset: 0,
};

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_GROUP_USERS_TABLE_CONFIG = {
  columns: DEFAULT_GROUP_USERS_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_ENTITY_TABLE_CONFIG = {
  columns: DEFAULT_ENTITY_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_ASSIGN_ENTITIES_TABLE_CONFIG = {
  columns: DEFAULT_ASSIGN_ENTITIES_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_REVIEW_ASSIGN_ENTITIES_TABLE_CONFIG = {
  columns: [
    {
      id: 'name',
      accessorFn: (row) => row?.displayName || row?.name || 'NONE',
      Header: 'NAME',
      minSize: 350,
      size: 350,
      defaultCanSort: true,
      sortType: 'alphanumeric',
    },
    {
      id: 'loginName',
      accessorFn: (row) => row?.loginName || 'NONE',
      Header: 'USER_ID',
      minSize: 350,
      size: 350,
      defaultCanSort: true,
      sortType: 'alphanumeric',
    },
    {
      id: 'scope',
      accessorFn: (row) => row.selectedScope?.map((scope) => scope.label).join(', ') || 'NONE',
      Header: 'MICROTENANT',
      minSize: 350,
      size: 350,
    },
    {
      id: 'role',
      accessorFn: (row) => row.selectedRole?.map((role) => role.label).join(', ') || 'NONE',
      Header: 'ROLE',
      minSize: 350,
      size: 350,
    },
  ],
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_ENTITY_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_GROUP_USERS_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

const DEFAULT_ROLE_COLUMNS_DETAIL = [
  {
    id: 'idx',
    accessorFn: (row, idx) => idx + 1,
    Header: 'TABLE_NUMBER',
    size: 50,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'roleName',
    accessorFn: (row) => row.name,
    Header: 'ROLE',
    size: 250,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'scopeAndRoleName',
    accessorFn: (row) => row.name,
    Header: 'SCOPES_N_ROLES',
    size: 250,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_ROLE_TABLE_CONFIG = {
  columns: DEFAULT_ROLE_COLUMNS_DETAIL,
  initialState: {
    sortBy: [{ roleName: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_SERVICES_ROLE_DETAIL = {};
export const DEFAULT_SERVICES_SCOPE_DETAIL = {};
export const DEFAULT_SCOPE_ROLES_DETAIL = {};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_ENTITY_TABLE_DETAIL]: DEFAULT_ENTITY_TABLE_DETAIL,
  [DATA_GROUP_USERS_TABLE_DETAIL]: DEFAULT_GROUP_USERS_TABLE_DETAIL,
  [DATA_ASSIGN_ENTITIES_TABLE_DETAIL]: DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
  [DATA_SELECTED_ENTITIES_TABLE_DETAIL]: DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL,
  [DATA_SERVICE_CONSTRAINTS]: DEFAULT_SERVICE_CONSTRAINTS,
  [DATA_SERVICES_ROLE_DETAIL]: DEFAULT_SERVICES_ROLE_DETAIL,
  [DATA_SERVICES_SCOPE_DETAIL]: DEFAULT_SERVICES_SCOPE_DETAIL,
  [DATA_SCOPE_ROLES_DETAIL]: DEFAULT_SCOPE_ROLES_DETAIL,
};
