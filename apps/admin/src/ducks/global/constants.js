export const REDUCER_KEY = 'global';

export const DATA_APP_STATE = 'appState';
export const DATA_NAV_STATE = 'navState';
export const DATA_APP_SETUP_DETAIL = 'appSetupDetail';
export const DATA_LOADING = 'loading';
export const DATA_NOTIFICATIONS = 'notifications';
export const DATA_BUILD_VERSION = 'buildVersion';
export const DATA_BANNER = 'banner';

export const API_ENDPOINT = '/admin/internal-api/v1';

export const APP_STATE = {
  UNSET: 'NOT_SET',
  SETUP_DONE: 'SETUP_DONE',
  SETUP_ERROR: 'SETUP_ERROR',
};

export const NOTIFICATIONS_TYPE = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  HELP: 'help',
};

export const DEFAULT_APP_STATE = APP_STATE.UNSET;

export const DEFAULT_NAV_STATE = {};

export const DEFAULT_APP_SETUP_DETAIL = {};

export const DEFAULT_LOADING = false;

export const DEFAULT_NOTIFICATIONS = {};

export const DEFAULT_BUILD_VERSION = {
  buildTime: '',
  buildVersion: '',
};

export const DEFAULT_BANNER = [
  {
    status: false,
  },
];

export const DEFAULT_STATE = {
  [DATA_APP_STATE]: DEFAULT_APP_STATE,
  [DATA_NAV_STATE]: DEFAULT_NAV_STATE,
  [DATA_APP_SETUP_DETAIL]: DEFAULT_APP_SETUP_DETAIL,
  [DATA_LOADING]: DEFAULT_LOADING,
  [DATA_NOTIFICATIONS]: DEFAULT_NOTIFICATIONS,
  [DATA_BUILD_VERSION]: DEFAULT_BUILD_VERSION,
  [DATA_BANNER]: DEFAULT_BANNER,
};
