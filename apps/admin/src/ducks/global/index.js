import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  APP_STATE,
  DATA_APP_SETUP_DETAIL,
  DATA_APP_STATE,
  DATA_BANNER,
  DATA_BUILD_VERSION,
  DATA_LOADING,
  DATA_NAV_STATE,
  DATA_NOTIFICATIONS,
  DEFAULT_APP_SETUP_DETAIL,
  DEFAULT_NAV_STATE,
  DEFAULT_STATE,
  NOTIFICATIONS_TYPE,
  REDUCER_KEY,
} from './constants';

let notificationCounter = 0;

const globalSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateAppState(state, { payload }) {
      state[DATA_APP_STATE] = payload;
    },
    updateNavState(state, { payload }) {
      state[DATA_NAV_STATE] = payload || DEFAULT_NAV_STATE;
    },
    updateAppSetupDetail(state, { payload }) {
      state[DATA_APP_SETUP_DETAIL] = payload || DEFAULT_APP_SETUP_DETAIL;
    },
    updateLoading(state, { payload }) {
      state[DATA_LOADING] = payload;
    },
    addNotification(state, { payload }) {
      const notificationId = payload.notificationId || ++notificationCounter;

      state[DATA_NOTIFICATIONS][notificationId] = { notificationId, ...payload };
    },
    removeNotification(state, { payload: id }) {
      delete state[DATA_NOTIFICATIONS][id];
    },
    updateBuildVersion(state, { payload }) {
      state[DATA_BUILD_VERSION] = payload;
    },
    updateBanner(state, { payload }) {
      state[DATA_BANNER] = payload;
    },
  },
});

const {
  updateAppState,
  updateNavState,
  updateAppSetupDetail,
  updateLoading,
  addNotification,
  removeNotification,
  updateBuildVersion,
  updateBanner,
} = globalSlice.actions;

export const appSetupReset = () => async (dispatch) => {
  dispatch(updateAppState(APP_STATE.UNSET));
  dispatch(updateAppSetupDetail());
};

export const appSetupDone = () => async (dispatch) => {
  dispatch(updateAppState(APP_STATE.SETUP_DONE));
  dispatch(updateAppSetupDetail());
};

export const appSetupError = (errorDetail) => async (dispatch) => {
  dispatch(updateAppState(APP_STATE.SETUP_ERROR));
  dispatch(updateAppSetupDetail(errorDetail));
};

export const updateNavStateDetail = (navState) => async (dispatch) => {
  dispatch(updateNavState(navState));
};

export const showLoader = () => async (dispatch) => {
  return dispatch(updateLoading(true));
};

export const hideLoader = () => async (dispatch) => {
  return dispatch(updateLoading(false));
};

export const showNotification =
  (notificationDetail = {}) =>
  async (dispatch) => {
    const notificationId = notificationDetail.notificationId || ++notificationCounter;

    dispatch(addNotification({ notificationId, ...notificationDetail }));

    return notificationId;
  };

export const hideNotification = (id) => async (dispatch) => {
  return dispatch(removeNotification(id));
};

export const showSuccessNotification =
  (notificationDetail = {}) =>
  async (dispatch) => {
    return dispatch(showNotification({ type: NOTIFICATIONS_TYPE.SUCCESS, ...notificationDetail }));
  };

export const showWarningNotification =
  (notificationDetail = {}) =>
  async (dispatch) => {
    return dispatch(showNotification({ type: NOTIFICATIONS_TYPE.WARNING, ...notificationDetail }));
  };

export const showErrorNotification =
  (notificationDetail = {}) =>
  async (dispatch) => {
    return dispatch(showNotification({ type: NOTIFICATIONS_TYPE.ERROR, ...notificationDetail }));
  };

export const getBuildVersion = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/system-info/build-version`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateBuildVersion(response.data));
  }
};

export const getBanner = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/system-info/ui-banner`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateBanner(response.data));
  }
};

export default globalSlice;
