import { createSlice } from '@reduxjs/toolkit';

import { isArray, noop } from 'lodash-es';

import { http } from '../../utils/http';

import { getAttachmentDetail } from '../helper';
import {
  API_ENDPOINT,
  DATA_TABLE_DETAIL,
  DATA_TYPES,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const userAttributesSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateDataTypes(state, { payload }) {
      const listDetail = state[DATA_TYPES];

      if (isArray(payload)) {
        listDetail.error = {};
        listDetail.hasError = false;
        listDetail.hasData = true;

        listDetail.data = [...payload];
        listDetail.hasFetchedAllRecords = true;
      }

      state[DATA_TYPES] = listDetail;
    },
  },
});

const { updateTableDetail, updateDataTypes } = userAttributesSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    type = 'BOTH',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}&type=${type}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.attrName}`;
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ attrName }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${attrName}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export const bulkRemove =
  ({ ids }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/bulk-delete`;

    await http.put(requestUrl, ids);

    dispatch(getList());
  };

export const getDataTypes = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/_datatypes`;

  const response = await http.get(requestUrl).catch(noop);

  if (response?.data) {
    dispatch(updateDataTypes(response.data));
  }
};

export const importFromCsvPolling =
  ({ override, filesDetail }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/import-async?override=${override}`;

    const files = new FormData();
    files.append('importcsv', filesDetail[0]);

    const response = await http.post(requestUrl, files, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response?.data?.id) {
      const pollingResponse = await dispatch(
        getPollingStatus({ importId: response?.data.id, timeout: 0 }),
      );

      dispatch(getList());

      return pollingResponse?.importResult;
    }

    return {};
  };

export const getPollingStatus =
  ({ importId, timeout = 2 }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/import-async/${importId}/status`;

    let response = '';

    await new Promise((resolve, reject) => {
      setTimeout(() => {
        http
          .get(requestUrl)
          .then((apiResponse) => {
            response = apiResponse;
            resolve(response);
          })
          .catch((error) => {
            reject(error);
          });
      }, timeout * 1000);
    });

    if (response?.data?.importStatus === 'COMPLETED') {
      return response?.data;
    } else {
      return dispatch(getPollingStatus({ importId }));
    }
  };

export const downloadTemplateCSV = () => async () => {
  const requestUrl = API_ENDPOINT + '/import/download-template';

  const response = await http.get(requestUrl);

  if (response?.data) {
    return getAttachmentDetail(response);
  }
};

export default userAttributesSlice;
