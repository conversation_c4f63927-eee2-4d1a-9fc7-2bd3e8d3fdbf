import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_SYSTEM_FEATURES,
  DATA_TENANT_FEATURES,
  DEFAULT_STATE,
  REDUCER_KEY,
} from './constants';

const featuresSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateSystemFeatures(state, { payload }) {
      state[DATA_SYSTEM_FEATURES] = payload;
    },
    updateTenantFeatures(state, { payload }) {
      state[DATA_TENANT_FEATURES] = payload;
    },
  },
});

const { updateSystemFeatures, updateTenantFeatures } = featuresSlice.actions;

export const getTenantFeatures = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT + '/tenant-features');

  if (response?.data) {
    dispatch(updateTenantFeatures(response?.data));
  }
};

export const getSystemFeatures = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT + '/system-features');

  if (response?.data) {
    dispatch(updateSystemFeatures(response?.data));
  }
};

export default featuresSlice;
