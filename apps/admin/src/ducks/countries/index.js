import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_COUNTRY_CODES, DEFAULT_STATE, REDUCER_KEY } from './constants';

const countryCodesSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateCountryCodes(state, { payload }) {
      state[DATA_COUNTRY_CODES] = payload;
    },
  },
});

const { updateCountryCodes } = countryCodesSlice.actions;

export const getCountryCodes = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT);

  if (response?.data) {
    dispatch(updateCountryCodes(response.data));
  }
};

export default countryCodesSlice;
