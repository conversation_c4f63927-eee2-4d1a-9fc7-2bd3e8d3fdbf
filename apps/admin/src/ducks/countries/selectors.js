import { createSelector } from '@reduxjs/toolkit';

import { DATA_COUNTRY_CODES, DEFAULT_COUNTRY_CODES, REDUCER_KEY } from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectCountryCodes = createSelector(
  baseSelector,
  (state) => state[DATA_COUNTRY_CODES] || DEFAULT_COUNTRY_CODES,
);

export const selectCountryCodesList = createSelector(selectCountryCodes, (countryCodes) =>
  countryCodes.map(({ countryCode, name }) => ({ label: name, value: countryCode })),
);
