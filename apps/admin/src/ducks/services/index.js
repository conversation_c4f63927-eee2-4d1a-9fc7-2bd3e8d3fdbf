import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_SCOPE_ROLES_DETAIL,
  DATA_SERVICES_ASSIGNMENT_DETAIL,
  DATA_SERVICES_DETAIL,
  DATA_SERVICES_ROLE_DETAIL,
  DATA_SERVICES_SCOPE_DETAIL,
  DATA_SERVICE_CONSTRAINTS,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const servicesSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateAssignments(
      state,
      {
        payload: {
          id,
          data: { records, pageSize, pageOffset, totalRecord },
        },
      },
    ) {
      const servicesAssignmentsDetail = state[DATA_SERVICES_ASSIGNMENT_DETAIL];

      if (!servicesAssignmentsDetail[id]) {
        servicesAssignmentsDetail[id] = {
          ...DEFAULT_TABLE_DETAIL,
        };
      }

      const serviceAssignment = servicesAssignmentsDetail[id];

      if (isArray(records)) {
        serviceAssignment.error = {};
        serviceAssignment.hasError = false;
        serviceAssignment.hasData = true;

        serviceAssignment.pageSize = pageSize;
        serviceAssignment.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          serviceAssignment.totalRecord = totalRecord;
          serviceAssignment.data = [...records];
        } else {
          serviceAssignment.data = [...serviceAssignment.data, ...records];
        }

        if (serviceAssignment.totalRecord === serviceAssignment.data.length) {
          serviceAssignment.hasFetchedAllRecords = true;
        }

        servicesAssignmentsDetail[id] = serviceAssignment;
      }

      state[DATA_SERVICES_ASSIGNMENT_DETAIL] = servicesAssignmentsDetail;
    },
    updateRoles(state, { payload: { id, data } }) {
      const rolesDetail = state[DATA_SERVICES_ROLE_DETAIL];

      if (!rolesDetail[id]) {
        rolesDetail[id] = [];
      }

      if (isArray(data)) {
        rolesDetail[id] = data;
      }

      state[DATA_SERVICES_ROLE_DETAIL] = rolesDetail;
    },
    updateScopes(state, { payload: { id, data } }) {
      const scopesDetail = state[DATA_SERVICES_SCOPE_DETAIL];

      if (!scopesDetail[id]) {
        scopesDetail[id] = [];
      }

      if (isArray(data)) {
        scopesDetail[id] = data;
      }

      state[DATA_SERVICES_SCOPE_DETAIL] = scopesDetail;
    },
    updateScopeRoles(state, { payload: { id, data } }) {
      const scopeRoleDetail = state[DATA_SCOPE_ROLES_DETAIL];

      if (!scopeRoleDetail[id]) {
        scopeRoleDetail[id] = [];
      }

      if (isArray(data)) {
        scopeRoleDetail[id] = data;
      }

      state[DATA_SCOPE_ROLES_DETAIL] = scopeRoleDetail;
    },
    updateServices(state, { payload: { id, data } }) {
      const servicesDetail = state[DATA_SERVICES_DETAIL];

      if (!servicesDetail[id]) {
        servicesDetail[id] = {};
      }

      if (data) {
        let serviceDetail = servicesDetail[id];

        serviceDetail = data;

        servicesDetail[id] = serviceDetail;
      }

      state[DATA_SERVICES_DETAIL] = servicesDetail;
    },
    updateConstraints(state, { payload }) {
      state[DATA_SERVICE_CONSTRAINTS] = payload;
    },
  },
});

const {
  updateAssignments,
  updateRoles,
  updateScopes,
  updateScopeRoles,
  updateServices,
  updateTableDetail,
  updateConstraints,
} = servicesSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    groupname = '',
    linkedServicesOnly = true,
    includeDefaultServices = true,
    includeDepSvces = true,
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT +
      `?limit=${pageSize}&offset=${pageOffset}&linkedServicesOnly=${linkedServicesOnly}&includeDefaultServices=${includeDefaultServices}&includeDepSvces=${includeDepSvces}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    if (groupname) {
      requestUrl += `&groupname=${encodeURIComponent(groupname.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const getUsersAndGroup =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    id = '',
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `/${id}/assignments?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateAssignments({ id, data: response.data }));
    }
  };

export const addUsersAndGroup =
  ({ id, payload }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/${id}/assignments`;

    await http.post(requestUrl, payload);
  };

export const getServiceRoles =
  ({ id, scopeId }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}/roles${scopeId ? `?tsScopeId=${scopeId}` : ''}`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      if (scopeId) {
        dispatch(updateScopeRoles({ id: scopeId, data: response.data }));
      } else {
        dispatch(updateRoles({ id, data: response.data }));
      }
    }
  };

export const getServiceScopes =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}/scopes`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateScopes({ id, data: response.data }));
    }
  };

export const updateAssignment =
  ({ id, tservice, ...payload }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${tservice?.id}/assignments/${id}`;

    const response = await http.put(requestUrl, { ...payload, id, tservice });

    if (response?.data) {
      dispatch(getUsersAndGroup({ id: tservice?.id, requireTotal: true }));
    }
  };

export const deleteAssignment =
  ({ id, tservice }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${tservice?.id}/assignments/${id}`;

    await http.delete(requestUrl);

    dispatch(getUsersAndGroup({ id: tservice?.id, requireTotal: true }));
  };

export const deleteBulkAssignment =
  ({ id, ids }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}/assignments/bulk-delete`;

    await http.put(requestUrl, ids);

    dispatch(getUsersAndGroup({ id, requireTotal: true }));
  };

export const getServiceDetail =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateServices({ id, data: response.data }));
    }
  };

export const syncDomain = () => async () => {
  const requestUrl = API_ENDPOINT + `/sync-domains`;

  await http.post(requestUrl);
};

export const syncServiceRoles =
  ({ id }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/sync-roles?tServiceId=${id}`;

    await http.post(requestUrl);
  };

export const inheritSevice =
  ({ id, payload }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}/inherit-tservice-id`;

    const response = await http.put(requestUrl, { ...payload });

    if (response?.data) {
      dispatch(getServiceDetail({ id }));
    }
  };

export const linkUnlinkServices =
  ({ ids, link }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${link ? 'link' : 'unlink'}`;

    await http.put(requestUrl, ids);

    dispatch(
      getList({ linkedServicesOnly: false, includeDefaultServices: false, includeDepSvces: false }),
    );
  };

export const getServiceConstraints = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/service-constraints';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateConstraints(response.data));
  }
};

export default servicesSlice;
