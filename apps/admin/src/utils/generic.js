import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../images_base64/BI';
import CC<PERSON><PERSON>GO from '../images_base64/CCP';
import CELLEDGE<PERSON>OGO from '../images_base64/CELLEDGE';
import DECEPTION<PERSON>OGO from '../images_base64/Deception';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../images_base64/EASM';
import <PERSON><PERSON><PERSON>G<PERSON> from '../images_base64/EC';
import IAMLOGO from '../images_base64/IAM';
import TRUSTLOGO from '../images_base64/Trust';
import <PERSON><PERSON><PERSON><PERSON>GO from '../images_base64/ZBI';
import ZBPLOGO from '../images_base64/ZBP';
import ZD<PERSON><PERSON>OGO from '../images_base64/ZDX';
import Z<PERSON><PERSON><PERSON>G<PERSON> from '../images_base64/ZIA';
import <PERSON><PERSON><PERSON><PERSON>G<PERSON> from '../images_base64/ZPA';
import ZRAL<PERSON>G<PERSON> from '../images_base64/ZRA';
import Z<PERSON><PERSON>OGO from '../images_base64/ZWA';

export const getAppIcon = (value) => {
  if (value === 'ZPA') {
    return ZPALOGO;
  } else if (value === 'ZIAM') {
    return IAMLOGO;
  } else if (value === 'ZIA') {
    return ZIALOGO;
  } else if (value === 'ZDX') {
    return ZDXLOGO;
  } else if (value === 'CLOUD_CONNECTOR') {
    return ECLOGO;
  } else if (value === 'ZCC') {
    return CCPLOGO;
  } else if (value === 'ZBI') {
    return ZBILOGO;
  } else if (value === 'BI') {
    return BILOGO;
  } else if (value === 'ZRA') {
    return ZRALOGO;
  } else if (value === 'TRUST') {
    return TRUSTLOGO;
  } else if (value === 'DECEPTION') {
    return DECEPTIONLOGO;
  } else if (value === 'EASM') {
    return EASMLOGO;
  } else if (value === 'ZBP') {
    return ZBPLOGO;
  } else if (value === 'ZWA') {
    return ZWALOGO;
  } else if (value === 'CELLEDGE') {
    return CELLEDGELOGO;
  } else {
    return ZPALOGO;
  }
};

export const listGenerator = (num) => ({ label: num, value: num });

export const getDataTestId = (id = '', parentId = '') => {
  const parent = typeof parentId == 'number' ? parentId.toString() : parentId;
  const child = typeof id == 'number' ? id.toString() : id;

  return parent && child ? `${parent}-${child}` : child ? `${child}` : parent ? `${parent}` : '';
};
