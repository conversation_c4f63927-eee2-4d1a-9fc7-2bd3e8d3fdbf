Generic/Reusable HTTP methods to make server calls.

We are exposing a method and a object to be used in rest of the App.

- `http` A axios instance with config required to make authenticated http calls to server. Base url, timeout and auth headers are handle by this instance.

- `genericInterface` A generic object that can interface with REST APIs. You can bind this generic interface to an api endpoint supplied by you.

Example:

```
// in file ducks/<feature>/index.js

import { genericInterface } from 'utils/http';
import * as constants from './constants';

const uxScoreApi = genericInterface(constants.API_ENDPOINT);

export const getUXScore = () => (dispatch) => {
  dispatch(loading());
  return uxScoreApi.read()
    .then((response) => {
      dispatch(loadSuccess(response.data));
      dispatch(loaded());
    })
    .catch((error) => {
      dispatch(loadError(error));
      dispatch(loaded());
    });
};
```
