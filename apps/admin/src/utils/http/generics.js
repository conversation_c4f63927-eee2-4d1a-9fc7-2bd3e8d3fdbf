import http from './http';

export const genericInterface = (RESOURCE) => ({
  post: async (obj, opts) => http.post(RESOURCE, obj, opts),
  read: async (id, opts) => {
    const url = id ? `${RESOURCE}/${id}` : RESOURCE;
    return http.get(url, opts);
  },
  update: async (obj, opts) => http.put(`${RESOURCE}/${obj && obj.id ? obj.id : ''}`, obj, opts),
  del: async (obj, opts) => http.delete(RESOURCE, obj, opts),
});
