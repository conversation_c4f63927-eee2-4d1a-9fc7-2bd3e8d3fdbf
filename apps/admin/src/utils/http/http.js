'use client';

import axios from 'axios';

import { BASE_URL } from '../../config';
import { handleError } from './handleError';

const http = axios.create({
  baseURL: BASE_URL,
  timeout: 3 * 60 * 1000, // increased to support image download (usually 1 - 2 GB)
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

let environment;
let getBearerToken;

export function initializeHttpClient(env, tokenGetter) {
  environment = env;
  getBearerToken = tokenGetter;
}

if (process.env.IS_ONEUI) {
  http.interceptors.request.use((config) => {
    if (!environment || !getBearerToken) {
      throw new Error(
        'HTTP client is not initialized. Call initializeHttpClient before making requests.',
      );
    }
    const publicAPIEndpoint = environment.endpoint();
    const accessToken = getBearerToken();

    config.headers.Authorization = `Bearer ${accessToken}`;
    config.baseURL = `${publicAPIEndpoint}/private/ziam`;

    return config;
  });
}

http.interceptors.response.use((response) => response, handleError);

export default http;
