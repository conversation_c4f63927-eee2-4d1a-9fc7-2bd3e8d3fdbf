import deDE from './de-de';
import enUS from './en-us';
import esES from './es-es';
import frFR from './fr-fr';
import jaJP from './ja-jp';
import zhCN from './zh-cn';

export const getSelectedLocale = () => {
  const locale = localStorage?.locale || 'en-US';

  return locale;
};

export const LOCALES = {
  'en-US': enUS,
  'es-ES': esES,
  'fr-FR': frFR,
  'zh-CN': zhCN,
  'ja-JP': jaJP,
  'de-DE': deDE,
};
