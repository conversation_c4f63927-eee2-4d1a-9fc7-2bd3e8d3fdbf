import { MemoryRouter } from 'react-router-dom';

import PropTypes from 'prop-types';

import '../utils/i18n';

import StoreProvider from '../StoreProvider';
import TestAppLayout from './TestAppLayout';

const TestApp = ({ children }) => {
  return (
    <StoreProvider>
      <MemoryRouter>
        <TestAppLayout>{children}</TestAppLayout>
      </MemoryRouter>
    </StoreProvider>
  );
};

TestApp.propTypes = {
  children: PropTypes.node.isRequired,
};

export default TestApp;
