import { testApiServer } from '../api/server';

export const setupTestApiServer = () => {
  // establish API mocking before all tests
  beforeAll(() => testApiServer.listen());
  // reset any request handlers that are declared as a part of our tests
  // (i.e. for testing one-time error scenarios)
  afterEach(() => testApiServer.resetHandlers());
  // clean up once the tests are done
  afterAll(() => testApiServer.close());
};
