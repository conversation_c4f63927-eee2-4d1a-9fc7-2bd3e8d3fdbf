import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

import { faCheckCircle } from '@fortawesome/free-solid-svg-icons';
import { faExclamationCircle, faExternalLink } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  Field,
  Input,
  SmallCardLayout,
  Tooltip,
  getSearchParams,
  mergeFormValues,
  useApiCall,
  validEmail,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import Logo from '../../components/logo/Logo';
import InputOTP from '../../components/signup/InputOTP';

import {
  getEUSAContent,
  getOnboardStatusPolling,
  onboard,
  onboardVerify,
  sendEmailOTP,
  verifyEmailOTP,
} from '../../ducks/tenant';

import { getFormValidationDetail } from './helper';

const SignupPage = () => {
  const { t } = useTranslation();

  const buttonRef = useRef();
  const queryParam = getSearchParams(useLocation().search);

  const { apiCall } = useApiCall({ clearNotificationOnNextCall: true });
  const { domain = '', tenant = '', userSetup } = serverConfig || {};

  /**
   * 1: Organization name/First Name/Last Name
   * 1.2 : Email Address
   * 1.3 : Verify Email via OTP
   * 1.4 : Domain Name
   */
  const [formStep, setFormStep] = useState(1);
  const [formValues, setFormValues] = useState({
    eusaAccept: true,
    name: tenant || queryParam.name || '',
    pseudoDomainPrefix: queryParam.pseudoDomainPrefix || '',
    primaryEmail: queryParam.primaryEmail || '',
    firstName: queryParam.firstName || '',
    lastName: queryParam.lastName || '',
    password: '',
    confirmPassword: '',
    phone: '',
    phoneotp: '',
    domain,
  });
  const [validationDetail, setValidationDetail] = useState({});

  const [isEmailOTPSent, setEmailOTPSent] = useState(false);
  const [isInvalidOTP, setInvalidOTP] = useState(false);
  const [isEmailVerified, setEmailVerified] = useState(false);
  const [timer, setTimer] = useState(0);
  const [eusaContent, setEusaContent] = useState({});
  const [inputOTPKey, setInputOTPKey] = useState(0);
  const timeOutCallback = useCallback(() => setTimer((currTimer) => currTimer - 1), []);

  // show resent only after 60 sec
  useEffect(() => {
    timer > 0 && setTimeout(timeOutCallback, 1000);
  }, [timer, timeOutCallback]);

  const resetTimer = function () {
    if (!timer) {
      setTimer(60);
    }
  };

  //handle scenario if user closed browser
  useEffect(() => {
    // switch step for intermediat screen
    if (userSetup) {
      setFormStep(3);
    }
  }, []);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
      formStep,
      isEmailVerified,
      isEmailOTPSent,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onKeyPress = (evt) => {
    const { key } = evt;

    if (key === 'Enter') {
      evt?.preventDefault();

      if (!isNextDisabled()) {
        onNext();
      }
    }
  };

  const isNextDisabled = () => {
    if (formStep === 1) {
      let isValid = formValues.name && formValues.firstName && formValues.lastName;

      return !isValid;
    } else if (formStep === 1.3) {
      let isValid =
        formValues.name &&
        !validEmail(formValues.primaryEmail) &&
        formValues.firstName &&
        formValues.lastName &&
        isEmailVerified;
      return !isValid;
    } else if (formStep === 1.4) {
      let isValid =
        validationDetail.isValid &&
        formValues.name &&
        formValues.pseudoDomainPrefix &&
        !validEmail(formValues.primaryEmail) &&
        formValues.firstName &&
        formValues.lastName &&
        isEmailVerified;

      return !isValid;
    } else if (formStep === 2) {
      return false;
    } else if (formStep === 3) {
      return false;
    }

    return true;
  };

  const onNext = () => {
    if (formStep === 1) {
      setFormStep(1.2);
    } else if (formStep === 1.3) {
      setFormStep(1.4);
    } else if (formStep === 1.4) {
      const payload = { ...formValues };

      delete payload.confirmPassword;
      delete payload.domain;
      delete payload.phone;
      delete payload.phoneotp;
      // verify domain before onboard on first step
      verifyDomain(payload);
    } else if (formStep === 2) {
      const payload = { ...formValues };

      delete payload.confirmPassword;
      delete payload.domain;
      delete payload.phone;
      delete payload.phoneotp;
      // do onboarding as domain verfied
      onboardTenant(payload);
    } else if (formStep === 3) {
      getTenantUserStatus();
    } else {
      // wrong place step 3 is on sso portal
    }
  };

  // verify if tenant domain is valid
  const verifyDomain = (payload) => {
    apiCall(onboardVerify(payload))
      .then(() => {
        // if domain verifed open eusa and load content
        setFormStep(2);

        apiCall(getEUSAContent())
          .then((detail) => {
            setEusaContent(detail);
          })
          .catch(noop);
      })
      .catch(noop);
  };

  // onboard tenant after domain is verified
  const onboardTenant = (payload) => {
    apiCall(onboard(payload))
      .then(() => {
        getTenantUserStatus();
      })
      .catch(noop);
  };

  const getTenantUserStatus = () => {
    setFormStep(4);
    apiCall(getOnboardStatusPolling())
      .then(({ status, redirectUrl } = {}) => {
        if (status && redirectUrl) {
          // redirect to sso portal on vanity url
          window.location.href = redirectUrl;
        }
      })
      .catch(noop);
  };

  // send OTP on email to verify email address
  const onSendEmailOTP = () => {
    setEmailOTPSent(false);
    setInvalidOTP(false);

    apiCall(sendEmailOTP(formValues.primaryEmail), {
      errorNotificationPayload: {
        message: `Error Sending OTP to ${formValues.primaryEmail}`,
      },
    })
      .then((response) => {
        if (response.status === 200) {
          setEmailOTPSent(true);
          setInvalidOTP(false);
          setEmailVerified(false);
          setFormStep(1.3);
          resetTimer();
        }
      })
      .catch(() => {
        setEmailOTPSent(false);
      });
  };

  const submitEmailOTP = (otp) => {
    apiCall(verifyEmailOTP(otp))
      .then((response) => {
        if (response.status === 200 && response?.data && response?.data.valid) {
          setEmailVerified(true);
          setInvalidOTP(false);
        } else {
          setInputOTPKey(inputOTPKey + 1);
          setInvalidOTP(true);
          setEmailVerified(false);
        }
      })
      .catch(() => {
        setInvalidOTP(true);
        setEmailVerified(false);
      });
  };

  const onBack = () => {
    // back btn not allowed from step 3
    if (formStep === 2) {
      setFormStep(1.4);
    }
    if (formStep === 1.3) {
      setFormStep(1.2);
    }

    if (formStep === 1) {
      // reset email verification
      setEmailVerified(false);
      setEmailOTPSent(false);
    }
    return;
  };

  const getIntroContent = () => {
    if (formStep === 3 || formStep === 4 || formStep === 5) {
      return null;
    }

    let titleSection = 'Create your Zscaler Identity';
    let subTitleSection = 'Register your tenant with Zscaler Identity.';

    if (formStep === 2) {
      titleSection = 'EUSA Agreement';
      subTitleSection = 'Review the below agreement and indicate your acceptance.';
    }

    if (formStep === 1.2) {
      titleSection = 'Enter Email Address for Verification';
      subTitleSection = 'A one-time code will be sent to your email address.';
    }

    if (formStep === 1.3) {
      titleSection = 'Verify Email with One-Time Code';
      subTitleSection = `We have sent a code to ${formValues.primaryEmail}`;
    }

    if (formStep === 1.4) {
      titleSection = 'Domain';
      subTitleSection = 'We generated a random domain for you.';
    }

    return (
      <div className="intro-container">
        <div className="typography-header5 title">{titleSection}</div>
        <div className="typography-paragraph2 subtitle">{subTitleSection}</div>
      </div>
    );
  };

  const getBodyContent = () => {
    if (formStep === 5) {
      return (
        <>
          <p>
            Please contact{' '}
            <a
              href={'https://help.zscaler.com/submit-ticket-links'}
              className="has-color-primary"
              target={'_blank'}
              rel="noreferrer"
            >
              Zscaler Support <FontAwesomeIcon icon={faExternalLink} className="icon" />
            </a>{' '}
            to restart tenant creation.
          </p>
        </>
      );
    }

    if (formStep === 4) {
      return (
        <>
          <p>
            We are preparing your organization for the first time usage, it may take few minutes
          </p>

          <p style={{ marginTop: '2rem', fontSize: '1.4rem' }}>
            please wait (<strong>do not close the browser</strong>)...
          </p>
        </>
      );
    }

    if (formStep === 3) {
      return (
        <div className="tenant-partial">
          <p>{t('PARTIAL_TENANT_MSG')}</p>
          <p>{t('CLICK_CONTINUE_MSG')}</p>
        </div>
      );
    }

    if (formStep === 2) {
      const { agreement } = eusaContent;
      return (
        <div>
          <div
            className="text-p2 text-content-default-default h-[224px] overflow-y-auto"
            dangerouslySetInnerHTML={{ __html: agreement }}
          ></div>
        </div>
      );
    }

    if (formStep === 1.4) {
      return (
        <Field label="Domain Name" containerClass="no-m">
          <div className="typography-paragraph2 subtitle">
            You can access the Zscaler Identity portal with this domain.
          </div>

          <div className="is-flex has-ai-c" style={{ marginTop: '5px', gap: '8px' }}>
            <div style={{ width: '240px' }}>
              <Input
                name="pseudoDomainPrefix"
                onChange={onFormFieldChange}
                value={formValues.pseudoDomainPrefix}
                placeholder="Enter initial domain"
                maxLength="57"
                autoFocus
                info={validationDetail}
                containerClass="no-m"
              />
            </div>
            <div className="text-p1 text-content-default-default pt-[10px]">
              .{formValues.domain}
            </div>
          </div>
        </Field>
      );
    }

    if (formStep === 1.3) {
      return (
        <div className="otp-input-form-container">
          {isEmailOTPSent && (
            <div className="is-flex has-jc-sb has-ai-c" style={{ marginRight: '24px' }}>
              <span className="typography-paragraph2">Enter the Code</span>
              {!isEmailVerified && (
                <>
                  {timer > 0 ? (
                    <span className="typography-paragraph2-strong has-color-primary">{`00:${timer}`}</span>
                  ) : (
                    <span
                      className="typography-paragraph1-strong has-color-primary pointer"
                      onClick={onSendEmailOTP}
                      onKeyDown={noop}
                    >
                      {t('RESEND')}
                    </span>
                  )}
                </>
              )}
            </div>
          )}

          {!isEmailOTPSent && (
            <InputOTP
              key={inputOTPKey}
              isEmailVerified={isEmailVerified}
              isEmailOTPSent={isEmailOTPSent}
              isInvalidOTP={isInvalidOTP}
              primaryEmail={formValues.primaryEmail}
              submitEmailOTP={submitEmailOTP}
            />
          )}

          {isInvalidOTP || isEmailVerified ? (
            <div className="is-flex has-ai-c" style={{ gap: '6px' }}>
              <FontAwesomeIcon
                icon={isInvalidOTP ? faExclamationCircle : faCheckCircle}
                className={
                  isInvalidOTP ? 'has-color-error-secondary' : 'has-color-success-secondary'
                }
              />
              <span>{isInvalidOTP ? 'Incorrect code entered' : 'Code verified successfully.'}</span>
            </div>
          ) : null}
        </div>
      );
    }

    if (formStep === 1.2) {
      return (
        <Input
          name="primaryEmail"
          label="EMAIL_ADDRESS"
          placeholder="Enter your email address"
          autoFocus
          onChange={onFormFieldChange}
          value={formValues.primaryEmail}
          info={validationDetail}
        />
      );
    }

    return (
      <form className="form-steps-container" onKeyPress={onKeyPress}>
        <Input
          name="name"
          label="ORG_NAME"
          onChange={onFormFieldChange}
          value={formValues.name}
          maxLength="128"
          autoFocus
          placeholder="Enter the name of your organization"
          info={validationDetail}
        />

        <div className="field-group">
          <Input
            name="firstName"
            label="FIRST_NAME"
            placeholder="Enter your first name"
            onChange={onFormFieldChange}
            value={formValues.firstName}
            info={validationDetail}
          />

          <Input
            name="lastName"
            label="LAST_NAME"
            placeholder="Enter your last name"
            onChange={onFormFieldChange}
            value={formValues.lastName}
            info={validationDetail}
          />
        </div>
      </form>
    );
  };

  const getNextButtonLabel = () => {
    if (formStep === 3) {
      return t('CONTINUE');
    }

    if (formStep === 2) {
      return t('ACCEPT');
    }

    return t('NEXT');
  };

  return (
    <SmallCardLayout>
      <div id="signup-page" className="is-flex has-fd-c">
        <section>
          <Logo isFull />
          {getIntroContent()}
        </section>

        <section className="body">{getBodyContent()}</section>

        <section className="footer-section">
          {(formStep === 1.3 || formStep === 2) && (
            <Button type="tertiary" onClick={onBack}>
              {t('BACK')}
            </Button>
          )}

          {!(formStep === 5 || formStep === 4 || formStep === 1.2) && (
            <>
              <Button ref={buttonRef} type="primary" onClick={onNext} disabled={isNextDisabled()}>
                {getNextButtonLabel()}
              </Button>
              {isNextDisabled() && (
                <Tooltip elementRef={buttonRef}>
                  <div className="has-background-light shadow-small">
                    <Card>Fill the required fields.</Card>
                  </div>
                </Tooltip>
              )}
            </>
          )}

          {formStep === 1.2 && (
            <Button
              type="primary"
              onClick={onSendEmailOTP}
              disabled={validEmail(formValues.primaryEmail)}
            >
              Verify
            </Button>
          )}

          {formStep === 3 && (
            <Button type="tertiary" onClick={() => setFormStep(5)}>
              {t('RESTART_TENANT_CREATION')}
            </Button>
          )}
        </section>
      </div>
    </SmallCardLayout>
  );
};

export default SignupPage;
