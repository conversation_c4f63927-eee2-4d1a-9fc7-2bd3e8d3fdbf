import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { HelpContainer, TableContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import UsersAndGroupsTabs from '../../components/service-entitlements/UsersAndGroupsTabs';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList, resetTableData } from '../../ducks/service-entitlements';
import { selectTableConfig, selectTableDetail } from '../../ducks/service-entitlements/selectors';

import NavLayout from '../../layout/NavLayout';

import { getAppIcon } from '../../utils/generic';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const ServiceEntitlementsPage = () => {
  const { apiCall } = useApiCall();
  const dispatch = useDispatch();
  const privileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY),
  );

  const userAndGroupPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY),
  );

  const { noAccess } = privileges;

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const [showUsersAndGroupsTabs, setShowUsersAndGroupsTabs] = useState(false);
  const [usersAndGroupsTabsServiceDetail, setUsersAndGroupsTabsServiceDetail] = useState({});

  const { t } = useTranslation();

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  const hideUsersAndGroupsTabs = () => {
    dispatch(resetTableData());
    setShowUsersAndGroupsTabs(false);
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'serviceName') {
        const ServiceName = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              original: { serviceDescription, serviceName },
            },
          } = props;
          // eslint-disable-next-line react/prop-types
          const serviceDetail = props.row.original;

          const onServiceClick = (detail) => {
            const { noAccess } = userAndGroupPrivileges;

            if (!noAccess) {
              setUsersAndGroupsTabsServiceDetail(detail);
              setShowUsersAndGroupsTabs(true);
            }
          };

          return (
            <>
              <div
                className="is-flex has-ai-c pointer"
                onClick={() => {
                  onServiceClick(serviceDetail);
                }}
                onKeyDown={noop}
              >
                <span
                  style={{
                    width: /TRUST/.test(serviceName) ? '39px' : '32px',
                    height: /TRUST/.test(serviceName) ? '39px' : '32px',
                    margin: '0 1em',
                    marginLeft: '2.5em',
                  }}
                >
                  <img
                    src={getAppIcon(serviceName)}
                    style={{ height: '100%', width: '100%', objectFit: 'contain' }}
                    alt="service name"
                  />
                </span>
                <span> {serviceDescription}</span>
              </div>
            </>
          );
        };

        columnDetail.cell = ServiceName;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, userAndGroupPrivileges?.noAccess]);

  if (noAccess) {
    return <NoAccess />;
  }

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <NavLayout>
      {!showUsersAndGroupsTabs && <HelpContainer src={HELP_ARTICLES.SERVICE_ENTITLEMENTS} />}
      <article id="entitlements" className="page-container">
        {!showUsersAndGroupsTabs && (
          <section className="typography-header3 page-title">{t('SERVICE_ENTITLEMENTS')}</section>
        )}
        {showUsersAndGroupsTabs ? (
          <UsersAndGroupsTabs
            serviceDetail={usersAndGroupsTabsServiceDetail}
            onBackClick={hideUsersAndGroupsTabs}
            privileges={userAndGroupPrivileges}
            services={tableDetail?.data}
          />
        ) : (
          <TableContainer
            {...tableConfig}
            columns={tableColumnConfig}
            data={tableDetail.data}
            pagination={{ ...tableDetail, onLoadMoreClick }}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default ServiceEntitlementsPage;
