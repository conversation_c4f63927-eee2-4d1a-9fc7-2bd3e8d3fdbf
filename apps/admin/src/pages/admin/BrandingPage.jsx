import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faPencil, faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  Field,
  FieldGroup,
  HelpContainer,
  Input,
  InputFile,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop, startsWith } from 'lodash-es';

import {
  convertToBase64,
  getApiPayload,
  getFormValidationDetail,
  validateImage,
} from '../../components/branding/helper';

import { getBrandingInfo, update } from '../../ducks/branding';
import { selectBrandingDetails } from '../../ducks/branding/selectors';
import { showErrorNotification } from '../../ducks/global';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import ZscalerLogo from '../../images_base64/ZS';

const BrandingPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { apiCall } = useApiCall();

  const brandingPrivileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.BRANDING_POLICY));

  const { hasFullAccess, noAccess: brandingNoAccess } = brandingPrivileges;

  const brandingDetails = useSelector(selectBrandingDetails);

  const [formValues, setFormValues] = useState({});

  const [saveButtonDisabled, setSaveButtonDisabled] = useState(true);
  const [isFormDirty, setIsFormDirty] = useState(false);
  const [validationDetail, setValidationDetail] = useState({});

  useEffect(() => {
    apiCall(getBrandingInfo()).catch(noop);
  }, []);

  useEffect(() => {
    setFormValues(brandingDetails);
  }, [brandingDetails]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
      validationDetail,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    if (!isFormDirty || !validationDetail.isValid) {
      setSaveButtonDisabled(true);
    } else {
      setSaveButtonDisabled(false);
    }
  }, [isFormDirty, validationDetail]);

  const deleteLogo = () => {
    const newFormValues = { ...formValues };
    newFormValues['logo'] = '';
    setFormValues(newFormValues);
    setIsFormDirty(true);
  };

  const onFormFieldChange = (evt, index) => {
    setIsFormDirty(true);
    const { name, value } = evt?.target || {};
    const newFormValues = { ...formValues };

    if (name === 'fromEmail') {
      newFormValues['fromEmail'] = value;
    } else {
      const emailBranding = [...newFormValues.emailBranding];
      emailBranding[index] = {
        ...emailBranding[index],
        ...{ subject: value },
      };
      newFormValues.emailBranding = [...emailBranding];
    }

    setFormValues(newFormValues);
  };

  const ConfigurationSettings = () => {
    let imageSrc = ZscalerLogo;
    if (formValues?.logo) {
      imageSrc = startsWith(formValues?.logo, 'data:')
        ? formValues?.logo
        : 'data:image/png;base64, ' + formValues?.logo;
    }
    return (
      <>
        <section className="typography-paragraph1-uppercase">
          <span>{t('CONFIGURATION_SETTINGS')}</span>
        </section>
        <Card>
          <div
            style={{
              width: '230px',
              height: '146px',
            }}
          >
            <img
              src={imageSrc}
              style={{ height: '146px', width: '230px', objectFit: 'contain' }}
              alt="brand icon"
            />
          </div>
          {hasFullAccess && (
            <div className="buttons icon">
              <InputFile
                onChange={onLogoUpload}
                leftIcon={faPencil}
                hasLeftIcon
                buttonLabel="EDIT_LOGO"
                buttonType="primary"
                fileInfoPosition={'right'}
              />
              {formValues.logo && (
                <Button type="tertiary" onClick={deleteLogo}>
                  <FontAwesomeIcon icon={faTrash} className="icon left" />
                  <span>{t('DELETE')}</span>
                </Button>
              )}
            </div>
          )}
        </Card>
      </>
    );
  };

  const EmailSubjectSettings = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">
          <span>{t('CUSTOMIZE_EMAIL_SUBJECT')}</span>
        </section>
        <Card>
          <div className="email-subject">
            <FieldGroup containerClass="email-subject-header">
              <Field label={t('DESCRIPTION')} containerClass="field-stacked">
                {t('EMAIL_SUBJECT')}
              </Field>
            </FieldGroup>
            {formValues?.emailBranding?.map((item, index) => {
              return (
                <FieldGroup key={index}>
                  <Input
                    label={item.description}
                    maxLength="70"
                    name={item.name}
                    value={item.subject}
                    onChange={(evt) => {
                      onFormFieldChange(evt, index);
                    }}
                    info={validationDetail}
                    containerClass="field-stacked"
                    disabled={!hasFullAccess}
                    tooltip={{}}
                  />
                </FieldGroup>
              );
            })}
          </div>
        </Card>
      </>
    );
  };

  const EmailAddressSettings = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">
          <span>{t('CUSTOMIZE_EMAIL_ADDRESS_SENT_BY')}</span>
        </section>
        <Card containerClass="is-flex from-email">
          <Input
            label="EMAIL_FROM_ADDRESS"
            info={validationDetail}
            maxLength="254"
            name="fromEmail"
            value={formValues.fromEmail}
            containerStyle={{ maxWidth: '280px' }}
            tooltip={
              !hasFullAccess
                ? {}
                : {
                    content: 'Enter the From Email Address',
                  }
            }
            onChange={onFormFieldChange}
            disabled={!hasFullAccess}
          />
        </Card>
      </>
    );
  };

  const onLogoUpload = (detail) => {
    if (detail?.length > 0) {
      if (validateImage(detail[0])) {
        convertToBase64(detail[0])
          .then((data) => {
            const newFormValues = { ...formValues };
            newFormValues['logo'] = data;
            setIsFormDirty(true);
            setFormValues(newFormValues);
          })
          .catch(() => {
            dispatch(showErrorNotification({ message: 'BASE_64_CONVERSION_FAILED' }));
          });
      } else {
        dispatch(
          showErrorNotification({
            message: 'Must be a valid image and image size must not exceed 70KB.',
          }),
        );
      }
    }
  };

  const saveBrandingInfo = () => {
    const payload = getApiPayload(formValues);
    apiCall(update(payload), {
      successNotificationPayload: { ...getApiPUTNotificationOptions() },
    })
      .then(() => {
        setSaveButtonDisabled(true);
        setIsFormDirty(false);
      })
      .catch(noop);
  };

  const cancelChanges = () => {
    setSaveButtonDisabled(true);
    setIsFormDirty(false);
    apiCall(getBrandingInfo()).catch(noop);
  };

  const renderActionsSection = () => {
    if (!hasFullAccess) {
      return null;
    }

    return (
      <div className="action-section buttons">
        <Button
          disabled={saveButtonDisabled}
          onClick={() => {
            saveBrandingInfo();
          }}
        >
          {t('SAVE')}
        </Button>
        <Button
          disabled={saveButtonDisabled}
          type="tertiary"
          containerClass="no-p-l"
          onClick={() => {
            cancelChanges();
          }}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.BRANDING} />
      <article id="admin-branding" className="page-container">
        {!brandingNoAccess && (
          <>
            <section className="typography-header3 page-title">{t('BRANDING')}</section>
            {ConfigurationSettings()}
            {EmailSubjectSettings()}
            {EmailAddressSettings()}
            {renderActionsSection()}
          </>
        )}
      </article>
    </NavLayout>
  );
};

export default BrandingPage;
