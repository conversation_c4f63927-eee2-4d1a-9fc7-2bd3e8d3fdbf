import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import ApiClientsActions from '../../components/api-clients/ApiClientsActions';
import ApiClientsCRUD from '../../components/api-clients/ApiClientsCRUD';
import ApiClientsHelpContainer from '../../components/api-clients/ApiClientsHelpContainer';
import ApiClientsTable from '../../components/api-clients/ApiClientsTable';
import { searchOptions } from '../../components/api-clients/helper';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/api-clients';
import { getList as getResourcesList } from '../../ducks/api-resources';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const ApiClientsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
    apiCall(getResourcesList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <CRUDPageContextProvider defaultSearchOption={searchOptions[0]} privileges={privileges}>
        <ApiClientsHelpContainer />

        <article id="admin-api-clients" className="page-container">
          <section className="typography-header3 page-title">{t('API_CLIENTS')}</section>

          <Card>
            <ApiClientsActions />

            <ApiClientsTable />
          </Card>

          <ApiClientsCRUD />
        </article>
      </CRUDPageContextProvider>
    </NavLayout>
  );
};

export default ApiClientsPage;
