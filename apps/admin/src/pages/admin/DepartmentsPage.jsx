import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import DepartmentActions from '../../components/department/DepartmentActions';
import DepartmentCRUD from '../../components/department/DepartmentCRUD';
import DepartmentTable from '../../components/department/DepartmentTable';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/departments';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const DepartmentsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.DEPARTMENTS} />

      <article id="admin-groups" className="page-container">
        <section className="typography-header3 page-title">{t('DEPARTMENTS')}</section>

        <CRUDPageContextProvider privileges={privileges}>
          <Card>
            <DepartmentActions />

            <DepartmentTable />
          </Card>

          <DepartmentCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default DepartmentsPage;
