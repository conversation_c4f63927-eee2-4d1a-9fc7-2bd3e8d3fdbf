import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import GroupActions from '../../components/group/GroupActions';
import GroupCRUD from '../../components/group/GroupCRUD';
import GroupTable from '../../components/group/GroupTable';
import { searchOptions } from '../../components/group/helper';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/groups';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const UserGroupsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.USER_GROUPS} />

      <article id="admin-groups" className="page-container">
        <section className="typography-header3 page-title">{t('USER_GROUPS')}</section>

        <CRUDPageContextProvider defaultSearchOption={searchOptions[0]} privileges={privileges}>
          <Card>
            <GroupActions />

            <GroupTable />
          </Card>

          <GroupCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default UserGroupsPage;
