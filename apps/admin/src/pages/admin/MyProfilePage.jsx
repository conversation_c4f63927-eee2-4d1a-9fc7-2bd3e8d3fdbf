import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faPencilAlt } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  Field,
  HelpContainer,
  TextWithTooltip,
} from '@zscaler/zui-component-library';

import ChangeEmailModal from '../../components/profile/ChangeEmailModal';
import ChangePasswordModal from '../../components/profile/ChangePasswordModal';

import { selectMyProfileDetail } from '../../ducks/profile/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES } from '../../config';

const MyProfilePage = () => {
  const { t } = useTranslation();

  const profileDetail = useSelector(selectMyProfileDetail);

  const [showChangeEmailModal, setShowChangeEmailModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);

  const onChangeEmailClick = () => {
    setShowChangeEmailModal(true);
  };

  const onChangePasswordClick = () => {
    setShowChangePasswordModal(true);
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.USER_PROFILE} />
      <article id="admin-my-profile" className="page-container">
        <section className="typography-header3 page-title">{t('MY_PROFILE')}</section>
        <Card>
          <Field label="LOGIN_ID_LABEL">
            <TextWithTooltip containerStyle={{ marginTop: '1rem' }}>
              {profileDetail.loginName}
            </TextWithTooltip>
          </Field>

          <Field label="EMAIL_ADDRESS">
            <div className="is-flex has-ai-c" style={{ maxWidth: '100%' }}>
              <TextWithTooltip containerClass={'email-address-container'}>
                {profileDetail.primaryEmail}
              </TextWithTooltip>
              <Button
                type="tertiary"
                containerClass="no-p-l content-width"
                onClick={onChangeEmailClick}
              >
                <FontAwesomeIcon icon={faPencilAlt} className="icon right" />
              </Button>
            </div>
          </Field>

          <Field label="PASSWORD_LABEL">
            <Button type="secondary" onClick={onChangePasswordClick}>
              {t('CHANGE_PASSWORD')}
            </Button>
          </Field>
        </Card>

        {showChangeEmailModal && (
          <ChangeEmailModal
            show={showChangeEmailModal}
            detail={profileDetail}
            onCloseClick={() => {
              setShowChangeEmailModal(false);
            }}
          />
        )}

        {showChangePasswordModal && (
          <ChangePasswordModal
            show={showChangePasswordModal}
            onCloseClick={() => {
              setShowChangePasswordModal(false);
            }}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default MyProfilePage;
