import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  CRUDModal,
  Card,
  HelpContainer,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';

import ExternalIdentitiesForm from '../../components/external-identities/ExternalIdentitiesForm';
import IDPTable from '../../components/external-identities/IDPTable';
import {
  CONFIGURATION_MODE,
  getModalModeDetail,
} from '../../components/external-identities/helper';
import NoAccess from '../../components/no-access/NoAccess';

import {
  add,
  clearNewIdpDetail,
  getNewIdpDetail,
  remove,
  resetMetadata,
  update,
} from '../../ducks/external-identities';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_IDENTITY_DETAIL = {};

const ExternalIdentitesPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { apiCall } = useApiCall();

  const privileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.EXTERNAL_IDENTITIES_POLICY),
  );

  const { hasFullAccess, noAccess } = privileges;

  // '', 'add', 'edit', 'delete'
  const [modalMode, setModalMode] = useState(DEFAULT_MODAL_MODE);
  const [identityDetail, setIdentityDetail] = useState(DEFAULT_IDENTITY_DETAIL);

  const [isIdpPrimary, setIsIdpPrimary] = useState(true);
  const [selectedConfigurationMode, setSelectedConfigurationMode] = useState();

  useEffect(() => {
    const isAddMode = modalMode === 'add';

    return () => {
      if (isAddMode) {
        dispatch(clearNewIdpDetail());
      }
    };
  }, [modalMode]);

  const onAddClick = (isIdpPrimary) => {
    onCloseClick();
    setTimeout(() => {
      setModalMode('add');
      setIsIdpPrimary(!!isIdpPrimary);
      dispatch(getNewIdpDetail({ defaultIdp: !!isIdpPrimary }));
    }, 0);
  };

  const onEditClick = (detail) => {
    if (detail) {
      setIdentityDetail(detail);
      setModalMode(hasFullAccess ? 'edit' : 'view');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setIdentityDetail(detail);
      setModalMode('delete');
    }
  };

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setIdentityDetail(DEFAULT_IDENTITY_DETAIL);
    dispatch(resetMetadata());
  };

  const onSaveClick = () => {
    const payload = cloneDeep(identityDetail);

    if (payload?.samlConfigInfo && !payload?.samlConfigInfo?.samlIdpCertificateId) {
      delete payload?.samlConfigInfo?.samlIdpCertificateId;
    }

    if (payload?.samlConfigInfo && !payload?.samlConfigInfo?.samlIdpCertificateHash) {
      delete payload?.samlConfigInfo?.samlIdpCertificateHash;
    }

    if (payload.type === 'SAML') {
      if (selectedConfigurationMode !== CONFIGURATION_MODE.URL) {
        delete payload?.samlConfigInfo?.samlIdpMetaDataUrl;
      }

      payload.samlConfigInfo.loginIdAttribute = payload?.loginIdAttribute;

      delete payload?.oidcConfigInfo;
    }

    if (payload.type === 'OIDC') {
      if (selectedConfigurationMode !== CONFIGURATION_MODE.URL) {
        delete payload?.oidcConfigInfo?.oidcIdpMetaDataUrl;
      }

      payload.oidcConfigInfo.loginIdAttribute = payload?.loginIdAttribute;

      delete payload?.samlConfigInfo;
    }

    if (modalMode === 'add') {
      apiCall(add({ ...payload, default: isIdpPrimary }), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update({ ...payload, default: isIdpPrimary }), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove({ ...payload, default: isIdpPrimary }), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const renderPrimaryIDPSection = () => {
    return (
      <div className="primary-identity-container">
        <section className="typography-header5" style={{ marginBottom: '1.2rem' }}>
          {t('PRIMARY_IDENTITY_PROVIDER')}
        </section>

        <Card>
          <IDPTable
            isIdpPrimary
            onAddClick={() => {
              onAddClick(true);
            }}
            onEditClick={(detail) => {
              setIsIdpPrimary(true);
              onEditClick(detail);
            }}
            onDeleteClick={(detail) => {
              setIsIdpPrimary(true);
              onDeleteClick(detail);
            }}
            privileges={privileges}
          />
        </Card>
      </div>
    );
  };

  const renderSecondaryIDPSection = () => {
    return (
      <div className="secondary-identity-container">
        <section className="typography-header5" style={{ margin: '3rem 0 1.2rem' }}>
          {t('SECONDARY_IDENTITY_PROVIDER')}
        </section>

        <Card>
          {hasFullAccess ? (
            <div className="is-flex has-jc-sb">
              <div className="buttons">
                <Button onClick={() => onAddClick(false)}>
                  <FontAwesomeIcon icon={faPlus} className="icon left" />
                  <span>{t('ADD_SECONDARY_IDP')}</span>
                </Button>
              </div>
            </div>
          ) : null}

          <IDPTable
            onAddClick={onAddClick}
            onEditClick={(detail) => {
              setIsIdpPrimary(false);
              onEditClick(detail);
            }}
            onDeleteClick={(detail) => {
              setIsIdpPrimary(false);
              onDeleteClick(detail);
            }}
            privileges={privileges}
          />
        </Card>
      </div>
    );
  };

  const getHelpSrc = () => {
    if (modalMode && identityDetail) {
      return identityDetail.type === 'SAML'
        ? HELP_ARTICLES.ADDING_SAML_IDENTITY
        : HELP_ARTICLES.ADDING_OIDC_IDENTITY;
    }

    return isIdpPrimary ? HELP_ARTICLES.PRIMARY_IDP_PROVIDER : HELP_ARTICLES.SECONDAY_IDP_PROVIDER;
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={getHelpSrc()} />

      <article id="external-identities-page" className="page-container">
        <section className="typography-header3 page-title">{t('EXTERNAL_IDENTITIES')}</section>

        {renderPrimaryIDPSection()}

        {renderSecondaryIDPSection()}

        {modalMode && (
          <CRUDModal
            mode={modalMode}
            containerClass="external-identities-form-modal"
            renderFormSection={(props) => (
              <ExternalIdentitiesForm
                {...props}
                onDetailChange={setIdentityDetail}
                detail={identityDetail}
                privileges={privileges}
                onConfigurationModeChange={setSelectedConfigurationMode}
              />
            )}
            showSave={hasFullAccess}
            onSaveClick={onSaveClick}
            onCloseClick={onCloseClick}
            cancelText={hasFullAccess ? 'CANCEL' : 'CLOSE'}
            {...getModalModeDetail({ defaultIdp: isIdpPrimary })[modalMode]}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default ExternalIdentitesPage;
