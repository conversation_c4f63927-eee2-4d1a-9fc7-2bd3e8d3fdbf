import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import IpLocationGroupsActions from '../../components/ip-location-groups/IpLocationGroupsActions';
import IpLocationGroupsCRUD from '../../components/ip-location-groups/IpLocationGroupsCRUD';
import IpLocationGroupsTable from '../../components/ip-location-groups/IpLocationGroupsTable';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/ip-location-groups';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const IpLocationGroupsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.IP_LOCATION_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.IP_LOCATION_GROUPS} />

      <article id="admin-ip-locations" className="page-container">
        <section className="typography-header3 page-title">{t('IP_LOCATION_GROUPS')}</section>

        <CRUDPageContextProvider privileges={privileges}>
          <Card>
            <IpLocationGroupsActions />

            <IpLocationGroupsTable />
          </Card>

          <IpLocationGroupsCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default IpLocationGroupsPage;
