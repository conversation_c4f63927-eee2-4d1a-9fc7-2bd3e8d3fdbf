import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import RolesActions from '../../components/roles/RolesActions';
import RolesCRUD from '../../components/roles/RolesCRUD';
import RolesTable from '../../components/roles/RolesTable';

import { getAllPermissions } from '../../ducks/permissions';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList } from '../../ducks/roles/index';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const RolesPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.ROLES_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
    apiCall(getAllPermissions()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.ROLES} />

      <article id="roles" className="page-container">
        <section className="typography-header3 page-title">{t('ZIDENTITY_ROLES')}</section>

        <CRUDPageContextProvider privileges={privileges}>
          <Card>
            <RolesActions />

            <RolesTable />
          </Card>

          <RolesCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default RolesPage;
