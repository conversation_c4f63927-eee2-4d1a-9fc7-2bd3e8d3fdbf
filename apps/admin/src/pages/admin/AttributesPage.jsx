import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { HelpContainer, Tab, Tabs } from '@zscaler/zui-component-library';

import SessionAttributeContainer from '../../components/attribute/session/SessionAttributeContainer';
import UserAttributeContainer from '../../components/attribute/user/UserAttributeContainer';
import NoAccess from '../../components/no-access/NoAccess';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const TABS = {
  USER: 'USER_ATTRIBUTE',
  SESSION: 'SESSION_ATTRIBUTE',
};

const AttributesPage = () => {
  const { t } = useTranslation();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY));

  const { noAccess } = privileges;

  const [selectedTab, setSelectedTab] = useState(TABS.USER);

  if (noAccess) {
    return <NoAccess />;
  }

  const renderAttributesTabsSelectionSection = () => {
    return (
      <Tabs>
        <Tab
          label="USER_ATTRIBUTE"
          isActive={selectedTab === TABS.USER}
          onClick={() => {
            setSelectedTab(TABS.USER);
          }}
        />

        <Tab
          label="SESSION_ATTRIBUTE"
          isActive={selectedTab === TABS.SESSION}
          onClick={() => {
            setSelectedTab(TABS.SESSION);
          }}
        />
      </Tabs>
    );
  };

  return (
    <NavLayout>
      <HelpContainer
        src={
          selectedTab === TABS.USER
            ? HELP_ARTICLES.USER_ATTRIBUTES
            : HELP_ARTICLES.SESSION_ATTRIBUTE
        }
      />

      <article id="admin-user-attributes" className="page-container">
        <section className="typography-header3 page-title">{t('ATTRIBUTES')}</section>

        {renderAttributesTabsSelectionSection()}

        {selectedTab === TABS.USER && <UserAttributeContainer />}
        {selectedTab === TABS.SESSION && <SessionAttributeContainer />}
      </article>
    </NavLayout>
  );
};

export default AttributesPage;
