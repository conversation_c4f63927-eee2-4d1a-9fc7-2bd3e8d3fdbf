import { createContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import TokenValidatorsActions from '../../components/token-validators/TokenValidatorsActions';
import TokenValidatorsCRUD from '../../components/token-validators/TokenValidatorsCRUD';
import TokenValidatorsTable from '../../components/token-validators/TokenValidatorsTable';
import TestToken from '../../components/tokens/TestToken';
import { UI_VIEW_OPTIONS } from '../../components/tokens/helper';

import { getAllPermissions } from '../../ducks/permissions';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList, getZDKIds } from '../../ducks/token-validators/index';
import { selectZdkfeatureFlag } from '../../ducks/token-validators/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

export const TokenValidatorsContext = createContext({});

const TokenValidatorsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.TOKEN_VALIDATOR_POLICY));

  const { noAccess } = privileges;

  const zdkfeatureFlag = useSelector(selectZdkfeatureFlag);
  const isZdkCluster = zdkfeatureFlag?.isZdkCluster;

  const [view, setView] = useState(UI_VIEW_OPTIONS.TOKEN_VALIDATOR_VIEW);

  useEffect(() => {
    apiCall(getList({ isZdkCluster })).catch(noop);
    apiCall(getAllPermissions()).catch(noop);
  }, []);

  useEffect(() => {
    if (isZdkCluster) {
      apiCall(getZDKIds()).catch(noop);
    }
  }, [isZdkCluster]);

  if (noAccess) {
    return <NoAccess />;
  }

  const onBackClick = () => {
    setView(UI_VIEW_OPTIONS.TOKEN_VALIDATOR_VIEW);
  };

  const contextValue = {
    view,
    setView,
    isZdkCluster,
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.ROLES} />
      {/* <HelpContainer src={HELP_ARTICLES.TOKEN_VALIDATORS} /> */}

      <article id="token-validators" className="page-container">
        <section className="typography-header3 page-title">
          {view === UI_VIEW_OPTIONS.TEST_TOKEN_VIEW && (
            <FontAwesomeIcon
              icon={faArrowLeft}
              className="icon left pointer"
              onClick={onBackClick}
            />
          )}
          {t(view === UI_VIEW_OPTIONS.TOKEN_VALIDATOR_VIEW ? 'TOKEN_VALIDATORS' : 'TEST_TOKEN')}
        </section>

        <CRUDPageContextProvider privileges={privileges}>
          <TokenValidatorsContext.Provider value={contextValue}>
            {view === UI_VIEW_OPTIONS.TOKEN_VALIDATOR_VIEW && (
              <Card>
                <TokenValidatorsActions />

                <TokenValidatorsTable />
              </Card>
            )}

            {view === UI_VIEW_OPTIONS.TEST_TOKEN_VIEW && <TestToken />}
            <TokenValidatorsCRUD />
          </TokenValidatorsContext.Provider>
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default TokenValidatorsPage;
