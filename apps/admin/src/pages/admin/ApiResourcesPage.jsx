import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import ApiResourcesActions from '../../components/api-resources/ApiResourcesActions';
import ApiResourcesCRUD from '../../components/api-resources/ApiResourcesCRUD';
import ApiResourcesTable from '../../components/api-resources/ApiResourcesTable';
import { searchOptions } from '../../components/api-resources/helper';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/api-resources';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const ApiResourcesPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.API_RESOURCES} />

      <article id="admin-users" className="page-container">
        <section className="typography-header3 page-title">{t('API_RESOURCES')}</section>

        <CRUDPageContextProvider defaultSearchOption={searchOptions[0]} privileges={privileges}>
          <Card>
            <ApiResourcesActions />

            <ApiResourcesTable />
          </Card>

          <ApiResourcesCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default ApiResourcesPage;
