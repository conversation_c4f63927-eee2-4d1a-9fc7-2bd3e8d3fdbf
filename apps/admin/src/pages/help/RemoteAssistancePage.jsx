import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  <PERSON><PERSON>,
  Card,
  Field,
  FieldGroup,
  HelpContainer,
  InlineDatePicker,
  ToggleButton,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { cloneDeep, noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getRemoteAssistance, update } from '../../ducks/remote-assistance';
import { selectRemoteAssistanceSetting } from '../../ducks/remote-assistance/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const VIEW = 'viewOnly';
const FULL_ACCESS = 'fullAccess';

const RemoteAssistance = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.REMOTE_ASSISTANCE_MANAGEMENT),
  );

  const { hasFullAccess, noAccess } = privileges;

  useEffect(() => {
    apiCall(getRemoteAssistance());
  }, []);

  const setting = useSelector(selectRemoteAssistanceSetting);

  const [formValues, setFormValues] = useState({
    ...setting,
  });

  const updateSetting = () => {
    let newSetting = cloneDeep(setting);

    if (setting?.[VIEW]?.expiresAt) {
      newSetting[VIEW].expiresAt = dayjs.unix(newSetting?.[VIEW]?.expiresAt);
    }

    if (setting?.[FULL_ACCESS]?.expiresAt) {
      newSetting[FULL_ACCESS].expiresAt = dayjs.unix(newSetting?.[FULL_ACCESS]?.expiresAt);
    }

    setFormValues({ ...newSetting });
  };

  useEffect(() => {
    updateSetting();
  }, [setting]);

  const minDate = useMemo(() => {
    return dayjs().toDate();
  }, []);

  const onToggleEnableClick = (accessType) => {
    setFormValues((prevState) => ({
      ...prevState,
      [accessType]: { ...prevState[accessType], enabled: !prevState[accessType].enabled },
    }));
  };

  const onValueChange = (evt, accessType) => {
    if (hasFullAccess) {
      setFormValues((prevState) => ({
        ...prevState,
        [accessType]: { ...prevState[accessType], expiresAt: evt.target.value },
      }));
    }
  };

  const getToggleLable = (accessType) => {
    if (accessType === VIEW) {
      return formValues?.[VIEW]?.enabled ? 'VIEW_ONLY_ACCESS' : 'ENABLE_VIEW_ONLY_ACCESS';
    }

    return formValues?.[FULL_ACCESS]?.enabled ? 'FULL_ACCESS' : 'ENABLE_FULL_ACCESS';
  };

  const onSaveClick = () => {
    let payload = { ...formValues };

    if (payload?.[VIEW]?.expiresAt) {
      payload[VIEW].expiresAt = dayjs(payload?.[VIEW]?.expiresAt).endOf('day').unix();
    }

    if (payload?.[FULL_ACCESS]?.expiresAt) {
      payload[FULL_ACCESS].expiresAt = dayjs(payload?.[FULL_ACCESS]?.expiresAt).endOf('day').unix();
    }

    apiCall(update({ ...formValues }), {
      successNotificationPayload: { ...getApiPUTNotificationOptions() },
    }).catch(noop);
  };

  const isDeepEqual = (obj1, obj2) => {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (const key of keys1) {
      const val1 = obj1[key];
      const val2 = obj2[key];

      if (typeof val1 === 'object' && typeof val2 === 'object') {
        if (!isDeepEqual(val1, val2)) {
          return false;
        }
      } else if (key === 'expiresAt') {
        const unixTime1 = dayjs(val1).endOf('day').unix();
        const unixTime2 = val2;

        if (unixTime1 !== unixTime2) {
          return false;
        }
      } else if (val1 !== val2) {
        return false;
      }
    }

    return true;
  };

  const isSaveDisabled = () => {
    if (formValues?.[FULL_ACCESS]?.enabled && !formValues?.[FULL_ACCESS]?.expiresAt) {
      return true;
    }

    if (formValues?.[VIEW]?.enabled && !formValues?.[VIEW]?.expiresAt) {
      return true;
    }

    return isDeepEqual(formValues, setting);
  };

  const renderContentSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('VIEW_ONLY_ACCESS')}</section>

        <Card containerClass="remote-assistance-form">
          <FieldGroup>
            <Field label={getToggleLable(VIEW)} containerStyle={{ maxWidth: '200px' }}>
              <ToggleButton
                type="success"
                isOn={formValues?.[VIEW]?.enabled}
                onToggleClick={() => onToggleEnableClick(VIEW)}
                showLabel={false}
                disabled={!hasFullAccess}
              />
            </Field>

            {formValues?.[VIEW]?.enabled && (
              <div style={{ maxWidth: '350px' }}>
                <InlineDatePicker
                  label="ACCESS_VALID_UNTIL"
                  name="expiresAt"
                  selectedDate={formValues?.[VIEW]?.expiresAt}
                  onChange={(evt) => onValueChange(evt, VIEW)}
                  minDate={minDate}
                  elementProps={{ disabled: !hasFullAccess }}
                />
              </div>
            )}
          </FieldGroup>
        </Card>

        <section className="typography-paragraph1-uppercase">{t('FULL_ACCESS')}</section>

        <Card containerClass="remote-assistance-form">
          <FieldGroup>
            <Field label={getToggleLable(FULL_ACCESS)} containerStyle={{ maxWidth: '200px' }}>
              <ToggleButton
                type="success"
                isOn={formValues?.[FULL_ACCESS]?.enabled}
                onToggleClick={() => onToggleEnableClick(FULL_ACCESS)}
                showLabel={false}
                disabled={!hasFullAccess}
              />
            </Field>

            {formValues?.[FULL_ACCESS]?.enabled && (
              <div style={{ maxWidth: '350px' }}>
                <InlineDatePicker
                  label="ACCESS_VALID_UNTIL"
                  name="expiresAt"
                  selectedDate={formValues?.[FULL_ACCESS]?.expiresAt}
                  onChange={(evt) => onValueChange(evt, FULL_ACCESS)}
                  minDate={minDate}
                  elementProps={{ disabled: !hasFullAccess }}
                />
              </div>
            )}
          </FieldGroup>
        </Card>
      </>
    );
  };

  const renderActionsSection = () => {
    if (!hasFullAccess) {
      return null;
    }

    return (
      <div className="action-section buttons">
        <Button onClick={onSaveClick} disabled={isSaveDisabled()}>
          {t('SAVE')}
        </Button>

        <Button
          type="tertiary"
          containerClass="no-p-l"
          onClick={() => {
            updateSetting();
          }}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.REMOTE_ASSISTANCE} />
      <article id="remote-assistance-page" className="page-container">
        <section className="typography-header3 page-title">{t('REMOTE_ASSISTANCE')}</section>

        {renderContentSection()}

        {renderActionsSection()}
      </article>
    </NavLayout>
  );
};

export default RemoteAssistance;
