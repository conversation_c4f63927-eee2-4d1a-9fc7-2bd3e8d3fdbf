import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import AccessPolicyPageContainer from '../../components/access-policies/AccessPolicyPageContainer';
import { DEFAULT_POLICY_CRITERIA } from '../../components/access-policies/helper';
import NoAccess from '../../components/no-access/NoAccess';

import { getCriteriaValues, getList } from '../../ducks/access-policies';
import { selectIsAccessPolicyEnabled } from '../../ducks/features/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const AccessPolicyPage = () => {
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES));

  const isAccessPolicyEnabled = useSelector(selectIsAccessPolicyEnabled);

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getCriteriaValues())
      .catch(noop)
      .finally(() => {
        apiCall(getList()).catch(noop);
      });
  }, []);

  if (noAccess || !isAccessPolicyEnabled) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <CRUDPageContextProvider privileges={privileges} defaultDetail={DEFAULT_POLICY_CRITERIA}>
        <AccessPolicyPageContainer />
      </CRUDPageContextProvider>
    </NavLayout>
  );
};

export default AccessPolicyPage;
