import {
  API_METHOD,
  checkAndCreateDomElement,
  checkAndCreateDomElementAsync,
  setApiMethodMessageOption,
  setFloatingPortalRootId,
  setModalRootId,
  setToastPortalRootId,
  updateUseApiCallFunctionsRegistry,
} from '@zscaler/zui-component-library';

import { hideLoader, hideNotification, showLoader } from './ducks/global';
import { apiErrorNotifier, apiSuccessNotifier } from './ducks/helper';

import { APP_ID, PORTAL_ROOT_ID } from './config';

export const getAppRootElement = ({
  tagName = 'div',
  parentElement = document.body,
  id = APP_ID,
} = {}) => {
  const { element } = checkAndCreateDomElement({
    tagName,
    parentElement,
    attributes: { id },
  });

  return element;
};

export const setupUseApiCallFunctionsRegistry = () => {
  updateUseApiCallFunctionsRegistry({
    showLoader,
    hideLoader,
    hideNotification,
    apiSuccessNotifier,
    apiErrorNotifier,
  });
};

export const setupFloatingPortalRootId = ({ id = PORTAL_ROOT_ID } = {}) => {
  checkAndCreateDomElementAsync({ attributes: { id } });

  setFloatingPortalRootId(id);
  setModalRootId(id);
  setToastPortalRootId(id);
};

export const setupApiMethodMessageOption = () => {
  setApiMethodMessageOption({
    [API_METHOD.GET]: { message: '', translationMapping: {} },
    [API_METHOD.PUT]: { message: 'ALL_CHANGES_SAVED', translationMapping: {} },
    [API_METHOD.POST]: { message: 'ALL_CHANGES_SAVED', translationMapping: {} },
    [API_METHOD.DELETE]: { message: 'ITEM_HAS_BEEN_DELETED', translationMapping: {} },
  });
};
