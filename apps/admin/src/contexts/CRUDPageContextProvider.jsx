import { createContext } from 'react';

import PropTypes from 'prop-types';

import { useCRUDPageContext } from '../hooks/useCRUDPageContext';

export const CRUDPageContext = createContext({});

const CRUDPageContextProvider = ({
  defaultModalMode,
  defaultDetail,
  defaultBulkActionOption,
  defaultSearchOption,
  privileges,
  children,
} = {}) => {
  const contextValue = useCRUDPageContext({
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultSearchOption,
    privileges,
  });

  return <CRUDPageContext.Provider value={contextValue}>{children}</CRUDPageContext.Provider>;
};

CRUDPageContextProvider.propTypes = {
  defaultModalMode: PropTypes.string,
  defaultDetail: PropTypes.object,
  defaultBulkActionOption: PropTypes.object,
  defaultSearchOption: PropTypes.object,
  privileges: PropTypes.object,
  children: PropTypes.node,
};

export default CRUDPageContextProvider;
