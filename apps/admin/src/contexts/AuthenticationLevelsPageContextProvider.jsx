import { createContext } from 'react';

import PropTypes from 'prop-types';

import { useAuthenticationLevelsPageContext } from '../hooks/useAuthenticationLevelsPageContext';

export const AuthenticationLevelsPageContext = createContext({});

const DEFAULT_PROPS = {
  defaultModalMode: '',
  defaultDetail: {},
};

const AuthenticationLevelsPageContextProvider = ({
  defaultModalMode = DEFAULT_PROPS.defaultModalMode,
  defaultDetail = DEFAULT_PROPS.defaultDetail,
  privileges,
  children,
}) => {
  const contextValue = useAuthenticationLevelsPageContext({
    defaultModalMode,
    defaultDetail,
    privileges,
  });

  return (
    <AuthenticationLevelsPageContext.Provider value={contextValue}>
      {children}
    </AuthenticationLevelsPageContext.Provider>
  );
};

AuthenticationLevelsPageContextProvider.propTypes = {
  defaultModalMode: PropTypes.string,
  defaultDetail: PropTypes.object,
  defaultBulkActionOption: PropTypes.object,
  defaultSearchOption: PropTypes.object,
  privileges: PropTypes.object,
  children: PropTypes.node,
};

export default AuthenticationLevelsPageContextProvider;
