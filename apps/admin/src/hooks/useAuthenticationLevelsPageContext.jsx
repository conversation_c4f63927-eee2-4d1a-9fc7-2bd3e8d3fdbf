import { useEffect, useMemo, useState } from 'react';

const DEFAULT_PAGE_MODE = '';
const DEFAULT_DETAIL = {};

const DEFAULT_PRIVILEGES = {
  hasFullAccess: false,
  noAccess: false,
};

export const useAuthenticationLevelsPageContext = ({
  defaultPageMode = DEFAULT_PAGE_MODE,
  defaultDetail = DEFAULT_DETAIL,
  privileges = DEFAULT_PRIVILEGES,
} = {}) => {
  const [pageMode, setPageMode] = useState(defaultPageMode);
  const [pageModeActionType, setPageModeActionType] = useState('');

  const [isFormReadOnly, setIsFormReadOnly] = useState(false);

  const [levelsList, setLevelsList] = useState([]);
  const [selectedTree, setSelectedTree] = useState('');
  const [detail, setDetail] = useState(defaultDetail);
  const [hideBranchMapping, setHideBranchMapping] = useState({});

  useEffect(() => {
    const { hasFullAccess } = privileges;

    if (pageMode === 'view' || !hasFullAccess) {
      setIsFormReadOnly(true);
    } else {
      setIsFormReadOnly(false);
    }
  }, [pageMode, privileges]);

  const contextValue = useMemo(() => {
    return {
      pageMode,
      setPageMode,
      isFormReadOnly,
      setIsFormReadOnly,
      levelsList,
      setLevelsList,
      selectedTree,
      setSelectedTree,
      detail,
      setDetail,
      hideBranchMapping,
      setHideBranchMapping,
      defaultPageMode,
      defaultDetail,
      privileges,
      pageModeActionType,
      setPageModeActionType,
    };
  }, [
    pageMode,
    setPageMode,
    isFormReadOnly,
    setIsFormReadOnly,
    levelsList,
    setLevelsList,
    selectedTree,
    setSelectedTree,
    detail,
    setDetail,
    hideBranchMapping,
    setHideBranchMapping,
    defaultPageMode,
    defaultDetail,
    privileges,
    pageModeActionType,
    setPageModeActionType,
  ]);

  return contextValue;
};
