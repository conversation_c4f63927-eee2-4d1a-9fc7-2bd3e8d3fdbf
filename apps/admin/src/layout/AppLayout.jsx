import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Outlet, useLocation } from 'react-router-dom';

import { LoaderContainer, ToastContainer } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getSystemFeatures, getTenantFeatures } from '../ducks/features';
import {
  appSetupDone,
  getBuildVersion,
  hideLoader,
  hideNotification,
  showLoader,
} from '../ducks/global';
import {
  selectAppSetupPending,
  selectIsAppSetupDone,
  selectIsLoading,
  selectNotificationsList,
} from '../ducks/global/selectors';
import { getMigrationStatus } from '../ducks/migration';
import { getMyPermissions } from '../ducks/permissions';
import { getProfile } from '../ducks/profile';
import { getZDKfeatureFlag } from '../ducks/token-validators';

import matchRoute from '../utils/matchRoute';

import {
  setupApiMethodMessageOption,
  setupFloatingPortalRootId,
  setupUseApiCallFunctionsRegistry,
} from '../setup';
import BannerLayout from './BannerLayout';

setupApiMethodMessageOption();
setupUseApiCallFunctionsRegistry();
setupFloatingPortalRootId();

const authFreePageList = ['/signup', '/pseudo-domain'];

const isAuthFreePage = (location = {}) => {
  return matchRoute(authFreePageList, location);
};

const AppLayout = () => {
  const dispatch = useDispatch();
  const location = useLocation();

  const appSetupPending = useSelector(selectAppSetupPending);
  const isAppSetupDone = useSelector(selectIsAppSetupDone);

  const isLoading = useSelector(selectIsLoading);
  const notificationList = useSelector(selectNotificationsList);

  const setupAuth = () => {
    dispatch(showLoader());

    dispatch(getZDKfeatureFlag()).catch(noop);

    dispatch(getMyPermissions())
      .catch(noop)
      .finally(() => {
        dispatch(getSystemFeatures()).catch(noop);
        dispatch(getTenantFeatures()).catch(noop);
        dispatch(getProfile()).catch(noop);
        dispatch(getBuildVersion()).catch(noop);
        dispatch(getMigrationStatus()).catch(noop);
        dispatch(appSetupDone());
        dispatch(hideLoader());
      });
  };

  useEffect(() => {
    if (appSetupPending) {
      if (!isAuthFreePage(location)) {
        setupAuth();
      } else {
        dispatch(appSetupDone());
      }
    }
  }, [appSetupPending]);

  const renderRoutesSection = () => {
    if (!isAppSetupDone) {
      return null;
    }

    return <Outlet />;
  };

  const onNotificationClose = (id) => {
    dispatch(hideNotification(id));
  };

  return (
    <>
      <LoaderContainer isLoading={isLoading} />
      <ToastContainer list={notificationList} onClose={onNotificationClose} />
      <BannerLayout isAppSetupDone={isAppSetupDone} isAuthFreePage={isAuthFreePage(location)} />
      {renderRoutesSection()}
    </>
  );
};

export default AppLayout;
