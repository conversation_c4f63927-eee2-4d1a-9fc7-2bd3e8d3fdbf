import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { faQuestionCircle } from '@fortawesome/pro-regular-svg-icons';
import {
  faCloud,
  faCog,
  faDashboard,
  faSignOutAlt,
  faSlidersUp,
  faTableTree,
  faUser,
} from '@fortawesome/pro-solid-svg-icons';
import { FooterContainer, NavigationBar } from '@zscaler/zui-component-library';

import { cloneDeep } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  selectIsAccessPolicyEnabled,
  selectIsOneApiEnabled,
  selectIsStepupSupportSystemEnabled,
  selectIsStepupSupportTenantEnabled,
  selectIsTokenValidatorSystemEnabled,
  selectIsTokenValidatorTenantEnabled,
  selectIsUserSSOEnabled,
  selectIsZiaStepupSupportSystemEnabled,
  selectIsZiaStepupSupportTenantEnabled,
} from '../ducks/features/selectors';
import { selectBuildVersion } from '../ducks/global/selectors';
import { DEFAULT_PERMISSION_LEVEL } from '../ducks/permissions/constants';
import { selectMyPermissionsLevel, selectPermissionsByKey } from '../ducks/permissions/selectors';
import { selectZdkfeatureFlag } from '../ducks/token-validators/selectors';

import i18n from '../utils/i18n';
import logoutUser from '../utils/logoutUser';

import { PERMISSIONS_KEY } from '../config';
import BiUiLogo from '../images/zs-logo.svg';

const filterEmptyNavGroups = (navGroup) => {
  return navGroup.filter((item) => {
    if (item.name === i18n.t('LOGOUT')) {
      return true;
    }

    if (!item.navigationURL && (!item.subMenu || item.subMenu.length === 0)) {
      return false;
    }

    if (item.subMenu) {
      item.subMenu = filterEmptyNavGroups(item.subMenu);
    }

    return true;
  });
};

const addAssetPathToRoutes = (navGroup) => {
  return navGroup.map((menuItem) => ({
    ...menuItem,
    relatedRouteKeys: menuItem.relatedRouteKeys?.map((key) => `${ASSET_PATH}${key}`),
    subMenu: menuItem.subMenu ? addAssetPathToRoutes(menuItem.subMenu) : undefined,
  }));
};

const DEFAULT_PROPS = {
  children: null,
  navGroup: [
    {
      name: i18n.t('DASHBOARD'),
      iconProps: { icon: faDashboard },
      relatedRouteKeys: ['/dashboard'],
      navigationURL: '/dashboard',
    },
    {
      name: i18n.t('DIRECTORY'),
      iconProps: { icon: faTableTree },
      relatedRouteKeys: [
        '/admin/users',
        '/admin/user-groups',
        '/admin/attributes',
        '/admin/departments',
      ],
      onHoverMenu: false,
      separateLine: false,
      subMenu: [
        {
          name: i18n.t('USERS'),
          navigationURL: '/admin/users',
          permissionKey: PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY,
        },
        {
          name: i18n.t('USER_GROUPS'),
          navigationURL: '/admin/user-groups',
          permissionKey: PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY,
        },
        {
          name: i18n.t('ATTRIBUTES'),
          navigationURL: '/admin/attributes',
          permissionKey: PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY,
        },
        {
          name: i18n.t('DEPARTMENTS'),
          navigationURL: '/admin/departments',
          permissionKey: PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY,
        },
      ],
    },
    {
      name: i18n.t('INTEGRATION'),
      iconProps: { icon: faSlidersUp },
      relatedRouteKeys: [
        '/admin/external-identities',
        '/admin/api-clients',
        '/admin/api-resources',
        '/admin/token-validators',
        '/admin/tokens',
      ],
      onHoverMenu: false,
      separateLine: false,
      subMenu: [
        {
          name: i18n.t('EXTERNAL_IDENTITIES'),
          navigationURL: '/admin/external-identities',
          permissionKey: PERMISSIONS_KEY.EXTERNAL_IDENTITIES_POLICY,
        },
        {
          name: i18n.t('TOKEN_VALIDATORS'),
          navigationURL: '/admin/token-validators',
          permissionKey: PERMISSIONS_KEY.AUTHENTICATION_METHODS,
        },
        {
          name: i18n.t('API_CLIENTS'),
          navigationURL: '/admin/api-clients',
          permissionKey: PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES,
        },
        {
          name: i18n.t('API_RESOURCES'),
          navigationURL: '/admin/api-resources',
          permissionKey: PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES,
        },
        {
          name: i18n.t('TOKENS'),
          navigationURL: '/admin/tokens',
          permissionKey: PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES,
        },
      ],
    },
    {
      name: i18n.t('POLICY'),
      iconProps: { icon: faCloud },
      relatedRouteKeys: ['/policy/signon', '/policy/password', '/policy/access'],
      onHoverMenu: false,
      separateLine: false,
      subMenu: [
        {
          name: i18n.t('ADMIN_SIGN_ON'),
          navigationURL: '/policy/signon',
          permissionKey: PERMISSIONS_KEY.SIGN_ON_POLICY,
        },
        {
          name: i18n.t('PASSWORD_LABEL'),
          navigationURL: '/policy/password',
          permissionKey: PERMISSIONS_KEY.AUTHENTICATION_METHODS,
        },
        {
          name: i18n.t('API_CLIENT_ACCESS_POLICY'),
          navigationURL: '/policy/access',
          permissionKey: PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES,
        },
      ],
    },
    {
      name: i18n.t('ADMINISTRATION'),
      iconProps: { icon: faCog },
      relatedRouteKeys: [
        '/admin/advanced-settings',
        '/admin/authentication-methods',
        '/admin/device-token',
        '/admin/admin-entitlements',
        '/admin/service-entitlements',
        '/admin/roles',
        '/admin/branding',
        '/admin/domains',
        '/admin/ip-locations',
        '/admin/ip-location-groups',
        '/admin/linked-services',
        '/admin/audit-logs',
      ],
      onHoverMenu: false,
      separateLine: false,
      subMenu: [
        {
          name: i18n.t('AUTHENTICATION'),
          relatedRouteKeys: [
            '/admin/advanced-settings',
            '/admin/authentication-methods',
            '/admin/device-token',
          ],
          onHoverMenu: true,
          separateLine: false,
          subMenu: [
            {
              name: i18n.t('AUTHENTICATION_SESSION'),
              navigationURL: '/admin/advanced-settings',
              permissionKey: PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY,
            },
            {
              name: i18n.t('AUTHENTICATION_LEVELS'),
              navigationURL: '/admin/authentication-levels',
              permissionKey: PERMISSIONS_KEY.AUTHENTICATION_METHODS,
            },
            {
              name: i18n.t('AUTHENTICATION_METHODS'),
              navigationURL: '/admin/authentication-methods',
              permissionKey: PERMISSIONS_KEY.AUTHENTICATION_METHODS,
            },
            {
              name: i18n.t('DEVICE_TOKEN'),
              navigationURL: '/admin/device-token',
              permissionKey: PERMISSIONS_KEY.AUTHENTICATION_METHODS,
            },
          ],
        },
        {
          name: i18n.t('ENTITLEMENTS_LABEL'),
          relatedRouteKeys: [
            '/admin/admin-entitlements',
            '/admin/service-entitlements',
            '/admin/roles',
          ],
          onHoverMenu: true,
          separateLine: false,
          subMenu: [
            {
              name: i18n.t('ADMINISTRATIVE'),
              navigationURL: '/admin/admin-entitlements',
              permissionKey: PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY,
            },
            {
              name: i18n.t('SERVICE'),
              navigationURL: '/admin/service-entitlements',
              permissionKey: PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY,
            },
            {
              name: i18n.t('ZIDENTITY_ROLES'),
              navigationURL: '/admin/roles',
              permissionKey: PERMISSIONS_KEY.ROLES_POLICY,
            },
          ],
        },
        {
          name: i18n.t('ENVIRONMENT'),
          relatedRouteKeys: [
            '/admin/branding',
            '/admin/domains',
            '/admin/ip-locations',
            '/admin/ip-location-groups',
          ],
          onHoverMenu: true,
          separateLine: false,
          subMenu: [
            {
              name: i18n.t('BRANDING'),
              navigationURL: '/admin/branding',
              permissionKey: PERMISSIONS_KEY.BRANDING_POLICY,
            },
            {
              name: i18n.t('DOMAINS'),
              navigationURL: '/admin/domains',
              permissionKey: PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY,
            },
            {
              name: i18n.t('IP_LOCATIONS'),
              navigationURL: '/admin/ip-locations',
              permissionKey: PERMISSIONS_KEY.IP_LOCATION_POLICY,
            },
            {
              name: i18n.t('IP_LOCATION_GROUPS'),
              navigationURL: '/admin/ip-location-groups',
              permissionKey: PERMISSIONS_KEY.IP_LOCATION_POLICY,
            },
          ],
        },
        {
          name: i18n.t('SYSTEM'),
          relatedRouteKeys: ['/admin/linked-services', '/admin/audit-logs'],
          onHoverMenu: true,
          separateLine: false,
          subMenu: [
            {
              name: i18n.t('LINKED_SERVICES'),
              navigationURL: '/admin/linked-services',
              permissionKey: PERMISSIONS_KEY.LINKED_TENATS_POLICY,
            },
            {
              name: i18n.t('AUDIT_LOGS'),
              navigationURL: '/admin/audit-logs',
              permissionKey: PERMISSIONS_KEY.AUDIT_LOGS_POLICY,
            },
          ],
        },
      ],
    },
    {
      name: i18n.t('MY_PROFILE'),
      iconProps: { icon: faUser },
      relatedRouteKeys: ['/admin/my-profile'],
      navigationURL: '/admin/my-profile',
    },
    {
      name: i18n.t('REMOTE_ASSISTANCE'),
      iconProps: { icon: faQuestionCircle },
      relatedRouteKeys: ['/help/remote-assistance'],
      navigationURL: '/help/remote-assistance',
      permissionKey: PERMISSIONS_KEY.REMOTE_ASSISTANCE_MANAGEMENT,
    },
    {
      name: i18n.t('LOGOUT'),
      iconProps: {
        icon: faSignOutAlt,
        transform: { rotate: 180 },
      },
      onClick: logoutUser,
    },
  ],
};

const isDevMode = process.env.NODE_ENV === 'development';
const ASSET_PATH = isDevMode ? '' : process.env.ASSET_PATH || '/iam';

const NavLayoutZID = ({ navGroup = DEFAULT_PROPS.navGroup, children = DEFAULT_PROPS.children }) => {
  const { noAccess } = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.SIGN_ON_POLICY));
  const isOneApiEnabled = useSelector(selectIsOneApiEnabled);
  const isStepupSupportSystemEnabled = useSelector(selectIsStepupSupportSystemEnabled);
  const isStepupSupportTenantEnabled = useSelector(selectIsStepupSupportTenantEnabled);
  const isZiaStepupSupportSystemEnabled = useSelector(selectIsZiaStepupSupportSystemEnabled);
  const isZiaStepupSupportTenantEnabled = useSelector(selectIsZiaStepupSupportTenantEnabled);
  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);
  const isAccessPolicyEnabled = useSelector(selectIsAccessPolicyEnabled);
  const zdkfeatureFlag = useSelector(selectZdkfeatureFlag);
  const isTokenValidatorTenantEnabled = useSelector(selectIsTokenValidatorTenantEnabled);
  const isTokenValidatorSystemEnabled = useSelector(selectIsTokenValidatorSystemEnabled);

  const ipLocationPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.IP_LOCATION_POLICY),
  );

  const allPermissions = useSelector(selectMyPermissionsLevel);

  const filterNoAccessNavGroup = (navGroup) => {
    return navGroup.filter((item) => {
      if (item.permissionKey) {
        const { noAccess } = allPermissions
          ? allPermissions[item.permissionKey] || DEFAULT_PERMISSION_LEVEL
          : DEFAULT_PERMISSION_LEVEL;

        if (noAccess) {
          return false;
        }
      }

      if (item.subMenu) {
        item.subMenu = filterNoAccessNavGroup(item.subMenu);
      }

      return true;
    });
  };

  const { buildVersion, buildTime } = useSelector(selectBuildVersion);

  let _navGroup = cloneDeep(navGroup);

  if (noAccess || ipLocationPrivileges?.noAccess) {
    navGroup = navGroup.map((menuItem) => {
      if (menuItem.name === i18n.t('POLICY') && menuItem.subMenu.length > 0) {
        const authIndex = menuItem.subMenu.findIndex(
          (subMenu) => subMenu.name === i18n.t('ADMIN_SIGN_ON'),
        );

        if (authIndex !== -1 && menuItem.subMenu[authIndex]) {
          menuItem.subMenu = menuItem.subMenu.filter(
            (subMenuItem) => subMenuItem.name !== i18n.t('ADMIN_SIGN_ON'),
          );
        }
      }
      return menuItem;
    });
  }

  if (
    !zdkfeatureFlag.isZdkCluster &&
    (!isTokenValidatorTenantEnabled || !isTokenValidatorSystemEnabled)
  ) {
    _navGroup = _navGroup.map((menuItem) => {
      if (menuItem.name === i18n.t('INTEGRATION') && menuItem?.subMenu?.length > 0) {
        menuItem.subMenu = menuItem?.subMenu?.filter(
          (subMenuItem) => subMenuItem?.name !== i18n.t('TOKEN_VALIDATORS'),
        );
      }
      return menuItem;
    });
  }

  if (!isUserSSOEnabled) {
    _navGroup = _navGroup.map((menuItem) => {
      if (menuItem.name === i18n.t('ADMINISTRATION') && menuItem.subMenu.length > 0) {
        const authIndex = menuItem.subMenu.findIndex(
          (subMenu) => subMenu.name === i18n.t('ENTITLEMENTS_LABEL'),
        );
        if (authIndex !== -1) {
          menuItem.subMenu[authIndex].subMenu = menuItem.subMenu[authIndex].subMenu.filter(
            (subMenuItem) => subMenuItem.name !== i18n.t('SERVICE'),
          );
        }
      }
      return menuItem;
    });
  }

  if (!isOneApiEnabled) {
    _navGroup = _navGroup.map((menuItem) => {
      if (menuItem.name === i18n.t('INTEGRATION') && menuItem.subMenu.length > 0) {
        menuItem.subMenu = menuItem.subMenu.filter(
          (subMenu) =>
            subMenu.name !== i18n.t('API_CLIENTS') &&
            subMenu.name !== i18n.t('API_RESOURCES') &&
            subMenu.name !== i18n.t('TOKENS'),
        );
      }
      return menuItem;
    });
  }

  if (!isAccessPolicyEnabled) {
    _navGroup = _navGroup.map((menuItem) => {
      if (menuItem.name === i18n.t('POLICY') && menuItem.subMenu.length > 0) {
        menuItem.subMenu = menuItem.subMenu.filter(
          (subMenu) => subMenu.name !== i18n.t('API_CLIENT_ACCESS_POLICY'),
        );
      }
      return menuItem;
    });
  }

  if (
    !(isStepupSupportSystemEnabled && isStepupSupportTenantEnabled) &&
    !(isZiaStepupSupportSystemEnabled && isZiaStepupSupportTenantEnabled)
  ) {
    _navGroup = _navGroup.map((menuItem) => {
      if (menuItem.name === i18n.t('ADMINISTRATION') && menuItem.subMenu.length > 0) {
        const authIndex = menuItem.subMenu.findIndex(
          (subMenu) => subMenu.name === i18n.t('AUTHENTICATION'),
        );
        if (authIndex !== -1) {
          menuItem.subMenu[authIndex].subMenu = menuItem.subMenu[authIndex].subMenu.filter(
            (subMenuItem) => subMenuItem.name !== i18n.t('AUTHENTICATION_LEVELS'),
          );
        }
      }
      return menuItem;
    });
  }

  _navGroup = addAssetPathToRoutes(_navGroup);

  _navGroup = filterNoAccessNavGroup(_navGroup);

  _navGroup = filterEmptyNavGroups(_navGroup);

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleNavigate = (url) => {
    navigate(url);
  };

  return (
    <div id="nav-layout">
      <NavigationBar
        NAVIGATION_MENU={_navGroup}
        appLogo={BiUiLogo}
        onNavigate={handleNavigate}
        onDispatch={dispatch}
      />

      <div className="right">{children}</div>

      <FooterContainer
        buildVersion={buildVersion}
        buildTime={buildTime}
        dataPolicyLink={
          'https://help.zscaler.com/customer-logs-fair-use/zslogin-customer-logs-and-data'
        }
      />
    </div>
  );
};

NavLayoutZID.propTypes = {
  children: PropTypes.any,
  navGroup: PropTypes.arrayOf(Object),
};

export default NavLayoutZID;
