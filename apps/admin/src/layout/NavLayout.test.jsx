/* eslint-disable no-undef */
/* eslint-disable react/display-name */
import { useSelector } from 'react-redux';

import { render } from '@testing-library/react';

import NavLayout from './NavLayout';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('../components/NavigationBar', () => () => <div>NavigationBar</div>);
jest.mock('@zscaler/zui-component-library', () => ({
  FooterContainer: () => <div>FooterContainer</div>,
}));

describe('NavLayout', () => {
  beforeEach(() => {
    useSelector.mockImplementation((selector) => {
      switch (selector) {
        case selectIsUserSSOEnabled:
          return true;
        default:
          return {};
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render NavigationBar and FooterContainer', () => {
    const { getByText } = render(<NavLayout />);
    expect(getByText('NavigationBar')).toBeInTheDocument();
    expect(getByText('FooterContainer')).toBeInTheDocument();
  });

  it('should handle isUserSSOEnabled correctly', () => {
    useSelector.mockImplementation((selector) => {
      if (selector === selectIsUserSSOEnabled) {
        return false;
      }
      return {};
    });

    const { getByText } = render(<NavLayout />);
    expect(getByText('NavigationBar')).toBeInTheDocument();
    expect(getByText('FooterContainer')).toBeInTheDocument();
  });
});
