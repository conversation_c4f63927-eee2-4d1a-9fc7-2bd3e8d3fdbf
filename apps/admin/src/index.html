<!doctype html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <link
      rel="icon"
      type="image/png"
      href="<%= htmlWebpackPlugin.files.publicPath %>favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="<%= htmlWebpackPlugin.files.publicPath %>favicon-16x16.png"
      sizes="16x16"
    />

    <script type="text/javascript" th:inline="javascript">
      var serverConfig = {
        domain: /*[[${domain}]]*/ '',
        tenant: /*[[${tenant}]]*/ '',
        userSetup: /*[[${userSetup}]]*/ false,
        pseudoDomainPrefix: /*[[${pseudoDomainPrefix}]]*/ '',
      };
    </script>

    <title>ZIdentity Admin Portal</title>
  </head>
  <body>
    <noscript> You need to enable JavaScript to run ZScaler app. </noscript>
  </body>
</html>
