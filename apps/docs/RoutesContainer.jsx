import { Suspense } from 'react';
import { Route, Routes } from 'react-router-dom';

import Spinner from '../components/spinner/Spinner';

import AppLayout from './layout/AppLayout';

import CalendarPage from './pages/CalendarPage';
import DropDownPage from './pages/DropDownPage';
import FormPages from './pages/FormPages';
import LandingPage from './pages/LandingPage';
import LanguageSelectorPage from './pages/LanguageSelectorPage';
import ListBuilderPage from './pages/ListBuilderPage';
import ModalPage from './pages/ModalPages';
import TablePage from './pages/TablePage';
import TagsPage from './pages/TagsPage';
import TooltipPage from './pages/TooltipPage';
import AuditLogsPage from './pages/admin/AuditLogsPage';
import ExternalIdentitesPage from './pages/admin/ExternalIdentitiesPage';
import IpLocationGroupsPage from './pages/admin/IpLocationGroupsPage';
import IpLocationsPage from './pages/admin/IpLocationsPage';
import MyProfilePage from './pages/admin/MyProfilePage';
import ServicesPage from './pages/admin/ServicesPage';
import UsersPage from './pages/admin/UsersPage';
import PasswordPage from './pages/policy/PasswordPage';
import SignonPolicyPage from './pages/policy/SignonPolicyPage';

const RoutesContainer = () => {
  return (
    <Suspense fallback={<Spinner />}>
      <Routes>
        <Route
          path="/"
          element={
            <>
              <AppLayout />
            </>
          }
        >
          <Route index element={<MyProfilePage />} />

          <Route path="policy/signon" element={<SignonPolicyPage />} />
          <Route path="policy/password" element={<PasswordPage />} />

          <Route path="/admin/users" element={<UsersPage />} />
          <Route path="/admin/audit-logs" element={<AuditLogsPage />} />
          <Route path="/admin/zscaler-services" element={<ServicesPage />} />
          <Route path="admin/ip-locations" element={<IpLocationsPage />} />
          <Route path="admin/ip-location-groups" element={<IpLocationGroupsPage />} />
          <Route path="admin/external-identities" element={<ExternalIdentitesPage />} />

          <Route path="*" element={<MyProfilePage />} />

          <Route path="/dropdown" element={<DropDownPage />} />
          <Route path="/calendar" element={<CalendarPage />} />
          <Route path="/language-selector" element={<LanguageSelectorPage />} />
          <Route path="/form" element={<FormPages />} />
          <Route path="/list-builder" element={<ListBuilderPage />} />
          <Route path="/tags" element={<TagsPage />} />
          <Route path="/tooltip" element={<TooltipPage />} />
          <Route path="/modal" element={<ModalPage />} />
          <Route path="/table" element={<TablePage />} />

          <Route path="*" element={<LandingPage />} />
        </Route>
      </Routes>
    </Suspense>
  );
};

export default RoutesContainer;
