import { createSelector } from '@reduxjs/toolkit';

import { getDropDownList } from '../../../components/dropdowns/helper';

import {
  DATA_AUTHENTICATION_METHODS,
  DATA_IDP_CERTIFICATE,
  DATA_METADATA,
  DATA_NEW_IDP_DETAIL,
  DATA_PRIMARY_IDP_TABLE_DETAIL,
  DATA_SECONDARY_IDP_TABLE_DETAIL,
  DATA_SIGNING_ALGORITHMS,
  DATA_SP_CERTIFICATES,
  DATA_SP_CERTIFICATE_TYPES,
  DATA_VENDOR_NAMES,
  DEFAULT_AUTHENTICATION_METHODS,
  DEFAULT_IDP_CERTIFICATE,
  DEFAULT_METADATA,
  DEFAULT_NEW_IDP_DETAIL,
  DEFAULT_PRIMARY_IDP_TABLE_CONFIG,
  DEFAULT_PRIMARY_IDP_TABLE_DETAIL,
  DEFAULT_SECONDARY_IDP_TABLE_CONFIG,
  DEFAULT_SECONDARY_IDP_TABLE_DETAIL,
  DEFAULT_SIGNING_ALGORITHMS,
  DEFAULT_SP_CERTIFICATE_TYPES,
  DEFAULT_VENDOR_NAMES,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectPrimaryIdpTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_PRIMARY_IDP_TABLE_DETAIL] || DEFAULT_PRIMARY_IDP_TABLE_DETAIL,
);

export const selectIsTotalPrimaryIdpRecordsFetched = createSelector(
  selectPrimaryIdpTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectPrimaryIdpTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_PRIMARY_IDP_TABLE_CONFIG,
);

export const selectSecondaryIdpTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SECONDARY_IDP_TABLE_DETAIL] || DEFAULT_SECONDARY_IDP_TABLE_DETAIL,
);

export const selectIsTotalSecondaryRecordsFetched = createSelector(
  selectSecondaryIdpTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectSecondaryIdpTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_SECONDARY_IDP_TABLE_CONFIG,
);

export const selectExternalIdentitiesDetails = createSelector(
  selectPrimaryIdpTableDetail,
  selectSecondaryIdpTableDetail,
  ({ data: primaryData }, { data: secondaryData }) => {
    return [...primaryData, ...secondaryData];
  },
);

export const selectExternalIdentitiesList = createSelector(
  selectPrimaryIdpTableDetail,
  selectSecondaryIdpTableDetail,
  ({ data: primaryData }, { data: secondaryData }) => {
    const list = getDropDownList({ list: [...primaryData, ...secondaryData] });

    if (list.length == 0) {
      list.push({ label: 'NO_DATA', value: 'NO_DATA' });
    }

    return list;
  },
);

export const selectVendorNameList = createSelector(baseSelector, (state) => {
  const vendorNames = state[DATA_VENDOR_NAMES] || DEFAULT_VENDOR_NAMES;

  return vendorNames.map((name) => ({ label: name, value: name }));
});

export const selectSPCertificateTypes = createSelector(
  baseSelector,
  (state) => state[DATA_SP_CERTIFICATE_TYPES] || DEFAULT_SP_CERTIFICATE_TYPES,
);

export const selectSigningCertificateList = createSelector(baseSelector, (state) => {
  const certificateDetail = state[DATA_SP_CERTIFICATES].SIGNING || [];

  return certificateDetail.map(({ subjectName, id }) => ({
    label: subjectName,
    value: id,
  }));
});

export const selectEncryptionCertificateList = createSelector(baseSelector, (state) => {
  const certificateDetail = state[DATA_SP_CERTIFICATES].ENCRYPTION || [];

  return certificateDetail.map(({ subjectName, id }) => ({
    label: subjectName,
    value: id,
  }));
});

export const selectSigningAlgorithmList = createSelector(baseSelector, (state) => {
  const algorithmNames = state[DATA_SIGNING_ALGORITHMS] || DEFAULT_SIGNING_ALGORITHMS;

  return algorithmNames.map((name) => ({ label: name, value: name }));
});

export const selectSAMLMetadata = createSelector(
  baseSelector,
  (state) => state[DATA_METADATA] || DEFAULT_METADATA,
);

export const selectIdPCertificate = createSelector(
  baseSelector,
  (state) => state[DATA_IDP_CERTIFICATE] || DEFAULT_IDP_CERTIFICATE,
);

export const selectAuthenticationMethodsList = createSelector(baseSelector, (state) => {
  const authenticationMethods =
    state[DATA_AUTHENTICATION_METHODS] || DEFAULT_AUTHENTICATION_METHODS;

  return authenticationMethods.map((name) => ({ label: name, value: name }));
});

export const selectNewIdpDetail = createSelector(baseSelector, (state) => {
  return state[DATA_NEW_IDP_DETAIL] || DEFAULT_NEW_IDP_DETAIL;
});
