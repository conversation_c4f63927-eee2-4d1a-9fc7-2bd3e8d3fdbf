import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_SESSION_SETTINGS, DEFAULT_STATE, REDUCER_KEY } from './constants';

const advancedSettingsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateSettings(state, { payload }) {
      state[DATA_SESSION_SETTINGS] = payload;
    },
  },
});

const { updateSettings } = advancedSettingsSlice.actions;

export const getSessionSettings = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/session-settings';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateSettings(response.data));
  }
};

export const updateSessionSettings = (payload) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/session-settings';

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(updateSettings(response.data));
  }
};

export default advancedSettingsSlice;
