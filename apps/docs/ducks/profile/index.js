import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_MY_PROFILE, DEFAULT_STATE, REDUCER_KEY } from './constants';

const profileSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateMyProfile(state, { payload }) {
      let myProfileDetail = state[DATA_MY_PROFILE];

      if (payload) {
        myProfileDetail = { ...myProfileDetail, ...payload };
      }

      state[DATA_MY_PROFILE] = myProfileDetail;
    },
  },
});

const { updateMyProfile } = profileSlice.actions;

export const getProfile = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT);

  if (response?.data) {
    dispatch(updateMyProfile(response.data));
  }
};

export const updateProfile = (newDetails) => async (dispatch) => {
  const response = await http.put(API_ENDPOINT, { ...newDetails });

  if (response?.data) {
    dispatch(updateMyProfile(response.data));
  }
};

export const changeEmail = (primaryEmail) => async () => {
  await http.put(API_ENDPOINT + '/email', { primaryEmail });
};

export const verifyEmail = (code) => async (dispatch) => {
  const response = await http.post(API_ENDPOINT + '/email/verify', { code });

  if (response.statusText === 'OK') {
    dispatch(getProfile());
  }
};

export const updatePassword =
  ({ oldPwd, newPwd }) =>
  async () => {
    await http.post(API_ENDPOINT + '/pwd-change', { oldPwd, newPwd });
  };

export default profileSlice;
