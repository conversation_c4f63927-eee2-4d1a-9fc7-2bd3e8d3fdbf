import advancedSettingsSlice from './advanced-settings';
import attributesSlice from './attributes';
import auditLogsSlice from './audit-logs';
import countryCodesSlice from './countries';
import externalIdentitiesSlice from './external-identities';
import globalSlice from './global';
import groupsSlice from './groups';
import locationGroupsSlice from './ip-location-groups';
import locationsSlice from './ip-locations';
import loginSlice from './login';
import passwordPolicySlice from './password';
import profileSlice from './profile';
import servicesSlice from './services';
import signonPoliciesSlice from './signon-policies';
import tenantDomainsSlice from './tenant-domains';
import usersSlice from './users';

export const reducer = {
  [advancedSettingsSlice.name]: advancedSettingsSlice.reducer,
  [attributesSlice.name]: attributesSlice.reducer,
  [auditLogsSlice.name]: auditLogsSlice.reducer,
  [countryCodesSlice.name]: countryCodesSlice.reducer,
  [externalIdentitiesSlice.name]: externalIdentitiesSlice.reducer,
  [globalSlice.name]: globalSlice.reducer,
  [groupsSlice.name]: groupsSlice.reducer,
  [locationGroupsSlice.name]: locationGroupsSlice.reducer,
  [locationsSlice.name]: locationsSlice.reducer,
  [loginSlice.name]: loginSlice.reducer,
  [passwordPolicySlice.name]: passwordPolicySlice.reducer,
  [profileSlice.name]: profileSlice.reducer,
  [servicesSlice.name]: servicesSlice.reducer,
  [signonPoliciesSlice.name]: signonPoliciesSlice.reducer,
  [tenantDomainsSlice.name]: tenantDomainsSlice.reducer,
  [usersSlice.name]: usersSlice.reducer,
};
