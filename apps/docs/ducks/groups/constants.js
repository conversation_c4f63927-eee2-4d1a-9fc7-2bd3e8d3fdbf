export const REDUCER_KEY = 'groups';

export const API_ENDPOINT = '/admin/internal-api/v1/groups';

export const DATA_TABLE_DETAIL = 'tableDetail';
export const DATA_GROUPS = 'groups';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NA',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'description',
    accessorFn: (row) => row.description || 'NA',
    Header: 'DESCRIPTION',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_GROUPS = {};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_GROUPS]: DEFAULT_GROUPS,
};
