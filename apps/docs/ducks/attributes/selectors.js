import { createSelector } from '@reduxjs/toolkit';

import { getDropDownList } from '../../../components/dropdowns/helper';

import {
  DATA_TABLE_DETAIL,
  DATA_TYPES,
  DEFAULT_DATA_TYPES,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectAttributeList = createSelector(selectTableDetail, ({ data }) => {
  return data.map(({ attrName, displayName }) => ({ label: displayName, value: attrName }));
});

export const selectDataTypesDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TYPES] || DEFAULT_DATA_TYPES,
);

export const selectDataTypesList = createSelector(selectDataTypesDetail, ({ data }) => {
  return getDropDownList({ list: data });
});
