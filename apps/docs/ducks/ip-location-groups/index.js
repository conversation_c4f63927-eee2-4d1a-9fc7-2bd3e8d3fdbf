import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import { getAttachmentDetail } from '../helper';
import {
  API_ENDPOINT,
  DATA_LOCATION_GROUPS,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
  REDUCER_KEY,
} from './constants';

const locationGroupsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateLocationGroupDetail(state, { payload }) {
      const groups = state[DATA_LOCATION_GROUPS];

      const id = payload?.id;

      groups[id] = payload;
    },
  },
});

const { updateLocationGroupDetail, updateTableDetail } = locationGroupsSlice.actions;

export const getList =
  ({ pageSize = 100, pageOffset = 0, requireTotal = true, name = '' } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name.trim()}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const getDetail =
  ({ id, lite = false }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}?lite=${lite}`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateLocationGroupDetail(response.data || {}));
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.id}`;
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export const bulkRemove =
  ({ ids }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/bulk-delete`;

    await http.put(requestUrl, ids);

    dispatch(getList());
  };

export const importFromCsv =
  ({ override, filesDetail }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/import?override=${override}`;

    const files = new FormData();
    files.append('importcsv', filesDetail[0]);

    const response = await http.post(requestUrl, files, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    dispatch(getList());

    return response?.data || {};
  };

export const downloadTemplateCSV = () => async () => {
  const requestUrl = API_ENDPOINT + '/import/download-template';

  const response = await http.get(requestUrl);

  if (response?.data) {
    return getAttachmentDetail(response);
  }
};

export default locationGroupsSlice;
