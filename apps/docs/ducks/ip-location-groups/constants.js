export const REDUCER_KEY = 'ip-location-groups';

export const API_ENDPOINT = '/admin/internal-api/v1/location-groups';

export const DATA_TABLE_DETAIL = 'tableDetail';
export const DATA_LOCATION_GROUPS = 'locationGroups';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'NAME',
    size: 300,
    minSize: 300,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'locations',
    accessorFn: (row) => row.locations.map(({ name }) => name).join(', ') || '',
    Header: 'LOCATIONS',
    size: 300,
    minSize: 300,
    defaultCanSort: true,
    sortType: 'number',
  },
  {
    id: 'description',
    accessorFn: (row) => row.description || '',
    Header: 'DESCRIPTION',
    size: 300,
    minSize: 300,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'name' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_LOCATION_GROUP = {};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_LOCATION_GROUPS]: DEFAULT_LOCATION_GROUP,
};
