import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, API_EUSA_ENDPOINT, DEFAULT_STATE, REDUCER_KEY } from './constants';

const tenantSlice = createSlice({ name: REDUCER_KEY, initialState: DEFAULT_STATE, reducers: {} });

export const onboard = (payload) => async () => {
  await http.post(API_ENDPOINT + '/onboard', payload);
};

export const getEUSAContent = () => async () => {
  const response = await http.get(API_EUSA_ENDPOINT);
  if (response?.data) {
    return response;
  }
};

export default tenantSlice;
