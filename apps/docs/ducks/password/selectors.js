import { createSelector } from '@reduxjs/toolkit';

import {
  CONFIGURATION_TYPES,
  DATA_ACTIVE_PASSWORD_POLICY,
  DATA_PASSWORD_POLICIES,
  DEFAULT_ACTIVE_CONFIGURATION_TYPE,
  DEFAULT_ACTIVE_CONFIG_DETAIL,
  RECOMMENDED_PASSWORD_POLICY,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectActiveConfigurationType = createSelector(
  baseSelector,
  (data) =>
    data[DATA_ACTIVE_PASSWORD_POLICY].configurationType || DEFAULT_ACTIVE_CONFIGURATION_TYPE,
);

export const selectActiveConfigDetail = createSelector(
  baseSelector,
  (data) => data[DATA_ACTIVE_PASSWORD_POLICY].config || DEFAULT_ACTIVE_CONFIG_DETAIL,
);

export const selectRecommendedPasswordPolicyDetail = createSelector(
  baseSelector,
  (data) => data[DATA_PASSWORD_POLICIES][CONFIGURATION_TYPES.RECOMMENDED]?.config || {},
);

export const selectCustomPasswordPolicyDetail = createSelector(
  baseSelector,
  (data) => data[DATA_PASSWORD_POLICIES][CONFIGURATION_TYPES.CUSTOM]?.config || {},
);

export const selectIsPasswordPolicyWeek = (formValues) => {
  const { minLength, minLowerCase, minUpperCase, minNumeric, minSpecialChar, expiryAge } =
    RECOMMENDED_PASSWORD_POLICY;

  if (formValues.minLength < minLength) {
    return 'MIN_LENGTH';
  }

  if (minLowerCase.minLowerCase < minLowerCase) {
    return 'MIN_LOWER_CASE';
  }

  if (minLowerCase.minUpperCase < minUpperCase) {
    return 'MIN_UPPER_CASE';
  }

  if (minLowerCase.minNumeric < minNumeric) {
    return 'MIN_UPPER_CASE';
  }

  if (minLowerCase.minSpecialChar < minSpecialChar) {
    return 'MIN_UPPER_CASE';
  }

  if (minLowerCase.expiryAge < expiryAge) {
    return 'MIN_UPPER_CASE';
  }
};
