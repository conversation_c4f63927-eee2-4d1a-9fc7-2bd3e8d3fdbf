import { getErrorMessageFromApiResponse } from '../../utils/http';

import { showErrorNotification, showSuccessNotification } from './global';

export const apiSuccessNotifier =
  (dispatch, showNotification = true, payload = {}) =>
  async (response) => {
    if (showNotification && payload.message) {
      return dispatch?.(showSuccessNotification({ ...payload })).then((notificationId) => {
        return Promise.resolve({ notificationId, ...(response || {}) });
      });
    }

    // should be handled by components
    return Promise.resolve(response);
  };

export const apiErrorNotifier =
  (dispatch, showNotification = true, payload = {}) =>
  async ({ error }) => {
    if (showNotification) {
      const errorMessage = getErrorMessageFromApiResponse({ error });

      return dispatch?.(
        showErrorNotification({ autoHide: false, ...payload, message: errorMessage }),
      ).then((notificationId) => {
        return Promise.reject({ notificationId, ...error });
      });
    }

    // should be handled by components
    return Promise.reject(error);
  };

export const getAttachmentDetail = ({ data, headers }) => {
  const contentType = headers['content-type'];
  const headerLine = headers['content-disposition'];

  const startFileNameIndex = headerLine.indexOf('=') + 1;
  const endFileNameIndex = headerLine.length;

  const fileName = headerLine?.substring?.(startFileNameIndex, endFileNameIndex) || 'sample.txt';

  return { data, fileName, contentType };
};
