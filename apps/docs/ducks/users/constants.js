export const REDUCER_KEY = 'users';

export const API_ENDPOINT = '/admin/internal-api/v1/users';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const DATA_AUTO_GEN_PASS = 'autoGenPassword';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'displayName',
    accessorFn: (row) => row.displayName || 'NONE',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'loginName',
    accessorFn: (row) => row.loginName || 'NONE',
    Header: 'USER_ID',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'status',
    accessorFn: (row) => row.status || false,
    Header: 'STATUS',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_AUTO_GEN_PASS_DETAIL = '';

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 1,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_AUTO_GEN_PASS]: DEFAULT_AUTO_GEN_PASS_DETAIL,
};
