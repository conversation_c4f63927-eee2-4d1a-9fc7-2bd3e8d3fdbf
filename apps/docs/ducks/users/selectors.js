import { createSelector } from '@reduxjs/toolkit';

import { getDropDownList } from '../../../components/dropdowns/helper';

import {
  DATA_AUTO_GEN_PASS,
  DATA_TABLE_DETAIL,
  DEFAULT_AUTO_GEN_PASS_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectAutoGenPass = createSelector(
  baseSelector,
  (state) => state[DATA_AUTO_GEN_PASS] || DEFAULT_AUTO_GEN_PASS_DETAIL,
);

export const selectUsersList = createSelector(selectTableDetail, ({ data }) =>
  getDropDownList({ list: data, labelKey: 'displayName' }),
);
