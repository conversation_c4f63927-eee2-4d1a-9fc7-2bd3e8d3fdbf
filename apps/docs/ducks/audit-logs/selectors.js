import { createSelector } from '@reduxjs/toolkit';

import { getDropDownList } from '../../../components/dropdowns/helper';

import {
  DATA_ACTION_TYPES,
  DATA_CATEGORIES,
  DATA_INTERFACES,
  DATA_RESULTS,
  DATA_SUB_CATEGORIES,
  DATA_TABLE_DETAIL,
  DEFAULT_ACTION_TYPES,
  DEFAULT_CATEGORIES,
  DEFAULT_INTERFACES,
  DEFAULT_RESULTS,
  DEFAULT_SUB_CATEGORIES,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectInterfacesEnums = createSelector(
  baseSelector,
  (state) => state[DATA_INTERFACES] || DEFAULT_INTERFACES,
);

export const selectInterfacesEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_INTERFACES] || DEFAULT_INTERFACES;

  return getDropDownList({ list: data });
});

export const selectResultsEnums = createSelector(
  baseSelector,
  (state) => state[DATA_RESULTS] || DEFAULT_RESULTS,
);

export const selectResultsEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_RESULTS] || DEFAULT_RESULTS;

  return getDropDownList({ list: data });
});

export const selectActionTypesEnums = createSelector(
  baseSelector,
  (state) => state[DATA_ACTION_TYPES] || DEFAULT_ACTION_TYPES,
);

export const selectActionTypesEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_ACTION_TYPES] || DEFAULT_ACTION_TYPES;

  return getDropDownList({ list: data });
});

export const selectCategoriesEnums = createSelector(
  baseSelector,
  (state) => state[DATA_CATEGORIES] || DEFAULT_CATEGORIES,
);

export const selectCategoriesEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_CATEGORIES] || DEFAULT_CATEGORIES;

  return getDropDownList({ list: data });
});

export const selectSubCategoriesEnums = createSelector(
  baseSelector,
  (state) => state[DATA_SUB_CATEGORIES] || DEFAULT_SUB_CATEGORIES,
);

export const selectSubCategoriesEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_SUB_CATEGORIES] || DEFAULT_SUB_CATEGORIES;

  return getDropDownList({ list: data });
});
