import { createSelector } from '@reduxjs/toolkit';

import { find } from 'lodash-es';

import { getDropDownList } from '../../../components/dropdowns/helper';

import {
  DATA_SCOPE_ROLES_DETAIL,
  DATA_SERVICES_ASSIGNMENT_DETAIL,
  DATA_SERVICES_DETAIL,
  DATA_SERVICES_ROLE_DETAIL,
  DATA_SERVICES_SCOPE_DETAIL,
  DATA_SERVICE_CONSTRAINTS,
  DATA_TABLE_DETAIL,
  DEFAULT_LINKED_TENANTS_TABLE_CONFIG,
  DEFAULT_ROLE_TABLE_CONFIG,
  DEFAULT_SCOPE_ROLES_DETAIL,
  DEFAULT_SERVICES_ASSIGNMENT_DETAIL,
  DEFAULT_SERVICES_DETAIL,
  DEFAULT_SERVICES_ROLE_DETAIL,
  DEFAULT_SERVICES_SCOPE_DETAIL,
  DEFAULT_SERVICE_CONSTRAINTS,
  DEFAULT_SERVICE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  DEFAULT_USERS_GROUPS_TABLE_CONFIG,
  REDUCER_KEY,
} from './constants';

const EMPTY_ARRAY = [];
const EMPTY_OBJECT = {};

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectLinkedTenantsTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_LINKED_TENANTS_TABLE_CONFIG,
);

export const selectServicesDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICES_DETAIL] || DEFAULT_SERVICES_DETAIL,
);

export const selectServiceDetail = (servicesDetail, id) =>
  servicesDetail[id] || DEFAULT_SERVICE_DETAIL;

export const selectServiceIsTotalRecordsFetched = (servicesDetail, id) =>
  servicesDetail[id]?.hasFetchedAllRecords;

export const selectServicesAssignmentDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICES_ASSIGNMENT_DETAIL] || DEFAULT_SERVICES_ASSIGNMENT_DETAIL,
);

export const selectServiceAssignmentDetail = (servicesAssignmentDetail, id) =>
  servicesAssignmentDetail[id] || DEFAULT_TABLE_DETAIL;

export const selectServiceAssignmentIsTotalRecordsFetched = (servicesAssignmentDetail, id) =>
  servicesAssignmentDetail[id]?.hasFetchedAllRecords;

export const selectServicesRoleDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICES_ROLE_DETAIL] || DEFAULT_SERVICES_ROLE_DETAIL,
);

export const selectServiceRoleDetail = (servicesRoleDetail, id) =>
  servicesRoleDetail[id] || EMPTY_ARRAY;

export const selectServiceRoleList = (list) => getDropDownList({ list: list });

export const selectServicesScopeDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICES_SCOPE_DETAIL] || DEFAULT_SERVICES_SCOPE_DETAIL,
);

export const selectServiceScopeDetail = (servicesScopeDetail, id) =>
  servicesScopeDetail[id] || EMPTY_ARRAY;

export const selectServiceScopeList = (list) => getDropDownList({ list: list });

export const selectScopesRoleDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SCOPE_ROLES_DETAIL] || DEFAULT_SCOPE_ROLES_DETAIL,
);

export const selectScopeRoleDetail = (selectScopesRoleDetail, id) =>
  selectScopesRoleDetail[id] || EMPTY_ARRAY;

export const selectScopeRoleList = (list) => getDropDownList({ list: list });

export const selectRoleTableConfig = createSelector(baseSelector, () => DEFAULT_ROLE_TABLE_CONFIG);

export const selectUsersGroupsTableConfig = (baseSelector, () => DEFAULT_USERS_GROUPS_TABLE_CONFIG);

export const selectServiceConstraints = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICE_CONSTRAINTS] || DEFAULT_SERVICE_CONSTRAINTS,
);

export const selectServiceConstraintDetail = (serviceConstraints, serviceName) =>
  find(serviceConstraints, { serviceName }) || EMPTY_OBJECT;
