export const REDUCER_KEY = 'services';

export const API_ENDPOINT = '/admin/internal-api/v1/services';

export const DATA_TABLE_DETAIL = 'tableDetail';
export const DATA_SERVICES_DETAIL = 'servicesDetail';
export const DATA_SERVICES_ASSIGNMENT_DETAIL = 'servicesAssignmentDetail';
export const DATA_SERVICES_ROLE_DETAIL = 'servicesRoleDetail';
export const DATA_SERVICES_SCOPE_DETAIL = 'servicesScopeDetail';
export const DATA_SCOPE_ROLES_DETAIL = 'scopeRolesDetail';
export const DATA_ROLE_TABLE_DETAIL = 'roleTableDetail';
const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'serviceName',
    accessorFn: (row) => row.serviceDescription || 'NONE',
    Header: 'NAME',
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'cloudAndOrgId',
    accessorFn: (row) => {
      const cloudName = row.cloudDomainName || '';
      const orgName = row.orgName ? row.orgName : '';

      let detailedName = `${cloudName}${orgName}`;

      if (cloudName) {
        detailedName = `${cloudName}, ${orgName}`;
      }

      return detailedName || '';
    },
    Header: 'CLOUD_AND_ORG_ID',
    size: 550,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_LINKED_TENANTS_TABLE_COLUMNS_DETAILS = [
  {
    id: 'serviceName',
    accessorFn: (row) => row.serviceName || '',
    Header: 'PRODUCT',
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'cloudName',
    accessorFn: (row) => row.cloudName || '',
    Header: 'CLOUD_NAME',
    size: 250,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'orgName',
    accessorFn: (row) => row.orgName || '',
    Header: 'ORG_NAME',
    size: 300,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'status',
    Header: 'STATUS',
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_LINKED_TENANTS_TABLE_CONFIG = {
  columns: DEFAULT_LINKED_TENANTS_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

const DEFAULT_ROLE_COLUMNS_DETAIL = [
  {
    id: 'idx',
    accessorFn: (row, idx) => idx + 1,
    Header: 'TABLE_NUMBER',
    size: 100,
    enableResizing: false,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'roleName',
    accessorFn: (row) => row.name,
    Header: 'ROLE',
    size: 450,
    enableResizing: false,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'scopeAndRoleName',
    accessorFn: (row) => row.name,
    Header: 'SCOPES_N_ROLES',
    size: 450,
    enableResizing: false,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_ROLE_TABLE_CONFIG = {
  columns: DEFAULT_ROLE_COLUMNS_DETAIL,
  initialState: {
    sortBy: [{ roleName: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_SERVICES_DETAIL = {};
export const DEFAULT_SERVICE_DETAIL = { inheritTserviceId: '', tserviceRoles: [] };
export const DEFAULT_SERVICES_ASSIGNMENT_DETAIL = {};
export const DEFAULT_SERVICES_ROLE_DETAIL = {};
export const DEFAULT_SERVICES_SCOPE_DETAIL = {};
export const DEFAULT_SCOPE_ROLES_DETAIL = {};

export const DEFAULT_USERS_GROUPS_TABLE_COLUMNS_DETAILS = [
  {
    id: 'serviceName',
    accessorFn: (row) => row.resource?.name || '',
    Header: 'USERS_AND_GROUPS',
    size: 250,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'type',
    accessorFn: (row) => row.type || '',
    Header: 'TYPE',
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'role',
    accessorFn: (row) => row?.tsRoles.map?.(({ name }) => name)?.join?.(',') || '',
    Header: 'ROLE',
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'scope',
    accessorFn: (row) => row?.tsScope?.name || '',
    Header: 'SCOPE',
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 150,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_USERS_GROUPS_TABLE_CONFIG = {
  columns: DEFAULT_USERS_GROUPS_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DATA_SERVICE_CONSTRAINTS = 'serviceConstraints';

export const DEFAULT_SERVICE_CONSTRAINTS = [
  {
    serviceName: 'ZIA',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZPA',
    scopeSupport: true,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZCC',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZDX',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'ZBI',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: true,
  },
  {
    serviceName: 'DECEPTION',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: true,
    linkUnlink: true,
  },
  {
    serviceName: 'ZIAM',
    scopeSupport: false,
    roleSupport: true,
    multiRoleSupport: false,
    linkUnlink: false,
  },
];

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_SERVICES_DETAIL]: DEFAULT_SERVICES_DETAIL,
  [DATA_SERVICES_ASSIGNMENT_DETAIL]: DEFAULT_SERVICES_ASSIGNMENT_DETAIL,
  [DATA_SERVICES_ROLE_DETAIL]: DEFAULT_SERVICES_ROLE_DETAIL,
  [DATA_SERVICES_SCOPE_DETAIL]: DEFAULT_SERVICES_SCOPE_DETAIL,
  [DATA_SCOPE_ROLES_DETAIL]: DEFAULT_SCOPE_ROLES_DETAIL,
  [DATA_SERVICE_CONSTRAINTS]: DEFAULT_SERVICE_CONSTRAINTS,
};
