import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_TABLE_DETAIL, DEFAULT_STATE, REDUCER_KEY } from './constants';

const tenantDomainsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
  },
});

const { updateTableDetail } = tenantDomainsSlice.actions;

export const getList =
  ({
    pageSize = 100,
    pageOffset = 0,
    requireTotal = true,
    name = '',
    requirePseudoDomain = false,
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT +
      `?limit=${pageSize}&offset=${pageOffset}&requirePseudoDomain=${requirePseudoDomain}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name.trim()}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.idpid}`;
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ idpid }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${idpid}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export default tenantDomainsSlice;
