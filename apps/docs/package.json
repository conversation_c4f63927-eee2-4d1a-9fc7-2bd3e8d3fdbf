{"name": "docs", "version": "0.0.1", "description": "Docs for ZUI Reusable Component Library", "main": "dist/index.js", "module": "dist/index.es.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "storybook": "start-storybook -p 6006 -s ./dist", "build-storybook": "build-storybook", "build": "rollup -c", "dev": "rollup -c -w", "build-doc-dev": "webpack --mode development", "build-doc": "webpack --mode production", "start": "webpack serve --mode development", "format": "prettier --write .", "sass-watch": "sass --no-source-map -w src/scss/main.scss:dist/index.css", "update": "ncu -u && pnpm i", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "https://bitbucket.corp.zscaler.com/scm/oneidentity/zui-component-library.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "files": ["dist/"], "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@floating-ui/react": "^0.27.8", "@fortawesome/fontawesome-pro": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@reduxjs/toolkit": "^2.8.2", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@storybook/addon-actions": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/react": "^8.6.14", "@tanstack/react-table": "^8.21.3", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "airbnb-browser-shims": "^3.3.0", "axios": "^1.9.0", "babel-loader": "^10.0.0", "core-js": "^3.42.0", "css-loader": "^7.1.2", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "i18next": "^25.1.3", "i18next-http-backend": "^3.0.2", "jest": "^29.7.0", "lint-staged": "^16.0.0", "lodash-es": "^4.17.21", "mini-css-extract-plugin": "^2.9.2", "prettier": "^3.5.3", "process": "^0.11.10", "prop-types": "^15.8.1", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^8.3.0", "react-diff-viewer": "^3.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-merge-refs": "^3.0.2", "react-redux": "^9.2.0", "react-refresh": "^0.17.0", "react-router-dom": "^7.6.0", "rollup": "^4.40.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-scss": "^4.0.1", "sass": "^1.89.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "svg-url-loader": "^8.0.0", "terser-webpack-plugin": "^5.3.14", "url-loader": "^4.1.1", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "peerDependencies": {"@fortawesome/fontawesome-pro": "^6.2.1", "@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-brands-svg-icons": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/pro-light-svg-icons": "^6.2.1", "@fortawesome/pro-regular-svg-icons": "^6.2.1", "@fortawesome/pro-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "airbnb-browser-shims": "^3.3.0", "i18next": "^22.1.4", "lodash-es": "^4.17.21", "prop-types": "^15.8.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-i18next": "^12.1.1", "react-redux": "^8.0.5", "react-router-dom": "^6.4.5"}, "lint-staged": {"*.{js,jsx}": ["eslint --cache --fix", "prettier --ignore-unknown --plugin-search-dir=. --write"], "*.{css,scss,md,json}": "prettier --ignore-unknown --plugin-search-dir=. --write"}}