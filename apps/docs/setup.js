import { setFloatingPortalRootId, setModalRootId, setToastPortalRootId } from '../config/floating';
import { setApiMethodMessageOption } from '../config/toast';

import { hideLoader, hideNotification, showLoader } from './ducks/global';
import { apiErrorNotifier, apiSuccessNotifier } from './ducks/helper';

import { createDOMElement, isDOMElementPresent } from '../utils/dom';
import { API_METHOD } from '../utils/http';

import { updateUseApiCallFunctionsRegistry } from '../hooks/useApiCall';
import { APP_ID, PORTAL_ROOT_ID } from './config';

export const getAppRootElement = ({
  tagName = 'div',
  parentElement = document.body,
  id = APP_ID,
} = {}) => {
  const { isPresent } = isDOMElementPresent({ id });

  if (!isPresent) {
    createDOMElement({ tagName, parentElement, attributes: { id } });
  }

  const { element } = isDOMElementPresent({ id });

  return element;
};

export const setupUseApiCallFunctionsRegistry = () => {
  updateUseApiCallFunctionsRegistry({
    showLoader,
    hideLoader,
    hideNotification,
    apiSuccessNotifier,
    apiErrorNotifier,
  });
};

export const setupModalRootId = ({ rootId } = {}) => {
  setModalRootId(rootId || PORTAL_ROOT_ID);
};

export const setupFloatingPortalRootId = ({ rootId } = {}) => {
  setFloatingPortalRootId(rootId || PORTAL_ROOT_ID);
  setupModalRootId(rootId || PORTAL_ROOT_ID);
  setToastPortalRootId(rootId || PORTAL_ROOT_ID);
};

export const setupApiMethodMessageOption = () => {
  setApiMethodMessageOption({
    [API_METHOD.GET]: { message: '', translationMapping: {} },
    [API_METHOD.PUT]: { message: 'ALL_CHANGES_SAVED', translationMapping: {} },
    [API_METHOD.POST]: { message: 'ALL_CHANGES_SAVED', translationMapping: {} },
    [API_METHOD.DELETE]: { message: 'ITEM_HAS_BEEN_DELETED', translationMapping: {} },
  });
};
