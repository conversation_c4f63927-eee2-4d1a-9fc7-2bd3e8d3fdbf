#external-identities-page {
  .primary-idp-table-container {
    margin-top: 20px;
    height: auto;
    max-height: 200px;
  }

  .secondary-idp-table-container {
    max-height: calc(100vh - 250px);

    &.has-no-data {
      height: auto;
    }
  }
}

.external-identities-form-modal {
  .config-buttons {
    .button {
      width: auto;
    }
  }

  .provisioning-form-container {
    margin-top: 20px;

    .status-container {
      margin-top: 8px;

      &.enabled {
        color: $color-standard-green-text;
      }

      &.disabled {
        color: $color-standard-red-text;
      }
    }
  }
}

.attribute-mapping {
  .user-attribute-section {
    margin-left: 16px;
  }
}

.choose-identity-container {
  .card {
    box-shadow: none;
  }

  .heading {
    padding-top: 4px;

    padding-bottom: 28px;

    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
  }

  .radio {
    margin-bottom: 32px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .content-container {
    font-size: 13px;
    line-height: 14px;
  }

  .info-section {
    display: flex;

    margin: 16px 0;
    padding: 8px 12px;

    margin-left: 28px;

    border-radius: 5px;
    background-color: $color-lighter-gray-background;
  }

  .field {
    margin-top: 0;
    margin-bottom: 24px;

    padding-left: 28px;
  }

  .buttons {
    padding-top: 8px;
    margin-bottom: 0;
  }
}
