#nav-layout {
  display: flex;

  position: relative;

  & > .left {
    display: flex;
    flex-flow: column wrap;

    justify-content: space-between;

    width: 100px;
    height: calc(100vh - 32px);

    padding: 25px 0px;

    background-color: $color-standard-blue-text;

    .nav-group {
      width: 100%;
    }

    .menu-container {
      display: flex;
      flex-flow: column wrap;
      align-content: center;

      justify-content: center;

      padding: 15px 0px 9px;

      // &:first-of-type {
      //   padding-top: 0px;
      // }

      &.active {
        border-left: 4px solid $color-nav-active;
        background-color: $color-nav-active-background;
      }

      .menu {
        display: flex;
        flex-flow: column wrap;
        align-content: center;

        align-items: center;

        color: $color-white-background;

        text-decoration: unset;
      }

      .icon {
        // width: 30px;
        height: 30px;

        align-self: center;

        margin-bottom: 8px;

        &.small {
          height: 24px;
        }

        &.large {
          height: 40px;
        }
      }

      .text-normal {
        color: $color-white-background;
      }

      &:hover {
        .sub-menu-list-container {
          display: flex;
          flex-flow: row wrap;

          justify-content: space-between;
        }
      }
    }
  }

  & > .right {
    width: 100%;
    height: calc(100vh - 32px);

    overflow: hidden;

    &:hover {
      overflow: auto;
    }

    padding: 16px 20px 0px;

    background-color: $color-body-background;
  }

  .sub-menu-list-container {
    position: absolute;

    display: none;

    top: 0px;
    left: 100px;

    max-width: 460px;
    height: calc(100vh - 32px);

    overflow-y: auto;

    padding: 12px 8px 40px;

    z-index: 50;

    color: $color-white-background;
    background-color: $color-nav-active-background;

    .sub-menu-container {
      display: flex;
      flex-flow: column wrap;

      width: 200px;

      margin-top: 28px;

      .label-section {
        color: $color-nav-active;
        width: 100%;
        padding-left: 12px;

        padding-bottom: 8px;
      }

      .links-container {
        display: flex;
        flex-flow: column wrap;
      }

      & .link {
        color: $color-white-background;

        width: 100%;

        padding: 10px 12px;
        border-radius: 4px;

        &:hover {
          background-color: $color-standard-blue-text;
        }
      }
    }
  }
}

.help-container.bottom-right {
  bottom: 32px;
}
