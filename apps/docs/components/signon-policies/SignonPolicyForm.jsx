import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop, range, remove } from 'lodash-es';

import { Button, ToggleButton } from '../../../components/buttons';
import Card from '../../../components/cards/Card';
import DropDown from '../../../components/dropdowns/DropDown';
import { Field, FieldGroup, Input, Label, TextArea } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import { getList as getLocationGroupsList } from '../../ducks/ip-location-groups';
import {
  selectIsTotalRecordsFetched as selectIsLocationGroupsTotalRecordsFetched,
  selectTableDetail as selectLocationGroupsTableDetail,
} from '../../ducks/ip-location-groups/selectors';
import { getList as getLocationsList } from '../../ducks/ip-locations';
import {
  selectIsTotalRecordsFetched as selectIsLocationsTotalRecordsFetched,
  selectTableDetail as selectLocationsTableDetail,
} from '../../ducks/ip-locations/selectors';
import { getActions, getFields, getOperations } from '../../ducks/signon-policies';
import {
  selectActionList,
  selectFieldList,
  selectOperationList,
  selectTableDetail,
} from '../../ducks/signon-policies/selectors';

import { mergeFormValues } from '../../../utils/dom';

import useApiCall from '../../../hooks/useApiCall';
import {
  getFormTooltipDetail,
  getFormValidationDetail,
  isConditionValid,
  listGenerator,
} from './helper';

const DEFAULT_PROPS = defaultFormProps;

const defaultCondition = {
  field: [],
  op: [],
  value: [],
};

const SignonPolicyForm = ({
  onDetailChange,
  detail,
  mode,
  validationDetail,
  setValidationDetail,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const isEditMode = mode === 'edit';

  const { totalRecord } = useSelector(selectTableDetail);

  const ruleOrderList = useMemo(() => {
    if (isEditMode) {
      const maxRange = Math.max(totalRecord, 1);

      return range(1, maxRange + 1).map(listGenerator);
    }

    const maxRange = Math.max(totalRecord + 1, 1);

    return range(1, maxRange + 1).map(listGenerator);
  }, [isEditMode]);

  const fieldList = useSelector(selectFieldList);
  const operationList = useSelector(selectOperationList);
  const actionList = useSelector(selectActionList);

  const locationsTableDetail = useSelector(selectLocationsTableDetail);
  const isLocationsTotalRecordsFetched = useSelector(selectIsLocationsTotalRecordsFetched);

  const locationGroupsTableDetail = useSelector(selectLocationGroupsTableDetail);
  const isLocationGroupsTotalRecordsFetched = useSelector(
    selectIsLocationGroupsTotalRecordsFetched,
  );

  const [formValues, setFormValues] = useState({
    disabled: true,
    ...detail,
  });

  const [selectedCondition, setSelectedCondition] = useState(() => {
    const { conditions } = detail;

    const newSelectedCondition = [];

    if (conditions && conditions.length > 0) {
      conditions?.forEach(({ field, op, value: { id, name } }) => {
        newSelectedCondition.push({
          field: [{ label: field, value: field }],
          op: [{ label: op, value: op }],
          value: [{ label: name, value: id }],
        });
      });

      return newSelectedCondition;
    }

    return [{ ...defaultCondition }];
  });

  const [selectedAction, setSelectedAction] = useState(() => {
    const { action } = detail;

    if (action) {
      return [{ label: action, value: action }];
    }

    return [];
  });

  const [selectedRuleOrder, setSelectedRuleOrder] = useState(() => {
    const { ruleOrder } = detail;

    if (ruleOrder) {
      return [{ label: ruleOrder, value: ruleOrder }];
    }

    return [];
  });

  useEffect(() => {
    if (!isEditMode) {
      const newRuleOrder = Math.max(totalRecord + 1, 1);
      setSelectedRuleOrder([{ label: newRuleOrder, value: newRuleOrder }]);
      setFormValues((prevState) => ({ ...prevState, ruleOrder: newRuleOrder }));
    }
  }, [totalRecord, isEditMode]);

  const [locationsList, setLocationsList] = useState([]);
  const [locationGroupsList, setLocationGroupsList] = useState([]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    apiCall(getActions()).catch(noop);
    apiCall(getFields()).catch(noop);
    apiCall(getOperations()).catch(noop);

    if (isEditMode) {
      if (!isLocationsTotalRecordsFetched) {
        apiCall(getLocationsList()).catch(noop);
      }

      if (!isLocationGroupsTotalRecordsFetched) {
        apiCall(getLocationGroupsList()).catch(noop);
      }
    }
  }, []);

  useEffect(() => {
    onDetailChange(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    const data = locationsTableDetail?.data?.map(({ name, id }) => ({
      label: name,
      value: id,
    }));

    setLocationsList(data);
  }, [locationsTableDetail]);

  useEffect(() => {
    const data = locationGroupsTableDetail?.data?.map(({ name, id }) => ({
      label: name,
      value: id,
    }));

    setLocationGroupsList(data);
  }, [locationGroupsTableDetail]);

  const getSelectedConditionDetail = (key, idx) => selectedCondition?.[idx]?.[key];

  const updateConditionsFormValues = (newConditions) => {
    let conditions = [];

    newConditions?.forEach(({ field, op, value }) => {
      if (field?.[0]?.value && op?.[0]?.value && value?.[0]?.value) {
        conditions.push({
          field: field?.[0]?.value,
          op: op?.[0]?.value,
          // value: { name: value?.[0].label, id: value?.[0].value },
          value: { id: value?.[0].value },
        });
      }
    });

    setFormValues((prevState) => ({ ...prevState, conditions }));
  };

  const onDeleteCondition = (removeIdx) => {
    setSelectedCondition((prevState) => {
      const newConditions = remove(prevState, (_, idx) => idx !== removeIdx);

      if (newConditions.length === 0) {
        newConditions.push({ ...defaultCondition });
      }

      updateConditionsFormValues(newConditions);

      return newConditions;
    });
  };

  const onAddCondition = () => {
    setSelectedCondition((prevState) =>
      isConditionValid(prevState[prevState.length - 1])
        ? [...prevState, { ...defaultCondition }]
        : [...prevState],
    );
  };

  const onConditionSelection = (key, detail, idx) => {
    setSelectedCondition((prevState) => {
      const detailToUpdate = prevState[idx];

      detailToUpdate[key] = detail;

      if (key === 'field') {
        detailToUpdate.value = [];
      }

      prevState[idx] = detailToUpdate;

      updateConditionsFormValues(prevState);

      return [...prevState];
    });
  };

  const getResourceList = (idx) => {
    const { field } = selectedCondition[idx];

    const { value } = field[0] || {};

    if (value === 'LOCATION') {
      return locationsList;
    }

    if (value === 'LOCATION_GROUP') {
      return locationGroupsList;
    }

    return [];
  };

  const onLoadMoreClick =
    (idx) =>
    ({ term, fromStart }) => {
      const { field } = selectedCondition[idx];

      const { value } = field[0] || {};

      const isLocation = value === 'LOCATION';

      if (fromStart) {
        apiCall(
          (isLocation ? getLocationsList : getLocationGroupsList)?.({
            name: term,
            requireTotal: true,
          }),
        ).catch(noop);
      } else {
        const { pageSize, pageOffset } = isLocation
          ? locationsTableDetail
          : locationGroupsTableDetail;

        apiCall(
          (isLocation ? getLocationsList : getLocationGroupsList)?.({
            name: term,
            requireTotal: false,
            pageOffset: pageOffset + pageSize,
            pageSize,
          }),
        ).catch(noop);
      }
    };

  const getLoadMoreDetail = (idx) => {
    const { field } = selectedCondition[idx];

    const { value } = field[0] || {};

    if (value === 'LOCATION' && !isLocationsTotalRecordsFetched) {
      return { ...locationsTableDetail, onLoadMoreClick: onLoadMoreClick(idx) };
    }

    if (value === 'LOCATION_GROUP' && !isLocationGroupsTotalRecordsFetched) {
      return { ...locationGroupsTableDetail, onLoadMoreClick: onLoadMoreClick(idx) };
    }

    return {};
  };

  const onResourceListOpen = (idx) => {
    const { field } = selectedCondition[idx];

    const { value } = field[0] || {};

    if (value === 'LOCATION' && !isLocationsTotalRecordsFetched) {
      apiCall(getLocationsList()).catch(noop);
    }

    if (value === 'LOCATION_GROUP' && !isLocationGroupsTotalRecordsFetched) {
      apiCall(getLocationGroupsList()).catch(noop);
    }
  };

  const onRuleOrderSelection = (detail) => {
    setSelectedRuleOrder(detail);

    if (detail?.[0]) {
      setFormValues((prevState) => ({ ...prevState, ruleOrder: detail?.[0]?.value }));
    }
  };

  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      disabled: !formValues?.disabled,
    }));
  };

  const onActionSelection = (detail) => {
    setSelectedAction(detail);

    if (detail?.[0]) {
      setFormValues((prevState) => ({ ...prevState, action: detail?.[0]?.value }));
    }
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('INFORMATION')}</section>

        <Card containerClass="signon-policy-form">
          <FieldGroup>
            <Field
              label="RULE_ORDER"
              htmlFor="ruleOrder"
              info={validationDetail}
              tooltip={getFormTooltipDetail('ruleOrder')}
            >
              <DropDown
                list={ruleOrderList}
                selectedList={selectedRuleOrder}
                onSelection={onRuleOrderSelection}
                containerClass="full-width"
              />
            </Field>

            <Input
              label="NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              info={validationDetail}
              tooltip={getFormTooltipDetail('name')}
            />
          </FieldGroup>

          <FieldGroup>
            <Field
              label="RULE_STATUS"
              containerStyle={{ marginRight: '40px' }}
              tooltip={getFormTooltipDetail('ruleStatus')}
            >
              <ToggleButton
                type="success"
                isOn={!formValues?.disabled}
                onToggleClick={onToggleStatusClick}
                onLabel="ENABLED"
                offLabel="DISABLED"
              />
            </Field>
          </FieldGroup>

          <FieldGroup>
            <TextArea
              name="description"
              value={formValues.description}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              maxLength="512"
              rows="3"
              tooltip={getFormTooltipDetail('description')}
            />
          </FieldGroup>
        </Card>
      </>
    );
  };

  const renderConditionSection = () => {
    const showDelete = (idx) => {
      if (isEditMode) {
        return isConditionValid(selectedCondition[idx]);
      }

      return idx < selectedCondition.length - 1;
    };

    const isConditionsValid = validationDetail.context === 'conditions';

    return (
      <>
        <section className="text-upper-large">
          <Label
            text="CRITERIA"
            info={isConditionsValid ? validationDetail : {}}
            containerStyle={{ marginBottom: '0' }}
          />
        </section>

        <Card>
          {selectedCondition.map((_, idx) => (
            <div key={idx} className="is-flex full-width condition-section">
              <DropDown
                list={fieldList}
                selectedList={getSelectedConditionDetail('field', idx)}
                onSelection={(detail) => onConditionSelection('field', detail, idx)}
                containerClass="condition"
              />

              <DropDown
                list={operationList}
                selectedList={getSelectedConditionDetail('op', idx)}
                onSelection={(detail) => onConditionSelection('op', detail, idx)}
                containerClass="condition"
              />

              <DropDown
                list={getResourceList(idx)}
                selectedList={getSelectedConditionDetail('value', idx)}
                onSelection={(detail) => onConditionSelection('value', detail, idx)}
                onOpen={() => onResourceListOpen(idx)}
                loadMoreDetail={getLoadMoreDetail(idx)}
                containerClass="condition"
              />

              {showDelete(idx) && (
                <Button
                  type="tertiary"
                  containerClass="content-width no-p-l no-p-r has-as-s"
                  onClick={() => onDeleteCondition(idx)}
                >
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              )}
            </div>
          ))}

          <div className="is-flex has-jc-e">
            <Button type="tertiary" containerClass="no-p-r" onClick={onAddCondition}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              {t('ADD_MORE')}
            </Button>
          </div>
        </Card>
      </>
    );
  };

  const renderActionSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('ACTION')}</section>

        <Card>
          <Field
            label="ACTION"
            htmlFor="action"
            info={validationDetail}
            tooltip={getFormTooltipDetail('action')}
          >
            <DropDown
              list={actionList}
              selectedList={selectedAction}
              onSelection={onActionSelection}
              containerClass="action-dd"
            />
          </Field>
        </Card>
      </>
    );
  };

  return (
    <>
      {renderInformationSection()}

      {renderConditionSection()}

      {renderActionSection()}
    </>
  );
};

SignonPolicyForm.DEFAULT_PROPS = DEFAULT_PROPS;

SignonPolicyForm.propTypes = defaultFormPropTypes;

export default SignonPolicyForm;
