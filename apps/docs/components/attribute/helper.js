import { defaultValidationDetail } from '../../../components/forms/helper';

export const modalModeDetail = {
  '': {},
  add: {
    headerText: 'ADD_ATTRIBUTE',
  },
  edit: {
    headerText: 'EDIT_ATTRIBUTE',
  },
  delete: {
    headerText: 'DELETE_ATTRIBUTE',
    confirmationMessage:
      'Are you sure you want to delete this attribute? The changes cannot be undone.',
  },
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { attrName, displayName, dataType } = formValues || {};

  if (!attrName) {
    validationDetail.isValid = false;
    validationDetail.context = 'attrName';
    validationDetail.type = 'error';
    validationDetail.message = 'Attribute Name is Required';

    return validationDetail;
  }

  if (!displayName) {
    validationDetail.isValid = false;
    validationDetail.context = 'displayName';
    validationDetail.type = 'error';
    validationDetail.message = 'Display Name is Required';

    return validationDetail;
  }

  if (!dataType) {
    validationDetail.isValid = false;
    validationDetail.context = 'dataType';
    validationDetail.type = 'error';
    validationDetail.message = 'data type is the required attribute';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'attrName') {
    tooltipDetail.content = `Enter the SAML attribute name (e.g., Group or Email Address). To find all the available SAML attribute names, refer to the SAML JSON for your IdP.`;
  }

  if (name === 'displayName') {
    tooltipDetail.content = (
      <p>
        Enter a name for the SAML attribute. This name is only for your reference and doesn&apos;t
        need to be identical to the corresponding entry in{' '}
        <strong className="tooltip-bold">Attribute Name</strong> field.
      </p>
    );
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. Comments cannot exceed 512 characters.`;
  }

  if (name === 'dataType') {
    tooltipDetail.content = `Select the type of data the user needs to enter for this attribute`;
  }

  if (name === 'required') {
    tooltipDetail.content = (
      <p>
        Check the option to mandate the attribute information when{' '}
        <a href="https://help.zscaler.com/zslogin/adding-users">adding users</a>.
      </p>
    );
  }

  return tooltipDetail;
};
