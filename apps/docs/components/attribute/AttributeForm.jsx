import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import Card from '../../../components/cards/Card';
import DropDown from '../../../components/dropdowns/DropDown';
import Checkbox from '../../../components/forms/Checkbox';
import Field from '../../../components/forms/Field';
import FieldGroup from '../../../components/forms/FieldGroup';
import Input from '../../../components/forms/Input';
import TextArea from '../../../components/forms/TextArea';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import { getDataTypes } from '../../ducks/attributes';
import { selectDataTypesDetail, selectDataTypesList } from '../../ducks/attributes/selectors';

import { mergeFormValues } from '../../../utils/dom';

import useDropDownActions from '../../../hooks/useDropDownActions';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const AttributeForm = ({ onDetailChange, detail, mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();

  const inEditMode = mode === 'edit';

  const dataTypes = useSelector(selectDataTypesDetail);
  const dataTypesList = useSelector(selectDataTypesList);

  const { isDropDownLoading, onDropDownOpen } = useDropDownActions({
    detail: dataTypes,
    apiCallFunc: getDataTypes,
  });

  const [selectedOption, setSelectedOption] = useState([
    { label: detail.dataType, value: detail.dataType },
  ]);

  const [formValues, setFormValues] = useState({
    attrName: '',
    displayName: '',
    comment: '',
    dataType: '',
    required: false,
    ...detail,
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    onDetailChange(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onSelection = (detail) => {
    if (detail && detail[0]) {
      setFormValues((prevState) => ({ ...prevState, dataType: detail?.[0]?.value }));
    }

    setSelectedOption(detail);
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('INFORMATION')}</section>

        <Card>
          <FieldGroup>
            <Input
              label="ATTRIBUTE_NAME"
              name="attrName"
              onChange={onFormFieldChange}
              value={formValues.attrName}
              info={validationDetail}
              readOnly={inEditMode}
              tooltip={getFormTooltipDetail('attrName')}
            />
            <Input
              label="DISPLAY_NAME"
              name="displayName"
              onChange={onFormFieldChange}
              value={formValues.displayName}
              info={validationDetail}
              tooltip={getFormTooltipDetail('displayName')}
            />
          </FieldGroup>

          <div className="is-flex">
            <TextArea
              name="comment"
              value={formValues.comment}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              maxLength="512"
              rows="3"
              tooltip={getFormTooltipDetail('description')}
            />
          </div>

          <FieldGroup>
            <Field label="DATA_TYPE" tooltip={getFormTooltipDetail('dataType')}>
              <DropDown
                list={dataTypesList}
                selectedList={selectedOption}
                onSelection={onSelection}
                containerClass="full-width"
                onOpen={onDropDownOpen}
                loading={isDropDownLoading}
              />
            </Field>
            <Field label="ATTRIBUTE_REQUIRED" tooltip={getFormTooltipDetail('required')}>
              <Checkbox
                name="required"
                checked={formValues.required}
                onChange={onFormFieldChange}
              />
            </Field>
          </FieldGroup>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

AttributeForm.DEFAULT_PROPS = DEFAULT_PROPS;

AttributeForm.propTypes = { ...defaultFormPropTypes };

export default AttributeForm;
