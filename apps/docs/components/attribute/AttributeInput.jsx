import { find, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import InlineDatePicker from '../../../components/calendar/InlineDatePicker';
import DropDown from '../../../components/dropdowns/DropDown';
import Field from '../../../components/forms/Field';
import Input from '../../../components/forms/Input';

const BOOLEAN_OPTIONS = [
  { label: 'TRUE', value: 'True' },
  { label: 'FALSE', value: 'False' },
];

const DEFAULT_PROPS = {
  detail: {},
  onChange: noop,
  value: '',
};

const getInputType = (dataType) => {
  if (dataType === 'DECIMAL' || dataType === 'INTEGER') {
    return 'number';
  }

  if (dataType === 'DATE') {
    return 'date';
  }

  return 'text';
};

const AttributeInput = ({ detail, onChange, value, ...rest }) => {
  const inputType = getInputType(detail.dataType);

  const onValueChange = (evt) => {
    onChange(evt);
  };

  const onBooleanSelection = (selectionDetail) => {
    const { value } = selectionDetail[0] || {};

    const evt = {
      target: {
        name: detail.attrName,
        value: value,
      },
    };

    onChange(evt);
  };

  if (detail.dataType === 'BOOLEAN') {
    const selectedDetail = find(BOOLEAN_OPTIONS, { value });

    const selectedList = selectedDetail ? [selectedDetail] : [];

    return (
      <Field label={detail.displayName} htmlFor={detail.attrName} {...rest}>
        <DropDown
          list={BOOLEAN_OPTIONS}
          selectedList={selectedList}
          onSelection={onBooleanSelection}
          toggleSelected
          containerStyle={{ minWidth: '100%' }}
        />
      </Field>
    );
  }

  if (detail.dataType === 'DATE') {
    return (
      <InlineDatePicker
        label={detail.displayName}
        name={detail.attrName}
        selectedDate={value}
        onChange={onValueChange}
        elementProps={{ containerStyle: { height: '32px' } }}
        {...rest}
      />
    );
  }

  return (
    <Input
      type={inputType}
      label={detail.displayName}
      name={detail.attrName}
      onChange={onValueChange}
      value={value}
      maxLength="128"
      {...rest}
    />
  );
};

AttributeInput.DEFAULT_PROPS = DEFAULT_PROPS;

AttributeInput.propTypes = {
  detail: PropTypes.object,
  onChange: PropTypes.func,
  value: PropTypes.any,
};

export default AttributeInput;
