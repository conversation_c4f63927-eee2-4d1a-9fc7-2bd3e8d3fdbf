import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import { Field, FieldGroup, Input } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';
import ListBuilder from '../../../components/listBuilder/ListBuilder';

import { getCountryCodes } from '../../ducks/countries';
import { selectCountryCodesList } from '../../ducks/countries/selectors';

import { mergeFormValues } from '../../../utils/dom';

import useApiCall from '../../../hooks/useApiCall';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const IpLocationsForm = ({
  onDetailChange,
  detail,
  mode,
  validationDetail,
  setValidationDetail,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const [list, setList] = useState(detail?.ipInLocations || []);

  const [formValues, setFormValues] = useState({
    name: '',
    country: {},
    ipInLocations: [],
    ...detail,
  });

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ipInLocations: list }));
  }, [list]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    onDetailChange(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    apiCall(getCountryCodes());
  }, []);

  const countryCodesList = useSelector(selectCountryCodesList);

  const [selectedCountry, setSelectedCountry] = useState(() => {
    const { country } = detail;

    if (country?.id) {
      return [{ label: country.name, value: country.id }];
    }

    return [];
  });

  const onCountrySelection = (detail) => {
    setSelectedCountry(detail);

    if (detail?.[0]) {
      setFormValues((prevState) => ({ ...prevState, country: { id: detail?.[0]?.value } }));
    }
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('INFORMATION')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              maxLength="128"
              info={validationDetail}
              tooltip={getFormTooltipDetail('name')}
            />
          </FieldGroup>

          <FieldGroup>
            <Field
              label="COUNTRY"
              containerClass="full-width"
              info={validationDetail}
              tooltip={getFormTooltipDetail('country')}
            >
              <DropDown
                list={countryCodesList}
                selectedList={selectedCountry}
                onSelection={onCountrySelection}
              />
            </Field>
          </FieldGroup>

          <Field
            label="IP_ADDRESS"
            containerClass="full-width"
            info={validationDetail}
            tooltip={getFormTooltipDetail('ipAddress')}
          >
            <ListBuilder list={list} setList={setList} separator={/[\r\n,]+/} />
          </Field>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

IpLocationsForm.DEFAULT_PROPS = DEFAULT_PROPS;

IpLocationsForm.propTypes = defaultFormPropTypes;

export default IpLocationsForm;
