import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { filter } from 'lodash-es';

import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import MultiSelection from '../../../components/dropdowns/MultiSelection';
import { Field, FieldGroup, Input, TextArea } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import { getList } from '../../ducks/ip-locations';
import { selectIpLocationsList, selectTableDetail } from '../../ducks/ip-locations/selectors';

import { mergeFormValues } from '../../../utils/dom';

import useDropDownActions from '../../../hooks/useDropDownActions';
import { getFormTooltipDetail, getFormValidationDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const IpLocationGroupsForm = ({
  onDetailChange,
  detail,
  mode,
  validationDetail,
  setValidationDetail,
}) => {
  const { t } = useTranslation();

  const locationsListTableDetail = useSelector(selectTableDetail);
  const ipLocationsList = useSelector(selectIpLocationsList);

  const [selectedOption, setSelectedOption] = useState(() => {
    const { locations } = detail || {};

    if (locations) {
      return locations.map(({ id, name }) => ({ label: name, value: id }));
    }

    return [];
  });

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: locationsListTableDetail,
    apiCallFunc: getList,
  });

  const [formValues, setFormValues] = useState({
    name: '',
    locations: [],
    ...detail,
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    onDetailChange(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onSelection = (detail) => {
    const selectedLocationIds = detail?.map(({ value }) => value);

    setFormValues((prevState) => ({
      ...prevState,
      locations: filter(
        locationsListTableDetail.data,
        (o) => selectedLocationIds.indexOf(o.id) !== -1,
      ).map(({ id, name }) => ({ id, name })),
    }));

    setSelectedOption(detail);
  };

  const renderInformationSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('INFORMATION')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="NAME"
              name="name"
              onChange={onFormFieldChange}
              value={formValues.name}
              maxLength="128"
              info={validationDetail}
              tooltip={getFormTooltipDetail('name')}
            />
          </FieldGroup>
          <FieldGroup>
            <Field
              label="LOCATIONS"
              containerClass="full-width"
              htmlFor="locations"
              info={validationDetail}
              tooltip={getFormTooltipDetail('locations')}
            >
              <DropDown
                list={ipLocationsList}
                selectedList={selectedOption}
                onOpen={onDropDownOpen}
                onSelection={onSelection}
                renderItemsSelection={(props) => (
                  <MultiSelection
                    unselectedTitle="UNSELECTED_LOCATIONS"
                    selectedTitle="SELECTED_LOCATIONS"
                    {...props}
                  />
                )}
                isMulti
                hasSearch
                containerClass="full-width"
                loadMoreDetail={{ ...locationsListTableDetail, onLoadMoreClick }}
                loading={isDropDownLoading}
                containerStyle={{ maxWidth: '250px' }}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <TextArea
              name="description"
              value={formValues.description}
              onChange={onFormFieldChange}
              label="DESCRIPTION"
              maxLength="512"
              rows="3"
              tooltip={getFormTooltipDetail('description')}
            />
          </FieldGroup>
        </Card>
      </>
    );
  };

  return <>{renderInformationSection()}</>;
};

IpLocationGroupsForm.DEFAULT_PROPS = DEFAULT_PROPS;

IpLocationGroupsForm.propTypes = defaultFormPropTypes;

export default IpLocationGroupsForm;
