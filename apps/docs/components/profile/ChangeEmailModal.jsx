import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faCheckCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import { FieldGroup, Input } from '../../../components/forms';
import Modal from '../../../components/modal/Modal';
import ModalBody from '../../../components/modal/ModalBody';
import ModalFooter from '../../../components/modal/ModalFooter';
import ModalHeader from '../../../components/modal/ModalHeader';

import { changeEmail, verifyEmail } from '../../ducks/profile';

import { mergeFormValues } from '../../../utils/dom';
import { validEmail } from '../../../utils/validations';

import useApiCall from '../../../hooks/useApiCall';

const DEFAULT_PROPS = {
  show: false,
  detail: {},
  onCLoseClick: noop,
};

const ChangeEmailModal = ({ show, detail, onCloseClick }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const [formStep, setFormStep] = useState(1);

  const [formValues, setFormValues] = useState({ ...detail, code: '' });

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...detail }));
  }, [detail]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  const onValidateEmailClick = () => {
    apiCall(changeEmail(formValues.primaryEmail))
      ?.then?.(() => {
        setFormStep(2);
      })
      ?.catch?.(noop);
  };

  const onVerifyClick = () => {
    apiCall(verifyEmail(formValues.code))
      ?.then?.(() => {
        setFormStep(3);
      })
      ?.catch?.(noop);
  };

  const onResendCodeClick = () => {
    apiCall(changeEmail(formValues.primaryEmail))?.catch?.(noop);
  };

  const renderBodySection = () => {
    return (
      <>
        <section className="text-upper-large">{t('UPDATE_EMAIL')}</section>

        <Card>
          <div className="is-flex full-width">
            <FieldGroup containerClass="has-width-auto">
              <Input
                label="NEW_EMAIL"
                name="primaryEmail"
                onChange={onFormFieldChange}
                value={formValues.primaryEmail}
                disabled={formStep !== 1}
              />
            </FieldGroup>
            <div className="is-flex has-ai-c actions-info-section">
              {formStep === 1 ? (
                <Button
                  onClick={onValidateEmailClick}
                  disabled={validEmail(formValues.primaryEmail)}
                >
                  {t('VALIDATE_EMAIL')}
                </Button>
              ) : null}
              {formStep === 2 ? (
                <div className="info">
                  We have sent the validation code to {formValues.primaryEmail}.
                </div>
              ) : null}
            </div>
          </div>

          {formStep !== 1 ? (
            <div className="is-flex full-width">
              <FieldGroup containerClass="has-width-auto">
                <Input
                  label="VALIDATION_CODE"
                  name="code"
                  onChange={onFormFieldChange}
                  value={formValues.code}
                  disabled={formStep === 3}
                />
              </FieldGroup>
              <div className="is-flex has-ai-c actions-info-section">
                {formStep === 2 ? (
                  <>
                    <Button onClick={onVerifyClick} disabled={!formValues.code}>
                      {t('VERIFY')}
                    </Button>
                    <div className="is-flex has-ai-c info">
                      <div>Did not receive?</div>
                      <Button type="tertiary" containerClass="no-p-l" onClick={onResendCodeClick}>
                        {t('RESEND_CODE')}
                      </Button>
                    </div>
                  </>
                ) : null}
                {formStep === 3 ? (
                  <div className="is-flex has-ai-c info">
                    <span className="text success">
                      <FontAwesomeIcon
                        icon={faCheckCircle}
                        className="icon left has-color-success"
                      />
                    </span>
                    <span>{formValues.primaryEmail} has been verified successfully.</span>
                  </div>
                ) : null}
              </div>
            </div>
          ) : null}
        </Card>
      </>
    );
  };

  return (
    <Modal show={show} onEscape={onCloseClick} containerClass="change-email-modal-container">
      <ModalHeader text="EDIT_EMAIL" onClose={onCloseClick} />
      <ModalBody>{renderBodySection()}</ModalBody>
      <ModalFooter
        saveText="DONE"
        isSaveDisabled={formStep !== 3}
        onSave={onCloseClick}
        onCancel={onCloseClick}
      />
    </Modal>
  );
};

ChangeEmailModal.DEFAULT_PROPS = DEFAULT_PROPS;

ChangeEmailModal.propTypes = {
  show: PropTypes.bool,
  detail: PropTypes.object,
  onCloseClick: PropTypes.func,
};

export default ChangeEmailModal;
