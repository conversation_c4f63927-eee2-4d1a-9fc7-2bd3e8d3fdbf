import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../../components/cards';
import { PasswordInput } from '../../../components/forms';
import Modal from '../../../components/modal/Modal';
import ModalBody from '../../../components/modal/ModalBody';
import ModalFooter from '../../../components/modal/ModalFooter';
import ModalHeader from '../../../components/modal/ModalHeader';

import { getApiMethodNotificationOptions } from '../../../config/toast';

import { getPasswordPolicy } from '../../ducks/password';
import { selectActiveConfigDetail } from '../../ducks/password/selectors';
import { updatePassword } from '../../ducks/profile';

import { mergeFormValues } from '../../../utils/dom';
import { API_METHOD } from '../../../utils/http';

import useApiCall from '../../../hooks/useApiCall';
import { getChangePasswordFormValidationDetail } from './helper';

const DEFAULT_PROPS = {
  show: false,
  onCloseClick: noop,
};

const ChangePasswordModal = ({ show, onCloseClick }) => {
  const { apiCall } = useApiCall();

  const [validationDetail, setValidationDetail] = useState({});

  const [passwordPolicyValidation, setPasswordPolicyValidation] = useState({
    validationDetail: {},
    isValid: false,
  });

  const activeConfigDetail = useSelector(selectActiveConfigDetail);

  useEffect(() => {
    apiCall(getPasswordPolicy(), {
      successNotificationPayload: { ...getApiMethodNotificationOptions(API_METHOD.GET) },
    }).catch(noop);
  }, []);

  const [formValues, setFormValues] = useState({
    oldPwd: '',
    newPwd: '',
    confirmNewPwd: '',
  });

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    const formValidationDetail = getChangePasswordFormValidationDetail({
      formValues,
      passwordPolicyValidation,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues, passwordPolicyValidation]);

  const onResetPasswordClick = () => {
    if (validationDetail.isValid) {
      apiCall(updatePassword({ oldPwd: formValues.oldPwd, newPwd: formValues.newPwd }))
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const renderBodySection = () => {
    return (
      <Card>
        <PasswordInput
          label="CURRENT_PASSWORD"
          name="oldPwd"
          type="password"
          value={formValues.oldPwd}
          onChange={onFormFieldChange}
          info={validationDetail}
        />

        <PasswordInput
          label="NEW_PASSWORD"
          name="newPwd"
          type="password"
          value={formValues.newPwd}
          onChange={onFormFieldChange}
          showPasswordValidation
          onValidationChange={setPasswordPolicyValidation}
          passwordConfig={activeConfigDetail}
          info={validationDetail}
        />

        <PasswordInput
          label="CONFIRM_NEW_PASSWORD"
          name="confirmNewPwd"
          type="password"
          value={formValues.confirmNewPwd}
          onChange={onFormFieldChange}
          info={validationDetail}
        />
      </Card>
    );
  };

  return (
    <Modal show={show} onEscape={onCloseClick} containerClass="change-password-modal-container">
      <ModalHeader text="CHANGE_PASSWORD" onClose={onCloseClick} />
      <ModalBody> {renderBodySection()}</ModalBody>
      <ModalFooter
        saveText="RESET"
        onSave={onResetPasswordClick}
        isSaveDisabled={!validationDetail.isValid}
        onCancel={onCloseClick}
      />
    </Modal>
  );
};

ChangePasswordModal.DEFAULT_PROPS = DEFAULT_PROPS;

ChangePasswordModal.propTypes = {
  show: PropTypes.bool,
  onCloseClick: PropTypes.func,
};

export default ChangePasswordModal;
