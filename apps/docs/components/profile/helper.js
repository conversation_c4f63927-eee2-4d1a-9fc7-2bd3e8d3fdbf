import { defaultValidationDetail } from '../../../components/forms/helper';

export const getChangePasswordFormValidationDetail = ({ formValues, passwordPolicyValidation }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { oldPwd, newPwd, confirmNewPwd } = formValues || {};

  if (!oldPwd) {
    validationDetail.isValid = false;
    validationDetail.context = 'oldPwd';
    validationDetail.type = 'error';
    validationDetail.message = 'Current Password is Required';

    return validationDetail;
  }

  if (!newPwd) {
    validationDetail.isValid = false;
    validationDetail.context = 'newPwd';
    validationDetail.type = 'error';
    validationDetail.message = 'New Password is Required';

    return validationDetail;
  }

  if (!passwordPolicyValidation.isValid) {
    validationDetail.isValid = false;
    validationDetail.context = 'newPwd';
    validationDetail.type = 'error';
    validationDetail.message = `New Password doesn't follow password policy`;

    return validationDetail;
  }

  if (confirmNewPwd !== newPwd) {
    validationDetail.isValid = false;
    validationDetail.context = 'confirmNewPwd';
    validationDetail.type = 'error';
    validationDetail.message = `Confirm Password doesn't match New Password`;

    return validationDetail;
  }

  return validationDetail;
};
