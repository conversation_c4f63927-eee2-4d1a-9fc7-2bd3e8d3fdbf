import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { filter, noop } from 'lodash-es';

import Button from '../../../components/buttons/Button';
import ToggleButton from '../../../components/buttons/ToggleButton';
import Card from '../../../components/cards/Card';
import DropDown from '../../../components/dropdowns/DropDown';
import MultiSelection from '../../../components/dropdowns/MultiSelection';
import Checkbox from '../../../components/forms/Checkbox';
import Field from '../../../components/forms/Field';
import FieldGroup from '../../../components/forms/FieldGroup';
import Input from '../../../components/forms/Input';
import PasswordInput from '../../../components/forms/PasswordInput';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';
import Tab from '../../../components/tabs/Tab';
import Tabs from '../../../components/tabs/Tabs';

import { getList as getAttributeList } from '../../ducks/attributes';
import {
  selectTableDetail as selectAttributeTableDetail,
  selectIsTotalRecordsFetched as selectIsTotalAttributeRecordsFetched,
} from '../../ducks/attributes/selectors';
import { getList as getGroupList } from '../../ducks/groups';
import { selectGroupsList, selectTableDetail } from '../../ducks/groups/selectors';
import { getPasswordPolicy } from '../../ducks/password';
import { selectActiveConfigDetail } from '../../ducks/password/selectors';
import { getList as getDomainsList } from '../../ducks/tenant-domains';
import { selectTableDetail as selectTenantDomainsTableDetail } from '../../ducks/tenant-domains/selectors';
import { getAutoGeneratedPassword } from '../../ducks/users';
import { selectAutoGenPass } from '../../ducks/users/selectors';

import { getOnChangeValue } from '../../../utils/dom';

import useApiCall from '../../../hooks/useApiCall';
import useDropDownActions from '../../../hooks/useDropDownActions';
import AttributeInput from '../attribute/AttributeInput';
import {
  getCustomAttributeValidationDetail,
  getFormTooltipDetail,
  getFormValidationDetail,
} from './helper';

const TABS = {
  GENERAL: 'GENERAL',
  ATTRIBUTES: 'ADDITIONAL_ATTRIBUTES',
};

const DEFAULT_PROPS = defaultFormProps;

const getSelectedGroupsDetail = (list) => {
  return list?.map(({ name, id }) => {
    return { label: name, value: id };
  });
};

const UserForm = ({ onDetailChange, detail, validationDetail, setValidationDetail, mode }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const navigate = useNavigate();

  const [selectedTab, setSelectedTab] = useState(TABS.GENERAL);

  const groupListTableDetail = useSelector(selectTableDetail);
  const groupsList = useSelector(selectGroupsList);

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: groupListTableDetail,
    apiCallFunc: getGroupList,
  });

  const { data: attributeList = [] } = useSelector(selectAttributeTableDetail);
  const isTotalAttributeRecordsFetched = useSelector(selectIsTotalAttributeRecordsFetched);

  const customAttributeList = attributeList.filter((detail) => !detail.systemDefined);

  const { data: tenantDomainList } = useSelector(selectTenantDomainsTableDetail);
  const { allowAdminSetPasswords, forcePasswordChange } = useSelector(selectActiveConfigDetail);

  const autoGenPass = useSelector(selectAutoGenPass);

  const isEditMode = mode === 'edit';

  const passwordOptions = allowAdminSetPasswords
    ? [
        { label: 'SET_BY_ADMIN', value: 'SET_BY_ADMIN' },
        { label: 'SET_BY_USER', value: 'SET_BY_USER' },
        { label: 'AUTO_GENERATED', value: 'AUTO_GENERATED' },
      ]
    : [{ label: 'SET_BY_USER', value: 'SET_BY_USER' }];

  const [showSecuritySettings, setShowSecuritySettings] = useState(!isEditMode);
  const [isPrimaryEmailSameAsUserId, setIsPrimaryEmailSameAsUserId] = useState(
    detail?.loginName && detail?.loginName === detail?.primaryEmail,
  );
  const [selectedPasswordOption, setSelectedPasswordOption] = useState(passwordOptions[0]);
  const [selectedGroupOptions, setSelectedGroupOptions] = useState(
    getSelectedGroupsDetail([...(detail.groups || [])]),
  );

  const [formValues, setFormValues] = useState({
    loginName: '',
    displayName: '',
    firstName: '',
    lastName: '',
    primaryEmail: '',
    secondaryEmail: '',
    password: '',
    confirmPassword: '',
    customAttrsInfo: {},
    pwdConfig: { resetOnLogin: true, setByUser: false },
    ...detail,
  });

  const [attributeFormFileds, setAttributeFormFields] = useState(formValues.customAttrsInfo);

  const onFormFieldChange = (evt) => {
    setFormValues((prevState) => {
      const { name, value } = evt.target;

      const updatedValue = getOnChangeValue(evt.target);

      if (name === 'firstName' || name === 'lastName') {
        if (name === 'firstName') {
          updatedValue['displayName'] = value?.trim() + ' ' + prevState.lastName?.trim();

          updatedValue['displayName'] = updatedValue['displayName']?.trim();
        }

        if (name === 'lastName') {
          updatedValue['displayName'] = prevState.firstName?.trim() + ' ' + value?.trim();

          updatedValue['displayName'] = updatedValue['displayName']?.trim();
        }
      }

      if (name === 'loginName' && isPrimaryEmailSameAsUserId) {
        updatedValue['primaryEmail'] = updatedValue[name];
      }

      if (
        (name === 'loginName' && prevState['primaryEmail'] === updatedValue[name]) ||
        (name === 'primaryEmail' && prevState['loginName'] === updatedValue[name])
      ) {
        setIsPrimaryEmailSameAsUserId(true);
      }

      const newState = { ...prevState, ...updatedValue };

      return newState;
    });
  };

  useEffect(() => {
    setSelectedPasswordOption(passwordOptions[0]);

    if (!allowAdminSetPasswords) {
      setFormValues((prevState) => {
        const pwdConfig = { resetOnLogin: false, setByUser: true };

        return { ...prevState, pwdConfig };
      });
    }
  }, [allowAdminSetPasswords]);

  useEffect(() => {
    if (!isTotalAttributeRecordsFetched) {
      apiCall(getAttributeList({ requireTotal: true })).catch(noop);
    }

    apiCall(getPasswordPolicy()).catch(noop);
    apiCall(getDomainsList({ requirePseudoDomain: true })).catch(noop);
  }, []);

  useEffect(() => {
    onDetailChange(formValues);

    const domainList = tenantDomainList.map(({ objectName }) => objectName);

    const formValidationDetail = getFormValidationDetail({
      formValues,
      attributeList,
      domainList,
      allowAdminSetPasswords,
      selectedPasswordOption,
      showSecuritySettings,
    });

    setValidationDetail(formValidationDetail);
  }, [
    formValues,
    tenantDomainList,
    attributeList,
    allowAdminSetPasswords,
    selectedPasswordOption,
    showSecuritySettings,
  ]);

  const onSamePrimaryAndUserIdClick = () => {
    if (!isPrimaryEmailSameAsUserId) {
      setFormValues((prevState) => ({ ...prevState, primaryEmail: prevState.loginName }));
    }

    setIsPrimaryEmailSameAsUserId((prevState) => !prevState);
  };

  useEffect(() => {
    const { value } = selectedPasswordOption;

    if (value === 'AUTO_GENERATED') {
      setFormValues((prevState) => ({
        ...prevState,
        password: autoGenPass,
        pwdConfig: { ...prevState.pwdConfig, setByUser: false },
      }));
    }
  }, [autoGenPass]);

  const onPasswordSelection = (payload) => {
    const { value } = payload?.[0] || {};

    setSelectedPasswordOption(payload[0]);

    if (value === 'AUTO_GENERATED') {
      apiCall(getAutoGeneratedPassword()).catch(() => {
        setFormValues((prevState) => {
          const newState = { ...prevState };

          newState.password = '';
          newState.confirmPassword = '';

          return newState;
        });
      });
    } else {
      setFormValues((prevState) => {
        const isSetByUser = value === 'SET_BY_USER';

        const newState = { ...prevState };

        if (isSetByUser) {
          delete newState.password;
          newState.pwdConfig = { setByUser: isSetByUser };
        } else {
          newState.pwdConfig = { resetOnLogin: true, setByUser: isSetByUser };
        }

        newState.password = '';
        newState.confirmPassword = '';

        return newState;
      });
    }
  };

  const onResetOnLoginClick = (evt) => {
    const { checked } = evt.target;

    setFormValues((prevState) => ({
      ...prevState,
      pwdConfig: { ...prevState.pwdConfig, resetOnLogin: checked },
    }));
  };

  const onGroupSelection = (detail) => {
    const selectedGroupIds = detail?.map(({ value }) => value);

    setFormValues((prevState) => ({
      ...prevState,
      groups: filter(groupListTableDetail.data, (o) => selectedGroupIds.indexOf(o.id) !== -1).map(
        ({ id }) => ({ id }),
      ),
    }));

    setSelectedGroupOptions(detail);
  };

  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      status: !formValues?.status,
    }));
  };

  const renderUserInformationSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('USER_INFORMATION')}</section>
        <Card>
          <FieldGroup>
            <Input
              label="USER_ID"
              name="loginName"
              onChange={onFormFieldChange}
              value={formValues.loginName}
              maxLength="254"
              info={validationDetail}
              tooltip={getFormTooltipDetail('loginName')}
            />
            <Input
              label="NAME"
              name="displayName"
              placeholder=""
              onChange={onFormFieldChange}
              value={formValues.displayName}
              maxLength="128"
              info={validationDetail}
              disabled
              tooltip={getFormTooltipDetail('displayName')}
            />
          </FieldGroup>

          <FieldGroup>
            <Input
              label="FIRST_NAME"
              name="firstName"
              onChange={onFormFieldChange}
              value={formValues.firstName}
              maxLength="128"
              info={validationDetail}
              tooltip={getFormTooltipDetail('firstName')}
            />
            <Input
              label="LAST_NAME"
              name="lastName"
              onChange={onFormFieldChange}
              value={formValues.lastName}
              maxLength="128"
              info={validationDetail}
              tooltip={getFormTooltipDetail('lastName')}
            />
          </FieldGroup>

          <FieldGroup>
            <Input
              label="PRIMARY_EMAIL"
              name="primaryEmail"
              onChange={onFormFieldChange}
              value={formValues.primaryEmail}
              maxLength="254"
              disabled={isPrimaryEmailSameAsUserId}
              info={validationDetail}
              tooltip={getFormTooltipDetail('primaryEmail')}
            />
            <Field label="SAME_AS_USERID" tooltip={getFormTooltipDetail('sameAsUserId')}>
              <ToggleButton
                isOn={isPrimaryEmailSameAsUserId}
                onToggleClick={onSamePrimaryAndUserIdClick}
                showLabel={isEditMode}
              />
            </Field>
          </FieldGroup>

          <FieldGroup>
            <Input
              label="SECONDARY_EMAIL"
              name="secondaryEmail"
              onChange={onFormFieldChange}
              value={formValues.secondaryEmail}
              maxLength="254"
              info={validationDetail}
              containerStyle={{ maxWidth: '260px' }}
              tooltip={getFormTooltipDetail('secondaryEmail')}
            />

            <Field label="STATUS" tooltip={getFormTooltipDetail('status')}>
              <ToggleButton
                type="success"
                isOn={formValues?.status}
                onToggleClick={onToggleStatusClick}
                onLabel="ENABLED"
                offLabel="DISABLED"
              />
            </Field>
          </FieldGroup>
        </Card>
      </>
    );
  };

  const renderSecuritySettingsSection = () => {
    const isPasswordVisible = selectedPasswordOption.value !== 'SET_BY_USER';

    const isCheckBoxDisabled = selectedPasswordOption.value === 'SET_BY_USER';

    const isConfirmPasswordVisible = selectedPasswordOption.value === 'SET_BY_ADMIN';

    const canCopy = selectedPasswordOption.value === 'AUTO_GENERATED';

    return (
      <>
        <section className="text-upper-large">{t('SECURITY_SETTINGS')}</section>
        <Card>
          {isEditMode && (
            <Field label="CHANGE_PASSWORD_SETTINGS">
              <ToggleButton
                isOn={showSecuritySettings}
                onToggleClick={() => {
                  setShowSecuritySettings((prevState) => !prevState);
                }}
                showLabel={isEditMode}
              />
            </Field>
          )}

          {showSecuritySettings ? (
            <>
              <FieldGroup>
                <Field
                  label="PASSWORD_OPTION"
                  tooltip={getFormTooltipDetail('passwordOption')}
                  containerStyle={{ maxWidth: '260px' }}
                >
                  <DropDown
                    list={passwordOptions}
                    selectedList={[selectedPasswordOption]}
                    onSelection={onPasswordSelection}
                  />
                </Field>
                {isPasswordVisible ? (
                  <PasswordInput
                    label="PASSWORD_LABEL"
                    name="password"
                    type="password"
                    onChange={onFormFieldChange}
                    value={formValues.password}
                    canCopy={canCopy}
                    info={validationDetail}
                    tooltip={getFormTooltipDetail(canCopy ? 'canCopyPassword' : 'password')}
                  />
                ) : null}
              </FieldGroup>

              <FieldGroup>
                {!isCheckBoxDisabled && (
                  <Field
                    label="PROMPT_PASSWORD_FIRST_LOGIN"
                    tooltip={getFormTooltipDetail('promptPasswordFirstLogin')}
                  >
                    <Checkbox
                      name="resetOnLogin"
                      checked={formValues?.pwdConfig?.resetOnLogin}
                      disabled={isCheckBoxDisabled || forcePasswordChange}
                      onChange={onResetOnLoginClick}
                    />
                  </Field>
                )}

                {isConfirmPasswordVisible ? (
                  <PasswordInput
                    label="CONFIRM_PASSWORD"
                    name="confirmPassword"
                    type="password"
                    onChange={onFormFieldChange}
                    value={formValues.confirmPassword}
                    info={validationDetail}
                    tooltip={getFormTooltipDetail('confirmPassword')}
                  />
                ) : null}
              </FieldGroup>
            </>
          ) : null}
        </Card>
      </>
    );
  };

  const renderGroupAssignmentSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('ASSIGNMENT')}</section>
        <Card>
          <div className="is-flex full-width">
            <Field
              label="ASSIGN_GROUPS"
              containerClass="full-width"
              tooltip={getFormTooltipDetail('assignGroups')}
            >
              <DropDown
                list={groupsList}
                selectedList={selectedGroupOptions}
                onSelection={onGroupSelection}
                onOpen={onDropDownOpen}
                renderItemsSelection={(props) => (
                  <MultiSelection
                    unselectedTitle="Unselected User Groups"
                    selectedTitle="Selected User Groups"
                    {...props}
                  />
                )}
                isMulti
                hasSearch
                containerClass="full-width"
                loadMoreDetail={{ ...groupListTableDetail, onLoadMoreClick }}
                loading={isDropDownLoading}
              />
            </Field>
          </div>
        </Card>
      </>
    );
  };

  const renderAttributesSection = () => {
    if (selectedTab === TABS.ATTRIBUTES) {
      const onAttributeFormFieldChange = (evt) => {
        const updatedValue = getOnChangeValue(evt.target);

        for (const attrKey in updatedValue) {
          const attrValue = updatedValue[attrKey];

          if (attrValue === '') {
            setFormValues((prevState) => {
              const customAttrsInfo = { ...prevState.customAttrsInfo };

              delete customAttrsInfo[attrKey];

              setFormValues((prevState) => ({
                ...prevState,
                customAttrsInfo: { ...customAttrsInfo },
              }));
            });
          } else {
            setFormValues((prevState) => ({
              ...prevState,
              customAttrsInfo: { ...prevState.customAttrsInfo, ...updatedValue },
            }));
          }
        }

        setAttributeFormFields((prevState) => ({ ...prevState, ...updatedValue }));
      };

      const hasAttributes = customAttributeList.length > 0;

      return (
        <>
          <section className="text-upper-large">{t('INFORMATION')}</section>

          <div className="user-form-attribute-section">
            <Card>
              {hasAttributes && (
                <FieldGroup containerClass="has-jc-sb" containerStyle={{ flexWrap: 'wrap' }}>
                  {customAttributeList.map((detail) => (
                    <AttributeInput
                      key={detail.attrName}
                      detail={detail}
                      onChange={onAttributeFormFieldChange}
                      value={attributeFormFileds[detail.attrName]}
                      info={validationDetail}
                      containerStyle={{ minWidth: '240px', maxWidth: '240px', marginRight: '30px' }}
                    />
                  ))}
                </FieldGroup>
              )}

              <FieldGroup
                containerClass="has-jc-e"
                containerStyle={{ paddingTop: !hasAttributes ? '20px' : '' }}
              >
                <Button
                  type="secondary"
                  onClick={() => {
                    navigate('/admin/user-attributes?action=add');
                  }}
                >
                  {t('CREATE_NEW_ATTRIBUTES')}
                </Button>
              </FieldGroup>
            </Card>
          </div>
        </>
      );
    }

    return null;
  };

  const renderGeneralSection = () => {
    if (selectedTab === TABS.GENERAL) {
      return (
        <>
          {renderUserInformationSection()}
          {renderSecuritySettingsSection()}
          {renderGroupAssignmentSection()}
        </>
      );
    }

    return null;
  };

  const getAdditionalAttributeClassName = () => {
    const attributeValidationDetail = getCustomAttributeValidationDetail({
      customAttrsInfo: formValues.customAttrsInfo,
      attributeList,
    });

    if (
      !validationDetail.isValid &&
      validationDetail.context === attributeValidationDetail.context
    ) {
      return attributeValidationDetail.isValid ? '' : 'has-color-error';
    }

    return '';
  };

  return (
    <>
      <Tabs>
        <Tab
          label={TABS.GENERAL}
          isActive={selectedTab === TABS.GENERAL}
          containerClass={validationDetail.isValid ? '' : 'has-color-error'}
          onClick={() => {
            setSelectedTab(TABS.GENERAL);
          }}
        />

        <Tab
          label={TABS.ATTRIBUTES}
          isActive={selectedTab === TABS.ATTRIBUTES}
          containerClass={getAdditionalAttributeClassName()}
          onClick={() => {
            setSelectedTab(TABS.ATTRIBUTES);
          }}
        />
      </Tabs>

      {renderGeneralSection()}

      {renderAttributesSection()}
    </>
  );
};

UserForm.DEFAULT_PROPS = DEFAULT_PROPS;

UserForm.propTypes = defaultFormPropTypes;

export default UserForm;
