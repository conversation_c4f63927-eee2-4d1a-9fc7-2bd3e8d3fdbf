import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  label: '',
  links: [],
};

const SubMenu = ({ label, links }) => {
  const { t } = useTranslation();

  return (
    <div className="sub-menu-container">
      <div className="label-section">{t(label)}</div>

      <div className="links-container">
        {links?.map(({ linkTo, label }) => (
          <Link key={linkTo} to={linkTo} className="link">
            {t(label)}
          </Link>
        ))}
      </div>
    </div>
  );
};

SubMenu.DEFAULT_PROPS = DEFAULT_PROPS;

SubMenu.propTypes = {
  label: PropTypes.string,
  links: PropTypes.arrayOf(Object),
};

export default SubMenu;
