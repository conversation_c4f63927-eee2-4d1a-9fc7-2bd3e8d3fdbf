import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import SubMenu from './SubMenu';

const DEFAULT_PROPS = {
  linkTo: null,
  onClick: noop,
  icon: null,
  isIconImage: false,
  hasSmallIcon: false,
  iconClass: '',
  iconStyle: {},
  label: '',
  subMenu: [],
  containerClass: '',
  containerStyle: {},
};

const Menu = ({
  linkTo,
  onClick,
  icon,
  isIconImage,
  hasSmallIcon,
  iconClass,
  iconStyle,
  label,
  subMenu,
  containerClass,
  containerStyle,
}) => {
  const { t } = useTranslation();
  const location = useLocation();

  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (linkTo && linkTo !== '/') {
      const pathnameIndex = location.pathname.indexOf(linkTo);

      if (pathnameIndex === 0) {
        setIsActive(true);
      } else {
        setIsActive(false);
      }
    }
  }, [location]);

  const hasSubMenu = subMenu?.length > 0;

  return (
    <div
      className={`menu-container ${containerClass} ${isActive ? 'active' : ''}`}
      style={containerStyle}
    >
      <Link to={linkTo} onClick={onClick} className={`menu ${containerClass}`}>
        {isIconImage ? (
          <img
            src={icon}
            className={`icon ${hasSmallIcon ? 'small' : ''} ${iconClass}`}
            alt="nav image"
            style={iconStyle}
          />
        ) : (
          <FontAwesomeIcon
            icon={icon}
            className={`icon ${hasSmallIcon ? 'small' : ''} ${iconClass}`}
            style={iconStyle}
          />
        )}
        {label ? <span className="text-normal">{t(label)}</span> : null}
      </Link>

      {hasSubMenu && (
        <div className="sub-menu-list-container">
          {subMenu?.map((menuDetail) => (
            <SubMenu key={menuDetail.label} {...menuDetail} />
          ))}
        </div>
      )}
    </div>
  );
};

Menu.DEFAULT_PROPS = DEFAULT_PROPS;

Menu.propTypes = {
  linkTo: PropTypes.string,
  onClick: PropTypes.func,
  icon: PropTypes.any,
  isIconImage: PropTypes.bool,
  hasSmallIcon: PropTypes.bool,
  iconClass: PropTypes.string,
  iconStyle: PropTypes.object,
  label: PropTypes.string,
  subMenu: PropTypes.arrayOf(Object),
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default Menu;
