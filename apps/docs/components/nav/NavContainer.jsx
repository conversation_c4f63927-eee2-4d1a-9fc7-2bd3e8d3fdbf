import PropTypes from 'prop-types';

import Menu from './Menu';

const DEFAULT_PROPS = {
  navGroup: [],
};

const NavContainer = ({ navGroup }) => {
  return (
    <>
      {navGroup?.map((navs, idx) => (
        <div key={idx} className="nav-group">
          {navs?.map((nav, idx) => (
            <Menu key={`${idx}-${nav.linkTo}`} {...nav} />
          ))}
        </div>
      ))}
    </>
  );
};

NavContainer.DEFAULT_PROPS = DEFAULT_PROPS;

NavContainer.propTypes = {
  navGroup: PropTypes.arrayOf(Object),
};

export default NavContainer;
