import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button } from '../../../components/buttons';
import NoDataMessage from '../../../components/table/NoDataMessage';
import TableContainer from '../../../components/table/TableContainer';
import Actions from '../../../components/table/components/Actions';

import { getList } from '../../ducks/external-identities';
import {
  selectPrimaryIdpTableConfig,
  selectPrimaryIdpTableDetail,
  selectSecondaryIdpTableConfig,
  selectSecondaryIdpTableDetail,
} from '../../ducks/external-identities/selectors';

import useApiCall from '../../../hooks/useApiCall';
import EIStatus from './EIStatus';

const DEFAULT_PROPS = {
  isIdpPrimary: false,
  onAddClick: noop,
  onEditClick: noop,
  onDeleteClick: noop,
};

const IDPTable = ({ isIdpPrimary, onAddClick, onEditClick, onDeleteClick }) => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall({});

  useEffect(() => {
    apiCall(getList({ defaultIdp: isIdpPrimary })).catch(noop);
  }, [isIdpPrimary]);

  const tableConfig = useSelector(
    isIdpPrimary ? selectPrimaryIdpTableConfig : selectSecondaryIdpTableConfig,
  );

  const tableDetail = useSelector(
    isIdpPrimary ? selectPrimaryIdpTableDetail : selectSecondaryIdpTableDetail,
  );

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions {...props} onEditClick={onEditClick} onDeleteClick={onDeleteClick} />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'status') {
        columnDetail.cell = EIStatus;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  const renderNoDataSection = (props) => {
    if (isIdpPrimary) {
      return (
        <NoDataMessage {...props}>
          <p className="is-flex has-jc-c has-ai-c" style={{ padding: '1rem' }}>
            <span className="text-normal">{t('PRIMARY_IDP_NOT_AVAILABLE')}</span>
            &nbsp;
            <Button type="tertiary" containerClass="no-p-l" onClick={onAddClick}>
              {t('ADD_PRIMARY_IDP')}
            </Button>
          </p>
        </NoDataMessage>
      );
    }

    return (
      <NoDataMessage containerClass="cell-container" {...props}>
        <p className="is-flex has-jc-c has-ai-c">
          <span className="text-normal">{t('SECONDARY_IDP_NOT_AVAILABLE')}</span>
        </p>
      </NoDataMessage>
    );
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      renderNoDataSection={renderNoDataSection}
      containerClass={
        isIdpPrimary ? 'primary-idp-table-container' : 'secondary-idp-table-container'
      }
    />
  );
};

IDPTable.DEFAULT_PROPS = DEFAULT_PROPS;

IDPTable.propTypes = {
  isIdpPrimary: PropTypes.bool,
  onAddClick: PropTypes.func,
  onEditClick: PropTypes.func,
  onDeleteClick: PropTypes.func,
};

export default IDPTable;
