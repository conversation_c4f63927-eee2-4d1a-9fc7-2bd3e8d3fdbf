import { defaultValidationDetail } from '../../../components/forms/helper';

export const CONFIGURATION_MODE = {
  URL: 'URL',
  UPLOAD: 'UPLOAD',
  MANUAL: 'MANUAL',
};

export const CONFIGURATION_LIST = [
  { label: 'FETCH_WITH_URL', value: CONFIGURATION_MODE.URL },
  { label: 'UPLOAD_METADATA', value: CONFIGURATION_MODE.UPLOAD },
  { label: 'MANUAL_ENTRY', value: CONFIGURATION_MODE.MANUAL },
];

export const primaryModalModeDetail = {
  '': {},
  add: {
    headerText: 'ADD_PRIMARY_IDENTITY_PROVIDER',
  },
  edit: {
    headerText: 'EDIT_PRIMARY_IDENTITY_PROVIDER',
  },
  delete: {
    headerText: 'DELETE_PRIMARY_IDENTITY_PROVIDER',
    confirmationMessage:
      'Are you sure you want to delete this Identity Provider? The changes cannot be undone.',
  },
};

export const secondaryModalModeDetail = {
  '': {},
  add: {
    headerText: 'ADD_SECONDARY_IDENTITY_PROVIDER',
  },
  edit: {
    headerText: 'EDIT_SECONDARY_IDENTITY_PROVIDER',
  },
  delete: {
    headerText: 'DELETE_SECONDARY_IDENTITY_PROVIDER',
    confirmationMessage:
      'Are you sure you want to delete this Identity Provider? The changes cannot be undone.',
  },
};

export const getModalModeDetail = ({ defaultIdp }) => {
  if (defaultIdp) {
    return primaryModalModeDetail;
  }

  return secondaryModalModeDetail;
};

export const advancedSettingsModalModeDetail = {
  '': {},
  add: {
    headerText: 'IDP_ADVANCED_SETTINGS',
  },
  edit: {
    headerText: 'IDP_ADVANCED_SETTINGS',
  },
};

export const provisioningSettingsModalModeDetail = {
  '': {},
  add: {
    headerText: 'PROVISIONING_SETTINGS',
  },
  edit: {
    headerText: 'PROVISIONING_SETTINGS',
  },
};

export const jitAttributeModalModeDetail = {
  '': {},
  add: {
    headerText: 'JIT_ATTRIBUTE_MAPPING',
  },
  edit: {
    headerText: 'JIT_ATTRIBUTE_MAPPING',
  },
};

export const scimAttributeModalModeDetail = {
  '': {},
  add: {
    headerText: 'SCIM_ATTRIBUTE_MAPPING',
  },
  edit: {
    headerText: 'SCIM_ATTRIBUTE_MAPPING',
  },
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name, vendorName, domains, configInfo } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  if (!vendorName) {
    validationDetail.isValid = false;
    validationDetail.context = 'vendorName';
    validationDetail.type = 'error';
    validationDetail.message = 'Vendor Name is Required';

    return validationDetail;
  }

  if (domains?.length === 0) {
    validationDetail.isValid = false;
    validationDetail.context = 'domains';
    validationDetail.type = 'error';
    validationDetail.message = 'Domain is Required';

    return validationDetail;
  }

  if (!configInfo?.samlIdpEntityId) {
    validationDetail.isValid = false;
    validationDetail.context = 'samlIdpEntityId';
    validationDetail.type = 'error';
    validationDetail.message = (
      <p style={{ textTransform: 'initial' }}>SAML IDP Entity Id is Required</p>
    );

    return validationDetail;
  }

  if (!configInfo?.samlIdpSSOUrl) {
    validationDetail.isValid = false;
    validationDetail.context = 'samlIdpSSOUrl';
    validationDetail.type = 'error';
    validationDetail.message = (
      <p style={{ textTransform: 'initial' }}>SAML IDP SSO Url is Required</p>
    );

    return validationDetail;
  }

  return validationDetail;
};

export const getAdvancedSettingsFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { samlRequestSignEnabled, samlRequestSignAlgorithm } = formValues?.configInfo || {
    configInfo: {},
  };

  if (samlRequestSignEnabled && !samlRequestSignAlgorithm) {
    validationDetail.isValid = false;
    validationDetail.context = 'samlRequestSignAlgorithm';
    validationDetail.type = 'error';
    validationDetail.message = 'Signing Algorithm is Required';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the IdP`;
  }

  if (name === 'vendorName') {
    tooltipDetail.content = `Choose the IdP vendor`;
  }

  if (name === 'domains') {
    tooltipDetail.content = `Select the IdP domain. This allows the Zscaler service to display the correct IdP to authenticate an incoming user.`;
  }

  if (name === 'status') {
    tooltipDetail.content = `Enable or Disable the IdP`;
  }

  if (name === 'inputMethod') {
    tooltipDetail.content = `Select from the input methods listed to procure the information required for SAML configuration`;
  }

  if (name === 'idpMetadataUrl') {
    tooltipDetail.content = `Paste the link to the IdP's metadata file. You can find the link to the URL from the IdP.`;
  }

  if (name === 'idpMetadata') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Upload IdP Metadata</strong> to upload the metadata
        file of the IdP from your local machine
      </p>
    );
  }

  if (name === 'samlIdpEntityId') {
    tooltipDetail.content = `Enter the issuer URI of the IdP. This value is the entity ID in the IdP's metadata.`;
  }

  if (name === 'samlIdpSSOUrl') {
    tooltipDetail.content = `Enter the IdP's URL where the user is redirected for authentication`;
  }

  if (name === 'samlIdpCertificate') {
    tooltipDetail.content = `Upload the SAML certificate that is used to verify the digital signature of the IdP. This is the certificate you downloaded from your IdP. The certificate must be in base-64 encoded PEM format. The file extension must be .pem and have no other periods (.) in the file name.`;
  }

  if (name === 'samlRequestSignEnabled') {
    tooltipDetail.content = `Turn on the option to configure SAML request signing for user authentication`;
  }

  if (name === 'samlRequestSignAlgorithm') {
    tooltipDetail.content = `(Optional) Select the signing algorithm`;
  }

  if (name === 'spSAMLCertificate') {
    tooltipDetail.content = `Download the SP SAML certificate. You need to upload this certificate to the IdP.`;
  }

  if (name === 'samlAssertionEncEnabled') {
    tooltipDetail.content = `Turn on the option to configure encrypted SAML response for authentication.`;
  }

  if (name === 'samlEncryptionCertificate') {
    tooltipDetail.content = `Download the SAML encryption certificate. You need to upload this certificate to the IdP.`;
  }

  if (name === 'jitProvisionEnabled') {
    tooltipDetail.content = `Turn on this option for JIT provisioning`;
  }

  if (name === 'userGroupSamlAttribute') {
    tooltipDetail.content = `Enter the SAML user group attribute`;
  }

  if (name === 'samlAttribute') {
    tooltipDetail.content = `Enter the SAML attribute that you want to map`;
  }

  if (name === 'samlUserAttribute') {
    tooltipDetail.content = (
      <p>
        Select the user attribute that you want to map to the{' '}
        <strong className="tooltip-bold">SAML Attribute</strong>. Click{' '}
        <strong className="tooltip-bold">Add More</strong> to map more attributes.
      </p>
    );
  }

  if (name === 'scimProvisionEnabled') {
    tooltipDetail.content = `Turn on to activate SCIM-based provisioning for users`;
  }

  if (name === 'authenticationMethod') {
    tooltipDetail.content = `Select the authentication method`;
  }

  if (name === 'bearerToken') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Generate Token</strong> for a new bearer token. Copy
        the bearer token because you need it when configuring your IdP for SCIM provisioning. If
        you&apos;re generating a new bearer token for an existing SCIM configuration, ensure you
        update the token to your IdP.
      </p>
    );
  }

  if (name === 'scimAttribute') {
    tooltipDetail.content = `Enter the SCIM attribute that you want to map`;
  }

  if (name === 'scimUserAttribute') {
    tooltipDetail.content = (
      <p>
        Select the user attribute that you want to map to the{' '}
        <strong className="tooltip-bold">SCIM Attribute</strong>. Click{' '}
        <strong className="tooltip-bold">Add More</strong> to map more attributes.
      </p>
    );
  }

  return tooltipDetail;
};
