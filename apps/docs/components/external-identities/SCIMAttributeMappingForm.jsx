import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { remove } from 'lodash-es';

import { Button } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import { Field, Input } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import { getList } from '../../ducks/attributes';
import { selectAttributeList } from '../../ducks/attributes/selectors';

import useApiCall from '../../../hooks/useApiCall';
import { getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const defaultMapping = {
  mappedAttrName: '',
  user: [],
};

const SCIMAttributeMappingForm = ({ detail, onDetailChange }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const attributeList = useSelector(selectAttributeList);
  const [selectedAttributeMapping, setSelectedAtrributeMapping] = useState(() => {
    const attributes = [];

    const { scimUserAttrMappings } = detail;

    if (scimUserAttrMappings?.length > 0) {
      scimUserAttrMappings?.forEach(({ mappedAttrName, attrName }) => {
        attributes.push({
          mappedAttrName,
          user: [{ label: attrName, value: attrName }],
        });
      });

      return attributes;
    }

    return [{ ...defaultMapping }];
  });

  useEffect(() => {
    apiCall(getList(), { hasLoader: false });
  }, []);

  const updateFormValues = (newAttributeMapping) => {
    let scimUserAttrMappings = [];

    newAttributeMapping?.forEach(({ mappedAttrName, user }) => {
      if (mappedAttrName && user?.[0]?.value) {
        scimUserAttrMappings.push({
          mappedAttrName: mappedAttrName,
          attrName: user?.[0]?.value,
        });
      }
    });

    onDetailChange((prevState) => ({ ...prevState, scimUserAttrMappings }));
  };

  const getValue = (idx, type) => {
    return selectedAttributeMapping?.[idx]?.[type] || '';
  };

  const onChange = (type, idx) => (evt) => {
    const { value } = evt.target || {};

    const attributeMapping = selectedAttributeMapping[idx] || {};

    attributeMapping[type] = value;

    setSelectedAtrributeMapping((prevState) => {
      prevState[idx] = attributeMapping;

      updateFormValues(prevState);

      return [...prevState];
    });
  };

  const getSelectedUserAttribute = (idx) => {
    return selectedAttributeMapping?.[idx]?.user || [];
  };

  const onUserAttributeSelection = (detail, idx) => {
    const attributeMapping = selectedAttributeMapping[idx] || {};

    attributeMapping.user = detail;

    setSelectedAtrributeMapping((prevState) => {
      prevState[idx] = attributeMapping;

      updateFormValues(prevState);

      return [...prevState];
    });
  };

  const onDeleteMapping = (removeIdx) => {
    setSelectedAtrributeMapping((prevState) => {
      const newAttributeMappings = remove(prevState, (_, idx) => idx !== removeIdx);

      if (newAttributeMappings.length === 0) {
        newAttributeMappings.push([]);
      }

      updateFormValues(newAttributeMappings);

      return newAttributeMappings;
    });
  };

  const onAddMapping = () => {
    setSelectedAtrributeMapping((prevState) => {
      const lastAttributeDetail = prevState[prevState.length - 1];

      if (lastAttributeDetail.mappedAttrName && lastAttributeDetail?.user?.length > 0) {
        return [...prevState, { ...defaultMapping }];
      } else {
        return [...prevState];
      }
    });
  };

  const renderAttributeMappingSection = () => {
    const showDelete = (idx) => {
      return idx < selectedAttributeMapping.length - 1;
    };

    return (
      <>
        {selectedAttributeMapping.map((_, idx) => (
          <div
            key={idx}
            className="is-flex full-width attribute-mapping"
            style={{ marginBottom: '-16px' }}
          >
            <Input
              label="SCIM_ATTRIBUTE"
              value={getValue(idx, 'mappedAttrName')}
              name={`mappedAttrName-${idx}`}
              onChange={onChange('mappedAttrName', idx)}
              tooltip={getFormTooltipDetail('scimAttribute')}
            />

            <Field
              label="USER_ATTRIBUTE"
              containerClass="user-attribute-section"
              tooltip={getFormTooltipDetail('scimUserAttribute')}
            >
              <DropDown
                list={attributeList}
                selectedList={getSelectedUserAttribute(idx)}
                onSelection={(detail) => onUserAttributeSelection(detail, idx)}
                containerClass="full-width"
              />
            </Field>

            {showDelete(idx) && (
              <Button
                type="tertiary"
                containerClass="content-width no-p-r"
                containerStyle={{ paddingTop: '28px' }}
                onClick={() => onDeleteMapping(idx)}
              >
                <FontAwesomeIcon icon={faTrash} />
              </Button>
            )}
          </div>
        ))}

        <div className="is-flex has-jc-e">
          <Button type="tertiary" containerClass="no-p-r" onClick={onAddMapping}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            {t('ADD_MORE')}
          </Button>
        </div>
      </>
    );
  };

  return (
    <>
      <section className="text-upper-large">{t('SCIM_ATTRIBUTE_MAPPING')}</section>

      <Card>{renderAttributeMappingSection()}</Card>
    </>
  );
};

SCIMAttributeMappingForm.DEFAULT_PROPS = DEFAULT_PROPS;

SCIMAttributeMappingForm.propTypes = defaultFormPropTypes;

export default SCIMAttributeMappingForm;
