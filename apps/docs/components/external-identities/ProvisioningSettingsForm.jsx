import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Button, ToggleButton } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import { Field, FieldGroup, PasswordInput } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import { getBearerToken } from '../../ducks/external-identities';
import { selectAuthenticationMethodsList } from '../../ducks/external-identities/selectors';

import useApiCall from '../../../hooks/useApiCall';
import SAMLAttributeMappingForm from './SAMLAttributeMappingForm';
import SCIMAttributeMappingForm from './SCIMAttributeMappingForm';
import { getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const ProvisioningSettingsForm = ({ mode, detail, onDetailChange }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const isEditMode = mode === 'edit';

  const { jitProvisionEnabled, scimProvisionEnabled, endPointURL, bearerToken } =
    detail?.configInfo || {};

  const authenticationMethodsList = useSelector(selectAuthenticationMethodsList);

  const onJITProvisionToggle = () => {
    onDetailChange((prevState) => {
      const configInfo = {
        ...prevState.configInfo,
        jitProvisionEnabled: !prevState.configInfo.jitProvisionEnabled,
      };

      return { ...prevState, configInfo };
    });
  };

  const onSCIMProvisionToggle = () => {
    onDetailChange((prevState) => {
      const configInfo = {
        ...prevState.configInfo,
        scimProvisionEnabled: !prevState.configInfo.scimProvisionEnabled,
      };

      return { ...prevState, configInfo };
    });
  };

  const onGenerateTokenClick = () => {
    apiCall(getBearerToken()).then((bearerToken) => {
      if (bearerToken) {
        onDetailChange((prevState) => {
          const configInfo = {
            ...prevState.configInfo,
            bearerToken,
          };

          return { ...prevState, configInfo };
        });
      }
    });
  };

  const renderJITSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('JIT_PROVISIONING')}</section>

        <Card>
          <Field
            label="ENABLE_JIT_PROVISIONING"
            tooltip={getFormTooltipDetail('jitProvisionEnabled')}
          >
            <ToggleButton
              type="success"
              isOn={jitProvisionEnabled}
              onToggleClick={onJITProvisionToggle}
            />
          </Field>
        </Card>
      </>
    );
  };

  const renderSAMLAttributeMappingSection = () => {
    return <SAMLAttributeMappingForm onDetailChange={onDetailChange} detail={detail} />;
  };

  const renderSCIMSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('SCIM_PROVISIONING')}</section>

        <Card>
          <FieldGroup containerClass="has-jc-s has-ai-c">
            <Field
              label="ENABLE_SCIM_PROVISIONING"
              containerStyle={{ width: '45%' }}
              tooltip={getFormTooltipDetail('scimProvisionEnabled')}
            >
              <ToggleButton
                type="success"
                isOn={scimProvisionEnabled}
                onToggleClick={onSCIMProvisionToggle}
              />
            </Field>

            {isEditMode && (
              <>
                <PasswordInput
                  type="text"
                  label="SCIM_ENDPOINT_URL"
                  value={endPointURL}
                  showInputTypeToggle={false}
                  canCopy
                  showCopiedPassword={true}
                />
              </>
            )}
          </FieldGroup>

          <FieldGroup containerClass="has-jc-sb has-ai-c">
            <Field
              label="AUTHENTICATION_METHOD"
              tooltip={getFormTooltipDetail('authenticationMethod')}
            >
              <DropDown list={authenticationMethodsList} selectedList={authenticationMethodsList} />
            </Field>
          </FieldGroup>

          <FieldGroup containerClass="has-jc-sb has-ai-c">
            <PasswordInput
              label="TOKEN"
              value={bearerToken}
              showInputTypeToggle={false}
              canCopy
              tooltip={getFormTooltipDetail('bearerToken')}
              showCopiedPassword={true}
            />

            <Button
              type="secondary"
              onClick={onGenerateTokenClick}
              containerStyle={{ width: '200px', marginTop: '16px' }}
            >
              {t('GENERATE_TOKEN')}
            </Button>
          </FieldGroup>
        </Card>
      </>
    );
  };

  const renderSCIMAttributeMappingSection = () => {
    return <SCIMAttributeMappingForm onDetailChange={onDetailChange} detail={detail} />;
  };

  return (
    <>
      {renderJITSection()}
      {renderSAMLAttributeMappingSection()}

      {renderSCIMSection()}
      {renderSCIMAttributeMappingSection()}
    </>
  );
};

ProvisioningSettingsForm.DEFAULT_PROPS = DEFAULT_PROPS;

ProvisioningSettingsForm.propTypes = defaultFormPropTypes;

export default ProvisioningSettingsForm;
