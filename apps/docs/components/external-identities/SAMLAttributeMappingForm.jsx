import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { remove } from 'lodash-es';

import { Button } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import { Field, Input } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import { getList } from '../../ducks/attributes';
import { selectAttributeList } from '../../ducks/attributes/selectors';

import { mergeFormValues } from '../../../utils/dom';

import useApiCall from '../../../hooks/useApiCall';
import { getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const defaultMapping = {
  mappedAttrName: '',
  user: [],
};

const SAMLAttributeMappingForm = ({ detail, onDetailChange }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const attributeList = useSelector(selectAttributeList);
  const [selectedAttributeMapping, setSelectedAtrributeMapping] = useState(() => {
    const attributes = [];

    const { samlJitUserAttrMappings } = detail;

    if (samlJitUserAttrMappings?.length > 0) {
      samlJitUserAttrMappings?.forEach(({ mappedAttrName, attrName }) => {
        attributes.push({
          attrName,
          user: [{ label: mappedAttrName, value: mappedAttrName }],
        });
      });

      return attributes;
    }

    return [{ ...defaultMapping }];
  });

  useEffect(() => {
    apiCall(getList(), { hasLoader: false });
  }, []);

  const updateFormValues = (newAttributeMapping) => {
    let samlJitUserAttrMappings = [];

    newAttributeMapping?.forEach(({ attrName, user }) => {
      if (attrName && user?.[0]?.value) {
        samlJitUserAttrMappings.push({
          mappedAttrName: user?.[0]?.value,
          attrName,
        });
      }
    });

    onDetailChange((prevState) => ({ ...prevState, samlJitUserAttrMappings }));
  };

  const getValue = (idx, type) => {
    return selectedAttributeMapping?.[idx]?.[type] || '';
  };

  const onChange = (evt, type, idx) => {
    const { value } = evt.target || {};

    const attributeMapping = selectedAttributeMapping[idx] || {};

    attributeMapping[type] = value;

    setSelectedAtrributeMapping((prevState) => {
      prevState[idx] = attributeMapping;

      updateFormValues(prevState);

      return [...prevState];
    });
  };

  const getSelectedUserAttribute = (idx) => {
    return selectedAttributeMapping?.[idx]?.user || [];
  };

  const onUserAttributeSelection = (detail, idx) => {
    const attributeMapping = selectedAttributeMapping[idx] || {};

    attributeMapping.user = detail;

    setSelectedAtrributeMapping((prevState) => {
      prevState[idx] = attributeMapping;

      updateFormValues(prevState);

      return [...prevState];
    });
  };

  const onDeleteMapping = (removeIdx) => {
    setSelectedAtrributeMapping((prevState) => {
      const newAttributeMappings = remove(prevState, (_, idx) => idx !== removeIdx);

      if (newAttributeMappings.length === 0) {
        newAttributeMappings.push([]);
      }

      updateFormValues(newAttributeMappings);

      return newAttributeMappings;
    });
  };

  const onAddMapping = () => {
    setSelectedAtrributeMapping((prevState) => {
      const lastAttributeDetail = prevState[prevState.length - 1];

      if (lastAttributeDetail.attrName && lastAttributeDetail?.user?.length > 0) {
        return [...prevState, { ...defaultMapping }];
      } else {
        return [...prevState];
      }
    });
  };

  const renderAttributeMappingSection = () => {
    const showDelete = (idx) => {
      return idx < selectedAttributeMapping.length - 1;
    };

    return (
      <>
        {selectedAttributeMapping.map((_, idx) => (
          <div
            key={idx}
            className="is-flex full-width attribute-mapping"
            style={{ marginBottom: '-16px' }}
          >
            <Input
              label="SAML_ATTRIBUTE"
              value={getValue(idx, 'attrName')}
              name={`attrName-${idx}`}
              onChange={(evt) => onChange(evt, 'attrName', idx)}
              tooltip={getFormTooltipDetail('samlAttribute')}
            />

            <Field
              label="USER_ATTRIBUTE"
              containerClass="user-attribute-section"
              tooltip={getFormTooltipDetail('samlUserAttribute')}
            >
              <DropDown
                list={attributeList}
                selectedList={getSelectedUserAttribute(idx)}
                onSelection={(detail) => onUserAttributeSelection(detail, idx)}
                containerClass="full-width"
              />
            </Field>

            {showDelete(idx) && (
              <Button
                type="tertiary"
                containerClass="content-width no-p-r"
                containerStyle={{ paddingTop: '28px' }}
                onClick={() => onDeleteMapping(idx)}
              >
                <FontAwesomeIcon icon={faTrash} />
              </Button>
            )}
          </div>
        ))}

        <div className="is-flex has-jc-e">
          <Button type="tertiary" containerClass="no-p-r" onClick={onAddMapping}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            {t('ADD_MORE')}
          </Button>
        </div>
      </>
    );
  };

  const onSamlGroupAttrNameChange = (evt) => {
    onDetailChange(mergeFormValues(evt));
  };

  return (
    <>
      <section className="text-upper-large">{t('SAML_ATTRIBUTE_MAPPING')}</section>

      <Card>
        <Input
          name="samlGroupAttrName"
          label="USER_GROUP_SAML_ATTRIBUTE"
          value={detail.samlGroupAttrName}
          onChange={onSamlGroupAttrNameChange}
          containerStyle={{ maxWidth: '50%' }}
          tooltip={getFormTooltipDetail('userGroupSamlAttribute')}
        />

        {renderAttributeMappingSection()}
      </Card>
    </>
  );
};

SAMLAttributeMappingForm.DEFAULT_PROPS = DEFAULT_PROPS;

SAMLAttributeMappingForm.propTypes = defaultFormPropTypes;

export default SAMLAttributeMappingForm;
