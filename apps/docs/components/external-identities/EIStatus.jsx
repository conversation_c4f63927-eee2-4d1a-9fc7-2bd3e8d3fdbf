import { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { faCheckCircle, faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons';

import PropTypes from 'prop-types';

import { Card } from '../../../components/cards';
import { StatusTag } from '../../../components/tags';
import { Tooltip } from '../../../components/tooltip';

const DEFAULT_PROPS = {
  row: {
    original: {},
  },
};

const EIStatus = ({ row }) => {
  const { t } = useTranslation();

  const elementRef = useRef();

  const { original: { alertMessage = {}, status } = {} } = row;

  const { code, detailed } = alertMessage;

  const canShowTooltip = code === 'IDP_CERTIFICATE_EXPIRED';

  const renderTooltipSection = () => {
    return (
      <Card>
        <span className="has-color-error">{t(detailed)}</span>
      </Card>
    );
  };

  const getTruthyIcon = () => {
    if (code === 'IDP_CERTIFICATE_EXPIRED') {
      return faExclamationTriangle;
    } else {
      return faCheckCircle;
    }
  };

  const getTruthyIconClass = () => {
    if (code === 'IDP_CERTIFICATE_EXPIRED') {
      return 'has-color-warning';
    } else {
      return 'has-color-success';
    }
  };

  return (
    <div ref={elementRef}>
      <StatusTag
        type="ENABLED_DISABLED"
        truthyIcon={getTruthyIcon()}
        truthyIconClass={getTruthyIconClass()}
        value={status}
      />
      {canShowTooltip && <Tooltip elementRef={elementRef}>{renderTooltipSection()}</Tooltip>}
    </div>
  );
};

EIStatus.DEFAULT_PROPS = DEFAULT_PROPS;

EIStatus.propTypes = {
  row: PropTypes.object,
};

export default EIStatus;
