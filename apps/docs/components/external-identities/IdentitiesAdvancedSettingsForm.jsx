import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';

import { ToggleButton } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import DownLoadFile from '../../../components/file/DownloadFile';
import { Field, FieldGroup } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';

import {
  downloadSPCertificate,
  getSPCertificates,
  getSigningAlgorithms,
} from '../../ducks/external-identities';
import {
  selectEncryptionCertificateList,
  selectSPCertificateTypes,
  selectSigningAlgorithmList,
  selectSigningCertificateList,
} from '../../ducks/external-identities/selectors';

import useApiCall from '../../../hooks/useApiCall';
import { getAdvancedSettingsFormValidationDetail, getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = defaultFormProps;

const IdentitiesAdvancedSettingsForm = ({ detail, onDetailChange, mode, setValidationDetail }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { samlAssertionEncEnabled, samlRequestSignEnabled, samlRequestSignAlgorithm } =
    detail?.configInfo || {};

  const signingAlgorithmList = useSelector(selectSigningAlgorithmList);
  const [selectedAlgorithm, setSelectedAlgorithm] = useState(() => {
    if (samlRequestSignAlgorithm) {
      return [{ label: samlRequestSignAlgorithm, value: samlRequestSignAlgorithm }];
    }

    return [];
  });

  const spCertificateTypes = useSelector(selectSPCertificateTypes);

  const signingCertificateList = useSelector(selectSigningCertificateList);

  const encryptionCertificateList = useSelector(selectEncryptionCertificateList);

  useEffect(() => {
    apiCall(getSigningAlgorithms(), { hasLoader: false });
    apiCall(getSPCertificates(spCertificateTypes.SIGNING), { hasLoader: false });
    apiCall(getSPCertificates(spCertificateTypes.ENCRYPTION), { hasLoader: false });
  }, []);

  useEffect(() => {
    const formValidationDetail = getAdvancedSettingsFormValidationDetail({
      formValues: detail,
      mode,
    });

    setValidationDetail(formValidationDetail);
  }, [detail]);

  const onSigningToggle = () => {
    const samlRequestSignCertificate = {
      id: signingCertificateList?.[0]?.value,
    };

    onDetailChange((prevState) => {
      const configInfo = {
        ...prevState.configInfo,
        samlRequestSignEnabled: !prevState.configInfo.samlRequestSignEnabled,
        samlRequestSignCertificate,
      };

      return { ...prevState, configInfo };
    });
  };

  const onAssertionToggle = () => {
    const samlAssertionEncCertificate = {
      id: signingCertificateList?.[0]?.value,
    };

    onDetailChange((prevState) => {
      const configInfo = {
        ...prevState.configInfo,
        samlAssertionEncEnabled: !prevState.configInfo.samlAssertionEncEnabled,
        samlAssertionEncCertificate,
      };

      return { ...prevState, configInfo };
    });
  };

  const onAlgorithmSelection = (detail) => {
    setSelectedAlgorithm(detail);
    const samlRequestSignAlgorithm = detail?.[0]?.value;

    if (samlRequestSignAlgorithm) {
      onDetailChange((prevState) => {
        const configInfo = {
          ...prevState.configInfo,
          samlRequestSignAlgorithm,
        };

        return { ...prevState, configInfo };
      });
    }
  };

  const onDownloadSiginingCertificate = async () => {
    const value = signingCertificateList?.[0]?.value;

    if (value) {
      return await apiCall(downloadSPCertificate({ id: value })).catch(noop);
    }

    return null;
  };

  const onDownloadEncryptionCertificate = async () => {
    const value = encryptionCertificateList?.[0]?.value;

    if (value) {
      return await apiCall(downloadSPCertificate({ id: value })).catch(noop);
    }

    return null;
  };

  const renderRequestSigningSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('SAML_REQUEST_SIGNING')}</section>

        <Card>
          <FieldGroup>
            <Field
              label="ENABLE_SAML_REQUEST_SIGNING"
              tooltip={getFormTooltipDetail('samlRequestSignEnabled')}
            >
              <ToggleButton
                type="success"
                isOn={samlRequestSignEnabled}
                onToggleClick={onSigningToggle}
              />
            </Field>
          </FieldGroup>

          {samlRequestSignEnabled && (
            <FieldGroup>
              <Field
                label="SIGNING_ALGORITHM"
                tooltip={getFormTooltipDetail('samlRequestSignAlgorithm')}
              >
                <DropDown
                  list={signingAlgorithmList}
                  selectedList={selectedAlgorithm}
                  onSelection={onAlgorithmSelection}
                />
              </Field>

              {samlRequestSignEnabled && (
                <Field
                  label="SP_SAML_CERTIFICATE"
                  tooltip={getFormTooltipDetail('spSAMLCertificate')}
                >
                  <DownLoadFile
                    variantType="iconWithText"
                    label="DOWNLOAD_CERTIFICATE"
                    onDownloadClick={onDownloadSiginingCertificate}
                  />
                </Field>
              )}
            </FieldGroup>
          )}
        </Card>
      </>
    );
  };

  const renderEncryptedResponseSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('ENCRYPTED_SAML_RESPONSE')}</section>

        <Card>
          <FieldGroup>
            <Field
              label="ENABLE_ENCRYPTED_SAML_ASSERTION"
              tooltip={getFormTooltipDetail('samlAssertionEncEnabled')}
            >
              <ToggleButton
                type="success"
                isOn={samlAssertionEncEnabled}
                onToggleClick={onAssertionToggle}
              />
            </Field>

            {samlAssertionEncEnabled && (
              <Field
                label="SAML_ENCRYPTION_CERTIFICATE"
                tooltip={getFormTooltipDetail('samlEncryptionCertificate')}
              >
                <DownLoadFile
                  variantType="iconWithText"
                  label="DOWNLOAD_CERTIFICATE"
                  onDownloadClick={onDownloadEncryptionCertificate}
                />
              </Field>
            )}
          </FieldGroup>
        </Card>
      </>
    );
  };

  return (
    <>
      {renderRequestSigningSection()}
      {renderEncryptedResponseSection()}
    </>
  );
};

IdentitiesAdvancedSettingsForm.DEFAULT_PROPS = DEFAULT_PROPS;

IdentitiesAdvancedSettingsForm.propTypes = defaultFormPropTypes;

export default IdentitiesAdvancedSettingsForm;
