import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import PropTypes from 'prop-types';

import { Label } from '../../../components/forms';
import { defaultFormPropTypes, defaultFormProps } from '../../../components/forms/helper';
import Tab from '../../../components/tabs/Tab';
import Tabs from '../../../components/tabs/Tabs';

import { selectNewIdpDetail } from '../../ducks/external-identities/selectors';

import { mergeFormValues } from '../../../utils/dom';

import GeneralSectionForm from './GeneralSectionForm';
import IdentitiesAdvancedSettingsForm from './IdentitiesAdvancedSettingsForm';
import ProvisioningForm from './ProvisioningForm';
import SAMLConfigurationForm from './SAMLConfigurationForm';
import { CONFIGURATION_MODE, getFormValidationDetail } from './helper';

const TABS = {
  BASIC: 'BASIC',
  ADVANCED: 'ADVANCED',
  PROVISIONING: 'PROVISIONING',
};

const DEFAULT_PROPS = defaultFormProps;

const defaultFormValues = {
  name: '',
  status: false,
  vendorName: '',
  domains: [],
  protocol: 'SAML2',
  samlJitUserAttrMappings: [],
  scimUserAttrMappings: [],
  isIdpPrimary: false,
  configInfo: {
    jitProvisionEnabled: false,
    scimProvisionEnabled: false,
    samlIdpEntityId: '',
    samlIdpSSOUrl: '',
    samlIdpCertificate: {},
    samlAssertionEncEnabled: false,
    samlAssertionEncCertificate: {},
    samlRequestSignEnabled: false,
    samlRequestSignCertificate: {},
    samlRequestSignAlgorithm: 'NONE',
    samlIdpMetadataUrl: '',
    samlSpEntityId: '',
    samlSpAcsUrl: '',
  },
};

const ExternalIdentitiesForm = ({
  onDetailChange,
  detail,
  mode,
  validationDetail,
  setValidationDetail,
  isIdpPrimary,
}) => {
  const { t } = useTranslation();

  const newIdpDetail = useSelector(selectNewIdpDetail);

  const isEditMode = mode === 'edit';
  const isAddMode = mode === 'add';

  const [formValues, setFormValues] = useState({ ...defaultFormValues, ...detail });

  const [activeConfigurationMode, setActiveConfigurationMode] = useState('URL');
  const [isActionChoosen, setIsActionChoosen] = useState(false);

  const [selectedTab, setSelectedTab] = useState(TABS.BASIC);

  useEffect(() => {
    if (isAddMode) {
      const { id, configInfo = { samlSpAcsUrl: '', samlSpEntityId: '' } } = newIdpDetail;

      setFormValues((prevState) => ({
        ...prevState,
        id,
        configInfo: {
          ...prevState.configInfo,
          samlSpAcsUrl: configInfo.samlSpAcsUrl,
          samlSpEntityId: configInfo.samlSpEntityId,
        },
      }));
    }
  }, [isAddMode, newIdpDetail]);

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    onDetailChange(formValues);

    const formValidationDetail = getFormValidationDetail({ formValues, mode });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  useEffect(() => {
    if (isEditMode) {
      setIsActionChoosen(true);
    } else {
      if (activeConfigurationMode === CONFIGURATION_MODE.MANUAL) {
        setIsActionChoosen(true);
      } else {
        setIsActionChoosen(false);
      }
    }

    setFormValues(formValues);
  }, [activeConfigurationMode]);

  const renderGeneralSection = () => {
    return (
      <>
        <section className="text-upper-large">{t('GENERAL')}</section>

        <GeneralSectionForm
          isEditMode={isEditMode}
          formValues={formValues}
          validationDetail={validationDetail}
          onFormFieldChange={onFormFieldChange}
          setFormValues={setFormValues}
          isIdpPrimary={isIdpPrimary}
        />
      </>
    );
  };

  const renderSAMLConfigurationSection = () => {
    const isConditionsValid =
      validationDetail.context === 'samlIdpEntityId' ||
      validationDetail.context === 'samlIdpSSOUrl';

    return (
      <>
        <section className="text-upper-large">
          <Label
            text="SAML_CONFIGURATION"
            info={isConditionsValid ? validationDetail : {}}
            containerStyle={{ marginBottom: '0' }}
          />
        </section>

        <SAMLConfigurationForm
          isEditMode={isEditMode}
          formValues={formValues}
          onFormFieldChange={onFormFieldChange}
          setFormValues={setFormValues}
          isActionChoosen={isActionChoosen}
          setIsActionChoosen={setIsActionChoosen}
          activeConfigurationMode={activeConfigurationMode}
          setActiveConfigurationMode={setActiveConfigurationMode}
        />
      </>
    );
  };

  const renderBasicSection = () => {
    if (selectedTab === TABS.BASIC) {
      return (
        <>
          {renderGeneralSection()}

          {renderSAMLConfigurationSection()}
        </>
      );
    }

    return null;
  };

  const renderAdvancedSection = () => {
    if (selectedTab === TABS.ADVANCED) {
      return (
        <>
          <IdentitiesAdvancedSettingsForm
            detail={formValues}
            onDetailChange={setFormValues}
            mode={mode}
          />
        </>
      );
    }

    return null;
  };

  const renderProvisioningSection = () => {
    if (selectedTab === TABS.PROVISIONING) {
      return (
        <>
          <section className="text-upper-large">{t('STATUS')}</section>

          <ProvisioningForm
            isEditMode={isEditMode}
            formValues={formValues}
            onFormFieldChange={onFormFieldChange}
            setFormValues={setFormValues}
          />
        </>
      );
    }

    return null;
  };

  return (
    <>
      <Tabs>
        <Tab
          label={TABS.BASIC}
          isActive={selectedTab === TABS.BASIC}
          onClick={() => {
            setSelectedTab(TABS.BASIC);
          }}
        />

        <Tab
          label={TABS.ADVANCED}
          isActive={selectedTab === TABS.ADVANCED}
          onClick={() => {
            setSelectedTab(TABS.ADVANCED);
          }}
        />

        <Tab
          label={TABS.PROVISIONING}
          isActive={selectedTab === TABS.PROVISIONING}
          onClick={() => {
            setSelectedTab(TABS.PROVISIONING);
          }}
        />
      </Tabs>

      {renderBasicSection()}

      {renderAdvancedSection()}

      {renderProvisioningSection()}
    </>
  );
};

ExternalIdentitiesForm.DEFAULT_PROPS = DEFAULT_PROPS;

ExternalIdentitiesForm.propTypes = { ...defaultFormPropTypes, isIdpPrimary: PropTypes.bool };

export default ExternalIdentitiesForm;
