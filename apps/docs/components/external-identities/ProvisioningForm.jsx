import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../../components/cards';
import { FieldGroup } from '../../../components/forms';

import ProvisioningSettingsForm from './ProvisioningSettingsForm';

const DEFAULT_PROPS = {
  isEditMode: false,
  formValues: {},
  onFormFieldChange: noop,
  setFormValues: noop,
};

const ProvisioningForm = ({ isEditMode, formValues, setFormValues }) => {
  const { t } = useTranslation();

  const { scimProvisionEnabled } = formValues?.configInfo || {};

  return (
    <>
      <Card>
        <FieldGroup containerClass="has-jc-sb has-ai-c provisioning-form-container">
          <div className="is-flex has-fd-c">
            <div className="is-flex has-ai-c">
              <span> {t('SCIM_PROVISIONING_STATUS')} </span>
            </div>
            <div className={`status-container ${scimProvisionEnabled ? 'enabled' : 'disabled'}`}>
              {scimProvisionEnabled ? t('ENABLED') : t('DISABLED')}
            </div>
          </div>
        </FieldGroup>
      </Card>

      <ProvisioningSettingsForm
        onDetailChange={setFormValues}
        detail={formValues}
        mode={isEditMode ? 'edit' : 'add'}
      />
    </>
  );
};

ProvisioningForm.DEFAULT_PROPS = DEFAULT_PROPS;

ProvisioningForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
};

export default ProvisioningForm;
