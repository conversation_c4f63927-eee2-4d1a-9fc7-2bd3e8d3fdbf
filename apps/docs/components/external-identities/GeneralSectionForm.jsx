import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { filter, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { ToggleButton } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import MultiSelection from '../../../components/dropdowns/MultiSelection';
import { Field, FieldGroup, Input } from '../../../components/forms';

import { getVendorList } from '../../ducks/external-identities';
import { selectVendorNameList } from '../../ducks/external-identities/selectors';
import { getList } from '../../ducks/tenant-domains';
import { selectDomainNameList } from '../../ducks/tenant-domains/selectors';

import useApiCall from '../../../hooks/useApiCall';
import { getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = {
  isEditMode: false,
  isIdpPrimary: false,
  formValues: {},
  validationDetail: {},
  onFormFieldChange: noop,
  setFormValues: noop,
};

const GeneralSectionForm = ({
  isEditMode,
  formValues,
  validationDetail,
  onFormFieldChange,
  setFormValues,
  isIdpPrimary,
}) => {
  const { apiCall } = useApiCall();

  const vendorNameList = useSelector(selectVendorNameList);
  const [selectedVendorName, setSelectedVendorName] = useState(() => {
    if (formValues.vendorName) {
      return [{ label: formValues.vendorName, value: formValues.vendorName }];
    }

    return [];
  });

  const domainNameList = useSelector(selectDomainNameList);

  const getDomainNameList = () => {
    return filter(domainNameList, (domain) => {
      const { idp } = domain;

      if (isEditMode) {
        if (idp && idp?.id !== formValues.id) {
          return false;
        }
      }

      if (!isEditMode && idp?.id) {
        return false;
      }

      return true;
    });
  };

  const [selectedDomainName, setSelectedDomainName] = useState(
    formValues.domains.map(({ name, id }) => ({ label: name, value: id })),
  );

  useEffect(() => {
    apiCall(getVendorList(), { hasLoader: false }).catch(noop);
    apiCall(getList(), { hasLoader: false }).catch(noop);
  }, []);

  const onVendorSelection = (detail) => {
    setSelectedVendorName(detail);
    setFormValues((prevState) => ({ ...prevState, vendorName: detail?.[0]?.value || '' }));
  };

  const onDomainSelection = (detail) => {
    setSelectedDomainName(detail);

    setFormValues((prevState) => ({
      ...prevState,
      domains: detail.map(({ label, value }) => ({ name: label, id: value })),
    }));
  };

  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      status: !formValues?.status,
    }));
  };

  return (
    <Card>
      <FieldGroup>
        <Input
          label="NAME"
          name="name"
          onChange={onFormFieldChange}
          value={formValues.name}
          info={validationDetail}
          tooltip={getFormTooltipDetail('name')}
        />

        <Field
          label="IDENTITY_VENDOR"
          htmlFor="vendorName"
          info={validationDetail}
          tooltip={getFormTooltipDetail('vendorName')}
        >
          <DropDown
            list={vendorNameList}
            selectedList={selectedVendorName}
            onSelection={onVendorSelection}
            containerClass="full-width"
          />
        </Field>
      </FieldGroup>

      <FieldGroup>
        {!isIdpPrimary && (
          <Field
            label="DOMAIN"
            htmlFor="domains"
            info={validationDetail}
            tooltip={getFormTooltipDetail('domains')}
          >
            <DropDown
              list={getDomainNameList()}
              selectedList={selectedDomainName}
              onSelection={onDomainSelection}
              containerClass="full-width"
              renderItemsSelection={(props) => (
                <MultiSelection
                  unselectedTitle="Unselected Domain"
                  selectedTitle="Selected Domain"
                  {...props}
                />
              )}
              hasSearch
              isMulti
            />
          </Field>
        )}

        <Input
          label="PROTOCOL"
          name="protocol"
          onChange={onFormFieldChange}
          value={formValues.protocol}
          readOnly
          disabled
        />
      </FieldGroup>

      <FieldGroup>
        <Field label="STATUS" tooltip={getFormTooltipDetail('status')}>
          <ToggleButton
            type="success"
            isOn={formValues?.status}
            onToggleClick={onToggleStatusClick}
            onLabel="ENABLED"
            offLabel="DISABLED"
          />
        </Field>
      </FieldGroup>
    </Card>
  );
};

GeneralSectionForm.DEFAULT_PROPS = DEFAULT_PROPS;

GeneralSectionForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  validationDetail: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
  isIdpPrimary: PropTypes.bool,
};

export default GeneralSectionForm;
