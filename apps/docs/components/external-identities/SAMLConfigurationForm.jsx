import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faUpload } from '@fortawesome/pro-regular-svg-icons';

import { cloneDeep, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button, RadioButtons } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DownLoadFile from '../../../components/file/DownloadFile';
import InputFile from '../../../components/file/InputFile';
import { Field, FieldGroup, Input } from '../../../components/forms';
import { TextWithTooltip } from '../../../components/tooltip';

import {
  downloadMetadata,
  getMetaData,
  uploadIDPCert,
  uploadMetadata,
} from '../../ducks/external-identities';
import {
  selectIdPCertificate,
  selectSAMLMetadata,
} from '../../ducks/external-identities/selectors';

import { getOnChangeValue } from '../../../utils/dom';

import useApiCall from '../../../hooks/useApiCall';
import { CONFIGURATION_LIST, CONFIGURATION_MODE, getFormTooltipDetail } from './helper';

const DEFAULT_PROPS = {
  isEditMode: false,
  formValues: {},
  setFormValues: noop,
  isActionChoosen: false,
  setIsActionChoosen: noop,
  activeConfigurationMode: '',
  setActiveConfigurationMode: noop,
};

// https://samltest.id/saml/idp
const SAMLConfigurationForm = ({
  isEditMode,
  formValues,
  setFormValues,
  isActionChoosen,
  setIsActionChoosen,
  activeConfigurationMode,
  setActiveConfigurationMode,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { samlIdpCertificate, samlIdpEntityId, samlIdpSSOUrl, samlSpEntityId, samlSpAcsUrl } =
    formValues.configInfo || {};

  const samlMetadata = useSelector(selectSAMLMetadata);

  const idpCertificate = useSelector(selectIdPCertificate);

  const isManualMode = activeConfigurationMode === CONFIGURATION_MODE.MANUAL;

  const isRadioButtonSelected = (detail = {}) => {
    const { value } = detail;

    return value === activeConfigurationMode;
  };

  const onRadioButtonsClick = (detail = {}) => {
    const { value } = detail;

    if (value) {
      setActiveConfigurationMode(value);
    }
  };

  useEffect(() => {
    const { samlIdpCertificate, samlIdpEntityId, samlIdpSSOUrl } = samlMetadata;

    if (samlIdpCertificate && samlIdpEntityId && samlIdpSSOUrl) {
      setFormValues((prevState) => {
        const configInfo = {
          ...prevState.configInfo,
          samlIdpCertificate,
          samlIdpEntityId,
          samlIdpSSOUrl,
        };

        return cloneDeep({ ...prevState, configInfo });
      });
    }
  }, [samlMetadata]);

  useEffect(() => {
    if (isManualMode) {
      const { hash } = idpCertificate;

      if (hash) {
        setFormValues((prevState) => {
          const configInfo = {
            ...prevState.configInfo,
            samlIdpCertificate: idpCertificate,
          };

          return cloneDeep({ ...prevState, configInfo });
        });
      } else {
        setFormValues((prevState) => {
          const configInfo = {
            ...prevState.configInfo,
            samlIdpCertificate: {},
          };

          return cloneDeep({ ...prevState, configInfo });
        });
      }
    }
  }, [idpCertificate, isManualMode]);

  const onFetchSAMLIdpMetadataUrl = () => {
    if (formValues.configInfo.samlIdpMetadataUrl) {
      apiCall(getMetaData({ metadataurl: formValues.configInfo.samlIdpMetadataUrl })).then(() => {
        setIsActionChoosen(true);
      });
    }
  };

  const onUploadMetadata = (detail) => {
    if (detail?.length > 0) {
      apiCall(uploadMetadata(detail))
        .then(() => {
          setIsActionChoosen(true);
        })
        .catch(noop);
    }
  };

  const onUploadCertificate = (detail) => {
    if (detail?.length > 0) {
      apiCall(uploadIDPCert(detail)).catch(noop);
    }
  };

  const onFormFieldChange = (evt) => {
    const updatedValue = getOnChangeValue(evt.target);

    setFormValues((prevState) => {
      const configInfo = {
        ...prevState.configInfo,
        ...updatedValue,
      };

      return { ...prevState, configInfo };
    });
  };

  const onDownloadSPMetadata = async () => {
    if (formValues.id) {
      return await apiCall(downloadMetadata(formValues.id));
    }

    return null;
  };

  const renderCertificatesSection = () => {
    if (!isActionChoosen && !isManualMode) {
      return null;
    }

    if (isManualMode) {
      return (
        <Field label="IDP_CERTIFICATE" tooltip={getFormTooltipDetail('samlIdpCertificate')}>
          <InputFile
            onChange={onUploadCertificate}
            leftIcon={faUpload}
            hasLeftIcon
            buttonLabel="UPLOAD_CERTIFICATE"
            buttonType="tertiary"
            buttonContainerClass="no-p-l"
            fileInfoPosition={'right'}
            fileInfoLabel={samlIdpCertificate?.hash || samlIdpCertificate?.id || ''}
          />
        </Field>
      );
    }

    return (
      <Field label="IDP_CERTIFICATE" tooltip={getFormTooltipDetail('samlIdpCertificate')}>
        <TextWithTooltip>
          {samlIdpCertificate?.subjectName ||
            samlIdpCertificate?.hash ||
            samlIdpCertificate?.id ||
            ''}
        </TextWithTooltip>
      </Field>
    );
  };

  const renderURLModeContent = () => {
    if (activeConfigurationMode === CONFIGURATION_MODE.URL) {
      return (
        <Field label="IDP_METADATA_URL" tooltip={getFormTooltipDetail('idpMetadataUrl')}>
          <div className="is-flex has-jc-sb has-ai-c full-width">
            <Input
              name="samlIdpMetadataUrl"
              value={formValues.configInfo.samlIdpMetadataUrl}
              onChange={onFormFieldChange}
              containerStyle={{ margin: '0' }}
            />

            <Button
              onClick={onFetchSAMLIdpMetadataUrl}
              containerStyle={{ marginLeft: '16px' }}
              disabled={!formValues.configInfo.samlIdpMetadataUrl}
            >
              {t('FETCH')}
            </Button>
          </div>
        </Field>
      );
    }

    return null;
  };

  const renderUploadModeContent = () => {
    if (activeConfigurationMode === CONFIGURATION_MODE.UPLOAD) {
      return (
        <Field label="IDP_METADATA" tooltip={getFormTooltipDetail('idpMetadata')}>
          <InputFile
            onChange={onUploadMetadata}
            leftIcon={faUpload}
            hasLeftIcon
            buttonLabel="UPLOAD_IDP_METADATA"
            buttonType="tertiary"
            buttonContainerClass="no-p-l"
            fileInfoPosition={'right'}
          />
        </Field>
      );
    }

    return null;
  };

  const renderCommonFormContent = () => {
    const spDetailSection = (
      <FieldGroup>
        <Field label="SP_ENTITY_ID">
          <TextWithTooltip text={samlSpEntityId} canCopy>
            {samlSpEntityId}
          </TextWithTooltip>
        </Field>
        <Field label="SP_URL">
          <TextWithTooltip text={samlSpAcsUrl} canCopy>
            {samlSpAcsUrl}
          </TextWithTooltip>
        </Field>
      </FieldGroup>
    );

    if (!isActionChoosen) {
      return (
        <>
          <FieldGroup>
            {renderURLModeContent()}
            {renderUploadModeContent()}
          </FieldGroup>

          {spDetailSection}
        </>
      );
    }

    return (
      <>
        <FieldGroup>
          {renderURLModeContent()}
          {renderUploadModeContent()}
        </FieldGroup>

        <FieldGroup>
          {isManualMode ? (
            <Input
              label="IDP_ENTITY_URI"
              name="samlIdpEntityId"
              value={samlIdpEntityId}
              onChange={onFormFieldChange}
              tooltip={getFormTooltipDetail('samlIdpEntityId')}
            />
          ) : (
            <Field label="IDP_ENTITY_URI" tooltip={getFormTooltipDetail('samlIdpEntityId')}>
              <TextWithTooltip text={samlIdpEntityId}>{samlIdpEntityId}</TextWithTooltip>
            </Field>
          )}

          {isManualMode ? (
            <Input
              label="IDP_SINGLE_SIGNON_URL"
              name="samlIdpSSOUrl"
              value={samlIdpSSOUrl}
              onChange={onFormFieldChange}
              tooltip={getFormTooltipDetail('samlIdpSSOUrl')}
            />
          ) : (
            <Field label="IDP_SINGLE_SIGNON_URL" tooltip={getFormTooltipDetail('samlIdpSSOUrl')}>
              <TextWithTooltip text={samlIdpSSOUrl} canCopy>
                {samlIdpSSOUrl}
              </TextWithTooltip>
            </Field>
          )}
        </FieldGroup>

        <FieldGroup>
          {renderCertificatesSection()}

          {isEditMode && (
            <Field label="SP_METADATA">
              <DownLoadFile
                variantType="iconWithText"
                label="DOWNLOAD_SP_METADATA"
                onDownloadClick={onDownloadSPMetadata}
              />
            </Field>
          )}
        </FieldGroup>

        {spDetailSection}
      </>
    );
  };

  return (
    <Card>
      <Field label="INPUT_METHOD" tooltip={getFormTooltipDetail('inputMethod')}>
        <RadioButtons
          list={CONFIGURATION_LIST}
          isSelected={isRadioButtonSelected}
          onClick={onRadioButtonsClick}
        />
      </Field>

      {renderCommonFormContent()}
    </Card>
  );
};

SAMLConfigurationForm.DEFAULT_PROPS = DEFAULT_PROPS;

SAMLConfigurationForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
  isActionChoosen: PropTypes.bool,
  setIsActionChoosen: PropTypes.func,
  activeConfigurationMode: PropTypes.string,
  setActiveConfigurationMode: PropTypes.func,
};

export default SAMLConfigurationForm;
