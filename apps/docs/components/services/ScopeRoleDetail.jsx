import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getServiceRoles } from '../../ducks/services';
import { selectScopeRoleDetail, selectScopesRoleDetail } from '../../ducks/services/selectors';

import useApiCall from '../../../hooks/useApiCall';

const DEFAULT_PROPS = {
  original: { id: '', tservice: { id: '' } },
};

const ScopeRoleDetail = ({ original }) => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall();

  const {
    id: scopeId,
    tservice: { id },
  } = original;

  const roles = selectScopeRoleDetail(useSelector(selectScopesRoleDetail), scopeId);

  useEffect(() => {
    apiCall(getServiceRoles({ id, scopeId })).catch(noop);
  }, [scopeId, id]);

  const roleName = roles?.map?.(({ name }) => name)?.join?.(', ');

  return <div className="scope-role-container">{roleName || t('NO_ITEMS_FOUND')}</div>;
};

ScopeRoleDetail.DEFAULT_PROPS = DEFAULT_PROPS;

ScopeRoleDetail.propTypes = {
  original: PropTypes.object,
};

export default ScopeRoleDetail;
