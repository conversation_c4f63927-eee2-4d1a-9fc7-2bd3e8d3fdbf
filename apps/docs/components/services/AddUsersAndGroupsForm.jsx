import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import MultiSelection from '../../../components/dropdowns/MultiSelection';
import { Field, FieldGroup } from '../../../components/forms';

import { getList as getGroupsList } from '../../ducks/groups';
import {
  selectGroupsList,
  selectTableDetail as selectGroupsTableDetail,
} from '../../ducks/groups/selectors';
import { getServiceRoles, getServiceScopes } from '../../ducks/services';
import {
  selectScopeRoleDetail,
  selectScopeRoleList,
  selectScopesRoleDetail,
  selectServiceConstraintDetail,
  selectServiceConstraints,
  selectServiceRoleDetail,
  selectServiceRoleList,
  selectServiceScopeDetail,
  selectServiceScopeList,
  selectServicesRoleDetail,
  selectServicesScopeDetail,
} from '../../ducks/services/selectors';
import { getList as getUsersList } from '../../ducks/users';
import {
  selectUsersList,
  selectTableDetail as selectUsersTableDetail,
} from '../../ducks/users/selectors';

import useDropDownActions from '../../../hooks/useDropDownActions';
import { getFormTooltipDetail, getSelectedUsersGroupRolesApiPayload } from './helper';

const DEFAULT_PROPS = {
  serviceDetail: {},
  formState: 1,
  selectedUsersGroupRoles: {
    selectedUsersRoleOption: [{ users: [], scope: [], roles: [] }],
    selectedGroupsRoleOption: [{ groups: [], scope: [], roles: [] }],
  },
  onUsersGroupsRoleSelection: noop,
};

const AddUsersAndGroupsForm = ({
  serviceDetail: { id, serviceName },
  formState,
  selectedUsersGroupRoles,
  onUsersGroupsRoleSelection,
}) => {
  const { t } = useTranslation();

  const usersListTableDetail = useSelector(selectUsersTableDetail);
  const usersList = useSelector(selectUsersList);

  const {
    roleSupport = true,
    scopeSupport = false,
    multiRoleSupport = false,
  } = selectServiceConstraintDetail(useSelector(selectServiceConstraints), serviceName);

  const {
    isDropDownLoading: isUsersDropDownLoading,
    onDropDownOpen: onUsersDropDownOpen,
    onLoadMoreClick: onUsersLoadMoreClick,
  } = useDropDownActions({
    detail: usersListTableDetail,
    apiCallFunc: getUsersList,
  });

  const groupsListTableDetail = useSelector(selectGroupsTableDetail);
  const groupsList = useSelector(selectGroupsList);

  const {
    isDropDownLoading: isGroupsDropDownLoading,
    onDropDownOpen: onGroupsDropDownOpen,
    onLoadMoreClick: onGroupsLoadMoreClick,
  } = useDropDownActions({
    detail: groupsListTableDetail,
    apiCallFunc: getGroupsList,
  });

  const { isDropDownLoading: isScopeDropDownLoading, onDropDownOpen: onScopeDropDownOpen } =
    useDropDownActions({
      detail: { hasFetchedAllRecords: true },
      apiCallFunc: getServiceScopes,
      defaultOnOpenApiPayload: { id },
      fetchOnce: true,
    });

  const serviceScopesDetail = selectServiceScopeDetail(useSelector(selectServicesScopeDetail), id);
  const serviceScopesList = selectServiceScopeList(serviceScopesDetail);

  const { isDropDownLoading: isRolesDropDownLoading, onDropDownOpen: onRolesDropDownOpen } =
    useDropDownActions({
      detail: { hasFetchedAllRecords: scopeSupport ? false : true },
      apiCallFunc: getServiceRoles,
      defaultOnOpenApiPayload: { id },
      fetchOnce: true,
    });

  const serviceRolesDetail = selectServiceRoleDetail(useSelector(selectServicesRoleDetail), id);
  const serviceRolesList = selectServiceRoleList(serviceRolesDetail);

  const scopesRoleDetail = useSelector(selectScopesRoleDetail);

  const [selectedUsersRoleOption, setSelectedUsersRoleOption] = useState([
    { users: [], scope: [], roles: [] },
  ]);

  const [selectedGroupsRoleOption, setSelectedGroupsRoleOption] = useState([
    { groups: [], scope: [], roles: [] },
  ]);

  useEffect(() => {
    onUsersGroupsRoleSelection({ selectedUsersRoleOption, selectedGroupsRoleOption });
  }, [selectedUsersRoleOption, selectedGroupsRoleOption]);

  const onUsersRoleSelection = (key, detail, idx) => {
    const selectedRoles = selectedUsersRoleOption[idx];

    selectedRoles[key] = detail;

    if (key === 'scope') {
      selectedRoles.roles = [];
    }

    selectedUsersRoleOption[idx] = selectedRoles;

    setSelectedUsersRoleOption([...selectedUsersRoleOption]);
  };

  const onGroupsRoleSelection = (key, detail, idx) => {
    const selectedRoles = selectedGroupsRoleOption[idx];

    selectedRoles[key] = detail;

    if (key === 'scope') {
      selectedRoles.roles = [];
    }

    selectedGroupsRoleOption[idx] = selectedRoles;

    setSelectedGroupsRoleOption([...selectedGroupsRoleOption]);
  };

  const getSelectedUsersRolesList = (key, idx) => selectedUsersRoleOption[idx][key];

  const getSelectedGroupsRolesList = (key, idx) => selectedGroupsRoleOption[idx][key];

  const onScopeRoleDropDownOpen = (key, idx) => {
    let scopeDetail = {};

    if (key === 'users') {
      scopeDetail = getSelectedUsersRolesList('scope', idx);
    } else if (key === 'groups') {
      scopeDetail = getSelectedGroupsRolesList('scope', idx);
    }

    // as of now sigle scope selection
    const scopeId = scopeDetail?.[0]?.value;

    if (scopeId) {
      return () => onRolesDropDownOpen({ apiPayload: { scopeId } });
    } else {
      return onRolesDropDownOpen;
    }
  };

  const getRoleList = (key, idx) => {
    if (scopeSupport) {
      let scopeDetail = {};

      if (key === 'users') {
        scopeDetail = getSelectedUsersRolesList('scope', idx);
      } else if (key === 'groups') {
        scopeDetail = getSelectedGroupsRolesList('scope', idx);
      }

      // as of now sigle scope selection
      const scopeId = scopeDetail?.[0]?.value;

      const scopeRoles = selectScopeRoleDetail(scopesRoleDetail, scopeId);

      return selectScopeRoleList(scopeRoles);
    } else {
      return serviceRolesList;
    }
  };

  const onAddUsersAndRoles = () => {
    setSelectedUsersRoleOption((prevState) => {
      const usersMapping = {};

      const updatedState = [];

      prevState.forEach(({ users, scope, roles }) => {
        const { value } = roles[0] || {};

        const newUsers = [];

        users.forEach((user) => {
          const { label, value: userValue } = user || {};

          const userRoles = usersMapping[userValue] || [];

          if (userRoles.length === 0) {
            userRoles.push(value);

            newUsers.push({ label, value: userValue });

            usersMapping[userValue] = userRoles;
          }
        });

        if (newUsers.length) {
          updatedState.push({ users: newUsers, scope, roles: roles });
        }
      });

      return [...updatedState, { users: [], scope: [], roles: [] }];
    });
  };

  const onAddGroupsAndRoles = () => {
    setSelectedGroupsRoleOption((prevState) => {
      const groupsMapping = {};

      const updatedState = [];

      prevState.forEach(({ groups, scope, roles }) => {
        const { value } = roles[0] || {};

        const newGroups = [];

        groups.forEach((group) => {
          const { label, value: groupValue } = group || {};

          const groupRoles = groupsMapping[groupValue] || [];

          if (groupRoles.length === 0) {
            groupRoles.push(value);

            newGroups.push({ label, value: groupValue });

            groupsMapping[groupValue] = groupRoles;
          }
        });

        if (newGroups.length) {
          updatedState.push({ groups: newGroups, scope, roles: roles });
        }
      });

      return [...updatedState, { groups: [], scope: [], roles: [] }];
    });
  };

  const onRemoveUsersAndRoles = (removeIdx) =>
    setSelectedUsersRoleOption((prevState) => remove(prevState, (_, idx) => idx !== removeIdx));

  const onRemoveGroupsAndRoles = (removeIdx) =>
    setSelectedGroupsRoleOption((prevState) => remove(prevState, (_, idx) => idx !== removeIdx));

  const renderStepperSection = () => {
    return (
      <section className="stepper-container">
        <div className={`step ${formState === 1 ? 'active' : ''} ${formState === 2 ? 'done' : ''}`}>
          <span className="step-no"> 1 </span>
          <span className="text">{t('ASSIGN_USERS_AND_GROUPS')} </span>
        </div>
        <div className={`step ${formState === 2 ? 'active' : ''}`}>
          <span className="step-no"> 2 </span>
          <span className="text">{t('REVIEW')} </span>
        </div>
      </section>
    );
  };

  const renderResourceRoleMappedSection = (rolesDetail = []) => {
    return (
      <Card containerClass="roles-detail">
        <section className="is-flex heading">
          <div className="resource-name text-gray-darkest"> {t('NAME')} </div>
          <div className="role-name text-gray-darkest"> {t('ROLES')} </div>
        </section>
        <section className="detail">
          {rolesDetail?.map?.(({ resource: { id: resourceId, name: resourceName }, tsRoles }) => {
            const roles = tsRoles?.map?.(({ name }) => name)?.join?.(', ');
            const roleIds = tsRoles?.map?.(({ id }) => id)?.join?.(', ');

            return (
              <div key={`${resourceId}-${id}-${roleIds}`} className="is-flex role">
                <div className="resource-name"> {resourceName} </div>
                <div className="role-name"> {roles} </div>
              </div>
            );
          })}
        </section>
      </Card>
    );
  };

  const renderUsersRolesSection = () => {
    if (formState === 1) {
      return (
        <Card>
          {selectedUsersRoleOption.map((_, idx) => (
            <FieldGroup key={idx}>
              <Field label="ASSIGN_USERS" tooltip={getFormTooltipDetail('assignUsers')}>
                <DropDown
                  list={usersList}
                  selectedList={getSelectedUsersRolesList('users', idx)}
                  onOpen={onUsersDropDownOpen}
                  onSelection={(detail) => {
                    onUsersRoleSelection('users', detail, idx);
                  }}
                  renderItemsSelection={(props) => (
                    <MultiSelection
                      unselectedTitle="Unselected Users"
                      selectedTitle="Selected Users"
                      {...props}
                    />
                  )}
                  isMulti
                  hasSearch
                  containerClass="full-width"
                  loadMoreDetail={{
                    ...usersListTableDetail,
                    onLoadMoreClick: onUsersLoadMoreClick,
                  }}
                  loading={isUsersDropDownLoading}
                  containerStyle={{ maxWidth: '250px' }}
                />
              </Field>

              {scopeSupport && (
                <Field label="ASSIGN_SCOPE">
                  <DropDown
                    list={serviceScopesList}
                    selectedList={getSelectedUsersRolesList('scope', idx)}
                    onSelection={(detail) => {
                      onUsersRoleSelection('scope', detail, idx);
                    }}
                    onOpen={onScopeDropDownOpen}
                    hasSearch
                    loading={isScopeDropDownLoading}
                    containerClass="full-width"
                    containerStyle={{ maxWidth: '250px' }}
                  />
                </Field>
              )}

              {roleSupport && (
                <Field label="ASSIGN_ROLE" tooltip={getFormTooltipDetail('assignUsersRole')}>
                  <DropDown
                    list={getRoleList('users', idx)}
                    selectedList={getSelectedUsersRolesList('roles', idx)}
                    onSelection={(detail) => {
                      onUsersRoleSelection('roles', detail, idx);
                    }}
                    onOpen={
                      scopeSupport ? onScopeRoleDropDownOpen('users', idx) : onRolesDropDownOpen
                    }
                    isMulti={multiRoleSupport}
                    hasSearch
                    loading={isRolesDropDownLoading}
                    containerClass="full-width"
                    containerStyle={{ maxWidth: '250px' }}
                  />
                </Field>
              )}

              {idx < selectedUsersRoleOption.length - 1 && (
                <Button
                  type="tertiary"
                  onClick={() => {
                    onRemoveUsersAndRoles(idx);
                  }}
                  containerClass="content-width no-p-l no-p-r"
                >
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              )}
            </FieldGroup>
          ))}
          <div className="is-flex has-jc-e">
            <Button type="tertiary" containerClass="no-p-r" onClick={onAddUsersAndRoles}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              {t('ADD_MORE')}
            </Button>
          </div>
        </Card>
      );
    }

    const rolesDetail = getSelectedUsersGroupRolesApiPayload(selectedUsersGroupRoles)?.filter(
      (detail) => detail.type === 'USER',
    );

    return renderResourceRoleMappedSection(rolesDetail);
  };

  const renderGroupsRolesSection = () => {
    if (formState === 1) {
      return (
        <Card>
          {selectedGroupsRoleOption.map((_, idx) => (
            <FieldGroup key={idx}>
              <Field label="ASSIGN_USER_GROUPS" tooltip={getFormTooltipDetail('assignGroups')}>
                <DropDown
                  list={groupsList}
                  selectedList={getSelectedGroupsRolesList('groups', idx)}
                  onSelection={(detail) => {
                    onGroupsRoleSelection('groups', detail, idx);
                  }}
                  onOpen={onGroupsDropDownOpen}
                  renderItemsSelection={(props) => (
                    <MultiSelection
                      unselectedTitle="Unselected User Groups"
                      selectedTitle="Selected User Groups"
                      {...props}
                    />
                  )}
                  isMulti
                  hasSearch
                  containerClass="full-width"
                  loadMoreDetail={{
                    ...groupsListTableDetail,
                    onLoadMoreClick: onGroupsLoadMoreClick,
                  }}
                  loading={isGroupsDropDownLoading}
                  containerStyle={{ maxWidth: '250px' }}
                  selectedItemsTooltipProps={{ showOnHover: false, showOnClick: true }}
                />
              </Field>

              {scopeSupport && (
                <Field label="ASSIGN_SCOPE">
                  <DropDown
                    list={serviceScopesList}
                    selectedList={getSelectedGroupsRolesList('scope', idx)}
                    onSelection={(detail) => {
                      onGroupsRoleSelection('scope', detail, idx);
                    }}
                    onOpen={onScopeDropDownOpen}
                    hasSearch
                    loading={isScopeDropDownLoading}
                    containerClass="full-width"
                    containerStyle={{ maxWidth: '250px' }}
                  />
                </Field>
              )}

              {roleSupport && (
                <Field label="ASSIGN_ROLE" tooltip={getFormTooltipDetail('assignGroupsRole')}>
                  <DropDown
                    list={getRoleList('groups', idx)}
                    selectedList={getSelectedGroupsRolesList('roles', idx)}
                    onSelection={(detail) => {
                      onGroupsRoleSelection('roles', detail, idx);
                    }}
                    onOpen={
                      scopeSupport ? onScopeRoleDropDownOpen('groups', idx) : onRolesDropDownOpen
                    }
                    isMulti={multiRoleSupport}
                    hasSearch
                    loading={isRolesDropDownLoading}
                    containerClass="full-width"
                    containerStyle={{ maxWidth: '250px' }}
                  />
                </Field>
              )}

              {idx < selectedGroupsRoleOption.length - 1 && (
                <Button
                  type="tertiary"
                  onClick={() => {
                    onRemoveGroupsAndRoles(idx);
                  }}
                  containerClass="content-width no-p-l no-p-r"
                >
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              )}
            </FieldGroup>
          ))}
          <div className="is-flex has-jc-e">
            <Button type="tertiary" containerClass="no-p-r" onClick={onAddGroupsAndRoles}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              {t('ADD_MORE')}
            </Button>
          </div>
        </Card>
      );
    }

    const rolesDetail = getSelectedUsersGroupRolesApiPayload(selectedUsersGroupRoles)?.filter(
      (detail) => detail.type === 'GROUP',
    );

    return renderResourceRoleMappedSection(rolesDetail);
  };

  return (
    <>
      {renderStepperSection()}

      <section className="text-upper-large">{t('USERS_ASSIGNMENT')}</section>

      {renderUsersRolesSection()}

      <section className="text-upper-large">{t('GROUPS_ASSIGNMENT')}</section>

      {renderGroupsRolesSection()}
    </>
  );
};

AddUsersAndGroupsForm.DEFAULT_PROPS = DEFAULT_PROPS;

AddUsersAndGroupsForm.propTypes = {
  serviceDetail: PropTypes.object,
  formState: PropTypes.number,
  selectedUsersGroupRoles: PropTypes.object,
  onUsersGroupsRoleSelection: PropTypes.func,
};

export default AddUsersAndGroupsForm;
