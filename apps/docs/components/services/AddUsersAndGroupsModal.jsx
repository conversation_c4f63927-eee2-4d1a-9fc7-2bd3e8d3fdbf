import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button } from '../../../components/buttons';
import Modal from '../../../components/modal/Modal';
import ModalBody from '../../../components/modal/ModalBody';
import ModalFooter from '../../../components/modal/ModalFooter';
import ModalHeader from '../../../components/modal/ModalHeader';

import { addUsersAndGroup } from '../../ducks/services';

import useApiCall from '../../../hooks/useApiCall';
import AddUsersAndGroupsForm from './AddUsersAndGroupsForm';
import {
  getSelectedUsersGroupRolesApiPayload,
  getSelectedUsersGroupRolesFormValidationDetail,
} from './helper';

const DEFAULT_PROPS = {
  showModal: false,
  serviceDetail: {},
  onCloseClick: noop,
};

const AddUsersAndGroupsModal = ({ showModal, serviceDetail, onCloseClick }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { serviceDescription } = serviceDetail || {};
  const [formState, setFormState] = useState(1);

  const [selectedUsersGroupRoles, setSelectedUsersGroupRoles] = useState({
    selectedUsersRoleOption: [{ users: [], roles: [] }],
    selectedGroupsRoleOption: [{ groups: [], roles: [] }],
  });

  const { isValid } = getSelectedUsersGroupRolesFormValidationDetail(selectedUsersGroupRoles);

  const getHeaderText = () => {
    return `${serviceDescription} - Assign`;
  };

  const renderBodySection = () => {
    return (
      <AddUsersAndGroupsForm
        serviceDetail={serviceDetail}
        formState={formState}
        selectedUsersGroupRoles={selectedUsersGroupRoles}
        onUsersGroupsRoleSelection={setSelectedUsersGroupRoles}
      />
    );
  };

  const onBackClick = () => {
    setFormState(1);
  };

  const onNextClick = () => {
    if (formState === 1 && isValid) {
      return setFormState(2);
    }

    const payload = getSelectedUsersGroupRolesApiPayload(selectedUsersGroupRoles);

    apiCall(addUsersAndGroup({ id: serviceDetail.id, payload }))
      .then(onCloseClick)
      .catch(noop);
  };

  return (
    <>
      <Modal show={showModal} onEscape={onCloseClick} containerClass="service-modal-container add">
        <ModalHeader text={getHeaderText()} onClose={onCloseClick} />
        <ModalBody>{renderBodySection()}</ModalBody>
        <ModalFooter containerClass="has-jc-sb">
          <Button type="tertiary" containerClass="no-p-l" onClick={onCloseClick}>
            {t('CANCEL')}
          </Button>
          <div className="buttons" style={{ marginBottom: '0px' }}>
            {formState !== 1 && (
              <Button type="secondary" onClick={onBackClick}>
                {t('BACK')}
              </Button>
            )}
            <Button onClick={onNextClick} disabled={!isValid}>
              {t(formState === 1 ? 'NEXT' : 'SAVE')}
            </Button>
          </div>
        </ModalFooter>
      </Modal>
    </>
  );
};

AddUsersAndGroupsModal.DEFAULT_PROPS = DEFAULT_PROPS;

AddUsersAndGroupsModal.propTypes = {
  showModal: PropTypes.bool,
  serviceDetail: PropTypes.object,
  onCloseClick: PropTypes.func,
};

export default AddUsersAndGroupsModal;
