import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../../../components/buttons/Button';
import { Search } from '../../../components/forms';
import CRUDModal from '../../../components/modal/CRUDModal';
import TableContainer from '../../../components/table/TableContainer';
import Actions from '../../../components/table/components/Actions';
import Selector from '../../../components/table/components/Selector';

import {
  deleteAssignment,
  deleteBulkAssignment,
  getUsersAndGroup,
  updateAssignment,
} from '../../ducks/services';
import {
  selectServiceAssignmentDetail,
  selectServicesAssignmentDetail,
  selectUsersGroupsTableConfig,
} from '../../ducks/services/selectors';

import useApiCall from '../../../hooks/useApiCall';
import EditUsersAndGroupsRoleForm from './EditUsersAndGroupsRoleForm';
import { getModalModeDetail } from './helper';

const DEFAULT_PROPS = {
  original: { id: '' },
};

const DEFAULT_MODAL_MODE = '';
const DEFAULT_SELECTED_SERVICE_DETAIL = {};

const UsersAndGroupsTable = ({ original: { id, serviceName } }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  // '','edit','delete'
  const [modalMode, setModalMode] = useState(DEFAULT_MODAL_MODE);
  const [searchTerm, setSearchTerm] = useState('');

  const [selectedServiceDetail, setSelectedServiceDetail] = useState(
    DEFAULT_SELECTED_SERVICE_DETAIL,
  );

  const [selectedRowDetail, setSelectedRowDetail] = useState([]);

  const serviceDetail = selectServiceAssignmentDetail(
    useSelector(selectServicesAssignmentDetail),
    id,
  );

  useEffect(() => {
    apiCall(getUsersAndGroup({ id, requireTotal: true })).catch(noop);
  }, [id]);

  const tableConfig = useSelector(selectUsersGroupsTableConfig);

  const onEditClick = (detail) => {
    setSelectedServiceDetail(detail);

    setModalMode('edit');
  };

  const onDeleteClick = (detail) => {
    setSelectedServiceDetail(detail);

    setModalMode('delete');
  };

  const onBulkDeleteClick = () => {
    if (selectedRowDetail.length !== -1) {
      setModalMode('bulkDelete');
    }
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions {...props} onEditClick={onEditClick} onDeleteClick={onDeleteClick} />
        );

        columnDetail.cell = ActionsCellComponent;

        columnDetail.Header = '';
      }

      return columnDetail;
    });

    const newColumns = [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 50,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];

    if (serviceName !== 'ZPA') {
      remove(newColumns, { id: 'scope' });
    }

    return newColumns;
  }, [tableConfig?.columns, selectedRowDetail.length]);

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);
  };

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setSelectedServiceDetail(DEFAULT_SELECTED_SERVICE_DETAIL);
  };

  const onSaveClick = () => {
    if (modalMode === 'edit') {
      apiCall(updateAssignment(selectedServiceDetail)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(deleteAssignment(selectedServiceDetail)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'bulkDelete') {
      apiCall(
        deleteBulkAssignment({
          id,
          ids: selectedRowDetail.map((detail) => detail.id),
        }),
      )
        .catch(noop)
        .then(onCloseClick);
    }
  };

  const onSearchEnter = (term) => {
    apiCall(getUsersAndGroup({ id, requireTotal: true, name: term })).catch(noop);

    setSearchTerm(term);
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = serviceDetail;

    apiCall(
      getUsersAndGroup({
        id,
        requireTotal: false,
        pageOffset: pageOffset + pageSize,
        pageSize,
      }),
    ).catch(noop);
  };

  const modalModeDetail = useMemo(
    () => getModalModeDetail({ modalMode, selectedServiceDetail, isBulkActionEnabled }),
    [modalMode, selectedServiceDetail, isBulkActionEnabled],
  );

  return (
    <div className="services-users-groups-container">
      <div className="is-flex has-jc-s search-section">
        <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ maxWidth: '250px' }} />

        {isBulkActionEnabled && (
          <Button type={'secondary'} onClick={onBulkDeleteClick} disabled={!isBulkActionEnabled}>
            <FontAwesomeIcon icon={faTrash} className="icon left" />

            {t('REMOVE')}
          </Button>
        )}
      </div>

      <TableContainer
        {...tableConfig}
        columns={tableColumnConfig}
        data={serviceDetail.data}
        pagination={{ ...serviceDetail, onLoadMoreClick }}
        onRowSelection={onRowSelection}
      />

      {modalMode && (
        <CRUDModal
          mode={modalMode}
          renderFormSection={(props) => (
            <EditUsersAndGroupsRoleForm
              {...props}
              onDetailChange={setSelectedServiceDetail}
              detail={selectedServiceDetail}
            />
          )}
          onSaveClick={onSaveClick}
          onCloseClick={onCloseClick}
          {...modalModeDetail}
        />
      )}
    </div>
  );
};

UsersAndGroupsTable.DEFAULT_PROPS = DEFAULT_PROPS;

UsersAndGroupsTable.propTypes = {
  original: PropTypes.object,
};

export default UsersAndGroupsTable;
