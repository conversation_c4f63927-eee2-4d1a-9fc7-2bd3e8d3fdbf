import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../../components/cards';
import { FieldGroup, Input } from '../../../components/forms';
import Modal from '../../../components/modal/Modal';
import ModalBody from '../../../components/modal/ModalBody';
import ModalFooter from '../../../components/modal/ModalFooter';
import ModalHeader from '../../../components/modal/ModalHeader';

import {
  getServiceDetail,
  getServiceRoles,
  getServiceScopes,
  syncServiceRoles,
} from '../../ducks/services';
import {
  selectServiceConstraintDetail,
  selectServiceConstraints,
} from '../../ducks/services/selectors';

import useApiCall from '../../../hooks/useApiCall';
import ServiceRolesAndScope from './ServiceRolesAndScope';

const DEFAULT_PROPS = {
  showModal: false,
  detail: {},
  onSaveClick: noop,
  onCloseClick: noop,
};

const EditServiceModal = ({ showModal, detail, onCloseClick }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall({ hasLoader: true });

  const { id, serviceName, serviceDescription } = detail;

  const { scopeSupport } = selectServiceConstraintDetail(
    useSelector(selectServiceConstraints),
    serviceName,
  );

  const refreshServiceDetail = () => {
    apiCall(getServiceDetail({ id })).catch(noop);
    apiCall(getServiceRoles({ id })).catch(noop);

    if (scopeSupport) {
      apiCall(getServiceScopes({ id })).catch(noop);
    }
  };

  const onSyncRolesAndScopeClick = () => {
    apiCall(syncServiceRoles({ id }))
      .catch(noop)
      .finally(() => refreshServiceDetail());
  };

  useEffect(() => {
    if (id) {
      refreshServiceDetail();
    }
  }, [id]);

  const renderBodySection = () => {
    return (
      <>
        <section className="text-upper-large">{t('GENERAL_INFORMATION')}</section>

        <Card>
          <FieldGroup>
            <Input
              name="name"
              label="NAME"
              value={serviceDescription}
              readOnly
              style={{ color: 'black' }}
            />
          </FieldGroup>

          <ServiceRolesAndScope
            detail={detail}
            scopeSupport={scopeSupport}
            onSyncRolesAndScopeClick={onSyncRolesAndScopeClick}
          />
        </Card>
      </>
    );
  };

  return (
    <Modal show={showModal} onEscape={onCloseClick} containerClass="service-modal-container edit">
      <ModalHeader text="APPLICATION_DETAIL" onClose={onCloseClick} />
      <ModalBody>{renderBodySection()}</ModalBody>
      <ModalFooter
        showSave={false}
        cancelText="CLOSE"
        onCancel={onCloseClick}
        cancelProps={{ containerClass: 'no-p-l' }}
      />
    </Modal>
  );
};

EditServiceModal.DEFAULT_PROPS = DEFAULT_PROPS;

EditServiceModal.propTypes = {
  showModal: PropTypes.bool,
  detail: PropTypes.object,
  onSaveClick: PropTypes.func,
  onCloseClick: PropTypes.func,
};

export default EditServiceModal;
