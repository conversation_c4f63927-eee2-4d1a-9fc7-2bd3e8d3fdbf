import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faAngleRight, faAngleUp } from '@fortawesome/pro-light-svg-icons';
import { faExternalLink, faSync, faUsers } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { filter, includes, noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button } from '../../../components/buttons';
import { Search } from '../../../components/forms';
import TableContainer from '../../../components/table/TableContainer';
import InlineText from '../../../components/table/components/InlineText';

import {
  selectRoleTableConfig,
  selectServiceRoleDetail,
  selectServiceScopeDetail,
  selectServicesRoleDetail,
  selectServicesScopeDetail,
} from '../../ducks/services/selectors';

import ScopeRoleDetail from './ScopeRoleDetail';

const DEFAULT_PROPS = {
  detail: {},
  scopeSupport: false,
  onSyncRolesAndScopeClick: noop,
};

const ServiceRolesAndScope = ({ detail, scopeSupport, onSyncRolesAndScopeClick }) => {
  const { t } = useTranslation();
  const { id, manageRolesUrl } = detail;

  const roleTableConfig = useSelector(selectRoleTableConfig);

  const tserviceRoles = selectServiceRoleDetail(useSelector(selectServicesRoleDetail), id);
  const tserviceScopes = selectServiceScopeDetail(useSelector(selectServicesScopeDetail), id);

  const [searchTerm, setSearchTerm] = useState('');
  const [activeRoles, setActiveRoles] = useState(tserviceRoles);

  useEffect(() => {
    if (searchTerm) {
      const filteredRoles = filter(scopeSupport ? tserviceScopes : tserviceRoles, ({ name }) => {
        return includes((name + '').toLowerCase(), (searchTerm + '').toLowerCase());
      });

      setActiveRoles(filteredRoles);
    } else {
      setActiveRoles(scopeSupport ? tserviceScopes : tserviceRoles);
    }
  }, [searchTerm, tserviceRoles, tserviceScopes]);

  const tableColumnConfig = useMemo(() => {
    roleTableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'idx') {
        const ItemCount = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              getIsExpanded,
              // eslint-disable-next-line react/prop-types
              toggleExpanded,
              // eslint-disable-next-line react/prop-types
              index,
            },
            // eslint-disable-next-line react/prop-types
            table,
          } = props;

          const isExpanded = getIsExpanded();

          const onToggleExpandClick = () => {
            // eslint-disable-next-line react/prop-types
            table?.toggleAllRowsExpanded(false);
            toggleExpanded(!isExpanded);
          };

          return (
            <>
              <div
                className={`is-flex has-ai-c ${scopeSupport ? 'pointer' : ''}`}
                onClick={onToggleExpandClick}
              >
                {scopeSupport && (
                  <FontAwesomeIcon
                    icon={isExpanded ? faAngleUp : faAngleRight}
                    style={{ marginLeft: '0.25em', marginRight: '0.5em' }}
                  />
                )}

                <span> {index + 1} </span>
              </div>
            </>
          );
        };

        columnDetail.cell = ItemCount;
      }

      if (columnDetail.id === 'roleName') {
        const InlineTextComponent = (props) => <InlineText {...props} />;

        columnDetail.cell = InlineTextComponent;
      }

      return columnDetail;
    });

    const newColumns = [...(roleTableConfig?.columns || [])];

    if (scopeSupport) {
      remove(newColumns, { id: 'roleName' });
    } else {
      remove(newColumns, { id: 'scopeAndRoleName' });
    }

    return newColumns;
  }, [roleTableConfig?.columns]);

  const ExpandedRowContainer = (props) => <ScopeRoleDetail {...props} />;

  return (
    <>
      <Search
        onSearch={setSearchTerm}
        term={searchTerm}
        label={scopeSupport ? 'SCOPES_N_ROLES' : 'ROLES'}
      />

      <div className="roles-section" style={{ marginTop: '16px' }}>
        <TableContainer
          {...roleTableConfig}
          columns={tableColumnConfig}
          data={activeRoles}
          hidePagination
          showFooter={false}
          renderExpandedRowContainer={ExpandedRowContainer}
        />
        <div className="is-flex has-jc-sb">
          {manageRolesUrl && (
            <a target="_blank" href={manageRolesUrl} rel="noreferrer" className="link">
              <Button type="tertiary" containerClass="no-p-l">
                <FontAwesomeIcon icon={faUsers} className="icon left" />
                <span>{t('MANAGE_ROLES')}</span>
                <FontAwesomeIcon icon={faExternalLink} className="icon right" />
              </Button>
            </a>
          )}

          <Button
            type="tertiary"
            onClick={onSyncRolesAndScopeClick}
            containerClass="no-p-l no-p-r has-as-e"
          >
            <FontAwesomeIcon icon={faSync} className="icon left" />
            <span>{t('SYNC')}</span>
          </Button>
        </div>
      </div>
    </>
  );
};

ServiceRolesAndScope.DEFAULT_PROPS = DEFAULT_PROPS;

ServiceRolesAndScope.propTypes = {
  detail: PropTypes.object,
  scopeSupport: PropTypes.bool,
  onSyncRolesAndScopeClick: PropTypes.func,
};

export default ServiceRolesAndScope;
