import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../../components/cards';
import DropDown from '../../../components/dropdowns/DropDown';
import { getDropDownList } from '../../../components/dropdowns/helper';
import { Field, FieldGroup, Input } from '../../../components/forms';

import { getServiceRoles, getServiceScopes } from '../../ducks/services';
import {
  selectScopeRoleDetail,
  selectScopeRoleList,
  selectScopesRoleDetail,
  selectServiceConstraintDetail,
  selectServiceConstraints,
  selectServiceRoleDetail,
  selectServiceRoleList,
  selectServiceScopeDetail,
  selectServiceScopeList,
  selectServicesRoleDetail,
  selectServicesScopeDetail,
} from '../../ducks/services/selectors';

import useDropDownActions from '../../../hooks/useDropDownActions';

const DEFAULT_PROPS = {
  detail: {},
  onDetailChange: noop,
};

const EditUsersAndGroupsRoleForm = ({ detail, onDetailChange }) => {
  const { t } = useTranslation();

  const { resource, type, tsRoles, tsScope = {}, tservice } = detail;

  const serviceRolesDetail = selectServiceRoleDetail(
    useSelector(selectServicesRoleDetail),
    tservice.id,
  );

  const serviceScopesDetail = selectServiceScopeDetail(
    useSelector(selectServicesScopeDetail),
    tservice.id,
  );

  const {
    roleSupport = true,
    scopeSupport = false,
    multiRoleSupport = false,
  } = selectServiceConstraintDetail(useSelector(selectServiceConstraints), tservice.name);

  const serviceRolesList = selectServiceRoleList(serviceRolesDetail);
  const serviceScopesList = selectServiceScopeList(serviceScopesDetail);

  const { isDropDownLoading: isRolesDropDownLoading, onDropDownOpen: onRolesDropDownOpen } =
    useDropDownActions({
      detail: { hasFetchedAllRecords: scopeSupport ? false : true },
      apiCallFunc: getServiceRoles,
      defaultOnOpenApiPayload: scopeSupport
        ? { id: tservice.id, scopeId: tsScope?.id }
        : { id: tservice.id },
    });

  const { isDropDownLoading: isScopesDropDownLoading, onDropDownOpen: onScopesDropDownOpen } =
    useDropDownActions({
      detail: { hasFetchedAllRecords: true },
      apiCallFunc: getServiceScopes,
      defaultOnOpenApiPayload: { id: tservice.id },
    });

  const [selectedRoleList, setSelectedRoleList] = useState(getDropDownList({ list: tsRoles }));
  const [selectedScopeList, setSelectedScopeList] = useState(getDropDownList({ list: [tsScope] }));

  const selectedScopeDetail = selectedScopeList[0];

  const selectedScopeRole = selectScopeRoleDetail(
    useSelector(selectScopesRoleDetail),
    selectedScopeDetail?.value,
  );

  const selectedScopeRoleList = selectScopeRoleList(selectedScopeRole);

  const onRoleSelection = (payload) => {
    setSelectedRoleList(payload);

    onDetailChange({
      ...detail,
      tsRoles: payload?.map?.(({ label, value }) => ({ id: value, name: label })),
    });
  };

  const onScopeSelection = (payload) => {
    setSelectedScopeList(payload);
    setSelectedRoleList([]);

    onDetailChange({
      ...detail,
      tsScope: payload?.map?.(({ label, value }) => ({ id: value, name: label }))?.[0],
      tsRoles: [],
    });
  };

  const onScopeRoleDropDownOpen = () => {
    if (scopeSupport) {
      const scopeDetail = selectedScopeList[0] || {};

      if (scopeDetail?.value) {
        return onRolesDropDownOpen({
          apiPayload: { id: tservice.id, scopeId: scopeDetail?.value },
        });
      }
    } else {
      return onRolesDropDownOpen();
    }
  };

  return (
    <>
      <section className="text-upper-large">{t('USERS_ASSIGNMENT')}</section>

      <Card>
        <FieldGroup>
          <Input
            name={type === 'USER' ? 'user' : 'group'}
            label={type === 'USER' ? 'USER' : 'GROUP'}
            value={resource.name}
            readOnly
            disabled
            style={{ color: 'black' }}
          />

          {scopeSupport && (
            <Field label="ASSIGN_SCOPE">
              <DropDown
                list={serviceScopesList}
                selectedList={selectedScopeList}
                onSelection={onScopeSelection}
                onOpen={onScopesDropDownOpen}
                hasSearch
                loading={isScopesDropDownLoading}
                containerClass="full-width"
                containerStyle={{ maxWidth: '250px' }}
              />
            </Field>
          )}

          {roleSupport && (
            <Field label="ASSIGN_ROLE">
              <DropDown
                list={scopeSupport ? selectedScopeRoleList : serviceRolesList}
                selectedList={selectedRoleList}
                onSelection={onRoleSelection}
                onOpen={onScopeRoleDropDownOpen}
                hasSearch
                isMulti={multiRoleSupport}
                loading={isRolesDropDownLoading}
                containerClass="full-width"
                containerStyle={{ maxWidth: '250px' }}
              />
            </Field>
          )}
        </FieldGroup>
      </Card>
    </>
  );
};

EditUsersAndGroupsRoleForm.DEFAULT_PROPS = DEFAULT_PROPS;

EditUsersAndGroupsRoleForm.propTypes = {
  detail: PropTypes.object,
  onDetailChange: PropTypes.func,
};

export default EditUsersAndGroupsRoleForm;
