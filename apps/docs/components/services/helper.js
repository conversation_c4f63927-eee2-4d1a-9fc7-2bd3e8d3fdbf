export const modalModeDetail = {
  USER: {
    '': {},
    edit: {
      headerText: 'EDIT_USERS_ROLE',
    },
    delete: {
      headerText: 'DELETE_USERS_ROLE',
      confirmationMessage:
        'Are you sure you want to delete this user assignment? The changes cannot be undone.',
    },
    bulkDelete: {
      headerText: 'BULK_DELETE_USERS_GROUPS_ROLE',
      confirmationMessage:
        'Are you sure you want to bulk delete these users/groups? The changes cannot be undone.',
    },
  },
  GROUP: {
    '': {},
    edit: {
      headerText: 'EDIT_GROUPS_ROLE',
    },
    delete: {
      headerText: 'DELETE_GROUPS_ROLE',
      confirmationMessage:
        'Are you sure you want to delete this group assignment? The changes cannot be undone.',
    },
    bulkDelete: {
      headerText: 'BULK_DELETE_USERS_GROUPS_ROLE',
      confirmationMessage:
        'Are you sure you want to bulk delete these users/groups assignments? The changes cannot be undone.',
    },
  },
};

export const getModalModeDetail = ({
  modalMode,
  selectedServiceDetail: { type },
  isBulkActionEnabled,
}) => {
  if (type && modalMode) {
    return modalModeDetail?.[type]?.[modalMode];
  }

  if (isBulkActionEnabled) {
    return modalModeDetail.USER.bulkDelete;
  }

  return modalModeDetail.USER?.[modalMode];
};

export const getSelectedUsersGroupRolesFormValidationDetail = ({
  selectedUsersRoleOption,
  selectedGroupsRoleOption,
}) => {
  const validationDetail = { ...validationDetail, isValid: false, message: '' };

  const { users, roles: usersRole } = selectedUsersRoleOption[0];
  const { groups, roles: groupsRole } = selectedGroupsRoleOption[0];

  if (
    (users.length !== 0 && usersRole.length !== 0) ||
    (groups.length !== 0 && groupsRole.length !== 0)
  ) {
    validationDetail.isValid = true;
    validationDetail.message = '';
  }

  return validationDetail;
};

export const getSelectedUsersGroupRolesApiPayload = ({
  selectedUsersRoleOption,
  selectedGroupsRoleOption,
}) => {
  const payload = [];

  const resourceRoleMapping = {};
  const resourcesDetail = {};
  const rolesDetail = {};

  selectedUsersRoleOption.forEach(({ users, scope, roles }) => {
    users.forEach(({ value: userId, label: userName }) => {
      roles.forEach(({ value: tsRoleId, label: tsRoleName }) => {
        resourcesDetail[userId] = {
          resource: { id: userId, name: userName },
          type: 'USER',
        };

        const { label: scopeName, value: scopeId } = scope[0] || {};

        if (scopeId) {
          resourcesDetail[userId].tsScope = { id: scopeId, name: scopeName };
        }

        rolesDetail[tsRoleId] = {
          id: tsRoleId,
          name: tsRoleName,
        };

        if (!resourceRoleMapping[userId]) {
          resourceRoleMapping[userId] = [];
        }

        const prevRoles = resourceRoleMapping[userId];

        resourceRoleMapping[userId] = [...new Set([...prevRoles, tsRoleId])];
      });
    });
  });

  selectedGroupsRoleOption.forEach(({ groups, scope, roles }) => {
    groups.forEach(({ value: groupId, label: groupName }) => {
      roles.forEach(({ value: tsRoleId, label: tsRoleName }) => {
        resourcesDetail[groupId] = {
          resource: { id: groupId, name: groupName },
          type: 'GROUP',
        };

        const { label: scopeName, value: scopeId } = scope[0] || {};

        if (scopeId) {
          resourcesDetail[groupId].tsScope = { id: scopeId, name: scopeName };
        }

        rolesDetail[tsRoleId] = {
          id: tsRoleId,
          name: tsRoleName,
        };

        if (!resourceRoleMapping[groupId]) {
          resourceRoleMapping[groupId] = [];
        }

        const prevRoles = resourceRoleMapping[groupId];

        resourceRoleMapping[groupId] = [...new Set([...prevRoles, tsRoleId])];
      });
    });
  });

  for (const resourceId in resourceRoleMapping) {
    const roleIds = resourceRoleMapping[resourceId];

    const tsRoles = roleIds.map((roleId) => rolesDetail[roleId]);

    const resourceAndType = resourcesDetail[resourceId];

    if (tsRoles.length > 0 && resourceAndType?.resource && resourceAndType?.type) {
      payload.push({ ...resourceAndType, tsRoles });
    }
  }

  return payload;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'assignUsers') {
    tooltipDetail.content = (
      <p>
        Select the users that you want to assign to the service. Click{' '}
        <strong className="tooltip-bold">X</strong> to remove a user or{' '}
        <strong className="tooltip-bold">Clear All</strong> to remove all selections.
      </p>
    );
  }

  if (name === 'assignUsersRole') {
    tooltipDetail.content = `Choose the role that you want to assign to the users. The drop-down shows the list of roles configured on the respective services' admin portal.`;
  }

  if (name === 'assignGroups') {
    tooltipDetail.content = (
      <p>
        Select the user groups that you want to assign to the service. Click{' '}
        <strong className="tooltip-bold">X</strong> to remove a user or{' '}
        <strong className="tooltip-bold">Clear All</strong> to remove all selections.
      </p>
    );
  }

  if (name === 'assignGroupsRole') {
    tooltipDetail.content = `Choose the role that you want to assign for the users in the group. The drop-down shows the list of roles configured on the respective services' admin portal.`;
  }

  return tooltipDetail;
};
