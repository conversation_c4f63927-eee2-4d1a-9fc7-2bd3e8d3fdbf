import { Link } from 'react-router-dom';

const NavLinks = () => {
  return (
    <div className="nav-links-container">
      <Link to="/" className="link">
        Home
      </Link>

      <Link to="/form" className="link">
        Forms
      </Link>

      <Link to="/dropdown" className="link">
        Dropdown
      </Link>

      <Link to="/calendar" className="link">
        Calendar
      </Link>

      <Link to="/table" className="link">
        Table
      </Link>

      <Link to="/list-builder" className="link">
        List Builder
      </Link>

      <Link to="/modal" className="link">
        Modals
      </Link>

      <Link to="/tooltip" className="link">
        Tooltip
      </Link>

      <Link to="/tags" className="link">
        Tags
      </Link>

      <Link to="/language-selector" className="link">
        Language Selector
      </Link>
    </div>
  );
};

export default NavLinks;
