import { useState } from 'react';

import ListBuilder from '../../components/listBuilder/ListBuilder';

import { createDOMElement } from '../../utils/dom';

const ListBuilderPage = () => {
  const [list, setList] = useState(['openid']);

  createDOMElement({ tagName: 'span', attributes: { id: 'myID', className: 'anc' } });

  return (
    <div className="component-page is-flex has-jc-c has-ai-c">
      {/* <ListBuilder list={list} setList={setList} separator={/[\r\n,:]+/} /> */}

      <ListBuilder
        list={list}
        setList={setList}
        removeBlackList={['openid']}
        separator={/[\r\n,]+/}
      />
    </div>
  );
};

export default ListBuilderPage;
