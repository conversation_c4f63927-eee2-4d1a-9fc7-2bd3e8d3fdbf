import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faCheckCircle, faChevronDown, faChevronUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop, range } from 'lodash-es';

import { Button, ToggleButton } from '../../../components/buttons';
import Card from '../../../components/cards/Card';
import DropDown from '../../../components/dropdowns/DropDown';
import { Field, FieldGroup, Label } from '../../../components/forms';
import HelpContainer from '../../../components/help/HelpContainer';
import Toast from '../../../components/toast/Toast';

import { createPasswordPolicy, getPasswordPolicy } from '../../ducks/password';
import { CONFIGURATION_TYPES } from '../../ducks/password/constants';
import {
  selectActiveConfigDetail,
  selectActiveConfigurationType,
  selectCustomPasswordPolicyDetail,
  selectIsPasswordPolicyWeek,
  selectRecommendedPasswordPolicyDetail,
} from '../../ducks/password/selectors';

import NavLayout from '../../layout/NavLayout';

import useApiCall from '../../../hooks/useApiCall';
import { HELP_ARTICLES } from '../../config';

const listGenerator = (num) => ({ label: num, value: num });

const passwordLengthList = range(1, 21).map(listGenerator);
const passwordCaseList = range(0, 6).map(listGenerator);
const expiryAgeList = range(1, 10000).map(listGenerator);

const PasswordPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const activeConfigurationType = useSelector(selectActiveConfigurationType);
  const activeConfigDetail = useSelector(selectActiveConfigDetail);

  const recommendedPolicyDetail = useSelector(selectRecommendedPasswordPolicyDetail);
  const customPolicyDetail = useSelector(selectCustomPasswordPolicyDetail);

  const [selectedConfigurationType, setSelectedConfigurationType] =
    useState(activeConfigurationType);

  const [showConfigDetail, setConfigDetail] = useState(true);
  const [showComplexityDetail, setShowComplexityDetail] = useState(true);
  const [showCriteriaDetail, setShowCriteriaDetail] = useState(true);

  const [showUpdateSection, setShowUpdateSection] = useState(false);

  const isRecommended = selectedConfigurationType === 'RECOMMENDED';

  const [formValues, setFormValues] = useState({});

  const isPasswordWeek = selectIsPasswordPolicyWeek(formValues);

  const [selectedListsDetail, setSelectedListsDetail] = useState({
    minLength: [{ label: formValues.minLength, value: formValues.minLength }],
    minLowerCase: [{ label: formValues.minLowerCase, value: formValues.minLowerCase }],
    minUpperCase: [{ label: formValues.minUpperCase, value: formValues.minUpperCase }],
    minNumeric: [{ label: formValues.minNumeric, value: formValues.minNumeric }],
    minSpecialChar: [{ label: formValues.minSpecialChar, value: formValues.minSpecialChar }],
    expiryAge: [{ label: formValues.expiryAge, value: formValues.expiryAge }],
  });

  useEffect(() => {
    apiCall(getPasswordPolicy()).catch(noop);
  }, []);

  const updateActiveDetail = () => {
    setSelectedConfigurationType(activeConfigurationType);

    resetForm(activeConfigDetail);
  };

  const resetForm = (configDetail) => {
    setFormValues({ ...configDetail });

    setSelectedListsDetail({
      minLength: [{ label: configDetail.minLength, value: configDetail.minLength }],
      minLowerCase: [{ label: configDetail.minLowerCase, value: configDetail.minLowerCase }],
      minUpperCase: [{ label: configDetail.minUpperCase, value: configDetail.minUpperCase }],
      minNumeric: [{ label: configDetail.minNumeric, value: configDetail.minNumeric }],
      minSpecialChar: [{ label: configDetail.minSpecialChar, value: configDetail.minSpecialChar }],
      expiryAge: [{ label: configDetail.expiryAge, value: configDetail.expiryAge }],
    });
  };

  useEffect(() => {
    updateActiveDetail();
    setShowUpdateSection(false);
  }, [activeConfigDetail]);

  useEffect(() => {
    if (selectedConfigurationType === CONFIGURATION_TYPES.RECOMMENDED) {
      resetForm(recommendedPolicyDetail);
    }

    if (selectedConfigurationType === CONFIGURATION_TYPES.CUSTOM) {
      resetForm(customPolicyDetail);
    }
  }, [selectedConfigurationType, recommendedPolicyDetail, customPolicyDetail]);

  const updateConfigurationType = (newType) => {
    setSelectedConfigurationType(newType);

    if (newType !== activeConfigurationType) {
      setShowUpdateSection(true);
    } else {
      setShowUpdateSection(false);
    }

    apiCall(getPasswordPolicy({ configurationType: newType })).catch(noop);
  };

  const renderConfigurationSection = () => {
    return (
      <>
        <section
          className="text-upper-large collapsable"
          onClick={() => {
            setConfigDetail((prevState) => !prevState);
          }}
        >
          <FontAwesomeIcon
            className="icon left"
            icon={showConfigDetail ? faChevronDown : faChevronUp}
          />

          <span>{t('CONFIGURATION')}</span>
        </section>

        {showConfigDetail && (
          <Card containerClass="is-flex configuration-type">
            <Field label="CONFIGURATION_TYPE">
              <div className="is-flex config-buttons">
                <Label
                  tooltip={{
                    content:
                      'Select this to enable Zscaler-recommended password policy configuration',
                  }}
                >
                  <Button
                    type={isRecommended ? 'primary' : ''}
                    onClick={() => {
                      updateConfigurationType('RECOMMENDED');
                    }}
                  >
                    {isRecommended && (
                      <FontAwesomeIcon className="icon left" icon={faCheckCircle} />
                    )}

                    <span>{t('RECOMMENDED')}</span>
                  </Button>
                </Label>
                <Label
                  tooltip={{
                    content: 'Select this to configure password policy for ZSLogin users',
                  }}
                >
                  <Button
                    type={!isRecommended ? 'primary' : ''}
                    onClick={() => {
                      updateConfigurationType('CUSTOM');
                    }}
                  >
                    {!isRecommended && (
                      <FontAwesomeIcon className="icon left" icon={faCheckCircle} />
                    )}

                    <span>{t('CUSTOM')}</span>
                  </Button>
                </Label>
              </div>
            </Field>

            {isPasswordWeek && (
              <Toast type="warning">
                <span>
                  {`The custom password policy is weaker than Zscaler's recommended password policy. We strongly advise to review the password policy with your Organisation's Security team.`}
                </span>
              </Toast>
            )}
          </Card>
        )}
      </>
    );
  };

  const onSelection = (name, detail) => {
    const { value } = detail[0] || {};

    setFormValues((prevState) => {
      setSelectedListsDetail((prevState) => ({
        ...prevState,
        [name]: [{ label: value, value: value }],
      }));

      return { ...prevState, [name]: value };
    });

    setShowUpdateSection(true);
  };

  const renderComplexitySection = () => {
    return (
      <>
        <section
          className="text-upper-large collapsable"
          onClick={() => {
            setShowComplexityDetail((prevState) => !prevState);
          }}
        >
          <FontAwesomeIcon
            className="icon left"
            icon={showComplexityDetail ? faChevronDown : faChevronUp}
          />

          <span>{t('PASSWORD_COMPLEXITY')}</span>
        </section>

        {showComplexityDetail && (
          <Card>
            <FieldGroup containerClass="has-width-auto">
              <Field
                label="PASSWORD_LENGTH"
                tooltip={{
                  content: 'Set the maximum length of the password',
                }}
              >
                <DropDown
                  list={passwordLengthList}
                  selectedList={selectedListsDetail.minLength}
                  onSelection={(detail) => {
                    onSelection('minLength', detail);
                  }}
                  readOnly={isRecommended}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
            <FieldGroup>
              <Field
                label="MIN_LWR_CASE"
                tooltip={{
                  content: 'Set the minimum number of lowercase letters required in the password',
                }}
              >
                <DropDown
                  list={passwordCaseList}
                  selectedList={selectedListsDetail.minLowerCase}
                  onSelection={(detail) => {
                    onSelection('minLowerCase', detail);
                  }}
                  readOnly={isRecommended}
                  disabled={isRecommended}
                />
              </Field>
              <Field
                label="MIN_UPPR_CASE"
                tooltip={{
                  content: 'Set the minimum number of uppercase letters required in the password',
                }}
              >
                <DropDown
                  list={passwordCaseList}
                  selectedList={selectedListsDetail.minUpperCase}
                  onSelection={(detail) => {
                    onSelection('minUpperCase', detail);
                  }}
                  readOnly={isRecommended}
                  disabled={isRecommended}
                />
              </Field>
              <Field
                label="MIN_NUMERIC"
                tooltip={{
                  content: 'Set the minimum number of numeric characters required in the password',
                }}
              >
                <DropDown
                  list={passwordCaseList}
                  selectedList={selectedListsDetail.minNumeric}
                  onSelection={(detail) => {
                    onSelection('minNumeric', detail);
                  }}
                  readOnly={isRecommended}
                  disabled={isRecommended}
                />
              </Field>
              <Field
                label="MIN_SPL_CHAR"
                tooltip={{
                  content: 'Set the minimum number of special characters required in the password',
                }}
              >
                <DropDown
                  list={passwordCaseList}
                  selectedList={selectedListsDetail.minSpecialChar}
                  onSelection={(detail) => {
                    onSelection('minSpecialChar', detail);
                  }}
                  readOnly={isRecommended}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
          </Card>
        )}
      </>
    );
  };

  const renderCriteriaSection = () => {
    const onToggleClick = (formName) => {
      setFormValues((prevState) => ({ ...prevState, [formName]: !prevState[formName] }));

      setShowUpdateSection(true);
    };

    return (
      <>
        <section
          className="text-upper-large collapsable"
          onClick={() => {
            setShowCriteriaDetail((prevState) => !prevState);
          }}
        >
          <FontAwesomeIcon
            className="icon left"
            icon={showCriteriaDetail ? faChevronDown : faChevronUp}
          />

          <span>{t('PASSWORD_CRITERIA')}</span>
        </section>

        {showCriteriaDetail && (
          <Card>
            <FieldGroup>
              <Field
                label="PASSWORD_DOES_NOT_INCLUDE"
                containerClass="field-stacked"
                tooltip={{
                  content:
                    'Turn on to restrict the user from including their company name, username, first name, or last name in their password',
                }}
              >
                <ToggleButton
                  isOn={formValues.excludeNames}
                  onToggleClick={() => {
                    onToggleClick('excludeNames');
                  }}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
            <FieldGroup>
              <Field
                label="REJECT_REUSE"
                containerClass="field-stacked"
                tooltip={{
                  content: 'Turn on to restrict users from reusing their last 5 passwords',
                }}
              >
                <ToggleButton
                  isOn={formValues.disallowRecentPassword}
                  onToggleClick={() => {
                    onToggleClick('disallowRecentPassword');
                  }}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
            <FieldGroup>
              <Field
                label="DEACTIVATE_USER"
                containerClass="field-stacked"
                tooltip={{
                  content:
                    'Turn on to deactivate the user account after 10 unsuccessful login attempts',
                }}
              >
                <ToggleButton
                  isOn={formValues.deactivateAfterUnsuccessfulAttempts}
                  onToggleClick={() => {
                    onToggleClick('deactivateAfterUnsuccessfulAttempts');
                  }}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
            <FieldGroup>
              <Field
                label="ALLOW_ADMIN_SET_PASSWORD"
                containerClass="field-stacked"
                tooltip={{
                  content: "Turn on to allow the administrator to create or change user's password",
                }}
              >
                <ToggleButton
                  isOn={formValues.allowAdminSetPasswords}
                  onToggleClick={() => {
                    onToggleClick('allowAdminSetPasswords');
                  }}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
            <FieldGroup>
              <Field
                label="FORCE_PASSWORD_CHANGE"
                containerClass="field-stacked"
                tooltip={{
                  content:
                    'Turn on to prompt users to change their password after the initial login',
                }}
              >
                <ToggleButton
                  isOn={formValues.forcePasswordChange}
                  onToggleClick={() => {
                    onToggleClick('forcePasswordChange');
                  }}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
            <FieldGroup>
              <Field
                label="PASSWORD_EXPIRY"
                containerClass="field-stacked"
                tooltip={{
                  content:
                    'Set the password expiration period after which users can reset their passwords. You can set the expiration period from 1 to 9999 days.',
                }}
              >
                <DropDown
                  list={expiryAgeList}
                  selectedList={selectedListsDetail.expiryAge}
                  onSelection={(detail) => {
                    onSelection('expiryAge', detail);
                  }}
                  containerStyle={{ width: '60px' }}
                  selectedItemsProps={{ containerStyle: { padding: '8px' } }}
                  readOnly={isRecommended}
                  disabled={isRecommended}
                />
              </Field>
            </FieldGroup>
          </Card>
        )}
      </>
    );
  };

  const savePassword = () => {
    apiCall(
      createPasswordPolicy({ configurationType: selectedConfigurationType, config: formValues }),
    ).catch(noop);
  };

  const cancelPassword = () => {
    updateActiveDetail();
    setShowUpdateSection(false);
  };

  const renderActionsSection = () => {
    return (
      <div className="action-section buttons">
        <Button
          onClick={() => {
            savePassword();
          }}
          disabled={!showUpdateSection}
        >
          {t('SAVE')}
        </Button>
        <Button
          type="tertiary"
          containerClass="no-p-l"
          onClick={() => {
            cancelPassword();
          }}
          disabled={!showUpdateSection}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.PASSWORD_POLICY} />
      <article id="password-policy" className="page-container">
        <section className="heading-small page-title">{t('PASSWORD_POLICY')}</section>

        <div className={`config-content ${showUpdateSection ? 'has-update-section' : ''}`}>
          {renderConfigurationSection()}
          {renderComplexitySection()}
          {renderCriteriaSection()}
        </div>

        {renderActionsSection()}
      </article>
    </NavLayout>
  );
};

export default PasswordPage;
