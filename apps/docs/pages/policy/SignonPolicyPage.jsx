import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';

import { Button } from '../../../components/buttons';
import HelpContainer from '../../../components/help/HelpContainer';
import CRUDModal from '../../../components/modal/CRUDModal';
import TableContainer from '../../../components/table/TableContainer';
import Actions from '../../../components/table/components/Actions';
import InlineText from '../../../components/table/components/InlineText';
import StatusTag from '../../../components/tags/StatusTag';
import SignonPolicyForm from '../../components/signon-policies/SignonPolicyForm';
import { modalModeDetail } from '../../components/signon-policies/helper';

import { add, getList, remove, update } from '../../ducks/signon-policies';
import { selectTableConfig, selectTableDetail } from '../../ducks/signon-policies/selectors';

import NavLayout from '../../layout/NavLayout';

import useApiCall from '../../../hooks/useApiCall';
import { HELP_ARTICLES } from '../../config';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_POLICY_DETAIL = {};
const tableShowMoreList = [
  { label: 'ENABLE', value: 'ENABLE' },
  { label: 'DISABLE', value: 'DISABLE' },
];

const SignonPolicyPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  // '', 'add', 'edit', 'delete'
  const [modalMode, setModalMode] = useState(DEFAULT_MODAL_MODE);
  const [policyDetail, setPolicyDetail] = useState(DEFAULT_POLICY_DETAIL);
  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'criteria') {
        const CriteriaCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const conditions = props?.row?.original?.conditions;

          return (
            <InlineText>
              {conditions
                .map(({ field, op, value: { name } }) => `${t(field)} ${t(op)} ${name ? name : ''}`)
                .join(', ') || ''}
            </InlineText>
          );
        };

        columnDetail.cell = CriteriaCellComponent;
      }

      if (columnDetail.id === 'action') {
        const RuleActionCellComponent = (props) =>
          // eslint-disable-next-line react/prop-types
          t(props?.row?.original?.action);

        columnDetail.cell = RuleActionCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const disabled = props?.row?.original?.disabled;

          return <StatusTag value={!disabled} type="ENABLED_DISABLED" />;
        };

        columnDetail.cell = StatusCellComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            showMore
            list={tableShowMoreList}
            onActionClick={onShowMoreClick}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  const onAddClick = () => {
    setModalMode('add');
  };

  const onEditClick = (detail) => {
    if (detail) {
      setPolicyDetail(detail);
      setModalMode('edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setPolicyDetail(detail);
      setModalMode('delete');
    }
  };

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setPolicyDetail(DEFAULT_POLICY_DETAIL);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add({ ruleOrder: tableDetail.data.length + 1, ...policyDetail }))
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(policyDetail)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(policyDetail)).then(onCloseClick).catch(noop);
    }
  };

  const onShowMoreClick = (detail, payload = {}) => {
    const policyDetail = payload?.row?.original || {};

    const { setIsOpen = noop } = payload;

    if (detail && policyDetail) {
      apiCall(update({ ...policyDetail, disabled: detail === 'DISABLE' }))
        .catch(noop)
        .finally(() => {
          setIsOpen?.(false);
        });
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.SIGNON_POLICY} />
      <article id="signon-policy" className="page-container">
        <section className="heading-small page-title">{t('SIGN_ON_POLICIES')}</section>

        <div className="is-flex has-jc-sb" style={{ marginTop: '2rem' }}>
          <div className="buttons">
            <Button onClick={onAddClick}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              <span>{t('ADD_RULE')}</span>
            </Button>
          </div>
        </div>

        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          data={tableDetail.data}
          pagination={{ ...tableDetail, onLoadMoreClick }}
        />

        {modalMode && (
          <CRUDModal
            mode={modalMode}
            renderFormSection={(props) => (
              <SignonPolicyForm {...props} onDetailChange={setPolicyDetail} detail={policyDetail} />
            )}
            onSaveClick={onSaveClick}
            onCloseClick={onCloseClick}
            {...modalModeDetail[modalMode]}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default SignonPolicyPage;
