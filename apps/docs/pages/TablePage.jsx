import dayjs from 'dayjs';

import { Selector } from '../../components/table';
import TableContainer from '../../components/table/TableContainer';
import DiffViewerModal from '../../components/viewer/DiffViewerModal';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'selection',
    Header: '',
    cell: (props) => <Selector {...props} />,
    size: 100,
    disableSortBy: true,
    defaultCanSort: false,
  },
  {
    id: 'number',
    Header: 'TABLE_NUMBER',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'timestamp',
    accessorFn: (row) => dayjs(row.timestamp).format('MMMM DD, YYYY - hh:mm A') || 0,
    Header: 'TIME_STAMP',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'actionType',
    accessorFn: (row) => row.actionType || '',
    Header: 'ACTION',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'resourceName',
    accessorFn: (row) => row.objectName || false,
    Header: 'RESOURCE',
    minSize: 250,
    size: 250,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
  {
    id: 'userName',
    accessorFn: (row) => row.userName || false,
    Header: 'ADMIN_ID',
    minSize: 150,
    size: 150,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
  {
    id: 'clientIP',
    accessorFn: (row) => row.clientIP || false,
    Header: 'CLIENT_IP',
    minSize: 150,
    size: 150,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
  {
    id: 'actionInterface',
    accessorFn: (row) => row.actionInterface || false,
    Header: 'INTERFACE',
    minSize: 100,
    size: 100,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
  {
    id: 'actionResult',
    accessorFn: (row) => row.actionResult || 'string',
    Header: 'RESULT',
    minSize: 100,
    size: 100,
    defaultanSort: true,
  },
  {
    id: 'category',
    accessorFn: (row) => row.category || '',
    Header: 'CATEGORY',
    minSize: 250,
    size: 250,
    defaultanSort: true,
  },
  {
    id: 'subcategory',
    accessorFn: (row) => row.subcategory || '',
    Header: 'SUB_CATEGORY',
    minSize: 250,
    size: 250,
    defaultanSort: true,
  },
  {
    id: 'dataBeforeAfter',
    accessorFn: (row) => JSON.stringify(row.dataBefore),
    Header: '',
    cell: (props) => {
      // eslint-disable-next-line react/prop-types
      const { dataBefore, dataAfter, ...rest } = props?.row?.original || {};

      if (dataBefore || dataAfter) {
        return <DiffViewerModal oldValue={dataBefore} newValue={dataAfter} {...rest} />;
      }

      return null;
    },
    minSize: 150,
    size: 150,
    defaultanSort: true,
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS.splice(0),
  initialState: {
    sortBy: [{ id: 'number' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  showRowTooltip: false,
};

const TablePage = () => {
  return (
    <div
      className="page table-page is-flex has-fd-c has-jc-c has-ai-c"
      style={{ marginTop: '3rem' }}
    >
      <div style={{ width: '75vw' }}>
        <p> Table page </p>

        <TableContainer
          {...DEFAULT_TABLE_CONFIG}
          data={tableData.records}
          containerClass="full-width"
        />
      </div>
    </div>
  );
};

const tableData = {
  totalRecord: 28,
  pageOffset: 0,
  pageSize: 100,
  records: [
    {
      timestamp: 1666372637232,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [
          { id: 'i8ovtkg4l022u', name: 'Global Admin' },
          { id: 'i8pkp1urc02a4', name: 'This is the test group' },
        ],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'FAILURE',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666372633514,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [
          { id: 'i8ovtkg4l022u', name: 'Global Admin' },
          { id: 'i8pkp1urc02a4', name: 'This is the test group' },
        ],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'FAILURE',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666372531811,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [
          { id: 'i8ovtkg4l022u', name: 'Global Admin' },
          { id: 'i8pkp1urc02a4', name: 'This is the test group' },
        ],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'FAILURE',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666372527748,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [
          { id: 'i8ovtkg4l022u', name: 'Global Admin' },
          { id: 'i8pkp1urc02a4', name: 'This is the test group' },
        ],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'FAILURE',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666372494498,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [
          { id: 'i8ovtkg4l022u', name: 'Global Admin' },
          { id: 'i8pkp1urc02a4', name: 'This is the test group' },
        ],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'FAILURE',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666372481932,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [
          { id: 'i8ovtkg4l022u', name: 'Global Admin' },
          { id: 'i8pkp1urc02a4', name: 'This is the test group' },
        ],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'FAILURE',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666372448729,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        'Custom Attrs Info': { a: '10/01/2022', Boolean: 'False' },
        'Display Name': 'Rupesh Kumar Singh',
        'First Name': 'Rupesh Kumar',
        Id: 'igqg0k78t02ek',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataBefore: {
        'Custom Attrs Info': { a: '10/01/2022', Boolean: 'False' },
        'Display Name': 'Rupesh Kumar Singh',
        'First Name': 'Rupesh Kumar',
        Id: 'igqg0k78t02ek',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666371309271,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.CustomUserAttributeDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        'Attr Name':
          'thistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltip',
        Comment:
          'thistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltip',
        'Data Type': 'STRING',
        'Display Name':
          'thistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextcontainstooltipthistextconta',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'CUSTOM_USER_ATTRIBUTE',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
    {
      timestamp: 1666368920399,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        'Custom Attrs Info': { a: '10/01/2022', Boolean: 'False' },
        'Display Name': 'Rupesh Kumar Singh',
        'First Name': 'Rupesh Kumar',
        Id: 'igqg0k78t02ek',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataBefore: {
        'Custom Attrs Info': { a: '10/01/2022', Boolean: 'True' },
        'Display Name': 'Rupesh Kumar Singh',
        'First Name': 'Rupesh Kumar',
        Id: 'igqg0k78t02ek',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666368842279,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        'Custom Attrs Info': { a: '10/01/2022', Boolean: 'True' },
        'Display Name': 'Rupesh Kumar Singh',
        'First Name': 'Rupesh Kumar',
        Id: 'igqg0k78t02ek',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666334526698,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'ALLOW',
        Conditions: [{ op: 'LTE', field: 'LOCATION', value: { id: 'l8qfe1ccrg2dg' } }],
        Description: 'asdas',
        Disabled: true,
        Id: 'h8qfg8jjm02eg',
        Name: ' asfklnasf',
        'Rule Order': 2,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
    {
      timestamp: 1666334520481,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'FAILURE',
      actionInterface: 'API',
    },
    {
      timestamp: 1666334428024,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'DENY',
        Conditions: [{ op: 'LTE', field: 'LOCATION', value: { id: 'l8qfe1ccrg2dg' } }],
        Description: 'asfa',
        Disabled: true,
        Id: 'h8qfg73dt02ec',
        Name: 'internal',
        'Rule Order': 1,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'internal',
    },
    {
      timestamp: 1666334397977,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'ALLOW',
        Conditions: [{ op: 'NEQ', field: 'LOCATION', value: { id: 'l8qfe1ccrg2dg' } }],
        Description: 'Policy',
        Disabled: false,
        Id: 'h8qfg6kodg2e8',
        Name: 'Policy',
        'Rule Order': 1,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'Policy',
    },
    {
      timestamp: 1666333607383,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'ALLOW',
        Conditions: [{ op: 'IN', field: 'LOCATION_GROUP', value: { id: 'lgqfe1odng2dk' } }],
        Description: 'abc',
        Disabled: false,
        Id: 'h8qffqincg2e4',
        Name: 'new policy 5',
        'Rule Order': 3,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'new policy 5',
    },
    {
      timestamp: 1666333175721,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'DENY',
        Conditions: [{ op: 'GT', field: 'LOCATION_GROUP', value: { id: 'lgqfe1odng2dk' } }],
        Description: 'sign on policy',
        Disabled: false,
        Id: 'h8qffjvulg2e0',
        Name: 'test idp',
        'Rule Order': 2,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'test idp',
    },
    {
      timestamp: 1666331843775,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'DENY',
        Conditions: [{ op: 'NEQ', field: 'LOCATION', value: { id: 'l8qfe1ccrg2dg' } }],
        Description: 'Next policy',
        Disabled: false,
        Id: 'h8qfevlj9g2ds',
        Name: 'policy 2',
        'Rule Order': 2,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'policy 2',
    },
    {
      timestamp: 1666331621444,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.SignOnPolicyDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Action: 'ALLOW',
        Conditions: [{ op: 'NOT', field: 'LOCATION_GROUP', value: { id: 'lgqfe1odng2dk' } }],
        Disabled: false,
        Id: 'h8qfes913g2do',
        Name: 'new policy',
        'Rule Order': 1,
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'SIGN_ON_POLICY',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'new policy',
    },
    {
      timestamp: 1666329883499,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.LocationGroupDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Description: 'India location groups',
        Id: 'lgqfe1odng2dk',
        Locations: [{ id: 'l8qfe1ccrg2dg' }],
        Name: 'india group',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'LOCATION_GROUP',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'india group',
    },
    {
      timestamp: 1666329858868,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.LocationDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        Country: { id: 'IN' },
        Id: 'l8qfe1ccrg2dg',
        'Ip In Locations': ['***********'],
        Name: 'india',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'LOCATIONS',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: 'india',
    },
    {
      timestamp: 1666194737663,
      actionType: 'UPDATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'True' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [{ id: 'i8ovtkg4l022u' }, { id: 'i8pkp1urc02a4' }],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataBefore: {
        'Custom Attrs Info': { a: '07/01/2021', Boolean: 'False' },
        'Display Name': 'Rupesh Singh',
        'First Name': 'Rupesh',
        Groups: [{ id: 'i8ovtkg4l022u', name: 'Global Admin' }],
        Id: 'igp2ls3elg26s',
        Language: 'English (US)',
        'Last Name': 'Singh',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666161095195,
      actionType: 'IMPORT',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
    {
      timestamp: 1666160524618,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.managesvc.dto.IdpProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'AUTHENTICATION_SETTINGS',
      subcategory: 'IDENTITY_PROVIDERS',
      actionResult: 'FAILURE',
      actionInterface: 'API',
    },
    {
      timestamp: 1666160392574,
      actionType: 'IMPORT',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
    {
      timestamp: 1666160295541,
      actionType: 'IMPORT',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
    {
      timestamp: 1666157750251,
      actionType: 'CREATE',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataAfter: {
        'Custom Attrs Info': { a: '10/04/2022', location: 'Bangalore,Karnataka', Boolean: 'True' },
        'Display Name': 'pratibha nayak',
        'First Name': 'pratibha',
        Groups: [{ id: 'i8ovtkg4l022u' }],
        Id: 'igqcrv70dg2ck',
        Language: 'English (US)',
        'Last Name': 'nayak',
        'Login Name': '<EMAIL>',
        'Primary Email': '<EMAIL>',
        Source: 'UI',
        Status: true,
        'Time Zone': '(UTC+00:00) GMT',
      },
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
      objectName: '<EMAIL>',
    },
    {
      timestamp: 1666157568397,
      actionType: 'LINK',
      resourceName: 'com.zscaler.oidp.dal.domain.TserviceProfile',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'TENANTS',
      subcategory: 'SERVICES',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
    {
      timestamp: 1666110473709,
      actionType: 'IMPORT',
      resourceName: 'com.zscaler.oidp.dto.UserProfileDto',
      userId: 'g400000000082',
      userName: 'null',
      dataACollection: false,
      clientIP: '*************',
      category: 'USER_MANAGEMENT',
      subcategory: 'USER',
      actionResult: 'SUCCESS',
      actionInterface: 'API',
    },
  ],
};

export default TablePage;
