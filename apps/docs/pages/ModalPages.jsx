import { useEffect, useState } from 'react';

import Button from '../../components/buttons/Button';
import CRUDModal from '../../components/modal/CRUDModal';
import Modal from '../../components/modal/Modal';
import ModalFooter from '../../components/modal/ModalFooter';
import ModalHeader from '../../components/modal/ModalHeader';

import { setCRUDModalRootId, setModalRootId } from '../../config/floating';

const testModalConfig = () => {
  setModalRootId('zui-component-doc-portal');
  setCRUDModalRootId('zui-component-doc-crud-portal');

  setTimeout(() => {
    setCRUDModalRootId('');
  }, 10 * 1000);
};

const ModalPage = () => {
  const [showCRUDModal, setShowCRUDModal] = useState(false);
  const [showGenericModal, setShowGenericModal] = useState(false);

  const onToggleCRUDModal = () => {
    setShowCRUDModal((prevState) => !prevState);
  };

  const onToggleGenericModal = () => {
    setShowGenericModal((prevState) => !prevState);
  };

  useEffect(() => {
    testModalConfig();
  }, []);

  return (
    <div className="page modal-page is-flex has-jc-c has-ai-c">
      <div className="buttons"></div>
      <Button onClick={onToggleCRUDModal}>Toggle CRUD Modal</Button>
      <Button onClick={onToggleGenericModal}>Toggle Generic Modal</Button>

      <div className="is-flex">
        <CRUDModal
          mode={showCRUDModal ? 'actionConfirmation' : ''}
          headerText="Dynamic root test"
          confirmationMessage="This is test confirmation"
          onCloseClick={onToggleCRUDModal}
          onSaveClick={onToggleCRUDModal}
        />
        <Modal mode="actionConfirmation" show={showGenericModal}>
          <ModalHeader> Generic Header </ModalHeader>
          <ModalFooter onSave={onToggleGenericModal} onCancel={onToggleGenericModal} />
        </Modal>
      </div>
    </div>
  );
};

export default ModalPage;
