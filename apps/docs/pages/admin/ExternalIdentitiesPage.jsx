import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';

import { Button } from '../../../components/buttons';
import HelpContainer from '../../../components/help/HelpContainer';
import CRUDModal from '../../../components/modal/CRUDModal';
import Tab from '../../../components/tabs/Tab';
import Tabs from '../../../components/tabs/Tabs';
import ExternalIdentitiesForm from '../../components/external-identities/ExternalIdentitiesForm';
import IDPTable from '../../components/external-identities/IDPTable';
import { getModalModeDetail } from '../../components/external-identities/helper';

import {
  add,
  clearNewIdpDetail,
  getNewIdpDetail,
  remove,
  resetMetadata,
  update,
} from '../../ducks/external-identities';
import { selectPrimaryIdpTableDetail } from '../../ducks/external-identities/selectors';

import NavLayout from '../../layout/NavLayout';

import useApiCall from '../../../hooks/useApiCall';
import { HELP_ARTICLES } from '../../config';

const TABS = {
  PRIMARY: 'PRIMARY',
  SECONDARY: 'SECONDARY',
};

const DEFAULT_MODAL_MODE = '';
const DEFAULT_IDENTITY_DETAIL = {};

const ExternalIdentitesPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { apiCall } = useApiCall();

  // '', 'add', 'edit', 'delete'
  const [modalMode, setModalMode] = useState(DEFAULT_MODAL_MODE);
  const [identityDetail, setIdentityDetail] = useState(DEFAULT_IDENTITY_DETAIL);

  const [selectedTab, setSelectedTab] = useState(TABS.PRIMARY);

  const { data: primaryIdpData } = useSelector(selectPrimaryIdpTableDetail);

  useEffect(() => {
    const isAddMode = modalMode === 'add';

    return () => {
      if (isAddMode) {
        dispatch(clearNewIdpDetail());
      }
    };
  }, [modalMode]);

  const onAddClick = () => {
    setModalMode('add');
    dispatch(getNewIdpDetail({ defaultIdp: selectedTab === TABS.PRIMARY }));
  };

  const onEditClick = (detail) => {
    if (detail) {
      setIdentityDetail(detail);
      setModalMode('edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setIdentityDetail(detail);
      setModalMode('delete');
    }
  };

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setIdentityDetail(DEFAULT_IDENTITY_DETAIL);
    dispatch(resetMetadata());
  };

  const onSaveClick = () => {
    if (!identityDetail?.configInfo?.samlIdpCertificateId) {
      delete identityDetail.configInfo.samlIdpCertificateId;
    }

    if (!identityDetail?.configInfo?.samlIdpCertificateHash) {
      delete identityDetail.configInfo.samlIdpCertificateHash;
    }

    if (modalMode === 'add') {
      apiCall(add({ ...identityDetail, default: selectedTab === TABS.PRIMARY }))
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update({ ...identityDetail, default: selectedTab === TABS.PRIMARY }))
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove({ ...identityDetail, default: selectedTab === TABS.PRIMARY }))
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const renderIDPsTabsSelectionSection = () => {
    return (
      <Tabs>
        <Tab
          label="PRIMARY_IDP_PROVIDER"
          isActive={selectedTab === TABS.PRIMARY}
          onClick={() => {
            setSelectedTab(TABS.PRIMARY);
          }}
        />

        <Tab
          label={'SECONDARY_IDP_PROVIDER'}
          isActive={selectedTab === TABS.SECONDARY}
          onClick={() => {
            setSelectedTab(TABS.SECONDARY);
          }}
        />
      </Tabs>
    );
  };

  const renderPrimaryIDPSection = () => {
    return (
      <IDPTable
        isIdpPrimary
        onAddClick={onAddClick}
        onEditClick={onEditClick}
        onDeleteClick={onDeleteClick}
      />
    );
  };

  const renderSecondaryIDPSection = () => {
    return (
      <>
        <div className="is-flex has-jc-sb">
          <div className="buttons">
            <Button onClick={onAddClick} disabled={primaryIdpData.length === 0}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              <span>{t('ADD_SECONDARY_IDP')}</span>
            </Button>
          </div>
        </div>

        <IDPTable onAddClick={onAddClick} onEditClick={onEditClick} onDeleteClick={onDeleteClick} />
      </>
    );
  };

  const renderIDPsSelectedTabSection = () => {
    if (selectedTab === TABS.SECONDARY) {
      return renderSecondaryIDPSection();
    }

    return renderPrimaryIDPSection();
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.EXTERNAL_IDENTITIES} />
      <article id="external-identities-page" className="page-container">
        <section className="heading-small page-title">{t('EXTERNAL_IDENTITIES')}</section>

        {renderIDPsTabsSelectionSection()}

        {renderIDPsSelectedTabSection()}

        {modalMode && (
          <CRUDModal
            mode={modalMode}
            containerClass="external-identities-form-modal"
            renderFormSection={(props) => (
              <ExternalIdentitiesForm
                {...props}
                onDetailChange={setIdentityDetail}
                detail={identityDetail}
              />
            )}
            onSaveClick={onSaveClick}
            onCloseClick={onCloseClick}
            {...getModalModeDetail({ defaultIdp: selectedTab === TABS.PRIMARY })[modalMode]}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default ExternalIdentitesPage;
