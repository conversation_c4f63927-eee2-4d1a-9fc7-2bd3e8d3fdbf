import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faAngleRight, faAngleUp } from '@fortawesome/pro-light-svg-icons';
import { faEye } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';

import { Search } from '../../../components/forms';
import HelpContainer from '../../../components/help/HelpContainer';
import TableContainer from '../../../components/table/TableContainer';
import Actions from '../../../components/table/components/Actions';
import AddUsersAndGroupsModal from '../../components/services/AddUsersAndGroupsModal';
import EditServiceModal from '../../components/services/EditServiceModal';
import UsersAndGroupsTable from '../../components/services/UsersAndGroupsTable';

import { getList, getServiceConstraints } from '../../ducks/services';
import {
  selectServiceConstraintDetail,
  selectServiceConstraints,
  selectTableConfig,
  selectTableDetail,
} from '../../ducks/services/selectors';

import NavLayout from '../../layout/NavLayout';

import useApiCall from '../../../hooks/useApiCall';
import { HELP_ARTICLES } from '../../config';
import BILOGO from '../../images/BI.png';
import CCPLOGO from '../../images/CCP.png';
import ECLOGO from '../../images/EC.svg';
import IAMLOGO from '../../images/IAM.png';
import ZDXLOGO from '../../images/ZDX.svg';
import ZIALOGO from '../../images/ZIA.svg';
import ZPALOGO from '../../images/ZPA.svg';

const getAppIcon = (value) => {
  if (value === 'ZPA') {
    return ZPALOGO;
  } else if (value === 'ZIAM') {
    return IAMLOGO;
  } else if (value === 'ZIA') {
    return ZIALOGO;
  } else if (value === 'ZDX') {
    return ZDXLOGO;
  } else if (value === 'ZCC') {
    return ECLOGO;
  } else if (value === 'ZCCP') {
    return CCPLOGO;
  } else if (value === 'ZBI') {
    return BILOGO;
  } else {
    return ZPALOGO;
  }
};

const ServicesPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const [showEditServiceModal, setShowEditServiceModal] = useState(false);
  const [editServiceDetail, setEditServiceDetail] = useState({});

  const [showAddUsersAndGroupsModal, setShowAddUsersAndGroupsModal] = useState(false);
  const [addUsersAndGroupsServiceDetail, setAddUsersAndGroupsServiceDetail] = useState({});

  const tableConfig = useSelector(selectTableConfig);
  const updatedTableDetail = useSelector(selectTableDetail);

  const serviceConstraints = useSelector(selectServiceConstraints);

  const [tableDetail, setTableDetail] = useState(updatedTableDetail);

  useEffect(() => {
    apiCall(getServiceConstraints()).catch(noop);
  }, []);

  useEffect(() => {
    if (!showEditServiceModal) {
      setTableDetail(updatedTableDetail);
    }
  }, [showEditServiceModal, updatedTableDetail]);

  const onEditClick = (detail, { table }) => {
    table?.toggleAllRowsExpanded(false);
    setEditServiceDetail(detail);
    setShowEditServiceModal(true);
  };

  const onAddUsersClick = (data, { table }) => {
    table?.toggleAllRowsExpanded(false);
    setAddUsersAndGroupsServiceDetail(data);
    setShowAddUsersAndGroupsModal(true);
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'serviceName') {
        const ServiceName = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              original: { serviceDescription, serviceName },
              // eslint-disable-next-line react/prop-types
              getIsExpanded,
              // eslint-disable-next-line react/prop-types
              toggleExpanded,
            },
            // eslint-disable-next-line react/prop-types
            table,
          } = props;

          const isExpanded = getIsExpanded();

          const onToggleExpandClick = () => {
            // eslint-disable-next-line react/prop-types
            table?.toggleAllRowsExpanded(false);
            toggleExpanded(!isExpanded);
          };

          return (
            <>
              <div className="is-flex has-ai-c pointer" onClick={onToggleExpandClick}>
                <FontAwesomeIcon
                  icon={isExpanded ? faAngleUp : faAngleRight}
                  size="2x"
                  style={{ marginLeft: '0.25em' }}
                />

                <span style={{ width: '32px', height: '32px', margin: '0 1em' }}>
                  <img
                    src={getAppIcon(serviceName)}
                    style={{ height: '100%' }}
                    alt="service name"
                  />
                </span>
                <span> {serviceDescription}</span>
              </div>
            </>
          );
        };

        columnDetail.cell = ServiceName;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          const { assignResource = true } = selectServiceConstraintDetail(
            serviceConstraints,
            // eslint-disable-next-line react/prop-types
            props?.row?.original?.serviceName,
          );

          return (
            <Actions
              {...props}
              onEditClick={onEditClick}
              editIcon={faEye}
              showDelete={false}
              showAddUsers={assignResource}
              onAddUsersClick={onAddUsersClick}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, serviceConstraints]);

  useEffect(() => {
    if (!showEditServiceModal) {
      apiCall(getList()).catch(noop);
    }
  }, [showEditServiceModal]);

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);
  };

  const ExpandedRowContainer = (props) => <UsersAndGroupsTable {...props} />;

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.ZSCALER_SERVICES} />
      <article id="admin-services" className="page-container">
        <div className="is-flex has-jc-sb">
          <section className="heading-small page-title">{t('ZS_SERVICES')}</section>

          <Search onSearch={onSearchEnter} containerStyle={{ maxWidth: '260px' }} />
        </div>

        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          data={tableDetail.data}
          pagination={{ ...tableDetail, onLoadMoreClick }}
          renderExpandedRowContainer={ExpandedRowContainer}
        />

        {showEditServiceModal && (
          <EditServiceModal
            showModal={showEditServiceModal}
            detail={editServiceDetail}
            onCloseClick={() => {
              setShowEditServiceModal(false);
              setEditServiceDetail({});
            }}
          />
        )}

        {showAddUsersAndGroupsModal && (
          <AddUsersAndGroupsModal
            showModal={showAddUsersAndGroupsModal}
            serviceDetail={addUsersAndGroupsServiceDetail}
            onCloseClick={() => {
              setAddUsersAndGroupsServiceDetail({});
              setShowAddUsersAndGroupsModal(false);
            }}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default ServicesPage;
