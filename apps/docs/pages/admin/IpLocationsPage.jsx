import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faFileSpreadsheet, faPlus, faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';

import { Button } from '../../../components/buttons';
import DropDown from '../../../components/dropdowns/DropDown';
import FileBrowserForm from '../../../components/file/FileBrowserForm';
import { Search } from '../../../components/forms';
import HelpContainer from '../../../components/help/HelpContainer';
import CRUDModal from '../../../components/modal/CRUDModal';
import TableContainer from '../../../components/table/TableContainer';
import Actions from '../../../components/table/components/Actions';
import InlineText from '../../../components/table/components/InlineText';
import Selector from '../../../components/table/components/Selector';
import IpLocationsForm from '../../components/ip-locations/IpLocationsForm';
import {
  bulkActionOptions,
  defaultBulkActionOption,
  getFormTooltipDetail,
  modalModeDetail,
} from '../../components/ip-locations/helper';

import {
  add,
  bulkRemove,
  downloadTemplateCSV,
  getList,
  importFromCsv,
  remove,
  update,
} from '../../ducks/ip-locations';
import { selectTableConfig, selectTableDetail } from '../../ducks/ip-locations/selectors';

import NavLayout from '../../layout/NavLayout';

import useApiCall from '../../../hooks/useApiCall';
import { HELP_ARTICLES } from '../../config';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_LOCATION_DETAIL = {};
const DEFAULT_CSV_IMPORT_DETAIL = {};
const DEFAULT_CSV_IMPORT_RESULT_DETAIL = null;

const IpLocationsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  // '', 'add', 'edit', 'delete', 'bulkDelete'
  const [modalMode, setModalMode] = useState(DEFAULT_MODAL_MODE);
  const [locationDetail, setLocationDetail] = useState(DEFAULT_LOCATION_DETAIL);
  const [csvImportDetail, setCSVImportDetail] = useState(DEFAULT_CSV_IMPORT_DETAIL);
  const [csvImportResult, setCSVImportResult] = useState(DEFAULT_CSV_IMPORT_RESULT_DETAIL);
  const [selectedRowDetail, setSelectedRowDetail] = useState([]);
  const [selectedBulkAction, setSelectedBulkAction] = useState(defaultBulkActionOption);
  const [searchTerm, setSearchTerm] = useState('');

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'locations') {
        const LocationCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const locations = props?.row?.original?.locations;

          return <InlineText>{locations.map(({ name }) => name).join(', ') || ''}</InlineText>;
        };

        columnDetail.cell = LocationCellComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions {...props} onEditClick={onEditClick} onDeleteClick={onDeleteClick} />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    return [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 100,
        minSize: 100,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];
  }, [tableConfig?.columns]);

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  const onCSVImportClick = () => {
    setModalMode('importFile');
  };

  const onAddClick = () => {
    setModalMode('add');
  };

  const onEditClick = (detail) => {
    if (detail) {
      setLocationDetail(detail);
      setModalMode('edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setLocationDetail(detail);
      setModalMode('delete');
    }
  };

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setLocationDetail(DEFAULT_LOCATION_DETAIL);
    setCSVImportDetail(DEFAULT_CSV_IMPORT_DETAIL);
    setCSVImportResult(null);
    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(locationDetail)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(locationDetail)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(locationDetail)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'bulkDelete') {
      onBulkActionSelection();
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsv(csvImportDetail))
        .then((response = {}) => {
          setCSVImportResult({ ...response });
        })
        .catch(noop);
    }
  };

  const onBulkActionSelection = () => {
    const { value } = selectedBulkAction;

    const ids = selectedRowDetail.map(({ id }) => id);

    if (value === 'DELETE') {
      apiCall(bulkRemove({ ids })).catch(noop).finally(onCloseClick);
    }
  };

  const onBulkActionSelectionClick = (payload) => {
    const newSelection = payload[0] || defaultBulkActionOption;

    setSelectedBulkAction(newSelection);

    if (selectedRowDetail?.length > 0) {
      const { value } = newSelection;

      let mode = '';

      if (value === 'DELETE') {
        mode = 'bulkDelete';
      }

      setModalMode(mode);
    }
  };

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);

    setSelectedBulkAction(defaultBulkActionOption);
    setSearchTerm('');
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);

    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  const isImportResultValid = !!csvImportResult;

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.IP_LOCATIONS} />
      <article id="admin-ip-locations" className="page-container">
        <section className="heading-small page-title">{t('IP_LOCATIONS')}</section>

        <div className="is-flex has-jc-sb">
          <div className="buttons">
            <Button onClick={onAddClick}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              <span>{t('ADD_LOCATION')}</span>
            </Button>
            <Button type="secondary" onClick={onCSVImportClick}>
              <FontAwesomeIcon icon={faFileSpreadsheet} className="icon left" />
              <span>{t('CSV_IMPORT')}</span>
            </Button>

            {isBulkActionEnabled && (
              <DropDown
                list={bulkActionOptions}
                selectedList={[selectedBulkAction]}
                onSelection={onBulkActionSelectionClick}
                selectedItemsProps={{
                  kind: 'secondary',
                  containerStyle: { justifyContent: 'center' },
                }}
                disabled={!isBulkActionEnabled}
              />
            )}
          </div>

          <div className="buttons">
            <Button
              type="tertiary"
              onClick={onRefreshClick}
              style={{ minWidth: '0px', paddingRight: '0px' }}
            >
              <FontAwesomeIcon icon={faSync} />
            </Button>

            <Search
              onSearch={onSearchEnter}
              term={searchTerm}
              containerStyle={{ width: '260px' }}
            />
          </div>
        </div>

        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          data={tableDetail.data}
          pagination={{ ...tableDetail, onLoadMoreClick }}
          onRowSelection={onRowSelection}
        />

        {modalMode && (
          <CRUDModal
            mode={modalMode}
            renderFormSection={(props) =>
              modalMode === 'importFile' ? (
                <FileBrowserForm
                  onDetailChange={setCSVImportDetail}
                  detail={csvImportDetail}
                  onDownloadClick={onDownloadClick}
                  overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
                  tooltip={getFormTooltipDetail('csvFile')}
                  result={csvImportResult}
                  {...props}
                />
              ) : (
                <IpLocationsForm
                  {...props}
                  onDetailChange={setLocationDetail}
                  detail={locationDetail}
                />
              )
            }
            saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
            showSave={!isImportResultValid}
            cancelText={isImportResultValid ? 'CLOSE' : 'CANCEL'}
            onSaveClick={onSaveClick}
            onCloseClick={onCloseClick}
            {...modalModeDetail[modalMode]}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default IpLocationsPage;
