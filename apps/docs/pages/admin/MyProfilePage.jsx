import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faPencilAlt } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { Button } from '../../../components/buttons';
import { Card } from '../../../components/cards';
import { Field, Input } from '../../../components/forms';
import HelpContainer from '../../../components/help/HelpContainer';
import ChangeEmailModal from '../../components/profile/ChangeEmailModal';
import ChangePasswordModal from '../../components/profile/ChangePasswordModal';

import { selectMyProfileDetail } from '../../ducks/profile/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES } from '../../config';

const MyProfilePage = () => {
  const { t } = useTranslation();

  const profileDetail = useSelector(selectMyProfileDetail);

  const [showChangeEmailModal, setShowChangeEmailModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);

  const onChangeEmailClick = () => {
    setShowChangeEmailModal(true);
  };

  const onChangePasswordClick = () => {
    setShowChangePasswordModal(true);
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.USER_PROFILE} />
      <article id="admin-my-profile" className="page-container">
        <section className="heading-small page-title">{t('MY_PROFILE')}</section>
        <Card>
          <Input label="LOGIN_ID_LABEL" name="loginName" value={profileDetail.loginName} disabled />

          <Field label="EMAIL_ADDRESS">
            <Button type="tertiary" containerClass="no-p-l" onClick={onChangeEmailClick}>
              <span>{profileDetail.primaryEmail}</span>
              <FontAwesomeIcon icon={faPencilAlt} className="icon right" />
            </Button>
          </Field>

          <Field label="PASSWORD_LABEL">
            <Button type="secondary" onClick={onChangePasswordClick}>
              {t('CHANGE_PASSWORD')}
            </Button>
          </Field>
        </Card>

        {showChangeEmailModal && (
          <ChangeEmailModal
            show={showChangeEmailModal}
            detail={profileDetail}
            onCloseClick={() => {
              setShowChangeEmailModal(false);
            }}
          />
        )}

        {showChangePasswordModal && (
          <ChangePasswordModal
            show={showChangePasswordModal}
            onCloseClick={() => {
              setShowChangePasswordModal(false);
            }}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default MyProfilePage;
