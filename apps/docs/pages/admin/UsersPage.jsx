import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faFileSpreadsheet, faPlus, faSync, faUnlockAlt } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';

import { Button } from '../../../components/buttons';
import DropDown from '../../../components/dropdowns/DropDown';
import FileBrowserForm from '../../../components/file/FileBrowserForm';
import { Search } from '../../../components/forms';
import HelpContainer from '../../../components/help/HelpContainer';
import CRUDModal from '../../../components/modal/CRUDModal';
import TableContainer from '../../../components/table/TableContainer';
import Actions from '../../../components/table/components/Actions';
import Selector from '../../../components/table/components/Selector';
import StatusTag from '../../../components/tags/StatusTag';
import UserForm from '../../components/user/UserForm';
import {
  bulkActionOptions,
  defaultBulkActionOption,
  getCustomAttributeApiPayload,
  getFormTooltipDetail,
  modalModeDetail,
  searchOptions,
} from '../../components/user/helper';

import { selectTableDetail as selectAttributeTableDetail } from '../../ducks/attributes/selectors';
import { selectMyProfileDetail } from '../../ducks/profile/selectors';
import {
  add,
  bulkActivate,
  bulkPasswordReset,
  bulkRemove,
  downloadTemplateCSV,
  getList,
  importFromCsv,
  remove,
  update,
} from '../../ducks/users';
import { selectTableConfig, selectTableDetail } from '../../ducks/users/selectors';

import NavLayout from '../../layout/NavLayout';

import useApiCall from '../../../hooks/useApiCall';
import { HELP_ARTICLES } from '../../config';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_USER_DETAIL = {};
const DEFAULT_CSV_IMPORT_DETAIL = {};
const DEFAULT_CSV_IMPORT_RESULT_DETAIL = null;

const tableShowMoreList = [
  { label: 'ACTIVATE', value: 'ACTIVATE' },
  { label: 'DE_ACTIVATE', value: 'DE_ACTIVATE' },
];

const UsersPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  // '', 'add', 'edit', 'delete', 'bulkActivate', 'bulkDeactivate', 'bulkDelete'
  const [modalMode, setModalMode] = useState(DEFAULT_MODAL_MODE);
  const [userDetail, setUserDetail] = useState(DEFAULT_USER_DETAIL);
  const [csvImportDetail, setCSVImportDetail] = useState(DEFAULT_CSV_IMPORT_DETAIL);
  const [csvImportResult, setCSVImportResult] = useState(DEFAULT_CSV_IMPORT_RESULT_DETAIL);
  const [selectedRowDetail, setSelectedRowDetail] = useState([]);
  const [selectedBulkAction, setSelectedBulkAction] = useState(defaultBulkActionOption);
  const [selectedSearchField, setSelectedSearchField] = useState(searchOptions[0]);
  const [searchTerm, setSearchTerm] = useState('');

  const profileDetail = useSelector(selectMyProfileDetail);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const { data: attributeList = [] } = useSelector(selectAttributeTableDetail);

  const customAttributeList = attributeList.filter((detail) => !detail.systemDefined);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { id } = props?.row?.original || {};

          const showDelete = id !== profileDetail?.id;

          return (
            <Actions
              {...props}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              showDelete={showDelete}
              showMore
              list={tableShowMoreList}
              onActionClick={onShowMoreClick}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { status } = props?.row?.original || {};

          return (
            <StatusTag
              value={status}
              truthyLabel="ACTIVE"
              falsyLabel="INACTIVE"
              type="ENABLED_DISABLED"
            />
          );
        };

        columnDetail.cell = StatusCellComponent;
      }

      return columnDetail;
    });

    return [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 100,
        minSize: 100,
        maxSize: 100,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];
  }, [tableConfig?.columns, profileDetail]);

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  const onCSVImportClick = () => {
    setModalMode('importFile');
  };

  const onAddClick = () => {
    setModalMode('add');
  };

  const onEditClick = (detail) => {
    if (detail) {
      setUserDetail(detail);
      setModalMode('edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setUserDetail(detail);
      setModalMode('delete');
    }
  };

  const onShowMoreClick = (detail, payload = {}) => {
    const id = payload?.row?.original?.id;

    const { setIsOpen = noop } = payload;

    if (detail && id) {
      apiCall(bulkActivate({ ids: [id], enable: detail === 'ACTIVATE' }))
        .catch(noop)
        .finally(() => {
          setIsOpen?.(false);
        });
    }
  };

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setUserDetail(DEFAULT_USER_DETAIL);
    setCSVImportDetail(DEFAULT_CSV_IMPORT_DETAIL);
    setCSVImportResult(null);
    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      const payload = {
        ...userDetail,
        customAttrsInfo: getCustomAttributeApiPayload({
          customAttrsInfo: userDetail.customAttrsInfo,
          attributeList: customAttributeList,
        }),
      };

      apiCall(add(payload)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'edit') {
      const payload = {
        ...userDetail,
        customAttrsInfo: getCustomAttributeApiPayload({
          customAttrsInfo: { ...userDetail.customAttrsInfo },
          attributeList: customAttributeList,
        }),
      };

      apiCall(update(payload)).then(onCloseClick).catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(userDetail)).then(onCloseClick).catch(noop);
    }

    if (
      modalMode === 'bulkActivate' ||
      modalMode === 'bulkDeactivate' ||
      modalMode === 'bulkDelete'
    ) {
      onBulkActionSelection();
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsv(csvImportDetail))
        .then((response = {}) => {
          setCSVImportResult({ ...response });
        })
        .catch(noop);
    }

    if (modalMode === 'actionConfirmation') {
      onBulkResetPassword();
    }
  };

  const onBulkActionSelection = () => {
    const { value } = selectedBulkAction;

    const ids = selectedRowDetail.map(({ id }) => id);

    if (value === 'ACTIVATE' || value === 'DE_ACTIVATE') {
      const enable = value === 'ACTIVATE';

      apiCall(bulkActivate({ ids, enable })).catch(noop).finally(onCloseClick);
    }

    if (value === 'DELETE') {
      apiCall(bulkRemove({ ids })).catch(noop).finally(onCloseClick);
    }
  };

  const onBulkActionSelectionClick = (payload) => {
    const newSelection = payload[0] || defaultBulkActionOption;

    setSelectedBulkAction(newSelection);

    if (selectedRowDetail?.length > 0) {
      const { value } = newSelection;

      let mode = '';

      if (value === 'ACTIVATE') {
        mode = 'bulkActivate';
      }

      if (value === 'DE_ACTIVATE') {
        mode = 'bulkDeactivate';
      }

      if (value === 'DELETE') {
        mode = 'bulkDelete';
      }

      setModalMode(mode);
    }
  };

  const onBulkResetPassword = () => {
    const ids = selectedRowDetail.map(({ id }) => id);

    apiCall(bulkPasswordReset({ ids })).catch(noop).finally(onCloseClick);
  };

  const onBulkResetPasswordClick = () => {
    setModalMode('actionConfirmation');
  };

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);

    setSelectedBulkAction(defaultBulkActionOption);
    setSelectedSearchField(searchOptions[0]);
    setSearchTerm('');
  };

  const onSearchEnter = (term) => {
    const { value } = selectedSearchField || {};

    let searchField = '';

    if (value === 'NAME') {
      searchField = 'name';
    }

    if (value === 'GROUP') {
      searchField = 'groupname';
    }

    if (searchField) {
      apiCall(getList({ [searchField]: term })).catch(noop);

      setSearchTerm(term);
    }
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);

    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  const isImportResultValid = !!csvImportResult;

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.USERS} />
      <article id="admin-users" className="page-container">
        <section className="heading-small page-title">{t('USERS')}</section>

        <div className="is-flex has-jc-sb">
          <div className="buttons">
            <Button onClick={onAddClick}>
              <FontAwesomeIcon icon={faPlus} className="icon left" />
              <span>{t('ADD_USER')}</span>
            </Button>
            <Button type="secondary" onClick={onCSVImportClick}>
              <FontAwesomeIcon icon={faFileSpreadsheet} className="icon left" />
              <span>{t('CSV_IMPORT')}</span>
            </Button>

            {isBulkActionEnabled && (
              <>
                <Button type="secondary" onClick={onBulkResetPasswordClick}>
                  <FontAwesomeIcon icon={faUnlockAlt} className="icon left" />
                  <span>{t('RESET_PASSWORD')}</span>
                </Button>

                <DropDown
                  list={bulkActionOptions}
                  selectedList={[selectedBulkAction]}
                  onSelection={onBulkActionSelectionClick}
                  selectedItemsProps={{
                    kind: 'secondary',
                    containerStyle: { justifyContent: 'center' },
                  }}
                  disabled={!isBulkActionEnabled}
                />
              </>
            )}
          </div>
          <div className="buttons">
            <Button
              type="tertiary"
              onClick={onRefreshClick}
              style={{ minWidth: '0px', paddingRight: '0px' }}
            >
              <FontAwesomeIcon icon={faSync} />
            </Button>

            <div className="dropdown-with-search">
              <DropDown
                list={searchOptions}
                selectedList={[selectedSearchField]}
                onSelection={(payload) => {
                  setSelectedSearchField(payload[0]);
                }}
                selectedItemsProps={{
                  kind: 'tertiary',
                  containerStyle: {
                    justifyContent: 'center',
                    paddingLeft: '4px',
                    paddingRight: '4px',
                    minWidth: '70px',
                  },
                }}
                itemsSelectionProps={{
                  containerStyle: {
                    minWidth: '130px',
                  },
                }}
              />

              <Search
                onSearch={onSearchEnter}
                term={searchTerm}
                containerStyle={{ width: '260px' }}
              />
            </div>
          </div>
        </div>

        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          data={tableDetail.data}
          pagination={{ ...tableDetail, onLoadMoreClick }}
          onRowSelection={onRowSelection}
        />

        {modalMode && (
          <CRUDModal
            mode={modalMode}
            renderFormSection={(props) =>
              modalMode === 'importFile' ? (
                <FileBrowserForm
                  onDetailChange={setCSVImportDetail}
                  detail={csvImportDetail}
                  onDownloadClick={onDownloadClick}
                  overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
                  tooltip={getFormTooltipDetail('csvFile')}
                  result={csvImportResult}
                  {...props}
                />
              ) : (
                <UserForm {...props} onDetailChange={setUserDetail} detail={userDetail} />
              )
            }
            saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
            showSave={!isImportResultValid}
            cancelText={isImportResultValid ? 'CLOSE' : 'CANCEL'}
            onSaveClick={onSaveClick}
            onCloseClick={onCloseClick}
            {...modalModeDetail[modalMode]}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default UsersPage;
