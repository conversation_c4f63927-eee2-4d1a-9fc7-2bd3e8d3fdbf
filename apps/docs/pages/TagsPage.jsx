import HelpContainer from '../../components/help/HelpContainer';
import Actions from '../../components/table/components/Actions';
import { StatusTag } from '../../components/tags';

const list = [
  { label: 'ACTIVATE', value: 'ACTIVATE' },
  { label: 'DEACTIVATE', value: 'DEACTIVATE' },
];

const TagsPage = () => {
  const onActionClick = () => {};

  return (
    <div className="page tags-page is-flex has-jc-c has-ai-c">
      <HelpContainer
        src={`https://help.zscaler.com/zia/about-dashboards?source=zia-admin-ui-tagsPage`}
        expanded
      />
      <div className="is-flex">
        <StatusTag value={true} type="ENABLED_DISABLED" />
        <StatusTag value={false} type="ENABLED_DISABLED" />
        <StatusTag value={true} type="ALLOWED_BLOCKED" />
        <StatusTag value={false} falsyLabel="Hell Yeah" type="ALLOWED_BLOCKED" />
      </div>

      <div className="is-flex">
        <Actions showMore list={list} onActionClick={onActionClick} />
      </div>
    </div>
  );
};

export default TagsPage;
