import { useTranslation } from 'react-i18next';

import Card from '../../components/cards/Card';
import LanguageSelector from '../../components/dropdowns/LanguageSelector';

const LanguageSelectorPage = () => {
  const { t } = useTranslation();

  return (
    <div className="page language-selector-page is-flex has-jc-c has-ai-c">
      <Card> {t('MY_NAME')}</Card>
      <LanguageSelector />
    </div>
  );
};

export default LanguageSelectorPage;
