import { useRef } from 'react';

import Card from '../../components/cards/Card';
import Input from '../../components/forms/Input';
import Toast from '../../components/toast/Toast';
import { renderNotificationMessage } from '../../components/toast/helper';
import { TextWithTooltip, Tooltip } from '../../components/tooltip';

const TooltipPage = () => {
  const inputRef = useRef();

  return (
    <div className="page tooltip-page is-flex has-jc-c has-ai-c">
      <p> tooltip page </p>

      <div className="is-flex has-fd-c">
        <div style={{ maxWidth: '400px' }}>
          <TextWithTooltip>
            this text contains tooltip this text contains tooltip this text contains tooltip this
            text contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip
          </TextWithTooltip>
        </div>

        <Toast>
          {renderNotificationMessage({
            message: `this text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text contains
          tooltip this text contains tooltip this text contains tooltip this text contains tooltip
          this text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text contains
          tooltip this text contains tooltip this text contains tooltip this text contains tooltip
          this text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text contains
          tooltip this text contains tooltip this text contains tooltip this text contains tooltip
          this text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text contains
          tooltip this text contains tooltip this text contains tooltip this text contains tooltip
          this text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip`,
          })}
        </Toast>

        <Input
          label="Tooltip"
          placeholder="sdagjlbsldag"
          value=""
          tooltip={{
            content: `this text contains tooltip this text contains tooltip this text contains tooltip this
          text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip`,
          }}
        />

        <Input ref={inputRef} label="First Name" placeholder="sdagjlbsldag" value="CJS tooltip" />

        <Tooltip elementRef={inputRef} showOnHover>
          <Card>
            this text contains tooltip this text contains tooltip this text contains tooltip this
            text contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip
          </Card>
        </Tooltip>
      </div>
    </div>
  );
};

export default TooltipPage;
