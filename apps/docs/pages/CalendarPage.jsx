import { useState } from 'react';

import { range } from 'lodash-es';

import { CalendarDropDown, CustomDatePicker, InlineDatePicker } from '../../components/calendar';
import DropDown from '../../components/dropdowns/DropDown';
import Field from '../../components/forms/Field';

const listGenerator = (num) => ({ label: num, value: num });

const numberList = range(1, 500).map(listGenerator);

const customConfig = {
  showHourMinuteOption: false,
  showHourOption: true,
  showMinuteOption: true,
  showSecondsOption: false,
  hourInterval: 1,
  roundHourTo: 1,
  minuteInterval: 1,
  roundMinutesTo: 1,
  secondsInterval: 1,
  roundSecondsTo: 1,
  defaultIntervalFromEndDate: 60 * 60 * 26,
  maxInterval: 60 * 60 * 24 * 92,
  message: { text: 'End date can be up to 92 days after start date', use },
};

const CalendarPage = () => {
  const [selectedList, setSelectedList] = useState([]);
  const [selectedDate, setSelectedDate] = useState();

  const onDateSelection = (date) => {
    setSelectedDate(date);
  };

  const content = (
    <p>
      This is content
      <a href="https://google.com" target="_blank" rel="noreferrer">
        Click here to know more
      </a>
    </p>
  );

  return (
    <div className="page calendar-page is-flex has-jc-c has-ai-c" style={{ marginTop: '15rem' }}>
      <div className="is-flex">
        <Field
          label="MAX_CHARACTER"
          htmlFor="character"
          info={{ character: { type: 'error', message: 'Required' } }}
          tooltip={{ content }}
        >
          <DropDown
            list={numberList}
            selectedList={selectedList}
            onSelection={setSelectedList}
            containerStyle={{ width: '120px' }}
            isMulti
          />
        </Field>
      </div>

      <CustomDatePicker />

      <div className="is-flex" style={{ marginLeft: '2rem' }}>
        <Field label="TIME_PICKER">
          <CalendarDropDown
            selectedList={selectedList}
            setSelectedList={setSelectedList}
            customConfig={customConfig}
          />
        </Field>
      </div>

      <div className="is-flex" style={{ marginLeft: '2rem' }}>
        <InlineDatePicker
          label={'Inline date Picker'}
          name={'date'}
          selectedDate={selectedDate}
          onSelection={onDateSelection}
        />
      </div>
    </div>
  );
};

export default CalendarPage;
