import { useState } from 'react';

import { noop } from 'lodash-es';

import { InlineDatePicker } from '../../components/calendar';
import DropDown from '../../components/dropdowns/DropDown';
import FileBrowserForm from '../../components/file/FileBrowserForm';
import { FieldGroup, Search } from '../../components/forms';
import Input from '../../components/forms/Input';
import PasswordInput from '../../components/forms/PasswordInput';
import TextArea from '../../components/forms/TextArea';

import { replaceNonNumericDigit } from '../../utils/validations';

const fileImportResult = {
  totalRecordsAdded: 3,
  totalRecordsDeleted: 0,
  totalRecordsUpdated: 2,
  failedRecords: [
    { errorCode: 'DUPLICATE_ITEM', name: 'Engineering', action: 'ADD', description: null },
    {
      errorCode: 'RESOURCE_NOT_FOUND',
      name: "Group 'groupName' does not exists",
      action: 'DELETE',
      description: null,
    },
    {
      errorCode: 'UNEXPECTED_ERROR',
      name: 'Password should be atleast 8 character(s) long.\nPassword should contain atleast 1 uppercase character(s).\nPassword should contain atleast 1 lowercase character(s).Error adding record at Line Number: 1',
      action: 'ADD',
    },
  ],
  processedRecords: 5,
  totalRecordsInImport: 5,
  errors: [
    {
      errorCode: 'CSV_FORMAT_INVALID',
      description:
        'Invalid CSV header. Valid header is : Action,Email-ID,User Name,Dept,Password,Status,Comments,Temp auth e-mail,Groups',
    },
  ],
};

const FormPages = () => {
  const [formValues, setFormValues] = useState({
    loginId: '',
    password: '',
    description: '',
    date: '',
    rememberMe: true,
    number: '',
  });

  const onFormFieldChange = (evt, payload) => {
    const { name, value, checked, type } = evt.target || payload;

    if (name == 'number') {
      setFormValues((prevState) => ({
        ...prevState,
        [name]: replaceNonNumericDigit({ value, allowDecimal: true }),
      }));
    } else {
      setFormValues((prevState) => ({
        ...prevState,
        [name]: type === 'checkbox' ? checked : value,
      }));
    }
  };

  return (
    <div className="page form-page is-flex has-jc-c has-ai-c">
      <form
        className=""
        onKeyPress={noop}
        style={{ paddingTop: '50px', maxHeight: '85vh', overflowY: 'scroll' }}
      >
        <Input
          label="LOGIN_ID_LABEL"
          name="loginId"
          placeholder="LOGIN_ID_PLACEHOLDER"
          onChange={onFormFieldChange}
          value={formValues.loginId}
        />

        <Input
          label="NUMBER"
          name="number"
          placeholder="LOGIN_ID_PLACEHOLDER"
          onChange={onFormFieldChange}
          value={formValues.number}
          info={{
            loginId: {
              type: 'error',
              message: `this text contains tooltip this text contains tooltip this text contains tooltip this
          text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip`,
            },
          }}
        />

        <FieldGroup>
          <Input
            label="login"
            name="loginId"
            placeholder="LOGIN_ID_PLACEHOLDER"
            onChange={onFormFieldChange}
            value={formValues.loginId}
            readOnly
            disabled
            info={{
              loginId: {
                type: 'error',
                message: `this text contains tooltip this text contains tooltip this text contains tooltip this
          text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip`,
              },
            }}
          />

          <PasswordInput
            label="this text contains tooltip this text contains tooltip this text contains tooltip this
            text contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip this text
            contains tooltip this text contains tooltip this text contains tooltip"
            name="password"
            placeholder="LOGIN_PASSWORD_PLACEHOLDER"
            onChange={onFormFieldChange}
            value={formValues.password}
            showPasswordValidation
            canCopy
          />
        </FieldGroup>

        <Input
          label="LOGIN_ID_LABEL"
          name="loginId"
          placeholder="LOGIN_ID_PLACEHOLDER"
          onChange={onFormFieldChange}
          value={formValues.loginId}
          readOnly
          disabled
          info={{
            loginId: {
              type: 'error',
              message: `this text contains tooltip this text contains tooltip this text contains tooltip this
          text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip`,
            },
          }}
        />

        <PasswordInput
          label="PASSWORD_LABEL"
          name="password"
          placeholder="LOGIN_PASSWORD_PLACEHOLDER"
          onChange={onFormFieldChange}
          value={formValues.password}
          showPasswordValidation
          canCopy
        />

        <div className="dropdown-with-search">
          <DropDown list={[]} selectedList={[]} onSelection={noop} />

          <Search containerStyle={{ maxWidth: '260px' }} iconPosition="right" />
        </div>

        <FileBrowserForm
          overrideProps={{ tooltip: { content: 'ancklnsdkgnsldkn' } }}
          tooltip={{ content: 'ancklnsdkgnsldkn' }}
        />

        <FileBrowserForm
          overrideProps={{ tooltip: { content: 'ancklnsdkgnsldkn' } }}
          tooltip={{ content: 'ancklnsdkgnsldkn' }}
          result={fileImportResult}
        />

        <TextArea
          label="Text Area"
          name="description"
          onChange={onFormFieldChange}
          value={formValues.description}
          rows={1}
        />

        <InlineDatePicker
          label="Date"
          name="date"
          selectedDate={formValues.date}
          onChange={onFormFieldChange}
          info={{
            date: {
              type: 'error',
              message: `this text contains tooltip this text contains tooltip this text contains tooltip this
          text contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip this text
          contains tooltip this text contains tooltip this text contains tooltip`,
            },
          }}
        />
      </form>
    </div>
  );
};

export default FormPages;
