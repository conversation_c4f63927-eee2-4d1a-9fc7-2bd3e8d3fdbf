import { useState } from 'react';

import { range } from 'lodash-es';

import DropDown from '../../components/dropdowns/DropDown';
import MultiSelection from '../../components/dropdowns/MultiSelection';
import Field from '../../components/forms/Field';

const listGenerator = (num) => ({ label: num, value: num });

const numberList = range(1, 500).map(listGenerator);

const DropDownPage = () => {
  const list = [
    { label: 'asfjlbaljebflaejbflafjlabsdfjlbasljkfnbalksfnlaksnflkasn', value: 'afglnak' },
    ...numberList,
  ];

  const [selectedList, setSelectedList] = useState([
    { label: 'asfjlbaljebflaejbflafjlabsdfjlbasljkfnbalksfnlaksnflkasn', value: 5 },
  ]);

  const content = (
    <p>
      This is content
      <a href="https://google.com" target="_blank" rel="noreferrer">
        Click here to know more
      </a>
    </p>
  );

  return (
    <div className="page drop-down-page is-flex has-jc-c has-ai-c">
      <div className="is-flex">
        <Field
          label="MAX_CHARACTER"
          htmlFor="character"
          info={{ character: { type: 'error', message: 'Required' } }}
          tooltip={{ content }}
        >
          <DropDown
            list={list}
            selectedList={selectedList}
            onSelection={setSelectedList}
            containerStyle={{ width: '120px' }}
            isMulti
          />
        </Field>

        <DropDown
          list={list}
          selectedList={selectedList}
          onSelection={setSelectedList}
          containerStyle={{ marginLeft: '2rem', width: '360px' }}
          isMulti
          renderItemsSelection={(props) => (
            <MultiSelection
              unselectedTitle="Unselected Users"
              selectedTitle="Selected Users"
              {...props}
            />
          )}
        />
      </div>
    </div>
  );
};

export default DropDownPage;
