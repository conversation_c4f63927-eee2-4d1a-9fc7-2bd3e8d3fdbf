import { useSelector } from 'react-redux';

import { faArrowAltFromRight, faCloud, faCog, faUser } from '@fortawesome/pro-solid-svg-icons';

import PropTypes from 'prop-types';

import NavContainer from '../components/nav/NavContainer';

import { selectBuildVersion } from '../ducks/global/selectors';

import logoutUser from '../utils/logoutUser';

import FooterContainer from '../../layouts/FooterContainer';
import IAMLOGO from '../images/IAM_White.png';

const DEFAULT_PROPS = {
  children: null,
  navGroup: [
    [
      {
        linkTo: '/',
        icon: IAMLOGO,
        isIconImage: true,
        hasSmallIcon: false,
        label: 'ZS_LOGIN',
        iconClass: 'large',
        subMenu: [],
      },
      {
        linkTo: '/policy',
        icon: faCloud,
        isIconImage: false,
        hasSmallIcon: false,
        label: 'POLICY',
        subMenu: [
          {
            label: 'POLICIES',
            links: [
              {
                linkTo: '/policy/signon',
                label: 'SIGNON_POLICY',
              },
              {
                linkTo: '/policy/password',
                label: 'PASSWORD_POLICY',
              },
            ],
          },
        ],
      },
      {
        linkTo: '/admin',
        icon: faCog,
        isIconImage: false,
        hasSmallIcon: false,
        label: 'Administration',
        subMenu: [
          {
            label: 'ACCOUNT_MANAGEMENT',
            links: [
              {
                linkTo: '/admin/my-profile',
                label: 'MY_PROFILE',
              },
            ],
          },
          {
            label: 'CLOUD_CONFIGURATION',
            links: [
              {
                linkTo: '/admin/linked-tenants',
                label: 'LINKED_TENANTS',
              },
              {
                linkTo: '/admin/user-attributes',
                label: 'USER_ATTRIBUTES',
              },
              {
                linkTo: '/admin/advanced-settings',
                label: 'ADVANCED_SETTINGS',
              },
            ],
          },
          {
            label: 'DIRECTORY',
            links: [
              {
                linkTo: '/admin/users',
                label: 'USERS',
              },
              {
                linkTo: '/admin/user-groups',
                label: 'USER_GROUPS',
              },
              {
                linkTo: '/admin/external-identities',
                label: 'EXTERNAL_IDENTITIES',
              },
            ],
          },
          {
            label: 'APPLICATIONS',
            links: [
              {
                linkTo: '/admin/zscaler-services',
                label: 'ZSCALER_SERVICES',
              },
            ],
          },
          {
            label: 'SECURITY',
            links: [
              {
                linkTo: '/admin/ip-locations',
                label: 'IP_LOCATIONS',
              },
              {
                linkTo: '/admin/ip-location-groups',
                label: 'IP_LOCATION_GROUPS',
              },
            ],
          },
          {
            label: 'ADMINISTRATION_CONTROLS',
            links: [
              {
                linkTo: '/admin/audit-logs',
                label: 'AUDIT_LOGS',
              },
            ],
          },
        ],
      },
    ],
    [
      {
        linkTo: '/admin/my-profile',
        icon: faUser,
        isIconImage: false,
        hasSmallIcon: true,
        label: '',
        subMenu: [],
      },
      // {
      //   linkTo: '/help',
      //   icon: faQuestionCircle,
      //   hasSmallIcon: true,
      //   isIconImage: false,
      //   label: '',
      //   subMenu: [],
      // },
      {
        linkTo: '',
        onClick: logoutUser,
        icon: faArrowAltFromRight,
        hasSmallIcon: true,
        isIconImage: false,
        label: '',
        subMenu: [],
      },
    ],
  ],
};

const NavLayout = ({ children, navGroup }) => {
  const buildVersion = useSelector(selectBuildVersion);

  return (
    <div id="nav-layout">
      <div className="left has-jc-sb">
        <NavContainer navGroup={navGroup} />
      </div>

      <div className="right">{children}</div>

      <FooterContainer buildVersion={buildVersion} />
    </div>
  );
};

NavLayout.DEFAULT_PROPS = DEFAULT_PROPS;

NavLayout.propTypes = {
  children: PropTypes.any,
  navGroup: PropTypes.arrayOf(Object),
};

export default NavLayout;
