import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Outlet, useLocation } from 'react-router-dom';

import { noop } from 'lodash-es';

import LoaderContainer from '../../components/spinner/LoaderContainer';
import ToastContainer from '../../components/toast/ToastContainer';

import {
  appSetupDone,
  getBuildVersion,
  hideLoader,
  hideNotification,
  showLoader,
} from '../ducks/global';
import {
  selectAppSetupPending,
  selectIsAppSetupDone,
  selectIsLoading,
  selectNotificationsList,
} from '../ducks/global/selectors';
import { getAuthSessionFromServer } from '../ducks/login';
import { getProfile } from '../ducks/profile';

import matchRoute from '../utils/matchRoute';

import {
  setupApiMethodMessageOption,
  setupFloatingPortalRootId,
  setupUseApiCallFunctionsRegistry,
} from '../setup';

setupApiMethodMessageOption();
setupUseApiCallFunctionsRegistry();
setupFloatingPortalRootId();

const authFreePageList = ['/signup'];

const isAuthFreePage = (location = {}) => {
  return matchRoute(authFreePageList, location);
};

const AppLayout = () => {
  const dispatch = useDispatch();
  const location = useLocation();

  const appSetupPending = useSelector(selectAppSetupPending);
  const isAppSetupDone = useSelector(selectIsAppSetupDone);

  const isLoading = useSelector(selectIsLoading);
  const notificationList = useSelector(selectNotificationsList);

  const setupAuth = () => {
    dispatch(showLoader());
    dispatch(getAuthSessionFromServer())
      .catch(noop)
      .finally(() => {
        dispatch(appSetupDone());
        dispatch(getProfile()).catch(noop);
        dispatch(getBuildVersion()).catch(noop);
        dispatch(hideLoader());
      });
  };

  useEffect(() => {
    if (appSetupPending) {
      if (!isAuthFreePage(location)) {
        setupAuth();
      } else {
        dispatch(appSetupDone());
      }
    }
  }, [appSetupPending]);

  const renderRoutesSection = () => {
    if (!isAppSetupDone) {
      return null;
    }

    return <Outlet />;
  };

  const onNotificationClose = (id) => {
    dispatch(hideNotification(id));
  };

  return (
    <>
      <LoaderContainer isLoading={isLoading} />
      <ToastContainer list={notificationList} onClose={onNotificationClose} />
      {renderRoutesSection()}
    </>
  );
};

export default AppLayout;
