const path = require('path');

const webpack = require('webpack');
const dotenv = require('dotenv');

const HtmlWebpackPlugin = require('html-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const TerserPlugin = require('terser-webpack-plugin');

const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');

module.exports = (env, argv) => {
  const isDevMode = argv.mode === 'development';
  const isProdMode = argv.mode === 'production';

  const dotEnv = dotenv.config().parsed;

  const ASSET_PATH = isDevMode ? '/' : process.env.ASSET_PATH || '/iam';

  const config = {
    entry: 'index.js',
    mode: argv.mode || 'development',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].[contenthash].js',
      publicPath: ASSET_PATH,
      clean: true,
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          resolve: {
            fullySpecified: false,
          },
          use: [
            {
              loader: require.resolve('babel-loader'),
              options: {
                plugins: [isDevMode && require.resolve('react-refresh/babel')].filter(Boolean),
              },
            },
          ],
          exclude: /node_modules/,
        },
        {
          test: /\.scss$/,
          use: [
            isProdMode && MiniCssExtractPlugin.loader,
            isDevMode && 'style-loader',
            'css-loader',
            'sass-loader',
          ].filter(Boolean),
        },
        {
          test: /\.svg$/,
          use: 'svg-url-loader',
        },
        {
          test: /\.png$/,
          use: [
            {
              loader: 'url-loader',
              options: {
                mimetype: 'image/png',
              },
            },
          ],
        },
      ],
    },
    resolve: {
      extensions: ['.js', '.jsx'],
      // alias: {
      //   'react/jsx-dev-runtime': 'react/jsx-dev-runtime.js',
      //   'react/jsx-runtime': 'react/jsx-runtime.js',
      // },
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: path.resolve(__dirname, 'index.html'),
      }),
      new webpack.DefinePlugin({
        'process.env': JSON.stringify({
          ...process.env,
          ...dotEnv,
          ASSET_PATH,
        }),
      }),
      isDevMode && new ReactRefreshWebpackPlugin(),
      isProdMode &&
        new MiniCssExtractPlugin({
          filename: `[name].[contenthash].css`,
        }),
      isProdMode &&
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
        }),
    ].filter(Boolean),
  };

  if (isDevMode) {
    config.devtool = 'source-map';

    config.devServer = {
      historyApiFallback: true,
      compress: true,
      hot: true,
      server: 'https',
      open: false,
      host: 'localhost',
      port: 8006,
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
      proxy: [
        {
          context: ['/admin/internal-api/'],
          target: 'http://************:8001',
          secure: false,
        },
      ],
      headers: {
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
        'Access-Control-Allow-Origin': 'https://************:8001/',
        'Access-Control-Allow-Credentials': false,
      },
    };
  }

  if (isProdMode) {
    // use for prod debug
    // config.devtool = 'source-map';

    config.optimization = {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
          },
        }),
      ],
      runtimeChunk: 'single',
      splitChunks: {
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      },
    };
  }

  return config;
};
