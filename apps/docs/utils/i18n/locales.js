import deDE from './de-de';
import enUS from './en-us';
import esES from './es-es';
import frFR from './fr-fr';
import jaJP from './ja-jp';
import zhCN from './zh-cn';

export const DROPDOWN_LANG_DATA = [
  { value: 'en-US', label: 'English (US)' },
  { value: 'es-ES', label: 'Español' },
  { value: 'fr-FR', label: 'Français' },
  { value: 'de-DE', label: 'Deutsch' },
  { value: 'zh-CN', label: '中文' },
  { value: 'ja-JP', label: '日本語' },
];

export const setSelectedLocale = (newLocale) => {
  localStorage.setItem('locale', newLocale || 'en-US');
};

export const getSelectedLocale = (fromDropDown) => {
  const locale = localStorage?.locale || 'en-US';

  if (fromDropDown) {
    return DROPDOWN_LANG_DATA.find((a) => a.value === locale);
  }

  return locale;
};

export const LOCALES = {
  'en-US': enUS,
  'es-ES': esES,
  'fr-FR': frFR,
  'zh-CN': zhCN,
  'ja-JP': jaJP,
  'de-DE': deDE,
};
