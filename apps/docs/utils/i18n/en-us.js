const enUS = {
  LOCALE: 'en-US',
  EMAIL_ADDRESS: 'Email Address',
  EMAIL_PLACEHOLDER: 'Enter your e-mail address...',
  LOGIN_ID_PLACEHOLDER: 'Enter Your Login ID...',
  LOGIN_ID_LABEL: 'Login ID',
  LOGIN_PASSWORD_PLACEHOLDER: 'Enter Your Password...',
  PASSWORD_LABEL: 'Password',
  NEW_PASSWORD_LABEL: 'New Password',
  OLD_PASSWORD_LABEL: 'Old Password',
  CONFIRM_NEW_PASSWORD_LABEL: 'Confirm New Password',
  PASSWORD_PLACEHOLDER: 'Enter Password',
  FORGOT_PASSWORD: 'Forgot Password?',
  REMEMBER_ME: 'Remember me',
  RESET_PASSWORD: 'Reset Password',
  CHANGE_PASSWORD: 'Change Password',
  SUCCESS: 'Success',
  RESEND: 'Resend',
  <PERSON>IG<PERSON>_IN_LABEL: 'Sign In',
  INITIAL_DOMAIN_NAME: 'Initial Domain Name',
  FIRST_NAME: 'First Name',
  LAST_NAME: 'Last Name',
  USERNAME: 'Username',
  REQUIRED_VALIDATION_MESSAGE: 'Required',
  INVALID_CREDENTIALS_MESSAGE: 'Invalid Login ID or Password',
  AUDITOR_DIRECT_LOGIN_NOT_ALLOWED: 'An auditor cannot log in from here',
  LANGUAGE: 'English (US)',
  COOKIES_DISABLED:
    'Cookies must be allowed to use this application. Please enable your browser"s cookie support for this site.',
  COOKIES_NOT_ALLOWED: 'Cookies Not Allowed',
  WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER:
    'This browser is not supported and may break this site"s functionality. We suggest that you update your browser to the latest version. To disregard this message, click OK.',
  BROWSER_NOT_SUPPORTED: 'Browser version not supported',
  OK: 'OK',
  NEXT: 'Next',
  BACK: 'Back',
  SAVE: 'Save',
  COPYRIGHT: 'Copyright',
  COPYRIGHT_STATEMENT: 'All rights reserved.',
  POWERED_BY: 'Powered By',
  ADMIN_PORTAL_SIGN_IN: 'Admin Portal Sign In',
  ALL_ORGANIZATIONS: 'All Organizations',
  SEARCH_PLACEHOLDER: 'Search...',
  GENERAL: 'General',
  ZPA: 'Zscaler Private Access',
  IAM: 'Identity and Access Management',
  ZIA: 'Zscaler Internet Access',
  ZDX: 'Zscaler Digital Experience',
  EC: 'Edgeconnector',
  CCP: 'Client Connector Portal',
  BI: 'Browser Isolation',
  LAUNCH: 'Launch',
  VERIFY: 'Verify',
  CANCEL: 'Cancel',
  DONE: 'Done',
  RESET: 'Reset',
  CLEAR: 'Clear',
  CLEAR_ALL: 'Clear All',
  MY_PROFILE: 'My Profile',
  ZS_LOGIN: 'ZSLogin',
  ACCOUNT_MANAGEMENT: 'ACCOUNT MANAGEMENT',
  CLOUD_CONFIGURATION: 'CLOUD CONFIGURATION',
  PRODUCT: 'Product',
  CLOUD_NAME: 'Cloud Name',
  CLOUD_ID: 'Cloud Id',
  ORG_NAME: 'Organization Name',
  LINKED_TENANTS: 'Linked Tenants',
  LINK: 'Link',
  UNLINK: 'Unlink',
  LINKED: 'Linked',
  UNLINKED: 'UnLinked',
  LINK_TENANT: 'Link Tenant',
  UNLINK_TENANT: 'Unlink Tenant',
  USER_ATTRIBUTES: 'User Attributes',
  ADVANCED_SETTINGS: 'Advanced Settings',
  DIRECTORY: 'DIRECTORY',
  USERS: 'Users',
  USER_GROUPS: 'User Groups',
  EXTERNAL_IDENTITIES: 'External Identities',
  APPLICATIONS: 'APPLICATIONS',
  ZSCALER_SERVICES: 'Zscaler Services',
  SECURITY: 'SECURITY',
  IP_LOCATIONS: 'IP Locations',
  IP_LOCATION_GROUPS: 'IP Location Groups',
  ADMINISTRATION_CONTROLS: 'ADMINISTRATION CONTROLS',
  AUDIT_LOGS: 'Audit Logs',
  ADD_USER: 'Add User',
  CSV_IMPORT: 'CSV Import',
  ACTIONS: 'Actions',
  ACTIVATE: 'Activate',
  DE_ACTIVATE: 'Deactivate',
  BULK_ACTIVATE: 'Bulk Activate',
  BULK_DEACTIVATE: 'Bulk Deactivate',
  BULK_DELETE: 'Bulk Delete',
  DELETE: 'Delete',
  NAME: 'Name',
  GROUP: 'Group',
  SELECT: 'Select',
  USER_ID: 'User ID',
  STATUS: 'Status',
  GENERAL_INFORMATION: 'General Information',
  MY_ACCOUNT: 'My Account',
  FULL_NAME: 'Full Name',
  VERIFIED: 'Verified',
  LANGUAGE_LABEL: 'Language',
  TIMEZONE: 'Timezone',
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
  USER_INFORMATION: 'User Information',
  PRIMARY_EMAIL: 'Primary Email',
  SAME_AS_USERID: 'Same As User ID',
  SECONDARY_EMAIL: 'Secondary Email',
  ADDITIONAL_ATTRIBUTES: 'Additional Attributes',
  SECURITY_SETTINGS: 'Security Settings',
  PASSWORD_OPTION: 'Password Option',
  PROMPT_PASSWORD_FIRST_LOGIN: 'Prompt for Password Change After the Initial Log In',
  CONFIRM_PASSWORD: 'Confirm Password',
  ASSIGNMENT: 'Assignment',
  ASSIGN_GROUPS: 'Assign Groups',
  SET_BY_ADMIN: 'Set By Administrator',
  SET_BY_USER: 'Set By User',
  AUTO_GENERATED: 'Auto-generated',
  DELETE_USER: 'Delete User',
  EDIT_USER: 'Edit User',
  UPDATE: 'Update',
  OFF: 'Off',
  ON: 'On',
  ADD_ATTRIBUTE: 'Add Attribute',
  CREATE_NEW_ATTRIBUTES: 'Create New Attributes',
  TABLE_NUMBER: 'No.',
  DISPLAY_NAME: 'Display Name',
  ATTRIBUTE_NAME: 'Attribute Name',
  DATA_TYPE: 'Data Type',
  TABLE_REQUIRED: 'Required',
  ORIGIN: 'Origin',
  SYSTEM_DEFINED: 'system-defined',
  USER_DEFINED: 'user-defined',
  ENABLE: 'Enable',
  ENABLED: 'Enabled',
  DISABLE: 'Disable',
  DISABLED: 'Disabled',
  INFORMATION: 'Information',
  ENTER_TEXT: 'Enter Text...',
  ATTRIBUTE_REQUIRED: 'Attribute Required',
  DESCRIPTION: 'Description',
  EDIT_ATTRIBUTE: 'Edit Attribute',
  DELETE_ATTRIBUTE: 'Delete Attribute',
  ADD_GROUP: 'Add Group',
  GROUP_NAME: 'Group Name',
  USER_NAME: 'Username',
  EDIT_GROUP: 'Edit Group',
  DELETE_GROUP: 'Delete Group',
  USER_GROUP: 'User Group',
  ASSIGN_USERS: 'Assign Users',
  SOURCE: 'Source',
  STRING: 'String',
  INTEGER: 'Integer',
  BOOLEAN: 'Boolean',
  DATE: 'Date',
  DECIMAL: 'Decimal',
  CHANGE_PASSWORD_SETTINGS: 'Change Password Settings',
  ZS_SERVICES: 'Zscaler Services',
  CLOUD_AND_ORG_ID: 'Cloud and Org ID',
  APPLICATION_DETAIL: 'Application Detail',
  CLONE_MEMBERSHIP_FOR_APPLICATION: 'Clone Membership From This Application',
  ROLE: 'Role',
  ROLES: 'Roles',
  MANAGE_ROLES: 'Manage Roles',
  SYNC: 'Sync',
  USERS_ASSIGNMENT: 'Users Assignment',
  GROUPS_ASSIGNMENT: 'User Group Assignment',
  ASSIGN_ROLE: 'Assign Role',
  ASSIGN_SCOPE: 'Assign Scope',
  ASSIGN_USER_GROUPS: 'Groups Assignment',
  ADD_MORE: 'Add More',
  ASSIGN_USERS_AND_GROUPS: 'Assign Users and User Groups',
  REVIEW: 'Review',
  USERS_AND_GROUPS: 'User and User Groups',
  TYPE: 'Type',
  EDIT_USERS_ROLE: 'Edit Role',
  DELETE_USERS_ROLE: 'Delete User',
  EDIT_GROUPS_ROLE: 'Edit Role',
  DELETE_GROUPS_ROLE: 'Delete Group',
  BULK_DELETE_USERS_GROUPS_ROLE: 'Bulk Delete Users and Groups',
  CONFIGURATION_TYPE: 'Configuration Type',
  RECOMMENDED: 'Recommended',
  CUSTOM: 'Custom',
  PASSWORD_COMPLEXITY: 'Password Complexity',
  PASSWORD_LENGTH: 'Password Length',
  MIN_LWR_CASE: 'Minimum No. of Lowercase Letters',
  MIN_UPPR_CASE: 'Minimum No. of Uppercase Letters',
  MIN_NUMERIC: 'Minimum No. of Numeric Characters',
  MIN_SPL_CHAR: 'Minimun No. of Special Characters',
  PASSWORD_DOES_NOT_INCLUDE:
    'Password must not include company name, username, first name, or last name',
  REJECT_REUSE: 'Reject reuse of last 5 passwords',
  DEACTIVATE_USER: 'Deactivate user after 10 unsuccessful attempts',
  ALLOW_ADMIN_SET_PASSWORD: "Allow administrator to create or change user's password",
  FORCE_PASSWORD_CHANGE: 'Enforce password change after the initial login',
  PASSWORD_EXPIRY: 'Password expiration period (in days)',
  PASSWORD_CRITERIA: 'Password Criteria',
  POLICY: 'Policy',
  POLICIES: 'Policies',
  AUTHENTICATION_POLICY: 'Authentication Policy',
  SIGNON_POLICY: 'Sign-On Policy',
  PASSWORD_POLICY: 'Password Policy',
  LOAD_MORE: 'Load More',
  EDIT_EMAIL: 'Edit Email Address',
  UPDATE_EMAIL: 'Update Email Address',
  NEW_EMAIL: 'Enter New Email Address',
  VALIDATE_EMAIL: 'Validate Email',
  VALIDATION_CODE: 'Enter Validation Code',
  RESEND_CODE: 'Resend Code',
  CURRENT_PASSWORD: 'Current Password',
  NEW_PASSWORD: 'New Password',
  CONFIRM_NEW_PASSWORD: 'Confirm New Password',
  MIN_LENGTH_REQUIRED: '{{value}} characters minimum',
  MIN_LOWER_CASE_REQUIRED: 'At least {{value}} lowercase character (a-z)',
  MIN_UPPER_CASE_REQUIRED: 'At least {{value}} uppercase character (A-Z)',
  MIN_NUMERIC_REQUIRED: 'At least {{value}} numeric character (0-9)',
  MIN_SPECIAL_CHAR_REQUIRED: 'At least {{value}} special character',
  SIGN_ON_POLICIES: 'Sign-On Policy',
  ADD_RULE: 'Add Rule',
  RULE_ORDER: 'Rule Order',
  RULE_STATUS: 'Rule Status',
  RULE_NAME: 'Rule Name',
  RULE_ACTION: 'Rule Action',
  CRITERIA: 'Criteria',
  ADD_SIGN_ON_POLICY: 'Add Sign-On Policy',
  EDIT_SIGN_ON_POLICY: 'Edit Sign-On Policy',
  DELETE_SIGN_ON_POLICY: 'Delete Sign-on Policy',
  LOCATION: 'Location',
  LOCATION_GROUP: 'Location Group',
  ALLOW: 'Allow',
  DENY: 'Deny',
  OPERATIONS: 'Operations',
  REMOVE: 'Remove',
  NO_DATA_FOUND: 'No Data found',
  NO_ITEMS_FOUND: 'No items found',
  BROWSE_FILE: 'Browse File',
  IMPORT: 'Import',
  NO_FILE_CHOOSEN: 'No File Chosen',
  OVERRIDE_EXISTING_ENTRIES: 'Override Existing Entries',
  CSV_FILE: 'CSV File',
  SAMPLE_CSV_DOWNLOAD: 'Download Sample Import CSV File',
  IMPORT_USERS: 'Import Users',
  IMPORT_GROUPS: 'Import Groups',
  PRIMARY_IDP_PROVIDER: 'Primary Identity Provider',
  SECONDARY_IDP_PROVIDER: 'Secondary Identity Providers',
  ADD_PRIMARY_IDP: 'Add Primary IdP',
  ADD_SECONDARY_IDP: 'Add Secondary IdP',
  ADD_PRIMARY_IDENTITY_PROVIDER: 'Add Primary Identity Provider',
  EDIT_PRIMARY_IDENTITY_PROVIDER: 'Edit Primary Identity Provider',
  DELETE_PRIMARY_IDENTITY_PROVIDER: 'Delete Primary Identity Provider',
  ADD_SECONDARY_IDENTITY_PROVIDER: 'Add Secondary Identity Provider',
  EDIT_SECONDARY_IDENTITY_PROVIDER: 'Edit Secondary Identity Provider',
  DELETE_SECONDARY_IDENTITY_PROVIDER: 'Delete Secondary Identity Provider',
  SAML_CONFIGURATION: 'SAML CONFIGURATION',
  IDENTITY_VENDOR: 'Identity Vendor',
  DOMAIN: 'Domain',
  YES: 'YES',
  NO: 'No',
  ADD_LOCATION: 'Add Location',
  EDIT_LOCATION: 'Edit Location',
  DELETE_LOCATION: 'Delete Location',
  IMPORT_LOCATION: 'Import Location',
  DELETE_LOCATION_CONFIRMATION_MESSAGE:
    'Are you sure you want to delete this location? The changes cannot be undone.',
  BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE:
    'Are you sure you want to bulk delete these locations? The changes cannot be undone.',
  IP_ADDRESS: 'IP Address',
  COUNTRY: 'Country',
  LOCATION_INFORMATION: 'Location Information',
  NAME_REQUIRED_MESSAGE: 'Name is required',
  LOCATIONS_REQUIRED_MESSAGE: 'Locations is required',
  COUNTRY_REQUIRED_MESSAGE: 'Country is required',
  IP_ADDRESS_REQUIRED_MESSAGE: 'IP Address is required',
  LOCATION_COUNT: 'Location Count',
  LOCATIONS: 'Locations',
  UNSELECTED_LOCATIONS: 'Unselected Locations',
  SELECTED_LOCATIONS: 'Selected Locations',
  ADD_LOCATION_GROUP: 'Add Location Group',
  EDIT_LOCATION_GROUP: 'Edit Location Group',
  DELETE_LOCATION_GROUP: 'Delete Location Group',
  DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE:
    'Are you sure you want to delete this location group? The changes cannot be undone.',
  BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE:
    'Are you sure you want to bulk delete these location groups? The changes cannot be undone.',
  IMPORT_LOCATION_GROUP: 'Import Location Group',
  PROTOCOL: 'Protocol',
  SAML_REQUEST_SIGNING: 'SAML Request Signing',
  ENABLE_SAML_REQUEST_SIGNING: 'Enable SAML Request Signing',
  SIGNING_ALGORITHM: 'Signing Algorithm',
  REQUEST_SIGNING_CERTIFICATE: 'Request Signing Certificate',
  SP_SAML_CERTIFICATE: 'SP SAML Certificate',
  DOWNLOAD_CERTIFICATE: 'Download Certificate',
  ENCRYPTED_SAML_RESPONSE: 'Encrypted SAML Response',
  ENABLE_ENCRYPTED_SAML_ASSERTION: 'Enable Encrypted SAML Assertion',
  CERTIFICATE: 'Certificate',
  SAML_ENCRYPTION_CERTIFICATE: 'SAML Encryption Certificate',
  IDP_METADATA: 'IdP Metadata',
  IDP_METADATA_URL: 'IdP Metadata URL',
  IDP_CERTIFICATE: 'IdP Certificate',
  IDP_ENTITY_URI: 'IdP Entity URI',
  PRIMARY_IDP_NOT_AVAILABLE: 'Primary Identity Provider is not added.',
  SECONDARY_IDP_NOT_AVAILABLE: 'Secondary Identiy Provider Not Available',
  SP_METADATA: 'SP Metadata',
  DOWNLOAD_SP_METADATA: 'Download SP Metadata',
  IDP_SINGLE_SIGNON_URL: 'IdP Single Sign-On URL',
  SP_ENTITY_ID: 'SP Entity ID',
  SP_URL: 'SP URL',
  SCIM_PROVISIONING_STATUS: 'SCIM Provisioning Status',
  PROVISIONING_SETTINGS: 'Provisioning Settings',
  JIT_PROVISIONING: 'Just-in-time (JIT) Provisioning',
  ENABLE_JIT_PROVISIONING: 'Enable Just-in-time (JIT) Provisioning',
  JIT_ATTRIBUTE_MAPPING: 'JIT Attribute Mapping',
  SAML_ATTRIBUTE_MAPPING: 'SAML Attribute Mapping',
  USER_GROUP_SAML_ATTRIBUTE: 'User Group SAML Attribute',
  SAML_ATTRIBUTE: 'SAML Attribute',
  USER_ATTRIBUTE: 'User Attribute',
  SCIM_PROVISIONING: 'SCIM Provisioning',
  ENABLE_SCIM_PROVISIONING: 'Enable SCIM Provisioning',
  SCIM_ATTRIBUTE_MAPPING: 'SCIM Attribute Mapping',
  SCIM_ATTRIBUTE: 'SCIM Attribute',
  SCIM_ENDPOINT_URL: 'SCIM Endpoint URL',
  AUTHENTICATION_METHOD: 'Authentication Method',
  TOKEN: 'Bearer Token',
  GENERATE_TOKEN: 'Generate Token',
  INPUT_METHOD: 'Input Method',
  FETCH_WITH_URL: 'Metadata URL',
  UPLOAD_METADATA: 'Upload Metadata',
  MANUAL_ENTRY: 'Enter Manually',
  UPLOAD_CERTIFICATE: 'Upload Certificate',
  UPLOAD_IDP_METADATA: 'Upload IdP Metadata',
  BEARER_TOKEN: 'Bearer Token',
  IDP_ADVANCED_SETTINGS: 'IdP Advanced Settings',
  SESSION_TIMEOUT: 'Session Timeout',
  SESSION_TIMEOUT_DURATION_IN_MIN: 'Session Timeout Duration (in Minutes)',
  NONE: 'None',
  TIME_RANGE: 'Time Range',
  ACTION: 'Action',
  CATEGORY: 'Category',
  SUB_CATEGORY: 'Sub Category',
  INTERFACE: 'Interface',
  RESULT: 'Result',
  LOGIN_ID: 'login Id',
  SCOPE: 'Scope',
  SCOPES_N_ROLES: 'Scopes and Roles',
  ADD_ITEMS: 'Add Items',
  TIME_STAMP: 'Time stamp',
  RESOURCE: 'Resource',
  ADMIN_ID: 'Admin ID',
  CLIENT_IP: 'Client IP',
  AM: 'AM',
  TIME_PM: 'PM',
  START_TIME: 'Start Time',
  END_TIME: 'END TIME',
  CURRENT_DAY: 'Today',
  CURRENT_WEEK: 'Current Week',
  CURRENT_MONTH: 'Current Month',
  PREVIOUS_DAY: 'Previous Day',
  PREVIOUS_WEEK: 'Previous Week',
  PREVIOUS_MONTH: 'Previous Month',
  HELP_BROWSER: 'Help',
  SELECT_DATE: 'Select Date',
  CLOSE: 'Close',
  ACCEPT: 'Accept',
  SYNC_DOMAINS: 'Sync Domains',
  IMPORT_RESULTS: 'Import Results',
  COMPLETE: 'Complete',
  PROCESSED_RECORD: 'Processed Records',
  TOTAL_RECORDS_ADDED: 'Total Records Added',
  TOTAL_RECORDS_DELETED: 'Total Records Deleted',
  TOTAL_RECORDS_IN_IMPORT: 'Total Records In Import',
  TOTAL_RECORDS_UPDATED: 'Total Records Updated',
  FAILED_RECORDS: 'Failed Records',
  DUPLICATE_ITEM: 'The given name is already in use',
  RESOURCE_NOT_FOUND: 'Resource not found',
  CSV_FORMAT_INVALID: 'Invalid CSV Format',
  UNEXPECTED_ERROR: 'Unexpected Error',
  DUPLICATE_RECORD: 'Duplicate Record',
  CONFIGURATION_CHANGES: 'Configuration Changes',
  PRE_CHANGES_CONFIGURATION: 'Pre-Changes Configuration',
  POST_CHANGES_CONFIGURATION: 'Post-Changes Configuration',
  VIEW_CHANGES: 'View Changes',
  OPEN_HELP_BROWSER: 'Open Help Browser',
  ALL: 'All',
  SIGN_IN: 'Sign In',
  SIGN_OUT: 'Sign Out',
  CREATE: 'Create',
  GET: 'Get',
  SEARCH: 'Search',
  BULK: 'Bulk',
  DOWNLOAD: 'Download',
  UPLOAD: 'Upload',
  USER_MANAGEMENT: 'User Management',
  LOGIN: 'Login',
  AUTHENTICATION_SETTINGS: 'Authentication Settings',
  TENANTS: 'Tenants',
  USER: 'User',
  CUSTOM_USER_ATTRIBUTE: 'Custom User Attribute',
  PASSWORD_CHANGE: 'Password Change',
  PASSWORD_POLICY_CHANGE: 'Password Policy Change',
  IDENTITY_PROVIDERS: 'Identity Providers',
  ADVANCE_SETTINGS: 'Advance Settings',
  SIGN_ON_POLICY: 'Sign-On Policy',
  SERVICE_ASSIGNMENT: 'Service Assignment',
  SERVICES: 'Services',
  SCIM_API: 'SCIM API',
  FAILURE: 'Failure',
  PARTIALLY_FAILED: 'Partially Failed',
  REMOVE_PAGE: 'Remove Page',
  REMOVE_ALL: 'Remove All',
  CONFIRMATION_REMOVE_PAGE: 'Remove Page',
  CONFIRMATION_REMOVE_ALL: 'Remove All',
  CONFIRM: 'Confirm',
  ENTER_FIRST_LAST_NAME: 'Enter First/Last Name',
  CUSTOMISE_COLUMNS: 'CUSTOMISE COLUMNS',
};

export default enUS;
