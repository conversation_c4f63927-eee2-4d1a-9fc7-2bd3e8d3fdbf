import { initReactI18next } from 'react-i18next';

import i18n, { use } from 'i18next';

// translated labels
import { LOCALES, getSelectedLocale } from './locales';

const resources = {};

Object.keys(LOCALES).forEach((locale) => (resources[locale] = { translation: LOCALES[locale] }));

// Docs
// https://www.i18next.com/
// React https://react.i18next.com/

use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: getSelectedLocale(),
    fallbackLng: ['en-US'],
    load: 'currentOnly',
    keySeparator: false, // we do not use keys in form messages.welcome

    interpolation: {
      escapeValue: false, // react already safes from xss
    },
  });

export default i18n;
