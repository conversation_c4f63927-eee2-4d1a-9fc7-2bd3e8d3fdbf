import axios from 'axios';

import { BASE_URL } from '../../config';
import { handleError } from './handleError';

const http = axios.create({
  baseURL: BASE_URL,
  timeout: 86400000, // increased to support image download (usually 1 - 2 GB)
  withCredentials: true,
  headers: { 'Content-Type': 'application/json' },
});

http.interceptors.response.use((response) => response, handleError);

export default http;
