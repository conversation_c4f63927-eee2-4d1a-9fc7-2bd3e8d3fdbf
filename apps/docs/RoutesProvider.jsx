import { BrowserRouter } from 'react-router-dom';

import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  children: null,
};

const RoutesProvider = ({ children }) => {
  return <BrowserRouter basename={process.env.ASSET_PATH}>{children}</BrowserRouter>;
};

RoutesProvider.DEFAULT_PROPS = DEFAULT_PROPS;

RoutesProvider.propTypes = {
  children: PropTypes.any,
};

export default RoutesProvider;
