# common script

all: prune workspace lib docs admin format

workspace:
	echo $@
	npm run update

lib:
	echo $@
	cd packages/zui-component-library && npm run update

docs:
	echo $@
	cd apps/docs && npm run update

admin:
	echo $@
	cd apps/admin && npm run update

prune:
	echo $@
	rm -rf pnpm-lock.yaml package-lock.json yarn.lock
	find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +
	find . -name 'dist' -type d -prune -exec rm -rf '{}' +
	find . -name 'build' -type d -prune -exec rm -rf '{}' +
	find . -name '.turbo' -type d -prune -exec rm -rf '{}' +
	
i:
	echo install
	make prune
	pnpm i

format:
	echo $@
	npm run format

rename:
	echo $@
	for file in ./**/*.jsx; do mv "$file" "${file%.jsx}.tsx"; done