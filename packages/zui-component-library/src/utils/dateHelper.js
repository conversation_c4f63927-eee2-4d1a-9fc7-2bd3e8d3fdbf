import dayjs from 'dayjs';
import minMax from 'dayjs/plugin/minMax.js';

dayjs.extend(minMax);

export function getTimeRange(level) {
  if (level === 'ZIDENTITY_DASHBOARD') {
    return [
      'ONE_DAY',
      'TWO_DAYS',
      'SEVEN_DAYS',
      'FOURTEEN_DAYS',
      'THIRTY_DAYS',
      'SIXTY_DAYS',
      'NINTY_DAYS',
    ];
  }

  if (level === 'REPORT') {
    return [
      'CURRENT_DAY',
      'CURRENT_WEEK',
      'CURRENT_MONTH',
      'PREVIOUS_DAY',
      'PREVIOUS_WEEK',
      'PREVIOUS_MONTH',
      'CUSTOM',
    ];
  }

  if (level === 'TOKEN') {
    return [
      'ALL',
      'TODAY',
      'LAST_FIFTEEN_MINUTES',
      'LAST_THIRTY_MINUTES',
      'LAST_ONE_HOUR',
      'LAST_TWENTY_FOUR_HOURS',
      'LAST_FORTY_EIGHT_HOURS',
      'CUSTOM',
    ];
  }

  if (level === 'EXPIRY') {
    return [
      'ALL',
      'TODAY',
      'LAST_FIFTEEN_MINUTES',
      'LAST_THIRTY_MINUTES',
      'LAST_ONE_HOUR',
      'LAST_TWENTY_FOUR_HOURS',
      'LAST_FORTY_EIGHT_HOURS',
      'NEXT_FIFTEEN_MINUTES',
      'NEXT_THIRTY_MINUTES',
      'NEXT_ONE_HOUR',
      'NEXT_TWENTY_FOUR_HOURS',
      'CUSTOM',
    ];
  }

  return [];
}

export function getDateRange({ name, formatPattern, startDate, endDate }) {
  let startTime = '';
  let endTime = '';
  let isRangeSame = false;
  let displayText = '';

  if (name === 'LAST_ONE_MINUTE') {
    startTime = dayjs().subtract(1, 'minutes').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_TWO_MINUTES') {
    startTime = dayjs().subtract(2, 'minutes').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_FIVE_MINUTES') {
    startTime = dayjs().subtract(5, 'minutes').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_FIFTEEN_MINUTES') {
    startTime = dayjs().subtract(15, 'minutes').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_THIRTY_MINUTES') {
    startTime = dayjs().subtract(30, 'minutes').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_ONE_HOUR') {
    startTime = dayjs().subtract(1, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_TWO_HOURS') {
    startTime = dayjs().subtract(2, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_FIVE_HOURS') {
    startTime = dayjs().subtract(5, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_TWENTY_FOUR_HOURS') {
    startTime = dayjs().subtract(24, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_FORTY_EIGHT_HOURS') {
    startTime = dayjs().subtract(48, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'LAST_TEN_HOURS') {
    startTime = dayjs().subtract(10, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'CURRENT_DAY' || name === 'TODAY') {
    startTime = dayjs().startOf('day').toDate();
    endTime = new Date();
    isRangeSame = true;
    displayText = dayjs(startTime).format(formatPattern);
  }

  if (name === 'ONE_DAY') {
    startTime = dayjs().subtract(24, 'hours').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'TWO_DAYS') {
    startTime = dayjs()
      .subtract(2 * 24, 'hours')
      .toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'SEVEN_DAYS') {
    startTime = dayjs()
      .subtract(7 * 24, 'hours')
      .toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'CURRENT_WEEK') {
    startTime = dayjs().startOf('week').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'FOURTEEN_DAYS') {
    startTime = dayjs()
      .subtract(14 * 24, 'hours')
      .toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'THIRTY_DAYS') {
    startTime = dayjs()
      .subtract(30 * 24, 'hours')
      .toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'CURRENT_MONTH') {
    startTime = dayjs().startOf('month').toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'PREVIOUS_DAY') {
    startTime = dayjs().subtract(1, 'day').startOf('day').toDate();
    endTime = dayjs().subtract(1, 'day').endOf('day').toDate();
    isRangeSame = true;
    displayText = dayjs(startTime).format(formatPattern);
  }

  if (name === 'PREVIOUS_WEEK') {
    startTime = dayjs().subtract(1, 'week').startOf('week').startOf('day').toDate();
    endTime = dayjs().subtract(1, 'week').endOf('week').endOf('day').toDate();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'SIXTY_DAYS') {
    startTime = dayjs()
      .subtract(60 * 24, 'hours')
      .toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'PREVIOUS_MONTH') {
    startTime = dayjs().subtract(1, 'month').startOf('month').startOf('day').toDate();
    endTime = dayjs().subtract(1, 'month').endOf('month').endOf('day').toDate();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'NINTY_DAYS') {
    startTime = dayjs()
      .subtract(90 * 24, 'hours')
      .toDate();
    endTime = new Date();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern);
  }

  if (name === 'NEXT_FIFTEEN_MINUTES') {
    startTime = new Date();
    endTime = dayjs().add(15, 'minutes').toDate();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'NEXT_THIRTY_MINUTES') {
    startTime = new Date();
    endTime = dayjs().add(30, 'minutes').toDate();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'NEXT_ONE_HOUR') {
    startTime = new Date();
    endTime = dayjs().add(60, 'minutes').toDate();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'NEXT_TWENTY_FOUR_HOURS') {
    startTime = new Date();
    endTime = dayjs().add(24, 'hours').toDate();
    isRangeSame = false;
    displayText =
      dayjs(startTime).format('M/D/YYYY h:m:s A') +
      ' - ' +
      dayjs(endTime).format('M/D/YYYY h:m:s A');
  }

  if (name === 'CUSTOM' && startDate && endDate) {
    startTime = startDate;
    endTime = endDate;
    isRangeSame = false;
    displayText =
      startDate && startDate
        ? dayjs(startTime).format(formatPattern) + ' - ' + dayjs(endTime).format(formatPattern)
        : '';
  }

  return {
    startTime,
    endTime,
    isRangeSame,
    displayText,
  };
}

export function getDatesInRange({ startDate, endDate, amount, unit }) {
  const ranges = [];

  const startOfRange = dayjs.min(startDate, endDate);
  const endOfRange = dayjs.max(startDate, endDate);

  let currentDate = dayjs(startOfRange);

  while (currentDate.isBefore(endOfRange)) {
    currentDate = currentDate.add(amount, unit);
    ranges.push(currentDate.toDate());
  }

  return ranges;
}
