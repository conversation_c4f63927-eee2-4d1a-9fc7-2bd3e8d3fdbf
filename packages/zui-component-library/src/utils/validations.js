export const patterns = {
  emailAddress:
    /^[a-zA-Z0-9_+&*-][a-zA-Z0-9_#+&*-]*(?:\.[a-zA-Z0-9_#+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/i,
  ipAddress:
    /^(\d|[1-9]\d|1\d\d|2([0-4]\d|5[0-5]))\.(\d|[1-9]\d|1\d\d|2([0-4]\d|5[0-5]))\.(\d|[1-9]\d|1\d\d|2([0-4]\d|5[0-5]))\.(\d|[1-9]\d|1\d\d|2([0-4]\d|5[0-5]))$/,
};

export const validEmail = (val) => {
  if (patterns.emailAddress.test(val) === true) {
    return '';
  }
  return 'VALIDATION_ERROR_INVALID_EMAIL_ADDRESS';
};

export const required = (val, msg) => {
  const message = msg || 'VALIDATION_ERROR_REQUIRED';
  let result;

  if (val == null) {
    result = message;
  } else if (typeof val === 'string' && val.trim().length === 0) {
    result = message;
  } else if (val instanceof Array && val.length === 0) {
    result = message;
    // } else if (val instanceof Object && _.isEmpty(val)) {
  } else if (val instanceof Object && val.length === 0) {
    result = message;
  } else {
    result = true;
  }
  return result;
};

const notRequired = (val) => {
  return val == null || val instanceof Array || val === '';
};

export const checkPassword = (val, strength) => {
  let result;
  if (notRequired(val)) {
    result = true;
  }

  const errorMsgStrongStrength = 'VALIDATION_ERROR_PASSWORD_NOT_STRONG';
  // const errorMsgMediumStrength = 'VALIDATION_ERROR_PASSWORD_NOT_MEDIUM_STRONG';

  // https://docs.google.com/document/d/1gsQcw3-k_zKTSonHBvMbxKSlhHRyB3j73TngCMsIMTo/edit#
  // All regex is copied from CA team to make authentication same across the product.
  let regex = null;
  if (strength === 'MEDIUM') {
    // when strength is medium, we check if
    // 1. length is greater or equal to 8
    // 2. has at least one non-alphabetic character.
    regex = /[^a-zA-Z]/;
    if (val.length < 8 || !regex.test(val)) {
      // result = errorMsgMediumStrength;
      return false;
    }
    result = true;
  } else if (strength === 'STRONG') {
    // when strength is strong, we check if
    // 1. length is greater or equal to 8
    // 2. at least one digit
    // 3. at least one capital letter
    // 4. at least one non-alphanumeric character
    if (val.length < 8) {
      result = errorMsgStrongStrength;
    }
    regex = /[^a-zA-Z0-9]/;
    if (!regex.test(val)) {
      result = errorMsgStrongStrength;
    }
    regex = /[0-9]/;
    if (!regex.test(val)) {
      result = errorMsgStrongStrength;
    }
    regex = /[A-Z]/;
    if (!regex.test(val)) {
      result = errorMsgStrongStrength;
    }
    result = true;
  } else {
    result = true;
  }
  return result;
};

export const getPasswordValidityDetail = (password, passwordConfig = {}) => {
  let isValid = true;

  const config = {
    minLength: 8,
    minLowerCase: 1,
    minUpperCase: 1,
    minNumeric: 1,
    minSpecialChar: 1,
    ...passwordConfig,
  };

  const validationDetail = {
    minLength: { isValid: true, message: 'MIN_LENGTH_REQUIRED', config },
    minLowerCase: { isValid: true, message: 'MIN_LOWER_CASE_REQUIRED', config },
    minUpperCase: { isValid: true, message: 'MIN_UPPER_CASE_REQUIRED', config },
    minNumeric: { isValid: true, message: 'MIN_NUMERIC_REQUIRED', config },
    minSpecialChar: { isValid: true, message: 'MIN_SPECIAL_CHAR_REQUIRED', config },
  };

  if (password.length < config.minLength) {
    isValid = false;
    validationDetail.minLength.isValid = false;
  }

  if ((password.match(/[a-z]/g) || []).length < config.minLowerCase) {
    isValid = false;
    validationDetail.minLowerCase.isValid = false;
  }

  if ((password.match(/[A-Z]/g) || []).length < config.minUpperCase) {
    isValid = false;
    validationDetail.minUpperCase.isValid = false;
  }

  if ((password.match(/\d/g) || []).length < config.minNumeric) {
    isValid = false;
    validationDetail.minNumeric.isValid = false;
  }

  if ((password.match(/[^a-zA-Z0-9]/g) || []).length < config.minSpecialChar) {
    isValid = false;
    validationDetail.minSpecialChar.isValid = false;
  }

  return { validationDetail, isValid };
};

export const replaceNonNumericDigit = ({
  value,
  replaceString = '',
  allowDecimal = true,
  customRegex,
}) => {
  // use to get positive integer, avoid -,+, e(exponential) declaration etc

  const positiveNumberWithFraction = /^\d+[.]\d+|\d+$/g;
  const purePositiveNumber = /[^\d]+/g;

  let regex = positiveNumberWithFraction;

  if (!allowDecimal) {
    regex = purePositiveNumber;
  }

  if (customRegex) {
    regex = customRegex;
  }

  if (allowDecimal) {
    return value.replace(regex, '$1');
  }

  return value.replace(regex, replaceString);
};
