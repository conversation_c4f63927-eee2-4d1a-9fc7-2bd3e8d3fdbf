export function getFormattedClassName(className) {
  let formattedClassName = (className + '').trim();

  formattedClassName = formattedClassName.replace(/ +/g, ' ');

  return formattedClassName;
}

export function getOnChangeValue({ name, value, checked, type } = {}) {
  return {
    [name]: type === 'checkbox' ? checked : value,
  };
}

export function mergeFormValues(evt) {
  const updatedValue = getOnChangeValue(evt.target);

  return (prevState) => ({ ...prevState, ...updatedValue });
}

export function getSearchParams({ searchTerm, allowMultiple = false } = {}) {
  const queryParams = new URLSearchParams(searchTerm);

  for (let searchKey of new Set(queryParams.keys())) {
    queryParams[searchKey] = allowMultiple
      ? queryParams.getAll(searchKey)
      : queryParams.get(searchKey);
  }

  return queryParams;
}

export function isDOMElementPresent({
  id = '',
  className = '',
  name = '',
  tagName = '',
  tagNameNS = '',
}) {
  let isPresent = false;
  let element;

  if (id) {
    element = document.getElementById(id);
  }

  if (className) {
    element = document.getElementsByClassName(className);
  }

  if (name) {
    element = document.getElementsByName(name);
  }

  if (tagName) {
    element = document.getElementsByTagName(tagName);
  }

  if (tagNameNS) {
    element = document.getElementsByTagNameNS(tagNameNS);
  }

  if (element) {
    isPresent = true;
  }

  return { isPresent, element };
}

export function createDOMElement({
  tagName = 'div',
  parentElement = document.body,
  attributes = {},
}) {
  const element = document.createElement(tagName);

  for (const [key, value] of Object.entries(attributes)) {
    element[key] = value;
  }

  parentElement.appendChild(element);

  return true;
}

export const checkAndCreateDomElement = ({
  tagName = 'div',
  parentElement = document.body,
  attributes = {},
} = {}) => {
  const { isPresent, element } = isDOMElementPresent({ ...attributes });

  if (!isPresent) {
    createDOMElement({ tagName, parentElement, attributes: { ...attributes } });

    return isDOMElementPresent({ ...attributes });
  }

  return { isPresent, element };
};

export const checkAndCreateDomElementAsync = async ({
  tagName = 'div',
  parentElement = document.body,
  attributes = {},
  timeout = 0,
} = {}) => {
  return new Promise((resolve) => {
    const timer = setTimeout(() => {
      clearTimeout(timer);

      resolve(checkAndCreateDomElement({ tagName, parentElement, attributes: { ...attributes } }));
    }, timeout);
  });
};
