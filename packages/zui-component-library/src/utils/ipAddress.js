import { Address4, Address6 } from 'ip-address';

export const ipAddressValidators = ({ item }) => {
  if (!item) {
    return false;
  }

  if ((item + '').includes('-')) {
    const [firstIp, secondIp] = item.split('-');

    if (
      (Address4.isValid(firstIp) && Address4.isValid(secondIp)) ||
      (Address6.isValid(firstIp) && Address6.isValid(secondIp))
    ) {
      return true;
    } else {
      return false;
    }
  }

  if (Address4.isValid(item) || Address6.isValid(item)) {
    return true;
  } else {
    return false;
  }
};
