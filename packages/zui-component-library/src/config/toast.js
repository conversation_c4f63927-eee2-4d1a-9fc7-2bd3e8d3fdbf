import { cloneDeep } from 'lodash-es';

import { API_METHOD } from '../utils/http';

let notificationOptions = {
  hideOnClick: true,
  autoHide: true,
  delay: 3000,
};

export const getNotificationOptions = () => cloneDeep(...notificationOptions);

export const setNotificationOptions = (newOptions = {}, clean = false) => {
  if (clean) {
    notificationOptions = { ...newOptions };
  } else {
    notificationOptions = { ...notificationOptions, ...newOptions };
  }
};

let apiMethodMessageOption = {
  [API_METHOD.GET]: { message: '', translationMapping: {} },
  [API_METHOD.PUT]: { message: 'ALL_CHANGES_SAVED', translationMapping: {} },
  [API_METHOD.POST]: { message: 'ALL_CHANGES_SAVED', translationMapping: {} },
  [API_METHOD.DELETE]: { message: 'ITEM_HAS_BEEN_DELETED', translationMapping: {} },
};

export const getApiMethodMessageOption = (method) => {
  const options = cloneDeep(apiMethodMessageOption);

  if (method) {
    return options[method] || {};
  } else {
    return options;
  }
};

export const setApiMethodMessageOption = (newOptions = {}, clean = false) => {
  if (clean) {
    apiMethodNotificationOptions = { ...newOptions };
  } else {
    apiMethodNotificationOptions = { ...apiMethodNotificationOptions, ...newOptions };
  }
};

/**
 * As of now the order of import matters
 * e.g. the correct order for overwriting config would be
 * setNotificationOptions()
 * setApiMethodMessageOption()
 * getApiMethodNotificationOptions()
 *
 * other wise config will pick outdated options
 *
 * TODO: change api config if message and options changes
 */

let apiMethodNotificationOptions = {
  [API_METHOD.GET]: { ...apiMethodMessageOption[API_METHOD.GET], ...notificationOptions },
  [API_METHOD.PUT]: { ...apiMethodMessageOption[API_METHOD.PUT], ...notificationOptions },
  [API_METHOD.POST]: { ...apiMethodMessageOption[API_METHOD.POST], ...notificationOptions },
  [API_METHOD.DELETE]: { ...apiMethodMessageOption[API_METHOD.DELETE], ...notificationOptions },
};

export const getApiMethodNotificationOptions = (method) => {
  const options = cloneDeep(apiMethodNotificationOptions);

  if (method) {
    return options[method] || {};
  } else {
    return options;
  }
};

export const setApiMethodNotificationOptions = (newOptions = {}, clean = false) => {
  if (clean) {
    apiMethodNotificationOptions = { ...newOptions };
  } else {
    apiMethodNotificationOptions = { ...apiMethodNotificationOptions, ...newOptions };
  }
};

export const getApiGETNotificationOptions = () => getApiMethodNotificationOptions(API_METHOD.GET);

export const getApiPOSTNotificationOptions = () => getApiMethodNotificationOptions(API_METHOD.POST);

export const getApiPUTNotificationOptions = () => getApiMethodNotificationOptions(API_METHOD.PUT);

export const getApiDELETENotificationOptions = () =>
  getApiMethodNotificationOptions(API_METHOD.DELETE);
