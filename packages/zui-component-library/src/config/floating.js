const PORTAL_ROOT_ID = 'zui-floating-portal';

let floatingPortalRootId = PORTAL_ROOT_ID;

export const getFloatingPortalRootId = () => floatingPortalRootId;

export const setFloatingPortalRootId = (rootId = floatingPortalRootId) =>
  (floatingPortalRootId = rootId);

let modalRootId = floatingPortalRootId;
let CRUDModalRootId = '';

export const getModalRootId = () => modalRootId;

export const setModalRootId = (rootId = modalRootId) => (modalRootId = rootId);

export const getCRUDModalRootId = () => CRUDModalRootId;

export const setCRUDModalRootId = (rootId = CRUDModalRootId) => (CRUDModalRootId = rootId);

let toastPortalRootId = floatingPortalRootId;

export const getToastPortalRootId = () => toastPortalRootId;

export const setToastPortalRootId = (rootId = toastPortalRootId) => (toastPortalRootId = rootId);
