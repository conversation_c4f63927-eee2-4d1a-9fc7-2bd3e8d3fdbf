import { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';

import { faCheckCircle, faCircleMinus, faCircleXmark } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const STATUS_TAGS_TYPES = {
  ENABLED_DISABLED: 'ENABLED_DISABLED',
  ALLOWED_BLOCKED: 'ALLOWED_BLOCKED',
};

const DEFAULT_PROPS = {
  containerRef: {},
  type: '',
  value: true,
  truthyIcon: '',
  truthyIconClass: '',
  truthyIconStyle: {},
  truthyLabel: '',
  truthyLabelClass: '',
  truthyLabelStyle: {},
  falsyIcon: '',
  falsyIconClass: '',
  falsyIconStyle: {},
  falsyLabel: '',
  falsyLabelClass: '',
  falsyLabelStyle: {},
  containerClass: '',
  containerStyle: {},
};

const StatusTag = ({
  containerRef = DEFAULT_PROPS.containerRef,
  type = DEFAULT_PROPS.type,
  value = DEFAULT_PROPS.value,
  truthyIcon = DEFAULT_PROPS.truthyIcon,
  truthyIconClass = DEFAULT_PROPS.truthyIconClass,
  truthyIconStyle = DEFAULT_PROPS.truthyIconStyle,
  truthyLabel = DEFAULT_PROPS.truthyLabel,
  truthyLabelClass = DEFAULT_PROPS.truthyLabelClass,
  truthyLabelStyle = DEFAULT_PROPS.truthyLabelStyle,
  falsyIcon = DEFAULT_PROPS.falsyIcon,
  falsyIconClass = DEFAULT_PROPS.falsyIconClass,
  falsyIconStyle = DEFAULT_PROPS.falsyIconStyle,
  falsyLabel = DEFAULT_PROPS.falsyLabel,
  falsyLabelClass = DEFAULT_PROPS.falsyLabelClass,
  falsyLabelStyle = DEFAULT_PROPS.falsyLabelStyle,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  const getIconAndLabel = () => {
    const iconAndLabel = {
      icon: value ? truthyIcon : falsyIcon,
      iconClass: value ? truthyIconClass : falsyIconClass,
      iconStyle: value ? truthyIconStyle : falsyIconStyle,
      label: value ? truthyLabel : falsyLabel,
      labelClass: value ? truthyLabelClass : falsyLabelClass,
      labelStyle: value ? truthyLabelStyle : falsyLabelStyle,
    };

    if (type === STATUS_TAGS_TYPES.ENABLED_DISABLED) {
      iconAndLabel.icon = iconAndLabel.icon
        ? iconAndLabel.icon
        : value
          ? faCheckCircle
          : faCircleMinus;
      iconAndLabel.iconClass = iconAndLabel.iconClass
        ? iconAndLabel.iconClass
        : value
          ? 'has-color-success'
          : 'text-gray';
      iconAndLabel.label = iconAndLabel.label ? iconAndLabel.label : value ? 'ENABLED' : 'DISABLED';
    }

    if (type === STATUS_TAGS_TYPES.ALLOWED_BLOCKED) {
      iconAndLabel.icon = iconAndLabel.icon
        ? iconAndLabel.icon
        : value
          ? faCheckCircle
          : faCircleXmark;
      iconAndLabel.iconClass = iconAndLabel.iconClass
        ? iconAndLabel.iconClass
        : value
          ? 'has-color-success'
          : 'has-color-error';
      iconAndLabel.label = iconAndLabel.label ? iconAndLabel.label : value ? 'ALLOWED' : 'BLOCKED';
    }

    return iconAndLabel;
  };

  const { icon, iconClass, iconStyle, label, labelClass, labelStyle } = getIconAndLabel();

  return (
    <div
      ref={containerRef}
      className={getFormattedClassName(`tag ${containerClass}`)}
      style={containerStyle}
    >
      <FontAwesomeIcon
        icon={icon}
        className={getFormattedClassName(`icon left ${iconClass}`)}
        style={iconStyle}
      />

      <span className={getFormattedClassName(`text ${labelClass}`)} style={labelStyle}>
        {t(label)}
      </span>
    </div>
  );
};

StatusTag.propTypes = {
  containerRef: PropTypes.any,
  type: PropTypes.oneOf(['', 'ENABLED_DISABLED', 'ALLOWED_BLOCKED']),
  value: PropTypes.bool,
  truthyIcon: PropTypes.any,
  truthyIconClass: PropTypes.string,
  truthyIconStyle: PropTypes.object,
  truthyLabel: PropTypes.string,
  truthyLabelClass: PropTypes.string,
  truthyLabelStyle: PropTypes.object,
  falsyIcon: PropTypes.any,
  falsyIconClass: PropTypes.string,
  falsyIconStyle: PropTypes.object,
  falsyLabel: PropTypes.string,
  falsyLabelClass: PropTypes.string,
  falsyLabelStyle: PropTypes.object,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

const StatusTagForwardRef = (props, ref) => <StatusTag {...props} containerRef={ref} />;

export default forwardRef(StatusTagForwardRef);
