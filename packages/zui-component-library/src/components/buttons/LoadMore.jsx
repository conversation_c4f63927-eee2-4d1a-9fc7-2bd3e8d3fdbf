import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import Button from './Button';

const DEFAULT_PROPS = {
  pageSize: 100,
  pageOffset: 0,
  totalRecord: -1,
  onLoadMoreClick: noop,
  containerClass: '',
  containerStyle: {},
};

const LoadMore = ({
  pageSize = DEFAULT_PROPS.pageSize,
  pageOffset = DEFAULT_PROPS.pageOffset,
  totalRecord = DEFAULT_PROPS.totalRecord,
  onLoadMoreClick = DEFAULT_PROPS.onLoadMoreClick,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  const isAllRecordsFetched = totalRecord <= pageOffset + pageSize;

  if (!isAllRecordsFetched) {
    return (
      <div
        className={getFormattedClassName(`load-more-container ${containerClass}`)}
        style={containerStyle}
      >
        <Button type="tertiary" onClick={onLoadMoreClick}>
          {t('LOAD_MORE')}
        </Button>
      </div>
    );
  }

  return null;
};

LoadMore.propTypes = {
  pageSize: PropTypes.number,
  pageOffset: PropTypes.number,
  totalRecord: PropTypes.number,
  onLoadMoreClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default LoadMore;
