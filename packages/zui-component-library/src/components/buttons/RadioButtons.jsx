import { useTranslation } from 'react-i18next';

import { faCheckCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from './Button';

const DEFAULT_PROPS = {
  list: [],
  isSelected: noop,
  onClick: noop,
  containerClass: '',
  containerStyle: {},
};

const RadioButtons = ({
  list = DEFAULT_PROPS.list,
  isSelected = DEFAULT_PROPS.isSelected,
  onClick = DEFAULT_PROPS.onClick,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  return (
    <div className={`radio-buttons-container ${containerClass}`} style={containerStyle}>
      {list.map((detail, idx) => (
        <Button
          key={idx}
          type={isSelected?.(detail) ? 'primary' : ''}
          onClick={(evt) => {
            onClick?.(detail, evt);
          }}
        >
          {isSelected?.(detail) && <FontAwesomeIcon className="icon left" icon={faCheckCircle} />}
          <span>{t(detail.label)}</span>
        </Button>
      ))}
    </div>
  );
};

RadioButtons.propTypes = {
  list: PropTypes.array,
  isSelected: PropTypes.func,
  onClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default RadioButtons;
