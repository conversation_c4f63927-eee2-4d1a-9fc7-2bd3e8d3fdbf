import { useTranslation } from 'react-i18next';

import { faCheck, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { faCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  type: 'primary',
  isOn: false,
  showLabel: true,
  disabled: false,
  onLabel: 'ON',
  offLabel: 'OFF',
  onToggleClick: noop,
};

const ToggleButton = ({
  type = DEFAULT_PROPS.type,
  isOn = DEFAULT_PROPS.isOn,
  showLabel = DEFAULT_PROPS.showLabel,
  disabled = DEFAULT_PROPS.disabled,
  onLabel = DEFAULT_PROPS.onLabel,
  offLabel = DEFAULT_PROPS.offLabel,
  onToggleClick = DEFAULT_PROPS.onToggleClick,
}) => {
  const { t } = useTranslation();

  return (
    <div
      className={`toggle-button-container ${isOn ? 'on' : 'off'} ${type} ${
        disabled ? 'disabled' : ''
      }`}
      onClick={disabled ? noop : onToggleClick}
    >
      <div className="icon-container">
        <FontAwesomeIcon icon={isOn ? faCheck : faTimes} className="icon" />
        <FontAwesomeIcon icon={faCircle} className="icon toggle-box" />
      </div>
      {showLabel ? <div className="toggle-text">{isOn ? t(onLabel) : t(offLabel)}</div> : null}
    </div>
  );
};

ToggleButton.propTypes = {
  type: PropTypes.oneOf(['primary', 'success', 'error']),
  isOn: PropTypes.bool,
  showLabel: PropTypes.bool,
  disabled: PropTypes.bool,
  onLabel: PropTypes.string,
  offLabel: PropTypes.string,
  onToggleClick: PropTypes.func,
};

export default ToggleButton;
