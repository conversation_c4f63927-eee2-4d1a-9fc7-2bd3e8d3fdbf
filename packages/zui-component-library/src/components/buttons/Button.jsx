import { forwardRef } from 'react';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const getButtonClass = ({ type, containerClass, hasFullWidth, isLarge, isSmall, info }) => {
  let buttonTypeClass = 'button ';

  if (type === 'primary') {
    buttonTypeClass += 'primary';
  } else if (type === 'secondary') {
    buttonTypeClass += 'secondary';
  } else if (type === 'tertiary') {
    buttonTypeClass += 'tertiary';
  }

  if (isLarge) {
    buttonTypeClass = buttonTypeClass.trim() + ' is-large';
  } else if (isSmall) {
    buttonTypeClass = buttonTypeClass.trim() + ' is-small';
  }

  if (hasFullWidth) {
    buttonTypeClass = buttonTypeClass.trim() + ' full-width';
  }

  if (containerClass) {
    buttonTypeClass = buttonTypeClass.trim() + ' ' + containerClass.trim();
  }

  return `${buttonTypeClass} ${info.type ? info.type : ''}`;
};

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  type: 'primary',
  hasFullWidth: false,
  isLarge: false,
  isSmall: false,
  onClick: noop,
  info: {},
  children: null,
  containerRef: null,
};

const Button = ({
  type = DEFAULT_PROPS.type,
  hasFullWidth = DEFAULT_PROPS.hasFullWidth,
  isLarge = DEFAULT_PROPS.isLarge,
  isSmall = DEFAULT_PROPS.isSmall,
  onClick = DEFAULT_PROPS.onClick,
  info = DEFAULT_PROPS.info,
  children = DEFAULT_PROPS.children,
  containerRef = DEFAULT_PROPS.containerRef,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  ...props
}) => {
  const buttonClass = getButtonClass({
    type,
    containerClass,
    hasFullWidth,
    isLarge,
    isSmall,
    info,
  });

  return (
    <button
      type="button"
      ref={containerRef}
      className={buttonClass}
      onClick={onClick}
      style={containerStyle}
      {...props}
    >
      {children}
    </button>
  );
};

Button.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  type: PropTypes.oneOf(['', 'primary', 'secondary', 'tertiary']),
  hasFullWidth: PropTypes.bool,
  isLarge: PropTypes.bool,
  isSmall: PropTypes.bool,
  onClick: PropTypes.func,
  info: PropTypes.object,
  children: PropTypes.any,
  containerRef: PropTypes.any,
};

const ButtonForwardRef = (props, ref) => <Button {...props} containerRef={ref} />;

export default forwardRef(ButtonForwardRef);
