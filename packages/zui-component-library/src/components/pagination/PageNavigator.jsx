import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faAngleLeft, faAngleRight } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { noop } from '@tanstack/react-table';

import { isNumber } from 'lodash-es';
import PropTypes from 'prop-types';

import { replaceNonNumericDigit } from '../../utils/validations';

import Button from '../buttons/Button';
import Input from '../forms/Input';

const DEFAULT_PROPS = {
  currentPage: 1,
  isFistPage: true,
  isLastPage: true,
  pageRange: 1,
  goTo: noop,
  goNext: noop,
  goPrev: noop,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const PageNavigator = ({
  currentPage = DEFAULT_PROPS.currentPage,
  isFistPage = DEFAULT_PROPS.isFistPage,
  isLastPage = DEFAULT_PROPS.isLastPage,
  pageRange = DEFAULT_PROPS.pageRange,
  goTo = DEFAULT_PROPS.goTo,
  goNext = DEFAULT_PROPS.goNext,
  goPrev = DEFAULT_PROPS.goPrev,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const [formValues, setFormValues] = useState({
    activePage: currentPage,
  });

  useEffect(() => {
    setFormValues((prevState) => ({
      ...prevState,
      activePage: currentPage,
    }));
  }, [currentPage]);

  const onFormFieldChange = (evt) => {
    const { name, value } = evt?.target || {};

    const inputNum = replaceNonNumericDigit({ value, allowDecimal: false });

    let allowedNum = inputNum;

    if (allowedNum > pageRange.length) {
      allowedNum = pageRange.length;
    }

    setFormValues((prevState) => ({
      ...prevState,
      [name]: allowedNum,
    }));
  };

  const onBlur = () => {
    const activePage = +formValues.activePage;

    if (currentPage !== activePage) {
      if (isNumber(activePage) && activePage > 0) {
        goTo(activePage);
      }
    }

    if (!activePage) {
      setFormValues((prevState) => ({ ...prevState, activePage: currentPage }));
    }
  };

  const onKeyPress = (evt) => {
    if (evt.key === 'Enter') {
      onBlur?.();
    }
  };

  if (children) {
    return children;
  }

  return (
    <div className={`page-navigator-container ${containerClass}`} style={containerStyle}>
      <Button
        containerClass="content-width navigator-button prev"
        onClick={goPrev}
        disabled={isFistPage}
        type="tertiary"
      >
        <FontAwesomeIcon icon={faAngleLeft} />
      </Button>

      <div className="goto-page-container">
        <Input
          label=""
          containerClass="goto-page-container-input"
          name="activePage"
          placeholder={t('')}
          onChange={onFormFieldChange}
          onBlur={onBlur}
          onKeyDown={onKeyPress}
          value={formValues.activePage}
        />
        <span className="range-container"> / &nbsp; {pageRange.length} </span>
      </div>

      <Button
        containerClass="content-width navigator-button next"
        onClick={goNext}
        disabled={isLastPage}
        type="tertiary"
      >
        <FontAwesomeIcon icon={faAngleRight} />
      </Button>
    </div>
  );
};

PageNavigator.propTypes = {
  currentPage: PropTypes.number,
  isFistPage: PropTypes.bool,
  isLastPage: PropTypes.bool,
  pageRange: PropTypes.number,
  goTo: PropTypes.func,
  goNext: PropTypes.func,
  goPrev: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node,
};

export default PageNavigator;
