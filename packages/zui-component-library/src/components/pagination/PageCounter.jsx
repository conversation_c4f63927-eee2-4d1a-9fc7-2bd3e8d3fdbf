import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  pageFirstIndex: 0,
  pageLastIndex: 0,
  totalRecord: -1,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const PageCounter = ({
  pageFirstIndex = DEFAULT_PROPS.pageFirstIndex,
  pageLastIndex = DEFAULT_PROPS.pageLastIndex,
  totalRecord = DEFAULT_PROPS.totalRecord,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  if (children) {
    return children;
  }

  if (totalRecord < 1) {
    return null;
  }

  return (
    <div className={`page-counter-container ${containerClass}`} style={containerStyle}>
      {pageFirstIndex + 1}-{Math.min(pageLastIndex, totalRecord)} of {totalRecord}
    </div>
  );
};

PageCounter.propTypes = {
  pageFirstIndex: PropTypes.number,
  pageLastIndex: PropTypes.number,
  totalRecord: PropTypes.number,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node,
};

export default PageCounter;
