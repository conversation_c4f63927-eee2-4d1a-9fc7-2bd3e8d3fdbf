import PropTypes from 'prop-types';

import PageActions from './PageActions';
import PageCounter from './PageCounter';
import PageNavigator from './PageNavigator';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
};

const PaginationContainer = ({
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  ...pageDetail
}) => {
  return (
    <div className={`pagination-container ${containerClass}`} style={containerStyle}>
      <PageCounter {...pageDetail} />
      <PageNavigator {...pageDetail} />
      <PageActions {...pageDetail} />
    </div>
  );
};

PaginationContainer.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default PaginationContainer;
