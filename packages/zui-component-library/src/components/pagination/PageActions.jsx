import { useState } from 'react';

import { cloneDeep, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import DropDown from '../dropdowns/DropDown';
import CRUDModal from '../modal/CRUDModal';

const DEFAULT_SELECTED_ACTION = { label: 'REMOVE', value: 'REMOVE' };

const DEFAULT_PROPS = {
  actionsList: [
    { label: 'REMOVE_PAGE', value: 'REMOVE_PAGE' },
    { label: 'REMOVE_ALL', value: 'REMOVE_ALL' },
  ],
  removeBlackList: [],
  modalRootId: '',
  disabled: false,
};

const PageActions = ({
  actionsList = DEFAULT_PROPS.actionsList,
  currentRecord = DEFAULT_PROPS.currentRecord,
  currentPage = DEFAULT_PROPS.currentPage,
  removeBlackList = DEFAULT_PROPS.removeBlackList,
  list = DEFAULT_PROPS.list,
  modalRootId = DEFAULT_PROPS.modalRootId,
  onRemove = DEFAULT_PROPS.onRemove,
  disabled = DEFAULT_PROPS.disabled,
}) => {
  const [modalMode, setModalMode] = useState('');
  const [selectedAction, setSelectedAction] = useState([DEFAULT_SELECTED_ACTION]);

  const onActionSelection = (detail) => {
    const value = detail?.[0]?.value || {};

    if (value !== 'REMOVE') {
      setModalMode('actionConfirmation');
    }

    setSelectedAction(detail);
  };

  const getHeaderText = () => {
    let headerText = '';

    const value = selectedAction?.[0]?.value || {};

    if (modalMode) {
      if (value === 'REMOVE_PAGE') {
        headerText = 'CONFIRMATION_REMOVE_PAGE';
      }

      if (value === 'REMOVE_ALL') {
        headerText = 'CONFIRMATION_REMOVE_ALL';
      }
    }

    return headerText;
  };

  const getModalMessage = () => {
    let message = '';

    const value = selectedAction?.[0]?.value || {};

    if (modalMode) {
      if (value === 'REMOVE_PAGE') {
        let idsToRemove = Array.isArray(currentRecord) ? currentRecord : [currentRecord];

        // remove option valid only for items not in blacklist
        idsToRemove = remove(cloneDeep(idsToRemove), (id) => removeBlackList.indexOf(id) === -1);

        const removeBlackListLength = removeBlackList.length;

        if (idsToRemove.length > 0) {
          message = `Please confirm that you want to remove ${
            idsToRemove?.length != 1 ? 'all' : ''
          } ${idsToRemove?.length} item${
            idsToRemove?.length != 1 ? 's' : ''
          } on page ${currentPage} of this list. 
          This action cannot be undone.`;
        } else {
          message = `List contains item${
            removeBlackListLength != 1 ? 's' : ''
          } which can't be removed. This action will have no impact on the list.`;
        }
      }

      if (value === 'REMOVE_ALL') {
        let idsToRemove = Array.isArray(list) ? list : [list];

        // remove option valid only for items not in blacklist
        idsToRemove = remove(cloneDeep(idsToRemove), (id) => removeBlackList.indexOf(id) === -1);

        const removeBlackListLength = removeBlackList.length;

        if (idsToRemove.length > 0) {
          message = `Please confirm that you want to remove ${
            idsToRemove.length != 1 ? 'all' : ''
          } ${idsToRemove.length} item${idsToRemove.length != 1 ? 's' : ''} on this list.
          ${
            removeBlackListLength > 0
              ? ` ${removeBlackListLength} item${
                  removeBlackListLength != 1 ? 's' : ''
                } won't be deleted.`
              : ''
          } 
          This action cannot be undone.`;
        } else {
          message = `List contains item${
            removeBlackListLength != 1 ? 's' : ''
          } which can't be removed. This action will have no impact on the list.`;
        }
      }
    }

    return message;
  };

  const onSaveClick = () => {
    const value = selectedAction?.[0]?.value || {};

    if (modalMode) {
      if (value === 'REMOVE_PAGE') {
        onRemove({ ids: [...currentRecord] });
      }

      if (value === 'REMOVE_ALL') {
        onRemove({ removeAll: true });
      }
    }

    onCloseClick();
  };

  const onCloseClick = () => {
    setModalMode('');
    setSelectedAction([DEFAULT_SELECTED_ACTION]);
  };

  const renderActionConfirmationModal = () => {
    if (modalMode) {
      const modalProps = {};

      if (modalRootId) {
        modalProps.modalRootId = modalRootId;
      }

      return (
        <CRUDModal
          mode={modalMode}
          headerText={getHeaderText()}
          confirmationMessage={getModalMessage()}
          saveText="CONFIRM"
          onSaveClick={onSaveClick}
          onCloseClick={onCloseClick}
          {...modalProps}
        />
      );
    }

    return null;
  };

  return (
    <div className="is-flex has-jc-sb has-ai-c pagination-actions-container">
      <DropDown
        list={actionsList}
        selectedList={selectedAction}
        onSelection={onActionSelection}
        kind="tertiary"
        disabled={disabled}
      />

      {renderActionConfirmationModal()}
    </div>
  );
};

PageActions.propTypes = {
  actionsList: PropTypes.array,
  modalRootId: PropTypes.string,
  currentRecord: PropTypes.array,
  removeBlackList: PropTypes.array,
  list: PropTypes.array,
  currentPage: PropTypes.number,
  totalRecord: PropTypes.number,
  onRemove: PropTypes.func,
  disabled: PropTypes.func,
};

export default PageActions;
