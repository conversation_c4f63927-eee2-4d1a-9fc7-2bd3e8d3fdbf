import { useTranslation } from 'react-i18next';

import { faArrowsAltH, faMinus } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';

const DEFAULT_PROPS = {
  onMinimizeClick: noop,
};

const HelpHeader = ({ onMinimizeClick = DEFAULT_PROPS.onMinimizeClick }) => {
  const { t } = useTranslation();

  return (
    <div className="is-flex has-jc-sb has-ai-c help-browser-header-container">
      <div className="is-flex">
        <div className="resizer-container">
          <FontAwesomeIcon icon={faArrowsAltH} className="icon left right" />
        </div>

        <div className="title">{t('HELP_BROWSER')}</div>
      </div>

      <div className="action">
        <Button type="tertiary" containerClass="content-width no-p-r" onClick={onMinimizeClick}>
          <FontAwesomeIcon icon={faMinus} />
        </Button>
      </div>
    </div>
  );
};

HelpHeader.propTypes = {
  onMinimizeClick: PropTypes.func,
};

export default HelpHeader;
