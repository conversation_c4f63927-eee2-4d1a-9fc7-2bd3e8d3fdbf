import { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { faSquareArrowUpLeft } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';
import Card from '../cards/Card';
import { Tooltip } from '../tooltip';

const DEFAULT_PROPS = {
  isIconImg: false,
  icon: faSquareArrowUpLeft,
  onClick: noop,
  containerClass: '',
  containerStyle: {},
};

const HelpButton = ({
  isIconImg = DEFAULT_PROPS.isIconImg,
  icon = DEFAULT_PROPS.icon,
  onClick = DEFAULT_PROPS.onClick,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  const buttonRef = useRef();

  return (
    <>
      <Button
        ref={buttonRef}
        onClick={onClick}
        containerClass={`help-button ${containerClass}`}
        containerStyle={containerStyle}
      >
        {isIconImg ? (
          <img src={icon} className="icon left" alt="help img" />
        ) : (
          <FontAwesomeIcon icon={icon} className="icon left" />
        )}

        {t('HELP')}
      </Button>

      <Tooltip elementRef={buttonRef} placement="left">
        <Card>{t('OPEN_HELP_BROWSER')}</Card>
      </Tooltip>
    </>
  );
};

HelpButton.propTypes = {
  isIconImg: PropTypes.bool,
  icon: PropTypes.any,
  onClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default HelpButton;
