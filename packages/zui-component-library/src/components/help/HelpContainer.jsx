import { useEffect, useState } from 'react';

import PropTypes from 'prop-types';

import Help<PERSON>rowser from './HelpBrowser';
import HelpButton from './HelpButton';

const DEFAULT_PROPS = {
  src: '',
  position: 'bottom-right',
  show: true,
  expanded: false,
  containerClass: '',
  containerStyle: {},
};

const HelpContainer = ({
  src = DEFAULT_PROPS.src,
  position = DEFAULT_PROPS.position,
  show = DEFAULT_PROPS.show,
  expanded = DEFAULT_PROPS.expanded,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const [showContent, setShowContent] = useState(expanded);

  useEffect(() => {
    setShowContent(expanded);
  }, [expanded]);

  const renderButtonSection = () => {
    if (showContent) {
      return null;
    }

    return (
      <HelpButton
        onClick={() => {
          setShowContent(true);
        }}
      />
    );
  };

  const onMinimizeClick = () => {
    setShowContent(false);
  };

  const renderContentSection = () => {
    if (showContent) {
      return <HelpBrowser src={src} onMinimizeClick={onMinimizeClick} />;
    }

    return null;
  };

  if (show) {
    return (
      <div className={`help-container ${position} ${containerClass}`} style={containerStyle}>
        <div className="help-content-container">
          {renderButtonSection()}
          {renderContentSection()}
        </div>
      </div>
    );
  }

  return null;
};

HelpContainer.propTypes = {
  src: PropTypes.string,
  position: PropTypes.oneOf(['bottom-right']),
  show: PropTypes.bool,
  expanded: PropTypes.bool,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default HelpContainer;
