import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import HelpHeader from './HelpHeader';

const DEFAULT_PROPS = {
  src: '',
  onMinimizeClick: noop,
};

const HelpBrowser = ({
  src = DEFAULT_PROPS.src,
  onMinimizeClick = DEFAULT_PROPS.onMinimizeClick,
}) => {
  const renderHelpHeaderSection = () => {
    return <HelpHeader onMinimizeClick={onMinimizeClick} />;
  };

  const renderHelpIframeContent = () => {
    return (
      <div className="help-browser-body-container">
        <iframe className="help-browser-iframe-container" src={src} />
      </div>
    );
  };

  return (
    <div className="help-browser-container">
      {renderHelpHeaderSection()}
      {renderHelpIframeContent()}
    </div>
  );
};

HelpBrowser.propTypes = {
  src: PropTypes.string,
  onMinimizeClick: PropTypes.func,
};

export default HelpBrowser;
