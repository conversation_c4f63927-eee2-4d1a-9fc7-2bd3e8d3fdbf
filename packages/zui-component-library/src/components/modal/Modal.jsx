import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getModalRootId } from '../../config/floating';

import { getFormattedClassName } from '../../utils/dom';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import FloatingPortalContainer from '../floating/FloatingPortalContainer';

const DEFAULT_PROPS = {
  modalRootId: '',
  show: false,
  onEscape: noop,
  hideOnEscape: true,
  align: 'center',
  isBlocking: true,
  containerClass: '',
  children: null,
};

const Modal = ({
  modalRootId = DEFAULT_PROPS.modalRootId,
  show = DEFAULT_PROPS.show,
  onEscape = DEFAULT_PROPS.onEscape,
  hideOnEscape = DEFAULT_PROPS.hideOnEscape,
  align = DEFAULT_PROPS.align,
  isBlocking = DEFAULT_PROPS.isBlocking,
  containerClass = DEFAULT_PROPS.containerClass,
  children = DEFAULT_PROPS.children,
}) => {
  const rootId = modalRootId || getModalRootId();

  const onOpenChange = (newState) => {
    if (!newState) {
      onEscape();
    }
  };

  const { open: isOpen, ...floatingLayout } = useFloatingLayout({
    initialOpen: show,
    showOnHover: false,
    showOnClick: true,
    onOpenChange,
    dismissConfig: { outsidePress: false, escapeKey: hideOnEscape },
    sizeConfig: {
      padding: 36,
      apply() {},
    },
  });

  const { refs, getReferenceProps, getFloatingProps } = floatingLayout;

  if (isOpen) {
    return (
      <>
        <div
          ref={refs.setReference}
          className="modal-reference-container"
          {...getReferenceProps()}
        />

        <FloatingPortalContainer id={rootId} show={isOpen} skipMeta>
          <article
            className={getFormattedClassName(
              `modal-container ${align} ${isBlocking ? 'is-blocking' : ''} ${containerClass}`,
            )}
            ref={refs.setFloating}
            {...getFloatingProps()}
          >
            <div className="modal-content-container">{children}</div>
          </article>
        </FloatingPortalContainer>
      </>
    );
  }

  return null;
};

Modal.propTypes = {
  modalRootId: PropTypes.string,
  show: PropTypes.bool,
  onEscape: PropTypes.func,
  hideOnEscape: PropTypes.bool,
  align: PropTypes.oneOf(['center', 'right']),
  isBlocking: PropTypes.bool,
  containerClass: PropTypes.string,
  children: PropTypes.any,
};

export default Modal;
