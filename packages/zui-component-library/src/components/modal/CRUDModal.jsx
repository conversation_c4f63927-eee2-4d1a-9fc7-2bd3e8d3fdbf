import { useEffect, useState } from 'react';

import { isUndefined, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getCRUDModalRootId } from '../../config/floating';

import { getFormattedClassName } from '../../utils/dom';

import ActionConfirmation from '../forms/ActionConfirmation';
import Modal from '../modal/Modal';
import ModalBody from '../modal/ModalBody';
import ModalFooter from '../modal/ModalFooter';
import ModalHeader from '../modal/ModalHeader';

const DEFAULT_PROPS = {
  modalRootId: '',
  mode: '',
  renderFormSection: noop,
  containerClass: '',
  headerText: '',
  confirmationMessage:
    'Are you sure you want to perform this action? The changes cannot be undone.',
  saveText: '',
  hideOnEscape: true,
  onSaveClick: noop,
  showClose: true,
  onCloseClick: noop,
  children: noop,
};

// '', 'add', 'edit', 'delete', 'bulkActivate', 'bulkDeactivate', 'bulkDelete', 'importFile', 'actionConfirmation'
const CRUDModal = ({
  modalRootId = DEFAULT_PROPS.modalRootId,
  mode = DEFAULT_PROPS.mode,
  align = DEFAULT_PROPS.align,
  isBlocking = DEFAULT_PROPS.isBlocking,
  renderFormSection = DEFAULT_PROPS.renderFormSection,
  containerClass = DEFAULT_PROPS.containerClass,
  headerText = DEFAULT_PROPS.headerText,
  confirmationMessage = DEFAULT_PROPS.confirmationMessage,
  saveText = DEFAULT_PROPS.saveText,
  hideOnEscape = DEFAULT_PROPS.hideOnEscape,
  onSaveClick = DEFAULT_PROPS.onSaveClick,
  showClose = DEFAULT_PROPS.showClose,
  onCloseClick = DEFAULT_PROPS.onCloseClick,
  children = DEFAULT_PROPS.children,
  ...rest
}) => {
  const showModal = !!mode;

  const [validationDetail, setValidationDetail] = useState({
    isValid: true,
    message: '',
  });

  const [isShallowValidation, setIsShallowValidation] = useState(
    mode === 'view' || mode === 'add' || mode === 'edit',
  );

  const [isActionConfirmed, setIsActionConfirmed] = useState(false);

  useEffect(() => {
    if (mode === 'bulkActivate' || mode === 'bulkDeactivate') {
      setIsActionConfirmed(true);
    } else {
      setIsActionConfirmed(false);
    }

    setIsShallowValidation(mode === 'view' || mode === 'add' || mode === 'edit');
  }, [mode]);

  const renderBodySection = () => {
    if (mode === 'view' || mode === 'add' || mode === 'edit' || mode === 'importFile') {
      return renderFormSection({
        mode,
        validationDetail,
        setValidationDetail,
        isShallowValidation,
      });
    }

    if (mode === 'delete' || mode === 'bulkDelete') {
      return (
        <ActionConfirmation message={confirmationMessage} onValidityChange={setIsActionConfirmed} />
      );
    }

    if (mode === 'bulkActivate' || mode === 'bulkDeactivate' || mode === 'actionConfirmation') {
      return <ActionConfirmation message={confirmationMessage} showConfirmation={false} />;
    }

    return children;
  };

  const getFooterProps = () => {
    const footerProps = {
      saveText,
    };

    if (mode === 'add' || mode === 'edit' || mode === 'importFile') {
      footerProps.isSaveDisabled = !validationDetail?.isValid;

      if (mode === 'add') {
        footerProps.saveText = saveText ? saveText : 'SAVE';
      }

      if (mode === 'edit') {
        footerProps.saveText = saveText ? saveText : 'UPDATE';
      }

      if (mode === 'importFile') {
        footerProps.saveText = saveText ? saveText : 'IMPORT';
      }
    }

    if (
      mode === 'delete' ||
      mode === 'bulkDelete' ||
      mode === 'bulkActivate' ||
      mode === 'bulkDeactivate'
    ) {
      if (mode === 'delete' || mode === 'bulkDelete') {
        footerProps.saveText = saveText ? saveText : 'DELETE';
      }

      if (mode === 'bulkActivate') {
        footerProps.saveText = saveText ? saveText : 'ACTIVATE';
      }

      if (mode === 'bulkDeactivate') {
        footerProps.saveText = saveText ? saveText : 'DE_ACTIVATE';
      }

      footerProps.isSaveDisabled = !isActionConfirmed;
    }

    return footerProps;
  };

  const onSave = () => {
    // if (isShallowValidation) {
    //   setIsShallowValidation(false);

    //   return;
    // }

    onSaveClick?.();
  };

  if (showModal) {
    let modalAlign, modalIsBlocking;

    if (isUndefined(align)) {
      if (mode === 'add' || mode === 'edit' || mode === 'view') {
        modalAlign = 'right';
      } else {
        modalAlign = 'center';
      }
    } else {
      modalAlign = align;
    }

    if (isUndefined(isBlocking)) {
      if (mode === 'add' || mode === 'edit' || mode === 'view') {
        modalIsBlocking = false;
      } else {
        modalIsBlocking = true;
      }
    } else {
      modalIsBlocking = isBlocking;
    }

    const modalProps = {
      containerClass: getFormattedClassName(`crud-modal ${containerClass}`),
      show: showModal,
      hideOnEscape,
      onEscape: onCloseClick,
      align: modalAlign,
      isBlocking: modalIsBlocking,
    };

    const CRUDRootId = getCRUDModalRootId();

    if (modalRootId || CRUDRootId) {
      modalProps.modalRootId = modalRootId || CRUDRootId;
    }

    return (
      <>
        <Modal {...modalProps}>
          <ModalHeader text={headerText} showClose={showClose} onClose={onCloseClick} />
          <ModalBody>{renderBodySection()}</ModalBody>
          <ModalFooter onSave={onSave} onCancel={onCloseClick} {...getFooterProps()} {...rest} />
        </Modal>
      </>
    );
  }

  return null;
};

CRUDModal.propTypes = {
  modalRootId: PropTypes.string,
  mode: PropTypes.oneOf([
    '',
    'view',
    'add',
    'edit',
    'delete',
    'bulkActivate',
    'bulkDeactivate',
    'bulkDelete',
    'importFile',
    'actionConfirmation',
  ]),
  align: PropTypes.oneOf(['center', 'right']),
  isBlocking: PropTypes.bool,
  renderFormSection: PropTypes.func,
  containerClass: PropTypes.string,
  headerText: PropTypes.string,
  confirmationMessage: PropTypes.string,
  saveText: PropTypes.string,
  hideOnEscape: PropTypes.bool,
  onSaveClick: PropTypes.func,
  showClose: PropTypes.bool,
  onCloseClick: PropTypes.func,
  children: PropTypes.any,
};

export default CRUDModal;
