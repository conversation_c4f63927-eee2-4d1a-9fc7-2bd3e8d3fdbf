import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  containerClass: '',
  children: null,
};

const ModalBody = ({
  containerClass = DEFAULT_PROPS.containerClass,
  children = DEFAULT_PROPS.children,
}) => {
  return <div className={`modal-body-container ${containerClass}`}>{children}</div>;
};

ModalBody.propTypes = {
  containerClass: PropTypes.string,
  children: PropTypes.any,
};

export default ModalBody;
