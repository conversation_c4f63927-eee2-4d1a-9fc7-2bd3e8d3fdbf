import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';

const DEFAULT_PROPS = {
  containerClass: '',
  skipTranslation: false,
  onSave: noop,
  saveText: 'SAVE',
  showSave: true,
  isSaveDisabled: false,
  saveProps: {},
  onCancel: noop,
  cancelText: 'CANCEL',
  showCancel: true,
  isCancelDisabled: false,
  cancelProps: {},
  children: null,
};

const ModalFooter = ({
  containerClass = DEFAULT_PROPS.containerClass,
  skipTranslation = DEFAULT_PROPS.skipTranslation,
  onSave = DEFAULT_PROPS.onSave,
  saveText = DEFAULT_PROPS.saveText,
  showSave = DEFAULT_PROPS.showSave,
  isSaveDisabled = DEFAULT_PROPS.isSaveDisabled,
  saveProps = DEFAULT_PROPS.saveProps,
  onCancel = DEFAULT_PROPS.onCancel,
  cancelText = DEFAULT_PROPS.cancelText,
  showCancel = DEFAULT_PROPS.showCancel,
  isCancelDisabled = DEFAULT_PROPS.isCancelDisabled,
  cancelProps = DEFAULT_PROPS.cancelProps,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const renderDefaultSection = () => {
    return (
      <>
        {showSave && (
          <Button onClick={onSave} disabled={isSaveDisabled} {...saveProps}>
            {skipTranslation ? saveText : t(saveText)}
          </Button>
        )}

        {showCancel && (
          <Button type="tertiary" onClick={onCancel} disabled={isCancelDisabled} {...cancelProps}>
            {skipTranslation ? cancelText : t(cancelText)}
          </Button>
        )}
      </>
    );
  };

  return (
    <div className={`modal-footer-container ${containerClass}`}>
      {children ? children : renderDefaultSection()}
    </div>
  );
};

ModalFooter.propTypes = {
  containerClass: PropTypes.string,
  skipTranslation: PropTypes.bool,
  onSave: PropTypes.func,
  saveText: PropTypes.string,
  showSave: PropTypes.bool,
  isSaveDisabled: PropTypes.bool,
  saveProps: PropTypes.object,
  onCancel: PropTypes.func,
  cancelText: PropTypes.string,
  showCancel: PropTypes.bool,
  isCancelDisabled: PropTypes.bool,
  cancelProps: PropTypes.object,
  children: PropTypes.any,
};

export default ModalFooter;
