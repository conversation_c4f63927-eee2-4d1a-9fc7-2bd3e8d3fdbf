import { useTranslation } from 'react-i18next';

import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';

const DEFAULT_PROPS = {
  containerClass: '',
  skipTranslation: false,
  text: '',
  showClose: true,
  onClose: noop,
  children: null,
};

const ModalHeader = ({
  containerClass = DEFAULT_PROPS.containerClass,
  skipTranslation = DEFAULT_PROPS.skipTranslation,
  text = DEFAULT_PROPS.text,
  showClose = DEFAULT_PROPS.showClose,
  onClose = DEFAULT_PROPS.onClose,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const renderDefaultSection = () => {
    return (
      <>
        <div className="section">{skipTranslation ? text : t(text)}</div>

        {showClose && (
          <Button type="tertiary" containerClass="no-p content-width" onClick={onClose}>
            <FontAwesomeIcon icon={faTimes} />
          </Button>
        )}
      </>
    );
  };

  return (
    <div className={`modal-header-container ${containerClass}`}>
      {children ? children : renderDefaultSection()}
    </div>
  );
};

ModalHeader.propTypes = {
  containerClass: PropTypes.string,
  skipTranslation: PropTypes.bool,
  text: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
  showClose: PropTypes.bool,
  onClose: PropTypes.func,
  children: PropTypes.any,
};

export default ModalHeader;
