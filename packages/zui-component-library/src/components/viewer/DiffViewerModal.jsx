import { useEffect, useRef, useState } from 'react';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { useTranslation } from 'react-i18next';

import { faCode, faCodeCompare } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import dayjs from 'dayjs';
import PropTypes from 'prop-types';

import { FLOATING_CONFIG_NAME, getDefaultFloatingConfig } from '../../hooks/floating/helper';
import Button from '../buttons/Button';
import Card from '../cards/Card';
import Modal from '../modal/Modal';
import ModalBody from '../modal/ModalBody';
import ModalFooter from '../modal/ModalFooter';
import ModalHeader from '../modal/ModalHeader';
import Tooltip from '../tooltip/Tooltip';

const dismissConfig = {
  ...getDefaultFloatingConfig({ configName: FLOATING_CONFIG_NAME.DISMISS }),
  referencePress: true,
};

const defaultStyles = {
  variables: {
    light: {
      codeFoldGutterBackground: '#6F767E',
      codeFoldBackground: '#E2E4E5',
    },
  },
  titleBlock: {
    fontWeight: 'bold',
  },
};

const DEFAULT_PROPS = {
  oldValueIcon: faCode,
  oldValue: '',
  oldValueTitle: 'PRE_CHANGES_CONFIGURATION',
  oldValueStringifySpace: 2,
  newValueIcon: faCode,
  newValue: '',
  newValueTitle: 'POST_CHANGES_CONFIGURATION',
  newValueStringifySpace: 2,
  allValueIcon: faCodeCompare,
  openModal: false,
  headerText: 'CONFIGURATION_CHANGES',
  timestamp: 0,
  userName: '',
  formatPattern: 'MMMM DD, YYYY - hh:mm A',
  containerClass: '',
  containerStyle: {},
  modalContainerClass: '',
  modalContainerStyle: {},
  viewerContainerClass: '',
  viewerContainerStyle: {},
  styles: {},
};

const DiffViewerModal = ({
  oldValueIcon = DEFAULT_PROPS.oldValueIcon,
  oldValue = DEFAULT_PROPS.oldValue,
  oldValueTitle = DEFAULT_PROPS.oldValueTitle,
  oldValueStringifySpace = DEFAULT_PROPS.oldValueStringifySpace,
  newValueIcon = DEFAULT_PROPS.newValueIcon,
  newValue = DEFAULT_PROPS.newValue,
  newValueTitle = DEFAULT_PROPS.newValueTitle,
  newValueStringifySpace = DEFAULT_PROPS.newValueStringifySpace,
  allValueIcon = DEFAULT_PROPS.allValueIcon,
  openModal = DEFAULT_PROPS.openModal,
  headerText = DEFAULT_PROPS.headerText,
  timestamp = DEFAULT_PROPS.timestamp,
  userName = DEFAULT_PROPS.userName,
  formatPattern = DEFAULT_PROPS.formatPattern,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  modalContainerClass = DEFAULT_PROPS.modalContainerClass,
  modalContainerStyle = DEFAULT_PROPS.modalContainerStyle,
  viewerContainerClass = DEFAULT_PROPS.viewerContainerClass,
  viewerContainerStyle = DEFAULT_PROPS.viewerContainerStyle,
  styles = DEFAULT_PROPS.styles,
  ...rest
}) => {
  const { t } = useTranslation();

  const buttonRef = useRef();

  const [showModal, setShowModal] = useState(openModal);

  const [buttonRefProps, setButtonRefProps] = useState({});

  useEffect(() => {
    setShowModal(openModal);
  }, [openModal]);

  const onViewDiffClick = () => {
    setShowModal(true);
  };

  const renderBodySection = () => {
    const viewerStyle = { ...defaultStyles, ...styles };
    return (
      <div className={`viewer-container ${viewerContainerClass}`} style={viewerContainerStyle}>
        <ReactDiffViewer
          oldValue={oldValue ? JSON.stringify(oldValue, undefined, oldValueStringifySpace) : ''}
          newValue={newValue ? JSON.stringify(newValue, undefined, newValueStringifySpace) : ''}
          splitView={true}
          compareMethod={DiffMethod.WORDS}
          styles={viewerStyle}
          leftTitle={t(oldValueTitle)}
          rightTitle={t(newValueTitle)}
          // renderContent={highlightSyntax}
          {...rest}
        />
      </div>
    );
  };

  const onCloseClick = () => {
    setShowModal(false);
  };

  const getheaderText = () => {
    let text = `${t(headerText)}`;

    if (timestamp) {
      text += ` ${dayjs(timestamp).format(formatPattern)}`;
    }

    if (userName) {
      text += ` by ${userName}`;
    }

    return text;
  };

  const renderModalSection = () => {
    if (showModal) {
      return (
        <Modal
          show={showModal}
          onEscape={onCloseClick}
          containerClass={`crud-modal diff-viewer-modal-container ${modalContainerClass}`}
          style={modalContainerStyle}
        >
          <ModalHeader text={getheaderText()} onClose={onCloseClick} />
          <ModalBody>{renderBodySection()}</ModalBody>
          <ModalFooter containerClass="has-jc-sb">
            <Button type="tertiary" containerClass="no-p-l" onClick={onCloseClick}>
              {t('CLOSE')}
            </Button>
          </ModalFooter>
        </Modal>
      );
    }
  };

  const hasAllValue = !!oldValue && !!newValue;
  const hasOldValue = !!oldValue;
  const hasNewValue = !!newValue;

  const getDiffIcon = () => {
    if (hasAllValue) {
      return allValueIcon;
    }

    if (hasOldValue) {
      return oldValueIcon;
    }

    if (hasNewValue) {
      return newValueIcon;
    }
  };

  return (
    <div className={`diff-viewer-container ${containerClass}`} style={containerStyle}>
      <Button ref={buttonRef} type="tertiary" {...buttonRefProps} onClick={onViewDiffClick}>
        <FontAwesomeIcon icon={getDiffIcon()} />
      </Button>

      <Tooltip
        placement="left"
        dismissConfig={dismissConfig}
        elementRef={buttonRef}
        updateReferenceElementsProps={setButtonRefProps}
      >
        <Card>{t('VIEW_CHANGES')}</Card>
      </Tooltip>

      {renderModalSection()}
    </div>
  );
};

DiffViewerModal.propTypes = {
  oldValueIcon: PropTypes.any,
  oldValue: PropTypes.any,
  oldValueTitle: PropTypes.string,
  oldValueStringifySpace: PropTypes.number,
  newValueIcon: PropTypes.any,
  newValue: PropTypes.any,
  newValueTitle: PropTypes.string,
  newValueStringifySpace: PropTypes.number,
  allValueIcon: PropTypes.any,
  openModal: PropTypes.bool,
  headerText: PropTypes.string,
  timestamp: PropTypes.number,
  userName: PropTypes.string,
  formatPattern: PropTypes.string,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  modalContainerClass: PropTypes.string,
  modalContainerStyle: PropTypes.object,
  viewerContainerClass: PropTypes.string,
  viewerContainerStyle: PropTypes.object,
  styles: PropTypes.object,
};

export default DiffViewerModal;
