import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faCopy } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import PropTypes from 'prop-types';

import useIsTextOverflow from '../../hooks/useIsTextOverflow';
import Button from '../buttons/Button';
import Card from '../cards/Card';
import ClipboardCopy from '../forms/ClipboardCopy';
import Tooltip from './Tooltip';

const DEFAULT_PROPS = {
  tag: 'div',
  text: '',
  children: null,
  tooltipText: '',
  tooltipChildren: null,
  showTooltip: true,
  canCopy: false,
  showCopy: false,
  placement: 'top',
  inlineConfig: {},
  offsetConfig: { mainAxis: 5 },
  containerClass: '',
  containerStyle: {},
  tooltipContainerClass: '',
  tooltipContainerStyle: {},
  tooltipContentContainerClass: '',
  tooltipContentContainerStyle: {},
};

const TextWithTooltip = ({
  tag = DEFAULT_PROPS.tag,
  text = DEFAULT_PROPS.text,
  children = DEFAULT_PROPS.children,
  tooltipText = DEFAULT_PROPS.tooltipText,
  tooltipChildren = DEFAULT_PROPS.tooltipChildren,
  showTooltip = DEFAULT_PROPS.showTooltip,
  canCopy = DEFAULT_PROPS.canCopy,
  showCopy = DEFAULT_PROPS.showCopy,
  placement = DEFAULT_PROPS.placement,
  offsetConfig = DEFAULT_PROPS.offsetConfig,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  tooltipContainerClass = DEFAULT_PROPS.tooltipContainerClass,
  tooltipContainerStyle = DEFAULT_PROPS.tooltipContainerClass,
  tooltipContentContainerClass = DEFAULT_PROPS.tooltipContentContainerClass,
  tooltipContentContainerStyle = DEFAULT_PROPS.tooltipContentContainerStyle,
  ...rest
}) => {
  const { t } = useTranslation();

  const textRef = useRef();

  const [isOverflow] = useIsTextOverflow({ elementRef: textRef });

  const [textRefProps, setTextRefProps] = useState({});

  const CustomTag = `${tag}`;

  const renderTextSection = () => {
    if (children) {
      return children;
    }

    if (text) {
      return t(text);
    }

    return null;
  };

  const renderTooltipSection = () => {
    if (tooltipChildren) {
      return tooltipChildren;
    }

    if (tooltipText) {
      return t(tooltipText);
    }

    return renderTextSection();
  };

  const onCopy = (evt) => {
    // prevent form submit
    evt?.preventDefault?.();
  };

  const renderCopySection = () => {
    if (canCopy && (isOverflow || showCopy)) {
      return (
        <ClipboardCopy text={text} containerClass="copy-container">
          <Button type="tertiary" containerClass="no-p content-width" onClick={onCopy}>
            <FontAwesomeIcon icon={faCopy} className="icon left" />
          </Button>
        </ClipboardCopy>
      );
    }
    return null;
  };

  return (
    <>
      <CustomTag
        ref={textRef}
        className={`text-tooltip-container ${containerClass} ${
          canCopy && (isOverflow || showCopy) ? 'has-copy' : ''
        }`}
        style={containerStyle}
        {...textRefProps}
      >
        {renderTextSection()}

        {renderCopySection()}
      </CustomTag>

      {isOverflow && showTooltip && (
        <Tooltip
          elementRef={textRef}
          offsetConfig={offsetConfig}
          placement={placement}
          containerClass={tooltipContainerClass}
          updateReferenceElementsProps={setTextRefProps}
          {...rest}
          containerStyle={tooltipContainerStyle}
        >
          <Card
            containerClass={`text-tooltip-content-container ${tooltipContentContainerClass}`}
            containerStyle={tooltipContentContainerStyle}
          >
            {renderTooltipSection()}
          </Card>
        </Tooltip>
      )}
    </>
  );
};

TextWithTooltip.propTypes = {
  tag: PropTypes.string,
  text: PropTypes.string,
  children: PropTypes.any,
  tooltipText: PropTypes.string,
  tooltipChildren: PropTypes.any,
  showTooltip: PropTypes.bool,
  canCopy: PropTypes.bool,
  showCopy: PropTypes.bool,
  placement: PropTypes.string,
  offsetConfig: PropTypes.object,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  tooltipContainerClass: PropTypes.string,
  tooltipContainerStyle: PropTypes.object,
  tooltipContentContainerClass: PropTypes.string,
  tooltipContentContainerStyle: PropTypes.object,
};

export default TextWithTooltip;
