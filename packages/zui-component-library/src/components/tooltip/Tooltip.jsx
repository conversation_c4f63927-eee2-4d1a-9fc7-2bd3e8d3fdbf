import { useEffect } from 'react';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import FloatingContainer from '../floating/FloatingContainer';

const DEFAULT_PROPS = {
  elementRef: { current: null },
  placement: 'top',
  showOnHover: true,
  showOnFocus: false,
  showOnClick: false,
  hideOnOutsideClick: true,
  updateReferenceElementsProps: noop,
  children: null,
  containerClass: '',
  containerStyle: {},
};

const Tooltip = ({
  elementRef = DEFAULT_PROPS.elementRef,
  placement = DEFAULT_PROPS.placement,
  updateReferenceElementsProps = DEFAULT_PROPS.updateReferenceElementsProps,
  children = DEFAULT_PROPS.children,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  ...rest
}) => {
  const layoutDetail = useFloatingLayout({
    placement,
    offsetConfig: 10,
    flipConfig: { padding: 36, crossAxis: false },
    ...rest,
  });

  const { refs, open, getReferenceProps } = layoutDetail;

  useEffect(() => {
    updateReferenceElementsProps?.(getReferenceProps);
  }, [getReferenceProps]);

  useEffect(() => {
    refs.setReference(elementRef.current);
  }, [elementRef.current]);

  return (
    <FloatingContainer
      containerClass={`tooltip-floating-container ${containerClass}`}
      show={open}
      hasArrow
      {...layoutDetail}
      containerStyle={containerStyle}
    >
      {children}
    </FloatingContainer>
  );
};

Tooltip.propTypes = {
  elementRef: PropTypes.object,
  placement: PropTypes.string,
  inline: PropTypes.any,
  offset: PropTypes.any,
  shift: PropTypes.any,
  flip: PropTypes.any,
  showOnHover: PropTypes.bool,
  showOnFocus: PropTypes.bool,
  showOnClick: PropTypes.bool,
  hideOnOutsideClick: PropTypes.bool,
  disabled: PropTypes.bool,
  updateReferenceElementsProps: PropTypes.func,
  children: PropTypes.node,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default Tooltip;
