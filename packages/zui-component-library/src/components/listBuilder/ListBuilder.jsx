import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName, mergeFormValues } from '../../utils/dom';

import Button from '../buttons/Button';
import TextArea from '../forms/TextArea';
import { ToastContainer } from '../toast';
import BuilderList from './BuilderList';

const DEFAULT_PROPS = {
  buttonText: 'ADD_ITEMS',
  list: [],
  setList: noop,
  removeBlackList: [],
  defaultRows: 1,
  expandedRows: 4,
  separator: /[\r\n]+/,
  validator: '',
  filterChars: /[,]+/,
  showValidationFailedNotification: true,
  getValidationMessage: noop,
  hidePagination: false,
  pageSize: 500,
  hideSearch: false,
  disabled: false,
};

const ListBuilder = ({
  buttonText = DEFAULT_PROPS.buttonText,
  list = DEFAULT_PROPS.list,
  setList = DEFAULT_PROPS.setList,
  removeBlackList = DEFAULT_PROPS.removeBlackList,
  defaultRows = DEFAULT_PROPS.defaultRows,
  expandedRows = DEFAULT_PROPS.expandedRows,
  separator = DEFAULT_PROPS.separator,
  validator = DEFAULT_PROPS.validator,
  filterChars = DEFAULT_PROPS.filterChars,
  showValidationFailedNotification = DEFAULT_PROPS.showValidationFailedNotification,
  getValidationMessage = DEFAULT_PROPS.getValidationMessage,
  hidePagination = DEFAULT_PROPS.hidePagination,
  pageSize = DEFAULT_PROPS.pageSize,
  hideSearch = DEFAULT_PROPS.hideSearch,
  disabled = DEFAULT_PROPS.disabled,
}) => {
  const { t } = useTranslation();

  const [formValues, setFormValues] = useState({
    source: '',
  });

  const [invalidItems, setInvalidItems] = useState([]);

  const [isNotificationClosed, setIsNotificationClosed] = useState(false);

  const onFormFieldChange = (evt) => setFormValues(mergeFormValues(evt));

  const [rows, setRows] = useState(defaultRows);

  const onFocus = () => {
    if (!disabled) {
      setRows(expandedRows);
    }
  };

  const onBlur = () => {
    setRows(defaultRows);
  };

  const onAddItemsClick = () => {
    const source = formValues.source || '';

    let sourceArray = source.trim()?.split?.(separator) || [];

    if (validator) {
      let newInvalidItems = [];

      sourceArray = sourceArray.filter((item) => {
        // check if regex is passed
        if (validator instanceof RegExp) {
          const isItemValid = validator.test(item);

          if (!isItemValid) {
            newInvalidItems.push(item);
          }

          return isItemValid;
        }

        if (typeof validator === 'function') {
          const isItemValid = validator?.({ list, item, newInvalidItems });

          if (!isItemValid) {
            newInvalidItems.push(item);
          }

          return isItemValid;
        }

        return true;
      });

      setInvalidItems(newInvalidItems);
      setIsNotificationClosed(false);
    }

    if (filterChars) {
      sourceArray = sourceArray.map((text) => text.replace(filterChars, ''));
    }

    setList((prevState) => [...new Set([...prevState, ...sourceArray])]);

    setFormValues((prevState) => ({ ...prevState, source: '' }));
  };

  const getTextAreaBorderClass = () => {
    let className = '';

    if (rows === defaultRows && list.length > 0) {
      className = 'has-border';
    }

    return className;
  };

  const onNotificationClose = () => {
    setIsNotificationClosed(true);
  };

  const renderErrorNotificationSection = () => {
    if (showValidationFailedNotification && !isNotificationClosed && invalidItems.length > 0) {
      let message =
        getValidationMessage?.({ invalidItems }) || `Invalid Entries - ${invalidItems.join(', ')}`;

      const list = [
        {
          message: message,
          type: 'error',
        },
      ];

      return <ToastContainer list={list} onClose={onNotificationClose} />;
    }

    return null;
  };

  return (
    <div className="is-flex has-ai-s list-builder-container">
      <div className={getFormattedClassName(`left-section ${getTextAreaBorderClass()}`)}>
        {!disabled && (
          <TextArea
            name="source"
            onChange={onFormFieldChange}
            value={formValues.source}
            rows={rows}
            onFocus={onFocus}
            onBlur={onBlur}
            containerClass="list-builder-text-area-container"
            inputClassName={getFormattedClassName(
              `list-builder-text-area ${rows === defaultRows ? '' : 'expanded'}`,
            )}
            disabled={disabled}
          />
        )}
        <BuilderList
          list={list}
          setList={setList}
          removeBlackList={removeBlackList}
          hidePagination={hidePagination}
          hideSearch={hideSearch}
          disabled={disabled}
          pageSize={pageSize}
        />
      </div>

      <div className="right-section">
        <Button onMouseDown={onAddItemsClick} disabled={!formValues.source || disabled}>
          {t(buttonText)}
        </Button>
      </div>

      {renderErrorNotificationSection()}
    </div>
  );
};

ListBuilder.propTypes = {
  buttonText: PropTypes.string,
  list: PropTypes.array,
  setList: PropTypes.func,
  removeBlackList: PropTypes.array,
  defaultRows: PropTypes.number,
  expandedRows: PropTypes.number,
  separator: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  validator: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  filterChars: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  showValidationFailedNotification: PropTypes.bool,
  getValidationMessage: PropTypes.func,
  hidePagination: PropTypes.bool,
  pageSize: PropTypes.number,
  hideSearch: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default ListBuilder;
