import { faTimesCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';
import TextWithTooltip from '../tooltip/TextWithTooltip';

const DEFAULT_PROPS = {
  item: '',
  showRemove: true,
  onRemove: noop,
  disabled: false,
};

const BuilderItem = ({
  item = DEFAULT_PROPS.item,
  showRemove = DEFAULT_PROPS.showRemove,
  onRemove = DEFAULT_PROPS.onRemove,
  disabled = DEFAULT_PROPS.disabled,
}) => {
  const onRemoveClick = () => {
    onRemove?.({ ids: [item] });
  };

  return (
    <div className="is-flex has-jc-sb has-ai-c builder-item">
      <TextWithTooltip className="detail-section"> {item}</TextWithTooltip>
      <div className="actions-section">
        {showRemove && (
          <Button
            type="tertiary"
            containerClass="content-width no-p-l no-p-r"
            onClick={onRemoveClick}
            disabled={disabled}
          >
            <FontAwesomeIcon icon={faTimesCircle} className="icon" />
          </Button>
        )}
      </div>
    </div>
  );
};

BuilderItem.propTypes = {
  item: PropTypes.string,
  showRemove: PropTypes.bool,
  onRemove: PropTypes.func,
  disabled: PropTypes.bool,
};

export default BuilderItem;
