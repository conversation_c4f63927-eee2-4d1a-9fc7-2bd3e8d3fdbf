import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { cloneDeep, filter, includes, noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import usePagination from '../../hooks/usePagination';
import Search from '../forms/Search';
import PaginationContainer from '../pagination/PaginationContainer';
import BuilderItem from './BuilderItem';

const DEFAULT_PROPS = {
  list: [],
  setList: noop,
  removeBlackList: [],
  noRecordMessage: 'NO_MATCHING_ITEMS_FOUND',
  actionsList: [
    { label: 'REMOVE_PAGE', value: 'REMOVE_PAGE' },
    { label: 'REMOVE_ALL', value: 'REMOVE_ALL' },
  ],
  modalRootId: '',
  hidePagination: false,
  pageSize: 500,
  hideSearch: false,
  disabled: false,
};

const BuilderList = ({
  list = DEFAULT_PROPS.list,
  setList = DEFAULT_PROPS.setList,
  removeBlackList = DEFAULT_PROPS.removeBlackList,
  noRecordMessage = DEFAULT_PROPS.noRecordMessage,
  actionsList = DEFAULT_PROPS.actionsList,
  modalRootId = DEFAULT_PROPS.modalRootId,
  hidePagination = DEFAULT_PROPS.hidePagination,
  pageSize = DEFAULT_PROPS.pageSize,
  hideSearch = DEFAULT_PROPS.hideSearch,
  disabled = DEFAULT_PROPS.disabled,
}) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');

  const [activeList, setActiveList] = useState(list);

  const pageDetail = usePagination({
    pageSize,
    list: activeList,
    totalRecord: activeList.length,
  });

  useEffect(() => {
    setActiveList(cloneDeep(list));
  }, [list]);

  const onSearch = async (term) => {
    if (searchTerm !== term) {
      const searchList = filter(list, (detail) =>
        includes((detail + '').toLowerCase(), (term + '').toLowerCase()),
      );

      setActiveList(searchList);
    }

    setSearchTerm(term);
  };

  const onRemove = ({ ids, removeAll = false }) => {
    if (disabled) {
      return;
    }

    if (removeAll) {
      // remove option valid only for items not in blacklist
      setList([...removeBlackList]);

      return;
    }

    let idsToRemove = Array.isArray(ids) ? ids : [ids];

    // remove option valid only for items not in blacklist
    idsToRemove = remove(idsToRemove, (id) => removeBlackList.indexOf(id) === -1);

    setList((prevState) => remove([...prevState], (item) => idsToRemove.indexOf(item) === -1));

    if (pageDetail.currentRecord.length === idsToRemove.length && pageDetail.currentPage != 1) {
      pageDetail.goPrev?.();
    }
  };

  const renderItemsSection = () => {
    if (pageDetail.currentRecord) {
      return (
        <div className="builder-item-container">
          {pageDetail.currentRecord.map((item) => {
            // remove option valid only for items not in blacklist
            const showRemove = removeBlackList.indexOf(item) < 0;

            return (
              <BuilderItem
                key={item}
                item={item}
                showRemove={showRemove}
                onRemove={onRemove}
                disabled={disabled}
              />
            );
          })}
        </div>
      );
    }

    return null;
  };

  const renderPaginationSection = () => {
    return (
      <PaginationContainer
        {...pageDetail}
        removeBlackList={removeBlackList}
        actionsList={actionsList}
        modalRootId={modalRootId}
        onRemove={onRemove}
        disabled={disabled}
      />
    );
  };

  const renderNoRecords = () => {
    return <div className="no-records-section">{t(noRecordMessage)}</div>;
  };

  if (list.length > 0) {
    const hasActiveRecords = pageDetail.currentRecord.length > 0;

    return (
      <div className="is-flex has-fd-c builder-list-container">
        {!hideSearch && (
          <div className="builder-list-search-container">
            <Search onSearch={onSearch} on term={searchTerm} />
          </div>
        )}
        {hasActiveRecords ? (
          <>
            {renderItemsSection()}

            {!hidePagination && renderPaginationSection()}
          </>
        ) : (
          renderNoRecords()
        )}
      </div>
    );
  }

  return null;
};

BuilderList.propTypes = {
  list: PropTypes.array,
  setList: PropTypes.func,
  removeBlackList: PropTypes.array,
  noRecordMessage: PropTypes.string,
  actionsList: PropTypes.array,
  modalRootId: PropTypes.string,
  hidePagination: PropTypes.bool,
  pageSize: PropTypes.number,
  hideSearch: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default BuilderList;
