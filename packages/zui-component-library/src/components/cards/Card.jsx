import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  onClick: noop,
  children: null,
};

const Card = ({
  containerClass = DEFAULT_PROPS.containerClass,
  onClick = DEFAULT_PROPS.onClick,
  containerStyle = DEFAULT_PROPS,
  children = DEFAULT_PROPS.children,
}) => {
  return (
    <section
      className={getFormattedClassName(`card ${containerClass}`)}
      onClick={onClick}
      style={containerStyle}
    >
      {children}
    </section>
  );
};

Card.propTypes = {
  containerClass: PropTypes.string,
  onClick: PropTypes.func,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default Card;
