import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  message: '',
  containerClass: '',
  containerStyle: {},
  children: null,
};

const NoDataMessage = ({
  message = DEFAULT_PROPS.messages,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  return (
    <div
      key={'no-data-message'}
      className={`is-flex has-jc-c no-data-container has-color-primary has-background-light ${containerClass}`}
      style={containerStyle}
    >
      {children ? children : t(message)}
    </div>
  );
};

NoDataMessage.propTypes = {
  message: PropTypes.string,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default NoDataMessage;
