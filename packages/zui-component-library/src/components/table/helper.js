export const TABLE_COLUMN_DEF_NUMBER = {
  id: 'number',
  Header: 'TABLE_NUMBER',
  minSize: 50,
  size: 50,
  enableResizing: false,
  defaultCanSort: true,
  sortingFn: 'alphanumeric',
  columnContainerClass: 'no-grow',
};

export const TABLE_COLUMN_DEF_SELECTOR = {
  id: 'selection',
  Header: '',
  size: 50,
  minSize: 50,
  maxSize: 50,
  enableResizing: false,
  disableSortBy: true,
  defaultCanSort: false,
  columnContainerClass: 'no-grow',
};

export const TABLE_COLUMN_DEF_ACTION = {
  id: 'actions',
  Header: 'ACTIONS',
  minSize: 100,
  size: 100,
  enableResizing: false,
  disableSortBy: true,
  defaultCanSort: false,
  sortingFn: 'alphanumeric',
  columnContainerClass: 'no-grow',
};
