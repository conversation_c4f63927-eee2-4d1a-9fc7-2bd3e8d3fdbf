import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  list: [],
  onActionClick: noop,
  children: null,
};

const ActionList = ({
  list = DEFAULT_PROPS.list,
  onActionClick = DEFAULT_PROPS.onActionClick,
  children = DEFAULT_PROPS.children,
  ...props
}) => {
  const { t } = useTranslation();

  if (children) {
    return children;
  }

  return (
    <div className="action-list-container">
      {list.map(({ label, value }) => (
        <div
          key={value}
          className="action"
          onClick={(evt) => onActionClick(value, { ...props }, evt)}
        >
          {t(label)}
        </div>
      ))}
    </div>
  );
};

ActionList.propTypes = {
  list: PropTypes.array,
  onActionClick: PropTypes.func,
  children: PropTypes.any,
};

export default ActionList;
