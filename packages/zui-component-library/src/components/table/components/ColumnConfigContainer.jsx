import { faEllipsisVertical } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import PropTypes from 'prop-types';

import Button from '../../buttons/Button';
import ConfigureColumn from './ConfigureColumn';

const DEFAULT_PROPS = {
  tableRef: { current: null },
  showMoreIcon: faEllipsisVertical,
  renderColumnConfigContent: (props) => <ConfigureColumn {...props} />,
  containerClass: '',
};

const ColumnConfigContainer = ({
  showMoreIcon = DEFAULT_PROPS.showMoreIcon,
  renderColumnConfigContent = DEFAULT_PROPS.renderColumnConfigContent,
  containerClass = DEFAULT_PROPS.containerClass,
  ...payload
}) => {
  return (
    <div className={`column-config-container ${containerClass}`} id="drag-and-drop-list-contianer">
      <Button type="tertiary" containerClass="content-width show-list-icon">
        <FontAwesomeIcon icon={showMoreIcon} />
      </Button>

      {renderColumnConfigContent({ ...payload })}
    </div>
  );
};

ColumnConfigContainer.propTypes = {
  tableRef: PropTypes.object,
  showMoreIcon: PropTypes.any,
  renderColumnConfigContent: PropTypes.func,
  containerClass: PropTypes.string,
};

export default ColumnConfigContainer;
