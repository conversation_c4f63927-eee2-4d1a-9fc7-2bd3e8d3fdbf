import { useTranslation } from 'react-i18next';

import { faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../../buttons/Button';
import ConfigureColumnList from './ConfigureColumnList';

const DEFAULT_PROPS = {
  setColumnOrder: noop,
  renderList: (props) => <ConfigureColumnList {...props} />,
};

const ConfigureColumn = ({
  setColumnOrder = DEFAULT_PROPS.setColumnOrder,
  renderList = DEFAULT_PROPS.renderList,
  ...props
}) => {
  const { t } = useTranslation();

  const onNewListOrder = (result) => {
    setColumnOrder(result);
  };

  const renderListSection = () => {
    return renderList({ id: 'drag-and-drop-list-contianer', onNewListOrder, ...props });
  };

  return (
    <section className="configure-column-container">
      <div className="is-flex has-jc-sb full-width">
        <div className="typography-paragraph1">{t('CUSTOMISE_COLUMNS')}</div>

        <Button type="tertiary" containerClass="content-width no-p-t no-p-b">
          <FontAwesomeIcon icon={faSync} className="icon right" />
        </Button>
      </div>

      {renderListSection()}
    </section>
  );
};

ConfigureColumn.propTypes = {
  setColumnOrder: PropTypes.func,
  renderList: PropTypes.func,
};

export default ConfigureColumn;
