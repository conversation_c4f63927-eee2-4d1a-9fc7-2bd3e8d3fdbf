import { faTrash, faUserPlus } from '@fortawesome/pro-regular-svg-icons';
import { faEllipsisVertical, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import useFloatingLayout from '../../../hooks/floating/useFloatingLayout';
import Button from '../../buttons/Button';
import FloatingContainer from '../../floating/FloatingContainer';
import ActionList from './ActionList';

const DEFAULT_PROPS = {
  showEdit: true,
  editIcon: faPencilAlt,
  onEditClick: noop,
  showDelete: true,
  deleteIcon: faTrash,
  onDeleteClick: noop,
  showAddUsers: false,
  addUsersIcon: faUserPlus,
  onAddUsersClick: noop,
  showMore: false,
  showMoreIcon: faEllipsisVertical,
  onShowMoreClick: noop,
  renderShowMoreActionContent: (props) => <ActionList {...props} />,
  row: {
    original: {},
  },
};

const Actions = ({
  showEdit = DEFAULT_PROPS.showEdit,
  editIcon = DEFAULT_PROPS.editIcon,
  onEditClick = DEFAULT_PROPS.onEditClick,
  showDelete = DEFAULT_PROPS.showDelete,
  deleteIcon = DEFAULT_PROPS.deleteIcon,
  onDeleteClick = DEFAULT_PROPS.onDeleteClick,
  showAddUsers = DEFAULT_PROPS.showAddUsers,
  addUsersIcon = DEFAULT_PROPS.addUsersIcon,
  onAddUsersClick = DEFAULT_PROPS.onAddUsersClick,
  showMore = DEFAULT_PROPS.showMore,
  showMoreIcon = DEFAULT_PROPS.showMoreIcon,
  onShowMoreClick = DEFAULT_PROPS.onShowMoreClick,
  renderShowMoreActionContent = DEFAULT_PROPS.renderShowMoreActionContent,
  row = DEFAULT_PROPS.row,
  ...payload
}) => {
  const {
    open: isOpen,
    setOpen: setIsOpen,
    ...floatingLayout
  } = useFloatingLayout({
    placement: 'bottom-end',
    showOnHover: false,
    showOnClick: true,
    dismissConfig: {
      ancestorScroll: true,
      referencePress: true,
    },
  });

  const { refs } = floatingLayout;

  return (
    <div className="actions-container is-flex">
      {showEdit && (
        <Button
          type="tertiary"
          onClick={() => {
            onEditClick(row?.original, { ...payload, row });
          }}
          containerClass="table-action-btn content-width"
        >
          <FontAwesomeIcon icon={editIcon} />
        </Button>
      )}

      {showDelete && (
        <Button
          type="tertiary"
          onClick={() => {
            onDeleteClick(row?.original, { ...payload, row });
          }}
          containerClass="table-action-btn content-width"
        >
          <FontAwesomeIcon icon={deleteIcon} />
        </Button>
      )}

      {showAddUsers && (
        <Button
          type="tertiary"
          onClick={() => {
            onAddUsersClick(row?.original, { ...payload, row });
          }}
          containerClass="table-action-btn content-width"
        >
          <FontAwesomeIcon icon={addUsersIcon} />
        </Button>
      )}

      {showMore && (
        <div ref={refs.setReference}>
          <Button
            type="tertiary"
            onClick={() => {
              setIsOpen(true);
              onShowMoreClick(row?.original, { ...payload, row });
            }}
            containerClass="table-action-btn content-width"
          >
            <FontAwesomeIcon icon={showMoreIcon} />
          </Button>

          <FloatingContainer show={isOpen} {...floatingLayout}>
            {renderShowMoreActionContent({ floatingLayout, isOpen, setIsOpen, ...payload, row })}
          </FloatingContainer>
        </div>
      )}
    </div>
  );
};

Actions.propTypes = {
  showEdit: PropTypes.bool,
  editIcon: PropTypes.any,
  onEditClick: PropTypes.func,
  showDelete: PropTypes.bool,
  deleteIcon: PropTypes.any,
  onDeleteClick: PropTypes.func,
  showAddUsers: PropTypes.bool,
  addUsersIcon: PropTypes.any,
  onAddUsersClick: PropTypes.func,
  showMore: PropTypes.bool,
  showMoreIcon: PropTypes.any,
  onShowMoreClick: PropTypes.func,
  renderShowMoreActionContent: PropTypes.func,
  row: PropTypes.object,
};

export default Actions;
