import { useTranslation } from 'react-i18next';

import { faBars } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import useDraggable from '../../../hooks/useDraggable';
import Button from '../../buttons/Button';
import Checkbox from '../../forms/Checkbox';

const DEFAULT_PROPS = {
  item: {},
  onReorder: noop,
  ignoreList: [],
  checkboxContainerClass: '',
  checkboxContainerStyle: {},
};

const ConfigureColumnItem = ({
  item = DEFAULT_PROPS.item,
  onReorder = DEFAULT_PROPS.onReorder,
  checkboxContainerClass = DEFAULT_PROPS.checkboxContainerClass,
  checkboxContainerStyle = DEFAULT_PROPS.checkboxContainerStyle,
}) => {
  const { t } = useTranslation();

  const {
    columnDef: { Header },
    getIsVisible,
    toggleVisibility,
  } = item;

  const onDrop = (source) => {
    if (!source.id) {
      return;
    }
    if (source.id === item.id) {
      return;
    }

    onReorder({ source, destination: item });
  };

  const { dropRef, dragRef } = useDraggable({
    type: 'drag-and-drop-item',
    onDrop,
    item,
  });

  const headerContent = typeof Header === 'function' ? Header() : t(Header);

  const isVisible = getIsVisible();

  const onChange = () => {
    toggleVisibility(!isVisible);
  };

  if (headerContent) {
    return (
      <div key={item.id} ref={dropRef} className="drag-and-drop-item-container">
        <div ref={dragRef} className="is-flex has-jc-sb has-ai-c">
          <Checkbox
            name={headerContent}
            isLarge={false}
            checked={isVisible}
            onChange={onChange}
            containerClass={`has-ai-c ${checkboxContainerClass}`}
            containerStyle={checkboxContainerStyle}
          >
            {headerContent}
          </Checkbox>
          <Button type="tertiary" containerClass="no-p-r content-width dnd-icon">
            <FontAwesomeIcon icon={faBars} className="icon right" />
          </Button>
        </div>
      </div>
    );
  }

  return null;
};

ConfigureColumnItem.propTypes = {
  item: PropTypes.object,
  onReorder: PropTypes.func,
  ignoreList: PropTypes.array,
  checkboxContainerClass: PropTypes.string,
  checkboxContainerStyle: PropTypes.object,
};

export default ConfigureColumnItem;
