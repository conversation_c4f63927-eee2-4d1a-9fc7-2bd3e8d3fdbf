import { useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import { findIndex, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import ConfigureColumnItem from './ConfigureColumnItem';

const reorder = (list, source, destination) => {
  const newColumnOrder = Array.from(list);

  const sourceIndex = findIndex(newColumnOrder, { id: source.id });
  const destinationIndex = findIndex(newColumnOrder, { id: destination.id });

  newColumnOrder[sourceIndex] = destination;
  newColumnOrder[destinationIndex] = source;

  return newColumnOrder.map((column) => column.id);
};

const DEFAULT_PROPS = {
  id: '',
  list: [],
  ignoreList: [],
  onNewListOrder: noop,
  renderItem: (props) => <ConfigureColumnItem {...props} />,
};

const ConfigureColumnList = ({
  id = DEFAULT_PROPS.string,
  list = DEFAULT_PROPS.list,
  ignoreList = DEFAULT_PROPS.ignoreList,
  onNewListOrder = DEFAULT_PROPS.onNewListOrder,
  renderItem = DEFAULT_PROPS.renderItem,
  ...props
}) => {
  const getActiveList = ({ list }) => {
    const activeList = [];

    list.forEach((item) => {
      const {
        columnDef: { id },
      } = item;

      const canShow = ignoreList?.indexOf?.(id) == -1;

      if (canShow) {
        activeList.push(item);
      }
    });

    return activeList;
  };
  const [activeList, setActiveList] = useState([]);

  const [context, setContext] = useState(null);

  useEffect(() => {
    setContext(document.getElementById(id));
  }, [id]);

  useEffect(() => {
    const newList = getActiveList({ list });

    setActiveList(newList);
  }, [list]);

  const onReorder = ({ source, destination }) => {
    const newColumnOrder = reorder(list, source, destination);

    onNewListOrder(newColumnOrder);
  };

  const renderItemSection = (item) => {
    return renderItem({ item, onReorder, onNewListOrder, ...props });
  };

  return context ? (
    <DndProvider backend={HTML5Backend} options={{ rootElement: context }}>
      <div className="configure-column-list-container">
        {activeList.map((item) => renderItemSection(item))}
      </div>
    </DndProvider>
  ) : null;
};

ConfigureColumnList.propTypes = {
  id: PropTypes.string.isRequired,
  list: PropTypes.array.isRequired,
  ignoreList: PropTypes.array.isRequired,
  onNewListOrder: PropTypes.func.isRequired,
  renderItem: PropTypes.func.isRequired,
};

export default ConfigureColumnList;
