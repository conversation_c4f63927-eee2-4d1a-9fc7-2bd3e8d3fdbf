import { faSquare } from '@fortawesome/pro-light-svg-icons';
import { faCheckSquare, faMinusSquare } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  type: 'checkbox',
  kind: 'page',
  level: 'row',
  getToggleAllPageRowsSelectedProps: noop,
  getToggleAllRowsSelectedProps: noop,
  row: { getToggleRowSelectedProps: noop },
  table: {},
};

const Selector = ({
  type = DEFAULT_PROPS.type,
  kind = DEFAULT_PROPS.kind,
  level = DEFAULT_PROPS.level,
  row = DEFAULT_PROPS.row,
  table = DEFAULT_PROPS.table,
}) => {
  let selectionProps = {
    checked: row.getIsSelected(),
    indeterminate: row.getIsSomeSelected(),
    onChange: row.getToggleSelectedHandler(),
  };

  if (level === 'column') {
    if (kind === 'page') {
      selectionProps = {
        checked: table.getIsAllPageRowsSelected(),
        indeterminate: table.getIsSomePageRowsSelected(),
        onChange: table.getToggleAllPageRowsSelectedHandler(),
      };
    } else {
      selectionProps = {
        checked: table.getIsAllRowsSelected(),
        indeterminate: table.getIsSomeRowsSelected(),
        onChange: table.getToggleAllRowsSelectedHandler(),
      };
    }
  }

  if (type === 'checkbox') {
    const { checked, indeterminate, ...rest } = selectionProps;

    let checkboxIcon = faSquare;

    if (checked) {
      checkboxIcon = faCheckSquare;
    }

    if (level !== 'row' && indeterminate) {
      checkboxIcon = faMinusSquare;
    }

    return (
      <div {...rest} onClick={rest.onChange} className="full-width selector checkbox">
        <FontAwesomeIcon icon={checkboxIcon} className="icon" />
      </div>
    );
  }

  return null;
};

Selector.propTypes = {
  type: PropTypes.oneOf(['checkbox']),
  kind: PropTypes.oneOf(['all', 'page']),
  level: PropTypes.oneOf(['column', 'row']),
  getToggleAllPageRowsSelectedProps: PropTypes.func,
  getToggleAllRowsSelectedProps: PropTypes.func,
  row: PropTypes.object,
  table: PropTypes.object,
};

export default Selector;
