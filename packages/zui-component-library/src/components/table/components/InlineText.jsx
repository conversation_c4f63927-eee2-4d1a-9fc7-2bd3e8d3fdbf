import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  getValue: noop,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const InlineText = ({
  getValue = DEFAULT_PROPS.getValue,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  return (
    <span className={`inline-text ${containerClass}`} style={containerStyle}>
      {children ? children : t(getValue())}
    </span>
  );
};

InlineText.propTypes = {
  getValue: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default InlineText;
