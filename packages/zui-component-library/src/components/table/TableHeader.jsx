import { useTranslation } from 'react-i18next';

import { faSortDown, faSortUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { flexRender } from '@tanstack/react-table';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  column: {
    columnDef: { Header: '' },
  },
  getContext: noop,
};

const TableHeader = ({ column = DEFAULT_PROPS.column, getContext = DEFAULT_PROPS.getContext }) => {
  const { t } = useTranslation();

  const { Header } = column.columnDef;

  const canSort = column.getCanSort();
  const isSorted = column.getIsSorted();
  const toggleSortHandler = column.getToggleSortingHandler();

  const renderSortSection = () => {
    if (canSort) {
      let angleUpClass = 'disabled';
      let angleDownClass = 'disabled';

      if (isSorted) {
        angleUpClass = isSorted === 'asc' ? 'active' : 'disabled';
        angleDownClass = isSorted === 'desc' ? 'active' : 'disabled';
      }

      return (
        <div className="is-flex has-fd-c" onClick={toggleSortHandler}>
          <FontAwesomeIcon icon={faSortUp} className={`icon ${angleUpClass}`} />

          <FontAwesomeIcon
            icon={faSortDown}
            className={`icon ${angleDownClass}`}
            style={{ marginTop: '-12px' }}
          />
        </div>
      );
    }

    return null;
  };

  const renderHeaderSection = () => {
    if (typeof Header === 'string') {
      return t(Header);
    }

    return flexRender('Header', getContext());
  };

  return (
    <div className="is-flex has-jc-sb has-ai-c full-width header-content-container">
      <div className="has-as-c header-section">{renderHeaderSection()}</div>

      <div className="sort-section">{renderSortSection()}</div>
    </div>
  );
};

TableHeader.propTypes = {
  column: PropTypes.object,
  getContext: PropTypes.func,
};

export default TableHeader;
