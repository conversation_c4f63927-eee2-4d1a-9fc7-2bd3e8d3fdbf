import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import TableHeader from './TableHeader';
import TableResizer from './TableResizer';

const DEFAULT_PROPS = {
  column: { render: noop },
  renderHeader: (props) => <TableHeader {...props} />,
  renderResizer: (props) => <TableResizer {...props} />,
};

const TableHeaderContainer = ({
  renderHeader = DEFAULT_PROPS.renderHeader,
  renderResizer = DEFAULT_PROPS.renderResizer,
  ...rest
}) => {
  return (
    <>
      {renderHeader({ ...rest })}
      {renderResizer({ ...rest })}
    </>
  );
};

TableHeaderContainer.propTypes = {
  column: PropTypes.object,
  renderHeader: PropTypes.func,
  renderResizer: PropTypes.func,
};

export default TableHeaderContainer;
