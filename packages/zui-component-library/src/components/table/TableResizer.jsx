import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  column: {},
  getResizeHandler: noop,
  table: {},
};

const TableResizer = ({
  column = DEFAULT_PROPS.column,
  getResizeHandler = DEFAULT_PROPS.getResizeHandler,
  table = DEFAULT_PROPS.table,
}) => {
  const canResize = column.getCanResize();
  const isResizing = column.getIsResizing();

  if (canResize) {
    const style = {
      transform: isResizing ? `translateX(${table.getState().columnSizingInfo.deltaOffset}px)` : '',
    };

    return (
      <div
        onMouseDown={getResizeHandler()}
        onTouchStart={getResizeHandler()}
        className={`resizer ${isResizing ? 'isResizing' : ''}`}
        style={style}
      />
    );
  }
  return null;
};

TableResizer.propTypes = {
  column: PropTypes.object,
  getResizeHandler: PropTypes.func,
  table: PropTypes.object,
};

export default TableResizer;
