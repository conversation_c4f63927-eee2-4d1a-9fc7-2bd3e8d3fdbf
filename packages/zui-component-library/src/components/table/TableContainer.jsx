import { useEffect, useMemo, useRef, useState } from 'react';

import { getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import ColumnConfigContainer from './components/ColumnConfigContainer';

import LoadMore from '../buttons/LoadMore';
import NoDataMessage from './NoDataMessage';
import CellContainer from './TableCellContainer';
import TableFooterContainer from './TableFooterContainer';
import HeaderContainer from './TableHeaderContainer';
import TableResizer from './TableResizer';

const DEFAULT_PROPS = {
  columns: [],
  data: [],
  pagination: {},
  hidePagination: false,
  noDataMessage: 'NO_ITEMS_FOUND',
  showColumnLayoutConfigurer: false,
  columnConfigIgnoreList: [],
  resetSelection: true,
  showFooter: true,
  onRowSelection: noop,
  renderHeaderContainer: (props) => <HeaderContainer {...props} />,
  renderResizer: (props) => <TableResizer {...props} />,
  renderCellContainer: (props) => <CellContainer {...props} />,
  renderLoadMoreContainer: (props) => <LoadMore {...props} />,
  renderNoDataSection: (props) => <NoDataMessage containerClass="cell-container" {...props} />,
  renderExpandedRowContainer: noop,
  renderFooterSection: (props) => <TableFooterContainer {...props} />,
  getTableActions: noop,
  headerContainerClass: '',
  headerContainerStyle: {},
  containerClass: '',
  containerStyle: {},
};

const TableContainer = ({
  columns = DEFAULT_PROPS.columns,
  data = DEFAULT_PROPS.data,
  pagination = DEFAULT_PROPS.pagination,
  hidePagination = DEFAULT_PROPS.hidePagination,
  noDataMessage = DEFAULT_PROPS.noDataMessage,
  showFooter = DEFAULT_PROPS.showFooter,
  resetSelection = DEFAULT_PROPS.resetSelection,
  showColumnLayoutConfigurer = DEFAULT_PROPS.showColumnLayoutConfigurer,
  columnConfigIgnoreList = DEFAULT_PROPS.columnConfigIgnoreList,
  renderHeaderContainer = DEFAULT_PROPS.renderHeaderContainer,
  renderResizer = DEFAULT_PROPS.renderResizer,
  renderCellContainer = DEFAULT_PROPS.renderCellContainer,
  renderLoadMoreContainer = DEFAULT_PROPS.renderLoadMoreContainer,
  onRowSelection = DEFAULT_PROPS.onRowSelection,
  renderNoDataSection = DEFAULT_PROPS.renderNoDataSection,
  renderExpandedRowContainer = DEFAULT_PROPS.renderExpandedRowContainer,
  renderFooterSection = DEFAULT_PROPS.renderFooterSection,
  getTableActions = DEFAULT_PROPS.getTableActions,
  headerContainerClass = DEFAULT_PROPS.headerContainerClass,
  headerContainerStyle = DEFAULT_PROPS.headerContainerStyle,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const tableRef = useRef(null);
  const headerRef = useRef(null);
  const tableColumns = useMemo(() => columns, [columns]);
  const tableData = useMemo(() => data, [data]);

  const [hasHeaderOverflow, setHasHeaderOverflow] = useState(false);

  const hasData = tableData.length > 0;

  const table = useReactTable({
    columns: tableColumns,
    data: tableData,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const {
    getAllLeafColumns,
    toggleAllColumnsVisible,
    getHeaderGroups,
    setColumnOrder,
    getRowModel,
    getState,
    getSelectedRowModel,
    resetRowSelection,
  } = table;

  const { columnSizingInfo } = getState();

  useEffect(() => {
    if (resetSelection) {
      resetRowSelection?.(false);
    }

    getTableActions({ ...table });
  }, [tableData]);

  const selectedRowIds = getSelectedRowModel().flatRows;

  useEffect(() => {
    const selectedRowDetail = [];

    selectedRowIds.forEach((detail) => {
      selectedRowDetail.push(detail?.original || {});
    });

    onRowSelection(selectedRowDetail);
  }, [selectedRowIds]);

  const updateIsHeaderOverflow = () => {
    const { clientWidth } = tableRef?.current || {};

    const { scrollWidth } = headerRef?.current || {};

    const hasOverflow = scrollWidth > clientWidth;

    if (hasOverflow != hasHeaderOverflow) {
      setHasHeaderOverflow(hasOverflow);
    }
  };

  useEffect(() => {
    updateIsHeaderOverflow();
  }, [tableRef?.current?.clientWidth, headerRef.current?.scrollWidth, columnSizingInfo]);

  const renderHeaderSection = () => {
    return (
      <div
        ref={headerRef}
        className={`header-container ${
          hasHeaderOverflow ? 'has-overflow' : ''
        } ${headerContainerClass}`}
        style={headerContainerStyle}
      >
        {getHeaderGroups().map((headerGroup) => (
          <div key={headerGroup.id} className="row-container">
            {headerGroup.headers.map((header) => {
              return (
                <div
                  key={header.id}
                  colSpan={header.colSpan}
                  className={`column-container ${header?.column?.columnDef?.columnContainerClass ?? ''}`}
                  style={{
                    width: header.getSize(),
                    ...(header?.column?.columnDef?.columnContainerStyle ?? {}),
                  }}
                >
                  {renderHeaderContainer({ ...header, renderResizer, table })}
                </div>
              );
            })}
          </div>
        ))}

        {/* no data text would(eventually) go out of viewport(as it is center aligned) in case we have 
            header(column) overflowing table width

            could have done in body-container but then we need to set body-container width expilicity
            in all cases or at least overlfow case using headerRef. Hence to avoid unnecessary calculation,
            resuing the header section
            
            Note: tableRef scrollWidht is needed to center align no data text in case of overflow
            also the trick here is to have row-container and no-data-container to have 
            same background color(white)
        */}

        {!hasData && (
          <div className="row-container has-background-light">
            <div style={{ width: tableRef?.current?.scrollWidth }}>
              {renderNoDataSection({ message: noDataMessage })}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderRowSection = () => {
    return (
      <>
        {getRowModel().rows.map((row) => {
          const isExpanded = row.getIsExpanded();

          return (
            <>
              <div key={row.id} className="row-container">
                {row.getVisibleCells().map((cell) => {
                  return (
                    <div
                      key={cell.id}
                      className={`cell-container ${cell?.column?.columnDef?.containerClass || ''}`}
                      style={{ width: cell.column.getSize() }}
                    >
                      {renderCellContainer({ ...cell, table })}
                    </div>
                  );
                })}
              </div>

              {isExpanded ? renderExpandedRowContainer({ ...row, table }) : null}
            </>
          );
        })}

        {!hidePagination && renderLoadMoreContainer(pagination)}
      </>
    );
  };

  return (
    <article
      ref={tableRef}
      className={`table-container ${hasData ? 'has-data' : 'has-no-data'} ${
        showFooter ? 'has-footer' : 'has-no-footer'
      } ${containerClass}`}
      style={containerStyle}
    >
      {showColumnLayoutConfigurer && (
        <ColumnConfigContainer
          tableRef={tableRef}
          table={table}
          list={getAllLeafColumns()}
          setColumnOrder={setColumnOrder}
          ignoreList={columnConfigIgnoreList}
          toggleAllColumnsVisible={toggleAllColumnsVisible}
        />
      )}

      <div className="table">
        {renderHeaderSection()}

        {hasData && <div className="body-container">{renderRowSection()}</div>}

        {showFooter && (
          <>
            {renderFooterSection({
              hasData,
              tableData,
              tableColumns,
              table,
              pagination,
              hidePagination,
            })}
          </>
        )}
      </div>
    </article>
  );
};

TableContainer.propTypes = {
  columns: PropTypes.array,
  data: PropTypes.array,
  pagination: PropTypes.object,
  hidePagination: PropTypes.bool,
  showFooter: PropTypes.bool,
  resetSelection: PropTypes.bool,
  noDataMessage: PropTypes.string,
  showColumnLayoutConfigurer: PropTypes.bool,
  columnConfigIgnoreList: PropTypes.array,
  onRowSelection: PropTypes.func,
  renderHeaderContainer: PropTypes.func,
  renderResizer: PropTypes.func,
  renderCellContainer: PropTypes.func,
  renderExpandedRowContainer: PropTypes.func,
  renderLoadMoreContainer: PropTypes.func,
  renderNoDataSection: PropTypes.func,
  renderFooterSection: PropTypes.func,
  getTableActions: PropTypes.func,
  headerContainerClass: PropTypes.string,
  headerContainerStyle: PropTypes.object,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default TableContainer;
