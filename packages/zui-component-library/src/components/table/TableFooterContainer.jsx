import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  pagination: {},
  hidePagination: false,
};

const TableFooterContainer = ({
  pagination = DEFAULT_PROPS.pagination,
  hidePagination = DEFAULT_PROPS.hidePagination,
}) => {
  const { totalRecord = 0, data = [] } = pagination || {};

  return (
    <div className="table-footer-container">
      <div className="left-section">
        {!hidePagination && totalRecord != -1 ? `Showing ${data?.length} of ${totalRecord}` : ''}
      </div>
      <div className="middle-section"></div>
      <div className="right-section"></div>
    </div>
  );
};

TableFooterContainer.propTypes = {
  pagination: PropTypes.object,
  hidePagination: PropTypes.bool,
};

export default TableFooterContainer;
