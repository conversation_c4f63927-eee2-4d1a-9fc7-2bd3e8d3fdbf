import { cloneElement } from 'react';

import {
  FloatingNode,
  FloatingTree,
  useFloatingNodeId,
  useFloatingParentNodeId,
  useFloatingTree,
} from '@floating-ui/react';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  renderReferenceElement: noop,
  renderFloatingElement: noop,
  children: null,
};

const FloatingNodeContainer = ({
  renderReferenceElement = DEFAULT_PROPS.renderReferenceElement,
  renderFloatingElement = DEFAULT_PROPS.renderFloatingElement,
  children = DEFAULT_PROPS.children,
}) => {
  const parentId = useFloatingParentNodeId();
  const floatingTree = useFloatingTree();

  const nodeId = useFloatingNodeId();

  const nodeMeta = {
    parentId,
    floatingTree,
    nodeId,
  };

  const renderFloatingPortalSection = () => {
    if (children) {
      return cloneElement(children, { nodeMeta });
    }

    return renderFloatingElement({ nodeMeta });
  };

  const renderFloatingNodeSection = () => {
    return (
      <FloatingNode id={nodeId}>
        {renderReferenceElement({ nodeMeta })}

        {renderFloatingPortalSection()}
      </FloatingNode>
    );
  };

  if (parentId === null) {
    return <FloatingTree>{renderFloatingNodeSection()}</FloatingTree>;
  }

  return renderFloatingNodeSection();
};

FloatingNodeContainer.propTypes = {
  renderReferenceElement: PropTypes.func,
  renderFloatingElement: PropTypes.func,
  children: PropTypes.node,
};

export default FloatingNodeContainer;
