import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import FloatingPortalContainer from './FloatingPortalContainer';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  arrowStyle: {},
  show: false,
  hasArrow: false,
  refs: { setReference: noop, setFloating: noop },
  middlewareData: {
    hide: {},
    arrow: {},
  },
  strategy: 'absolute',
  placement: 'bottom',
  x: 0,
  y: 0,
  arrowRef: null,
  getFloatingProps: noop,
  children: null,
};

const FloatingContainer = ({
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  arrowStyle = DEFAULT_PROPS.arrowStyle,
  show = DEFAULT_PROPS.show,
  hasArrow = DEFAULT_PROPS.hasArrow,
  refs = DEFAULT_PROPS.refs,
  middlewareData = DEFAULT_PROPS.middlewareData,
  strategy = DEFAULT_PROPS.strategy,
  placement = DEFAULT_PROPS.placement,
  x = DEFAULT_PROPS.x,
  y = DEFAULT_PROPS.y,
  arrowRef = DEFAULT_PROPS.arrowRef,
  getFloatingProps = DEFAULT_PROPS.getFloatingProps,
  children = DEFAULT_PROPS.children,
}) => {
  const { x: arrowX, y: arrowY } = middlewareData.arrow || {};

  const staticSide = {
    top: 'bottom',
    right: 'left',
    bottom: 'top',
    left: 'right',
  }[placement.split('-')[0]];

  return (
    <FloatingPortalContainer show={show}>
      <div
        className={getFormattedClassName(`floating-container ${placement} ${containerClass}`)}
        ref={refs.setFloating}
        style={{
          position: strategy,
          top: y ?? '',
          left: x ?? '',
          ...containerStyle,
        }}
        {...getFloatingProps()}
      >
        {children}

        {hasArrow && (
          <div
            className={`arrow-container ${placement}`}
            ref={arrowRef}
            style={{
              left: arrowX != null ? `${arrowX}px` : '',
              top: arrowY != null ? `${arrowY}px` : '',
              right: '',
              bottom: '',
              [staticSide]: '-4px',
              ...arrowStyle,
            }}
          />
        )}
      </div>
    </FloatingPortalContainer>
  );
};

FloatingContainer.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  arrowStyle: PropTypes.object,
  show: PropTypes.bool,
  hasArrow: PropTypes.bool,
  refs: PropTypes.object,
  middlewareData: PropTypes.object,
  strategy: PropTypes.oneOf(['absolute', 'fixed']),
  placement: PropTypes.string,
  x: PropTypes.number,
  y: PropTypes.number,
  arrowRef: PropTypes.any,
  getFloatingProps: PropTypes.func,
  children: PropTypes.any,
};

export default FloatingContainer;
