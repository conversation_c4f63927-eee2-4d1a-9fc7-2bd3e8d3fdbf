import { cloneElement } from 'react';

import { FloatingDelayGroup, FloatingOverlay, FloatingPortal } from '@floating-ui/react';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFloatingPortalRootId } from '../../config/floating';

const DEFAULT_PROPS = {
  id: '',
  root: null,
  show: false,
  nodeMeta: {},
  skipMeta: false,
  hasOverlay: false,
  lockScroll: true,
  overlayProps: {},
  hasDelayGroup: false,
  delay: {},
  renderContentSection: noop,
  children: null,
};

const FloatingPortalContainer = ({
  id = DEFAULT_PROPS.id,
  root = DEFAULT_PROPS.root,
  show = DEFAULT_PROPS.show,
  nodeMeta = DEFAULT_PROPS.nodeMeta,
  skipMeta = DEFAULT_PROPS.skipMeta,
  hasOverlay = DEFAULT_PROPS.hasOverlay,
  lockScroll = DEFAULT_PROPS.lockScroll,
  overlayProps = DEFAULT_PROPS.overlayProps,
  hasDelayGroup = DEFAULT_PROPS.hasDelayGroup,
  delay = DEFAULT_PROPS.delay,
  renderContentSection = DEFAULT_PROPS.renderContentSection,
  children = DEFAULT_PROPS.children,
}) => {
  const portalId = id || getFloatingPortalRootId();
  const meta = {
    id: portalId,
    root,
    show,
    nodeMeta,
    hasOverlay,
    lockScroll,
    overlayProps,
    hasDelayGroup,
    delay,
  };

  const renderFloatingContentSection = () => {
    if (children) {
      return skipMeta ? children : cloneElement(children, { meta });
    }

    return renderContentSection(skipMeta ? {} : { meta });
  };

  const renderOverlaySection = () => {
    if (hasOverlay) {
      return (
        <FloatingOverlay lockScroll={lockScroll} {...overlayProps}>
          {renderFloatingContentSection()}
        </FloatingOverlay>
      );
    }

    return null;
  };

  const renderDelayGroupSection = () => {
    if (hasDelayGroup) {
      return (
        <FloatingDelayGroup delay={delay}>
          {hasOverlay ? renderOverlaySection() : renderFloatingContentSection()}
        </FloatingDelayGroup>
      );
    }

    return null;
  };

  const renderSections = () => {
    if (hasDelayGroup) {
      return renderDelayGroupSection();
    }

    if (hasOverlay) {
      return renderOverlaySection();
    }

    return renderFloatingContentSection();
  };

  return (
    <FloatingPortal id={portalId} root={root}>
      {show && renderSections()}
    </FloatingPortal>
  );
};

FloatingPortalContainer.propTypes = {
  id: PropTypes.string,
  root: PropTypes.string,
  show: PropTypes.bool,
  nodeMeta: PropTypes.object,
  skipMeta: PropTypes.bool,
  hasOverlay: PropTypes.bool,
  lockScroll: PropTypes.bool,
  overlayProps: PropTypes.object,
  hasDelayGroup: PropTypes.bool,
  delay: PropTypes.object,
  renderContentSection: PropTypes.func,
  children: PropTypes.node,
};

export default FloatingPortalContainer;
