import { useEffect } from 'react';

import { isArray, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import FloatingContainer from '../floating/FloatingContainer';
import ItemsSelection from './ItemsSelection';
import SelectedItems from './SelectedItems';
import { SHARED_DEFAULT_PROPS, getSharedClassNames, sharedPropTypes } from './helper';

const DEFAULT_PROPS = {
  ...SHARED_DEFAULT_PROPS,
  onOpen: noop,
  onSelection: noop,
  renderSelectedItems: (props) => <SelectedItems {...props} />,
  renderItemsSelection: (props) => <ItemsSelection {...props} />,
  selectedItemsContainerClass: '',
  itemsSelectionContainerClass: '',
  selectedItemsContainerStyle: {},
  itemsSelectionContainerStyle: {},
  selectedItemsProps: {},
  itemsSelectionProps: {},
};

const DropDown = ({
  list = DEFAULT_PROPS.list,
  selectedList = DEFAULT_PROPS.selectedList,
  placeholderList = DEFAULT_PROPS.placeholderList,
  labelKey = DEFAULT_PROPS.labelKey,
  valueKey = DEFAULT_PROPS.valueKey,
  disabled = DEFAULT_PROPS.disabled,
  readOnly = DEFAULT_PROPS.readOnly,
  loading = DEFAULT_PROPS.loading,
  isMulti = DEFAULT_PROPS.isMulti,
  showDropDown = DEFAULT_PROPS.showDropDown,
  hasSearch = DEFAULT_PROPS.hasSearch,
  searchOnKeyPress = DEFAULT_PROPS.searchOnKeyPress,
  toggleSelected = DEFAULT_PROPS.toggleSelected,
  highlightSelected = DEFAULT_PROPS.highlightSelected,
  hasClearSelection = DEFAULT_PROPS.hasClearSelection,
  clearIcon = DEFAULT_PROPS.clearIcon,
  onOpen = DEFAULT_PROPS.onOpen,
  onSelection = DEFAULT_PROPS.onSelection,
  onClearSelection = DEFAULT_PROPS.onClearSelection,
  renderSelectedItems = DEFAULT_PROPS.renderSelectedItems,
  renderItemsSelection = DEFAULT_PROPS.renderItemsSelection,
  containerClass = DEFAULT_PROPS.containerClass,
  selectedItemsContainerClass = DEFAULT_PROPS.selectedItemsContainerClass,
  itemsSelectionContainerClass = DEFAULT_PROPS.itemsSelectionContainerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  selectedItemsContainerStyle = DEFAULT_PROPS.selectedItemsContainerStyle,
  itemsSelectionContainerStyle = DEFAULT_PROPS.itemsSelectionContainerStyle,
  selectedItemsProps = DEFAULT_PROPS.selectedItemsProps,
  itemsSelectionProps = DEFAULT_PROPS.itemsSelectionProps,
  info = DEFAULT_PROPS.info,
  floatingConfig = DEFAULT_PROPS.floatingConfig,
  ...rest
}) => {
  const {
    open: isActive,
    setOpen: setIsActive,
    ...floatingLayout
  } = useFloatingLayout({
    initialOpen: showDropDown,
    disabled: disabled,
    showOnHover: false,
    showOnClick: true,
    placement: 'bottom-start',
    flipConfig: { padding: 36 },
    sizeConfig: {
      padding: 36,
      apply(props) {
        const floatingEl = floatingLayout.refs.floating.current;

        if (floatingEl) {
          Object.assign(floatingEl.style, {
            maxHeight: `${props.availableHeight}px`,
          });
        }
      },
      ...floatingConfig,
    },
  });

  const { refs } = floatingLayout;

  useEffect(() => {
    if (isActive) {
      onOpen();
    }
  }, [isActive]);

  useEffect(() => {
    const floatingEl = floatingLayout?.refs?.floating?.current;

    if (isActive && floatingEl) {
      const selectedItem = floatingEl.getElementsByClassName('item selected')[0];

      if (selectedItem) {
        selectedItem?.scrollIntoView?.(false);
      }
    }
  }, [isActive, floatingLayout?.refs?.floating?.current]);

  useEffect(() => {
    if (isActive !== showDropDown) {
      setIsActive(showDropDown);
    }
  }, [showDropDown]);

  const getItemSelectedIndex = (data) => {
    return [...selectedList].map((item) => item[valueKey]).indexOf(data[valueKey]);
  };

  const isItemSelected = (data) => {
    const itemIndex = getItemSelectedIndex(data);

    return itemIndex !== -1;
  };

  const onItemClick = (data) => {
    let canHideOnSelection = true;

    if (isArray(data)) {
      canHideOnSelection = onSelection([...data]);
    } else {
      const isSelected = isItemSelected(data);

      if (isSelected) {
        if (isMulti) {
          const itemIndex = getItemSelectedIndex(data);

          selectedList.splice(itemIndex, 1);

          canHideOnSelection = onSelection([...selectedList]);
        } else {
          if (toggleSelected) {
            canHideOnSelection = onSelection([]);
          }
        }
      } else if (isMulti) {
        selectedList.push({ ...data });

        canHideOnSelection = onSelection([...selectedList]);
      } else {
        canHideOnSelection = onSelection([{ ...data }]);
      }
    }

    if (!isMulti && canHideOnSelection !== false) {
      setIsActive(false);
    }
  };

  const sharedClassNames = getSharedClassNames({
    disabled,
    readOnly,
    loading,
    isMulti,
    isActive,
    info,
  });

  const renderSelectedItemSection = () => {
    return (
      <div
        className={getFormattedClassName(
          `selected-items-container ${sharedClassNames} ${selectedItemsContainerClass}`,
        )}
        style={selectedItemsContainerStyle}
      >
        {renderSelectedItems({
          selectedList,
          placeholderList,
          labelKey,
          valueKey,
          disabled,
          readOnly,
          loading,
          isMulti,
          isActive,
          setIsActive,
          hasClearSelection,
          clearIcon,
          onSelection,
          onClearSelection,
          info,
          ...selectedItemsProps,
          ...rest,
        })}
      </div>
    );
  };

  const renderItemsSelectionSection = () => {
    return (
      <FloatingContainer
        show={isActive}
        containerClass={getFormattedClassName(
          `items-selection-container ${sharedClassNames} ${itemsSelectionContainerClass}`,
        )}
        containerStyle={{
          ...itemsSelectionContainerStyle,
        }}
        {...floatingLayout}
      >
        {renderItemsSelection({
          list,
          selectedList,
          placeholderList,
          labelKey,
          valueKey,
          disabled,
          readOnly,
          loading,
          isMulti,
          showDropDown,
          isActive,
          setIsActive,
          onItemClick,
          isItemSelected,
          hasSearch,
          searchOnKeyPress,
          highlightSelected,
          info,
          ...itemsSelectionProps,
          itemsStyle: {
            ...itemsSelectionProps.itemsStyle,
            minWidth: floatingLayout.refs.reference.current?.clientWidth,
            // height: dynamicPosition.height,
          },
          ...rest,
        })}
      </FloatingContainer>
    );
  };

  return (
    <div
      ref={refs.setReference}
      className={getFormattedClassName(`dropdown-container ${sharedClassNames} ${containerClass}`)}
      style={containerStyle}
    >
      {renderSelectedItemSection()}
      {renderItemsSelectionSection()}
    </div>
  );
};

DropDown.propTypes = {
  ...sharedPropTypes,
  onOpen: PropTypes.func,
  onSelection: PropTypes.func,
  renderSelectedItems: PropTypes.func,
  renderItemsSelection: PropTypes.func,
  selectedItemsContainerClass: PropTypes.string,
  itemsSelectionContainerClass: PropTypes.string,
  selectedItemsContainerStyle: PropTypes.object,
  itemsSelectionContainerStyle: PropTypes.object,
  selectedItemsProps: PropTypes.object,
  itemsSelectionProps: PropTypes.object,
};

export default DropDown;
