import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { filter, includes, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import LoadMore from '../buttons/LoadMore';
import Search from '../forms/Search';
import Spinner from '../spinner/Spinner';
import Items from './Items';
import { SHARED_DEFAULT_PROPS, sharedPropTypes } from './helper';

const DEFAULT_PROPS = {
  ...SHARED_DEFAULT_PROPS,
  isItemSelected: noop,
  itemsStyle: {},
  renderItem: (props) => <Items {...props} />,
  onItemClick: noop,
  hasCheckbox: false,
  showLoader: true,
  noItemText: 'NO_DATA_FOUND',
  noItemTextOnSearch: 'NO_DATA_FOUND',
};

const ItemsSelection = ({
  list = DEFAULT_PROPS.list,
  labelKey = DEFAULT_PROPS.labelKey,
  valueKey = DEFAULT_PROPS.valueKey,
  disabled = DEFAULT_PROPS.disabled,
  loading = DEFAULT_PROPS.loading,
  readOnly = DEFAULT_PROPS.readOnly,
  hasLeftIcon = DEFAULT_PROPS.hasLeftIcon,
  leftIcon = DEFAULT_PROPS.leftIcon,
  hasRightIcon = DEFAULT_PROPS.hasRightIcon,
  rightIcon = DEFAULT_PROPS.rightIcon,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  itemsStyle = DEFAULT_PROPS.itemsStyle,
  hasSearch = DEFAULT_PROPS.hasSearch,
  searchOnKeyPress = DEFAULT_PROPS.searchOnKeyPress,
  highlightSelected = DEFAULT_PROPS.highlightSelected,
  isItemSelected = DEFAULT_PROPS.isItemSelected,
  renderItem = DEFAULT_PROPS.renderItem,
  loadMoreDetail = DEFAULT_PROPS.loadMoreDetail,
  onItemClick = DEFAULT_PROPS.onItemClick,
  hasCheckbox = DEFAULT_PROPS.hasCheckbox,
  showLoader = DEFAULT_PROPS.showLoader,
  noItemText = DEFAULT_PROPS.noItemText,
  noItemTextOnSearch = DEFAULT_PROPS.noItemTextOnSearch,
  ...rest
}) => {
  const { t } = useTranslation();

  const [activeList, setActiveList] = useState(list);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    setActiveList(list);
  }, [list]);

  const onLoadMoreClick = async () => {
    await loadMoreDetail?.onLoadMoreClick?.({ term: searchTerm });
  };

  const onSearch = async (term) => {
    if (searchTerm !== term) {
      const currentList = await loadMoreDetail?.onLoadMoreClick?.({ term, fromStart: true });

      if (!currentList) {
        const searchList = filter(currentList || list, (detail) =>
          includes((detail[labelKey] + '').toLowerCase(), (term + '').toLowerCase()),
        );

        setActiveList(searchList);
      }
    }

    setSearchTerm(term);
  };

  const renderItemSection = (itemDetail) => {
    return (
      <>
        {renderItem({
          itemDetail,
          labelKey,
          valueKey,
          disabled,
          readOnly,
          hasLeftIcon,
          leftIcon,
          hasRightIcon,
          rightIcon,
          onClick: onItemClick,
          isItemSelected,
          highlightSelected,
          hasCheckbox,
          ...rest,
        })}
      </>
    );
  };

  const canShowSearch = hasSearch || searchTerm || activeList?.length > 7;

  const renderNoItemSection = () => {
    const show = activeList?.length === 0 && !loading;

    const text = canShowSearch && searchTerm ? noItemTextOnSearch : noItemText;

    if (show && text) {
      return <p className="no-item-section">{t(text)}</p>;
    }

    return null;
  };

  return (
    <div
      className={getFormattedClassName(
        `items-selection ${hasSearch ? 'has-search' : ''} ${containerClass}`,
      )}
      style={containerStyle}
    >
      {canShowSearch && (
        <div className="search-section">
          <Search onSearch={onSearch} term={searchTerm} searchOnKeyPress={searchOnKeyPress} />
        </div>
      )}

      <div
        className={getFormattedClassName(
          `items ${hasSearch ? 'has-search' : ''} ${loading ? 'loading' : ''}`,
        )}
        style={itemsStyle}
      >
        {activeList?.map((detail, index) => (
          <div className="item-container" key={`${detail[valueKey]}_${index}`}>
            {renderItemSection(detail)}
          </div>
        ))}

        {renderNoItemSection()}

        <LoadMore {...loadMoreDetail} onLoadMoreClick={onLoadMoreClick} />

        {!!loading && showLoader && <Spinner />}
      </div>
    </div>
  );
};

ItemsSelection.propTypes = {
  ...sharedPropTypes,
  itemsStyle: PropTypes.object,
  renderItem: PropTypes.func,
  onItemClick: PropTypes.func,
  hasCheckbox: PropTypes.bool,
  showLoader: PropTypes.bool,
  noItemTextOnSearch: PropTypes.string,
  noItemText: PropTypes.string,
};

export default ItemsSelection;
