import { isArray, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { faTimes } from '@fortawesome/pro-solid-svg-icons';

export const getSharedClassNames = ({
  disabled,
  readOnly,
  loading,
  isMulti,
  isActive,
  isSelected,
  info,
}) => {
  return `${disabled ? 'disabled' : ''}  ${readOnly ? 'readonly' : ''} ${
    loading ? 'loading' : ''
  } ${isMulti ? 'multi' : ''} ${isActive ? 'active' : ''} ${isSelected ? 'selected' : ''} ${
    info.type ? info.type : ''
  }`;
};

export const SHARED_DEFAULT_PROPS = {
  list: [],
  selectedList: [],
  placeholderList: [{ label: 'SELECT', value: 'SELECT' }],
  labelKey: 'label',
  valueKey: 'value',
  disabled: false,
  readOnly: false,
  loading: false,
  isMulti: false,
  showDropDown: false,
  hasSearch: false,
  searchOnKeyPress: false,
  toggleSelected: false,
  loadMoreDetail: { pageSize: 100, pageOffset: 0, totalRecord: -1, onLoadMoreClick: noop },
  highlightSelected: true,
  hasClearSelection: false,
  clearIcon: faTimes,
  onClearSelection: noop,
  info: { type: '' },
  containerClass: '',
  containerStyle: {},
  floatingConfig: {
    middleware: {},
  },
};

export const sharedPropTypes = {
  list: PropTypes.array,
  selectedList: PropTypes.array,
  placeholderList: PropTypes.array,
  labelKey: PropTypes.string,
  valueKey: PropTypes.string,
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  loading: PropTypes.bool,
  isMulti: PropTypes.bool,
  showDropDown: PropTypes.bool,
  hasSearch: PropTypes.bool,
  searchOnKeyPress: PropTypes.bool,
  toggleSelected: PropTypes.bool,
  loadMoreDetail: PropTypes.object,
  highlightSelected: PropTypes.bool,
  hasClearSelection: PropTypes.bool,
  clearIcon: PropTypes.any,
  onClearSelection: PropTypes.func,
  info: PropTypes.shape({
    type: PropTypes.oneOf(['', 'error']),
    message: PropTypes.string,
    context: PropTypes.string,
  }),
  onCancelClick: noop,
  onClearAllClick: noop,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

const defaultDropDownConfigMapping = {
  labelKey: 'name',
  valueKey: 'id',
  lite: true,
  list: [],
  getLabel: ({ detail, labelKey }) => (typeof detail === 'string' ? detail : detail[labelKey]),
  getValue: ({ detail, valueKey }) => (typeof detail === 'string' ? detail : detail[valueKey]),
};

export const getDropDownList = (config = {}) => {
  const { labelKey, valueKey, lite, list, getLabel, getValue } = {
    ...defaultDropDownConfigMapping,
    ...config,
  };

  if (isArray(list)) {
    return list?.map((detail) => {
      const mapping = {
        label: getLabel({ detail, labelKey }),
        value: getValue({ detail, valueKey }),
      };

      return lite
        ? { ...mapping }
        : {
            ...detail,
            ...mapping,
          };
    });
  }

  return [];
};
