import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { faCaretDown, faCaretUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import Button from '../buttons/Button';
import TextWithTooltip from '../tooltip/TextWithTooltip';
import { SHARED_DEFAULT_PROPS, getSharedClassNames, sharedPropTypes } from './helper';

export const getDDClass = ({ colorClass, bkgClass, kind }) => {
  let ddClass = { colorClass, bkgClass };

  if (kind === 'primary') {
    ddClass.colorClass = 'dd-selected-items-color-primary';
    ddClass.bkgClass = 'dd-selected-items-bkg-primary';
  }

  if (kind === 'secondary') {
    ddClass.colorClass = 'dd-selected-items-color-secondary';
    ddClass.bkgClass = 'dd-selected-items-bkg-secondary';
  }

  if (kind === 'tertiary') {
    ddClass.colorClass = 'dd-selected-items-color-tertiary';
    ddClass.bkgClass = 'dd-selected-items-bkg-tertiary';
  }

  return ddClass;
};

const defaultTooltipProps = {
  placement: 'top',
  flipConfig: true,
  shiftConfig: true,
  closeConfig: { handleClose: null },
};

const defaultSelectedItemsTextMapperFunc = ({ item, labelKey, t }) => {
  return t(item?.[labelKey]);
};

const DEFAULT_PROPS = {
  ...SHARED_DEFAULT_PROPS,
  hasTextIcon: false,
  textIcon: '',
  hasActiveIcon: true,
  activeIcon: faCaretUp,
  inActiveIcon: faCaretDown,
  colorClass: 'dd-selected-items-color-default',
  bkgClass: 'dd-selected-items-bkg-default',
  kind: 'default',
  renderSelectedItemsText: noop,
  selectedItemsTextMapperFunc: defaultSelectedItemsTextMapperFunc,
  selectedItemsTooltipProps: {
    ...defaultTooltipProps,
  },
};

const SelectedItems = ({
  selectedList = DEFAULT_PROPS.selectedList,
  placeholderList = DEFAULT_PROPS.placeholderList,
  labelKey = DEFAULT_PROPS.labelKey,
  disabled = DEFAULT_PROPS.disabled,
  readOnly = DEFAULT_PROPS.readOnly,
  loading = DEFAULT_PROPS.loading,
  isMulti = DEFAULT_PROPS.isMulti,
  isActive = DEFAULT_PROPS.isActive,
  setIsActive = DEFAULT_PROPS.setIsActive,
  hasTextIcon = DEFAULT_PROPS.hasTextIcon,
  textIcon = DEFAULT_PROPS.textIcon,
  hasActiveIcon = DEFAULT_PROPS.hasActiveIcon,
  activeIcon = DEFAULT_PROPS.activeIcon,
  inActiveIcon = DEFAULT_PROPS.inActiveIcon,
  hasClearSelection = DEFAULT_PROPS.hasClearSelection,
  clearIcon = DEFAULT_PROPS.clearIcon,
  onSelection = DEFAULT_PROPS.onSelection,
  onClearSelection = DEFAULT_PROPS.onClearSelection,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  colorClass = DEFAULT_PROPS.colorClass,
  bkgClass = DEFAULT_PROPS.bkgClass,
  kind = DEFAULT_PROPS.kind,
  info = DEFAULT_PROPS.info,
  selectedItemsTooltipProps = DEFAULT_PROPS.selectedItemsTooltipProps,
  renderSelectedItemsText = DEFAULT_PROPS.renderSelectedItemsText,
  selectedItemsTextMapperFunc = DEFAULT_PROPS.selectedItemsTextMapperFunc,
}) => {
  const { t } = useTranslation();

  const ddClass = getDDClass({ colorClass, bkgClass, kind });

  const sharedClassNames = getSharedClassNames({
    disabled,
    readOnly,
    loading,
    isMulti,
    isActive,
    info,
  });

  const isSelectedListEmpty = selectedList?.length === 0;

  const getItemText = useCallback(() => {
    const content = renderSelectedItemsText({
      labelKey,
      t,
      selectedList,
      placeholderList,
      isSelectedListEmpty,
      disabled,
      readOnly,
      loading,
      isMulti,
      isActive,
      setIsActive,
      hasTextIcon,
      textIcon,
      hasActiveIcon,
      activeIcon,
      inActiveIcon,
      containerClass,
      containerStyle,
      colorClass,
      bkgClass,
      kind,
      info,
    });

    if (content) {
      return content;
    }

    return (isSelectedListEmpty ? placeholderList : selectedList)
      ?.map((item) =>
        selectedItemsTextMapperFunc({
          item,
          labelKey,
          t,
          selectedList,
          placeholderList,
          isSelectedListEmpty,
          disabled,
          readOnly,
          loading,
          isMulti,
          isActive,
          setIsActive,
          hasTextIcon,
          textIcon,
          hasActiveIcon,
          activeIcon,
          inActiveIcon,
          containerClass,
          containerStyle,
          colorClass,
          bkgClass,
          kind,
          info,
        }),
      )
      .join(', ');
  }, [isSelectedListEmpty, selectedList]);

  const onClick = () => {
    if (!disabled) {
      setIsActive((prevState) => !prevState);
    }
  };

  const onClearClick = (evt) => {
    evt.stopPropagation(); // Prevent dropdown from opening/closing
    if (!disabled && !readOnly) {
      onClearSelection();
      onSelection([]);
    }
  };

  const shouldShowClearButton = hasClearSelection && !isSelectedListEmpty && !disabled && !readOnly;

  return (
    <div
      className={getFormattedClassName(
        `selected-items ${sharedClassNames} ${ddClass.colorClass} ${ddClass.bkgClass} ${containerClass}`,
      )}
      style={containerStyle}
      onClick={onClick}
    >
      <div className="left-content">
        {hasTextIcon && <FontAwesomeIcon className="icon left" icon={textIcon} />}
        <TextWithTooltip {...defaultTooltipProps} {...selectedItemsTooltipProps}>
          <span className="items">{getItemText()} </span>
        </TextWithTooltip>
      </div>
      <div className="right-content">
        {shouldShowClearButton && (
          <Button
            type="tertiary"
            containerClass="no-p-l no-p-r content-width clear-button"
            onClick={onClearClick}
          >
            <FontAwesomeIcon className="icon clear" icon={clearIcon} />
          </Button>
        )}
        {hasActiveIcon && (
          <FontAwesomeIcon className="icon right" icon={isActive ? activeIcon : inActiveIcon} />
        )}
      </div>
    </div>
  );
};

SelectedItems.propTypes = {
  ...sharedPropTypes,
  hasTextIcon: PropTypes.bool,
  textIcon: PropTypes.any,
  hasActiveIcon: PropTypes.bool,
  activeIcon: PropTypes.any,
  inActiveIcon: PropTypes.any,
  hasClearSelection: PropTypes.bool,
  clearIcon: PropTypes.any,
  onSelection: PropTypes.func,
  onClearSelection: PropTypes.func,
  colorClass: PropTypes.string,
  bkgClass: PropTypes.string,
  kind: PropTypes.oneOf(['default', 'primary', 'secondary', 'tertiary']),
  selectedItemsTooltipProps: PropTypes.object,
};

export default SelectedItems;
