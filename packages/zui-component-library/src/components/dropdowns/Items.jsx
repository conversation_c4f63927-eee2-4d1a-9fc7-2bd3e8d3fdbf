import { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';

import { faSquare } from '@fortawesome/pro-light-svg-icons';
import { faCheckSquare } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import TextWithTooltip from '../tooltip/TextWithTooltip';
import { SHARED_DEFAULT_PROPS, getSharedClassNames, sharedPropTypes } from './helper';

const defaultTooltipProps = {
  placement: 'top',
  flipConfig: true,
  shiftConfig: true,
  closeConfig: { handleClose: null },
};

const DEFAULT_PROPS = {
  ...SHARED_DEFAULT_PROPS,
  containerRef: null,
  itemDetail: {},
  onClick: noop,
  isItemSelected: noop,
  hasCheckbox: false,
  itemTooltipProps: {
    ...defaultTooltipProps,
  },
};

const Items = ({
  itemDetail = DEFAULT_PROPS.itemDetail,
  labelKey = DEFAULT_PROPS.labelKey,
  disabled = DEFAULT_PROPS.disabled,
  readOnly = DEFAULT_PROPS.readOnly,
  hasLeftIcon = DEFAULT_PROPS.hasLeftIcon,
  leftIcon = DEFAULT_PROPS.leftIcon,
  hasRightIcon = DEFAULT_PROPS.hasRightIcon,
  rightIcon = DEFAULT_PROPS.rightIcon,
  onClick = DEFAULT_PROPS.onClick,
  isItemSelected = DEFAULT_PROPS.isItemSelected,
  highlightSelected = DEFAULT_PROPS.highlightSelected,
  hasCheckbox = DEFAULT_PROPS.hasCheckbox,
  containerRef = DEFAULT_PROPS.containerRef,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  info = DEFAULT_PROPS.info,
  itemTooltipProps = DEFAULT_PROPS.itemTooltipProps,
  leftSectionClass = DEFAULT_PROPS.leftSectionClass,
  leftSectionStyle = DEFAULT_PROPS.rightSectionStyle,
  checkboxClass = DEFAULT_PROPS.checkboxClass,
  checkboxStyle = DEFAULT_PROPS.checkboxStyle,
  leftIconClass = DEFAULT_PROPS.leftIconClass,
  leftIconStyle = DEFAULT_PROPS.leftIconStyle,
  rightSectionClass = DEFAULT_PROPS.rightSectionClass,
  rightSectionStyle = DEFAULT_PROPS.rightSectionStyle,
  rightIconClass = DEFAULT_PROPS.rightIconClass,
  rightIconStyle = DEFAULT_PROPS.rightIconStyle,
  textIconClass = DEFAULT_PROPS.textIconClass,
  textIconStyle = DEFAULT_PROPS.textIconStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const isSelected = isItemSelected(itemDetail);

  const sharedClassNames = highlightSelected
    ? getSharedClassNames({ disabled, readOnly, isSelected, info })
    : getSharedClassNames({ disabled, readOnly, info });

  return (
    <div
      ref={containerRef}
      className={getFormattedClassName(`item ${sharedClassNames} ${containerClass}`)}
      style={containerStyle}
      onClick={() => {
        onClick(itemDetail);
      }}
    >
      <div
        className={`section with-text ${leftSectionClass} ${
          hasRightIcon ? 'has-right-section' : ''
        }`}
        style={leftSectionStyle}
      >
        {hasCheckbox && (
          <FontAwesomeIcon
            icon={isSelected ? faCheckSquare : faSquare}
            className={`checkbox icon left ${checkboxClass}`}
            style={checkboxStyle}
          />
        )}

        {hasLeftIcon && (
          <FontAwesomeIcon
            icon={leftIcon}
            className={`icon left ${leftIconClass}`}
            style={leftIconStyle}
          />
        )}

        {children ? (
          children
        ) : (
          <TextWithTooltip {...defaultTooltipProps} {...itemTooltipProps}>
            <span className={`text ${textIconClass}`} style={textIconStyle}>
              {t(itemDetail?.[labelKey])}{' '}
            </span>
          </TextWithTooltip>
        )}
      </div>

      {hasRightIcon && (
        <div className={`section ${rightSectionClass}`} style={rightSectionStyle}>
          <FontAwesomeIcon
            icon={rightIcon}
            className={`icon ${rightIconClass}`}
            style={rightIconStyle}
          />
        </div>
      )}
    </div>
  );
};

Items.propTypes = {
  ...sharedPropTypes,
  itemDetail: PropTypes.object,
  onClick: PropTypes.func,
  hasCheckbox: PropTypes.bool,
  itemTooltipProps: PropTypes.object,
};

const ItemsForwardRef = (props, ref) => <Items {...props} containerRef={ref} />;

export default forwardRef(ItemsForwardRef);
