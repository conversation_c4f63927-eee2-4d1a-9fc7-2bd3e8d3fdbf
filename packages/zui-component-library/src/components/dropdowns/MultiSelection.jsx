import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';
import Spinner from '../spinner/Spinner';
import ItemsSelection from './ItemsSelection';
import { SHARED_DEFAULT_PROPS, sharedPropTypes } from './helper';

const DEFAULT_PROPS = {
  ...SHARED_DEFAULT_PROPS,
  unselectedTitle: 'Unselected List',
  selectedTitle: 'Selected List',
  isItemSelected: noop,
  onItemClick: noop,
};

const MultiSelection = ({
  list = DEFAULT_PROPS.list,
  selectedList = DEFAULT_PROPS.selectedList,
  valueKey = DEFAULT_PROPS.valueKey,
  isMulti = DEFAULT_PROPS.isMulti,
  onItemClick = DEFAULT_PROPS.onItemClick,
  setIsActive = DEFAULT_PROPS.setIsActive,
  unselectedTitle = DEFAULT_PROPS.unselectedTitle,
  selectedTitle = DEFAULT_PROPS.selectedTitle,
  ...props
}) => {
  const elementRef = useRef();

  const { t } = useTranslation();

  // const {
  //   list,
  //   selectedList,
  //   valueKey,
  //   isMulti,
  //   onItemClick,
  //   setIsActive,
  //   unselectedTitle,
  //   selectedTitle,
  // } = props;

  const initialSelection = useRef([...selectedList]);

  const [activeList, setActiveList] = useState([...list]);
  const [activeSelectedList, setActiveSelectedList] = useState([...selectedList]);

  const onListUpdate = () => {
    setActiveList([...list]);
    setActiveSelectedList([...selectedList]);
  };

  useEffect(() => {
    onListUpdate();
  }, [list, selectedList]);

  const getItemSelectedIndex = (data) => {
    return [...activeSelectedList].map((item) => item[valueKey]).indexOf(data[valueKey]);
  };

  const isItemSelected = (data) => {
    const itemIndex = getItemSelectedIndex(data);

    return itemIndex !== -1;
  };

  const onItemSelection = (data) => {
    const isSelected = isItemSelected(data);

    let newList = [];

    if (isSelected) {
      if (isMulti) {
        const itemIndex = getItemSelectedIndex(data);

        activeSelectedList.splice(itemIndex, 1);

        newList = [...activeSelectedList];
      } else {
        newList = [];
      }
    } else if (isMulti) {
      activeSelectedList.push({ ...data });

      newList = [...activeSelectedList];
    } else {
      newList = [{ ...data }];
    }

    setActiveSelectedList(newList);
    onItemClick(newList);
  };

  const onToggleAll = (isSelectAll) => {
    if (isSelectAll) {
      setActiveSelectedList([...list]);
    } else {
      setActiveSelectedList([]);
    }
  };

  const onCancelClick = () => {
    onItemClick(initialSelection.current);

    setIsActive(false);

    props?.onCancelClick?.();
  };

  const onClearAllClick = () => {
    setActiveSelectedList([]);
    onItemClick([]);

    props?.onClearAllClick?.();
  };

  return (
    <div ref={elementRef} className="multi-selection-container">
      {!!props.loading && <Spinner />}

      <div className="selections-options">
        <div className="option">
          <div className="head-section unselected">
            <span className="title">{t(unselectedTitle)}</span>
          </div>
          <ItemsSelection
            showLoader={false}
            {...{ ...props, ...{ itemsStyle: { ...props.itemsStyle, minWidth: 0 } } }}
            isItemSelected={isItemSelected}
            onItemClick={onItemSelection}
            onToggleAll={onToggleAll}
            hasCheckbox
            highlightSelected={false}
            list={activeList}
            selectedList={activeSelectedList}
          />
        </div>
        <div className="option">
          <div className="head-section selected">
            <span className="title">
              {t(selectedTitle)} ({activeSelectedList.length})
            </span>
          </div>
          <ItemsSelection
            showLoader={false}
            {...{ ...props, ...{ itemsStyle: { ...props.itemsStyle, minWidth: 0 } } }}
            onItemClick={onItemSelection}
            highlightSelected={false}
            hasRightIcon
            rightIcon={faTimes}
            list={activeSelectedList}
            selectedList={[]}
            loadMoreDetail={{}}
          />
        </div>
      </div>
      <div className="actions-container">
        <div className="section">
          <Button containerClass="content-width" type="tertiary" onClick={onCancelClick}>
            <span>{t('CANCEL')}</span>
          </Button>
        </div>
        <div className="section">
          <Button containerClass="content-width" type="tertiary" onClick={onClearAllClick}>
            <span>{t('CLEAR_ALL')}</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

MultiSelection.propTypes = {
  ...sharedPropTypes,
  renderItem: PropTypes.func,
  onItemClick: PropTypes.func,
};

export default MultiSelection;
