import { useState } from 'react';

import { faGlobeAmericas } from '@fortawesome/pro-solid-svg-icons';

import { changeLanguage } from 'i18next';
import { find, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import DropDown from './DropDown';

const DROPDOWN_LANG_DATA = [
  { value: 'en-US', label: 'English (US)' },
  { value: 'es-ES', label: 'Español' },
  { value: 'fr-FR', label: 'Français' },
  { value: 'de-DE', label: 'Deutsch' },
  { value: 'zh-CN', label: '中文' },
  { value: 'ja-JP', label: '日本語' },
];

const defaultLocal = 'en-US';

const setSelectedLocale = ({ newLocale, keyName }) => {
  localStorage.setItem(keyName, newLocale || 'en-US');
};

const DEFAULT_PROPS = {
  hasIcon: true,
  icon: faGlobeAmericas,
  list: DROPDOWN_LANG_DATA,
  selectedList: [],
  onSelection: noop,
  storageKeyName: 'locale',
  containerClass: '',
  selectedItemsProps: {},
};

const LanguageSelector = ({
  hasIcon = DEFAULT_PROPS.hasIcon,
  icon = DEFAULT_PROPS.icon,
  list = DEFAULT_PROPS.list,
  selectedList = DEFAULT_PROPS.selectedList,
  onSelection = DEFAULT_PROPS.onSelection,
  storageKeyName = DEFAULT_PROPS.storageKeyName,
  selectedItemsProps = DEFAULT_PROPS.selectedItemsProps,
  ...props
}) => {
  const [locale, setLocale] = useState(() => {
    if (selectedList.length === 0) {
      const selectedLocal = localStorage.getItem(storageKeyName);

      const selectedLang = find(DROPDOWN_LANG_DATA, { value: selectedLocal || defaultLocal });

      return [selectedLang];
    }

    return selectedList;
  });

  const onLangSelection = (detail) => {
    const newLanguage = detail[0].value;

    onSelection(detail);

    setLocale(detail);

    setSelectedLocale({ newLocale: newLanguage, keyName: storageKeyName });

    try {
      changeLanguage(newLanguage);
    } catch (ex) {
      // incorrect localization setup
    }
  };

  return (
    <DropDown
      list={list}
      onSelection={onLangSelection}
      selectedList={locale}
      selectedItemsProps={{
        hasTextIcon: hasIcon,
        textIcon: icon,
        ...selectedItemsProps,
      }}
      {...props}
    />
  );
};

LanguageSelector.propTypes = {
  hasIcon: PropTypes.bool,
  icon: PropTypes.any,
  list: PropTypes.array,
  selectedList: PropTypes.array,
  onSelection: PropTypes.func,
  storageKeyName: PropTypes.string,
  containerClass: PropTypes.string,
  selectedItemsProps: PropTypes.object,
  listContainerStyle: PropTypes.object,
};

export default LanguageSelector;
