import { useEffect, useState } from 'react';

import {
  faCheck,
  faExclamationCircle,
  faExclamationTriangle,
  faQuestion,
  faTimes,
} from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { isNumber, noop } from 'lodash-es';
import PropTypes from 'prop-types';

const getClassName = ({ type, containerClass }) => {
  let className = 'toast is-flex jc-sb';

  if (type === 'success') {
    className += ' success';
  } else if (type === 'warning') {
    className += ' warning';
  } else if (type === 'error') {
    className += ' error';
  } else {
    className += ' help';
  }

  className = className.trim();

  className += ' ' + (containerClass + '').trim();

  return className.trim();
};

const getIcon = (type) => {
  let icon = faQuestion;

  if (type === 'success') {
    icon = faCheck;
  }

  if (type === 'warning') {
    icon = faExclamationTriangle;
  }

  if (type === 'error') {
    icon = faExclamationCircle;
  }

  return icon;
};

const DEFAULT_PROPS = {
  type: 'help',
  containerClass: '',
  containerStyle: {},
  onClose: noop,
  hideOnClick: true,
  autoHide: true,
  delay: 3000,
  onAutoHide: noop,
  children: null,
};

const Toast = ({
  type = DEFAULT_PROPS.type,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  onClose = DEFAULT_PROPS.onClose,
  hideOnClick = DEFAULT_PROPS.hideOnClick,
  autoHide = DEFAULT_PROPS.autoHide,
  delay = DEFAULT_PROPS.delay,
  onAutoHide = DEFAULT_PROPS.onAutoHide,
  children = DEFAULT_PROPS.children,
}) => {
  const className = getClassName({ type, containerClass });

  const [show, setShow] = useState(!!children);

  const onClick = () => {
    if (hideOnClick) {
      setShow(false);
    }

    onClose();
  };

  useEffect(() => {
    const timeoutAfter = isNumber(delay) ? delay : 5000;

    const timeoutId = setTimeout(() => {
      if (autoHide) {
        setShow(false);

        onAutoHide();
      }
    }, timeoutAfter);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [autoHide, delay]);

  if (show) {
    return (
      <div className={className} style={containerStyle}>
        <div className="icon-container is-flex has-jc-c has-ai-c">
          <span>
            <FontAwesomeIcon icon={getIcon(type)} />
          </span>
        </div>
        <div className="text-container is-flex has-fd-c has-ai-c">{children}</div>
        <div className="action-container is-flex has-jc-c has-ai-c" onClick={onClick}>
          <span>
            <FontAwesomeIcon icon={faTimes} />
          </span>
        </div>
      </div>
    );
  }

  return null;
};

Toast.propTypes = {
  type: PropTypes.oneOf(['help', 'success', 'warning', 'error']),
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  onClose: PropTypes.func,
  hideOnClick: PropTypes.bool,
  autoHide: PropTypes.bool,
  delay: PropTypes.number,
  onAutoHide: PropTypes.func,
  children: PropTypes.any,
};

export default Toast;
