import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Toast from './Toast';
import ToastMessage from './ToastMessage';
import ToastPortal from './ToastPortal';

const DEFAULT_PROPS = {
  list: [],
  onClose: noop,
};

const ToastContainer = ({ list = DEFAULT_PROPS.list, onClose = DEFAULT_PROPS.onClose }) => {
  const onHideClick = (notificationId) => {
    onClose(notificationId);
  };

  const hasToasts = list.length > 0;

  if (hasToasts) {
    return (
      <ToastPortal show={true}>
        {list?.map(
          ({
            notificationId,
            message,
            type,
            charLimit,
            translationMapping,
            toastMessageContainerClass,
            toastMessageContainerStyle,
            ...rest
          }) => (
            <Toast
              key={notificationId}
              type={type}
              onClose={() => {
                onHideClick(notificationId);
              }}
              onAutoHide={() => {
                onHideClick(notificationId);
              }}
              {...rest}
            >
              <ToastMessage
                message={message}
                charLimit={charLimit}
                translationMapping={translationMapping}
                containerClass={toastMessageContainerClass}
                containerStyle={toastMessageContainerStyle}
              />
            </Toast>
          ),
        )}
      </ToastPortal>
    );
  }

  return null;
};

ToastContainer.propTypes = {
  list: PropTypes.array,
  onClose: PropTypes.func,
};

export default ToastContainer;
