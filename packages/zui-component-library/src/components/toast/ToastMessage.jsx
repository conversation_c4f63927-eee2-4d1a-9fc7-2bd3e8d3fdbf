import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  message: '',
  charLimit: 160,
  translationMapping: {},
  containerClass: '',
  containerStyle: {},
};

const ToastMessage = ({
  message = DEFAULT_PROPS.message,
  charLimit = DEFAULT_PROPS.charLimit,
  translationMapping = DEFAULT_PROPS.translationMapping,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  let toastMessage = '';

  if (message) {
    if (Array.isArray(message)) {
      const hasSingleData = message.length === 1;

      toastMessage = message.map((msg) => (
        <p
          key={msg}
          className={getFormattedClassName(
            `text-content ${hasSingleData ? '' : 'has-as-s'} ${containerClass}`,
          )}
          style={containerStyle}
        >
          {msg.substring(0, charLimit)} {msg.length > charLimit ? '...' : ''}
        </p>
      ));
    } else if (typeof message === 'string') {
      toastMessage = (
        <p
          key={message}
          className={getFormattedClassName(`text-content has-as-s ${containerClass}`)}
          style={containerStyle}
        >
          {`${t(message.substring(0, charLimit), translationMapping)} ${
            message.length > charLimit ? '...' : ''
          }`}
        </p>
      );
    }
  }

  return toastMessage;
};

ToastMessage.propTypes = {
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  charLimit: PropTypes.number,
  translationMapping: PropTypes.object,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default ToastMessage;
