import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getToastPortalRootId } from '../../config/floating';

import { getFormattedClassName } from '../../utils/dom';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import FloatingPortalContainer from '../floating/FloatingPortalContainer';

const DEFAULT_PROPS = {
  modalRootId: '',
  show: false,
  onEscape: noop,
  containerClass: '',
  children: null,
};

const ToastPortal = ({
  modalRootId = DEFAULT_PROPS.modalRootId,
  show = DEFAULT_PROPS.show,
  containerClass = DEFAULT_PROPS.containerClass,
  children = DEFAULT_PROPS.children,
}) => {
  const rootId = modalRootId || getToastPortalRootId();

  const { open: isOpen, ...floatingLayout } = useFloatingLayout({
    initialOpen: show,
    showOnHover: false,
    showOnClick: true,
    dismissConfig: { outsidePress: false },
    sizeConfig: {
      padding: 36,
      apply() {},
    },
  });

  const { refs, getReferenceProps, getFloatingProps } = floatingLayout;

  return (
    <>
      <div ref={refs.setReference} className="toast-content-container" {...getReferenceProps()} />

      <FloatingPortalContainer id={rootId} show={isOpen} skipMeta>
        <article
          className={getFormattedClassName(`toast-container ${containerClass}`)}
          ref={refs.setFloating}
          {...getFloatingProps()}
        >
          <div className="toast-content-container">{children}</div>
        </article>
      </FloatingPortalContainer>
    </>
  );
};

ToastPortal.propTypes = {
  modalRootId: PropTypes.string,
  show: PropTypes.bool,
  onEscape: PropTypes.func,
  containerClass: PropTypes.string,
  children: PropTypes.any,
};

export default ToastPortal;
