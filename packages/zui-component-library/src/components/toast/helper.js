import { getFormattedClassName } from '../../utils/dom';

export const renderNotificationMessage = ({ message, charLimit = 160 }) => {
  let errorMessage = '';

  if (message) {
    if (Array.isArray(message)) {
      const hasSingleData = message.length === 1;

      errorMessage = message.map((error) => (
        <p
          key={error}
          className={getFormattedClassName(`text-content ${hasSingleData ? '' : 'has-as-s'}`)}
        >
          {error.substring(0, charLimit)} {error.length > charLimit ? '...' : ''}
        </p>
      ));
    } else if (typeof message === 'string') {
      errorMessage = `${message.substring(0, charLimit)} ${
        message.length > charLimit ? '...' : ''
      }`;
    }
  }

  return errorMessage;
};
