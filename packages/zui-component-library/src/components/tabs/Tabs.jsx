import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  type: 'horizontal',
  containerClass: '',
  containerStyle: {},
  children: null,
};

const Tabs = ({
  type = DEFAULT_PROPS.type,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  return (
    <div
      className={getFormattedClassName(`tabs-container ${containerClass} ${type}`)}
      style={containerStyle}
    >
      {children}
    </div>
  );
};

Tabs.propTypes = {
  type: PropTypes.oneOf(['horizontal', 'vertical']),
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default Tabs;
