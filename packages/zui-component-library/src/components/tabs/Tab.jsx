import { useTranslation } from 'react-i18next';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  layer: 'first',
  label: '',
  hasIcon: false,
  icon: '',
  iconPosition: 'left',
  iconProps: {},
  isActive: false,
  onClick: noop,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const Tab = ({
  layer = DEFAULT_PROPS.layer,
  label = DEFAULT_PROPS.label,
  hasIcon = DEFAULT_PROPS.hasIcon,
  icon = DEFAULT_PROPS.icon,
  iconPosition = DEFAULT_PROPS.iconPosition,
  iconProps = DEFAULT_PROPS.iconProps,
  isActive = DEFAULT_PROPS.isActive,
  onClick = DEFAULT_PROPS.onClick,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const hasIconLeft = hasIcon && iconPosition === 'left';
  const hasIconRight = hasIcon && iconPosition === 'right';

  if (children) {
    return children;
  }

  return (
    <div
      onClick={onClick}
      className={getFormattedClassName(
        `tab ${layer}-layer ${isActive ? 'active' : ''} ${containerClass}`,
      )}
      style={containerStyle}
    >
      {hasIconLeft && <FontAwesomeIcon icon={icon} {...iconProps} />}
      {t(label)}
      {hasIconRight && <FontAwesomeIcon icon={icon} {...iconProps} />}
    </div>
  );
};

Tab.propTypes = {
  layer: PropTypes.oneOf(['first', 'second', 'third', 'card']),
  label: PropTypes.string,
  hasIcon: PropTypes.bool,
  icon: PropTypes.any,
  iconPosition: PropTypes.string,
  iconProps: PropTypes.object,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default Tab;
