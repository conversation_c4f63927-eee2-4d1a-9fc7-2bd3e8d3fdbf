import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faCopy } from '@fortawesome/pro-regular-svg-icons';
import { faEye, faEyeSlash } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getPasswordValidityDetail } from '../../utils/validations';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import Button from '../buttons/Button';
import Card from '../cards/Card';
import FloatingContainer from '../floating/FloatingContainer';
import ClipboardCopy from './ClipboardCopy';
import Input from './Input';
import InputAction from './InputAction';
import ValidationInfo from './ValidationInfo';

const calculateOffset = ({ placement }) => (placement.includes('right') ? 0 : 10);

const DEFAULT_PROPS = {
  value: '',
  type: 'password',
  canCopy: false,
  showInputTypeToggle: true,
  showPasswordValidation: false,
  onValidationChange: noop,
  showCopiedPassword: false,
  passwordConfig: {
    minLength: 8,
    minLowerCase: 1,
    minUpperCase: 1,
    minNumeric: 1,
    minSpecialChar: 1,
  },
};

const PasswordInput = ({
  value = DEFAULT_PROPS.value,
  type = DEFAULT_PROPS.type,
  canCopy = DEFAULT_PROPS.canCopy,
  showInputTypeToggle = DEFAULT_PROPS.showInputTypeToggle,
  showPasswordValidation = DEFAULT_PROPS.showPasswordValidation,
  onValidationChange = DEFAULT_PROPS.onValidationChange,
  showCopiedPassword = DEFAULT_PROPS.showCopiedPassword,
  passwordConfig = DEFAULT_PROPS.passwordConfig,
  ...props
}) => {
  const [validationDetail, setValidationDetail] = useState({});

  const {
    open: isOpen,
    setOpen: setIsOpen,
    ...floatingLayout
  } = useFloatingLayout({
    placement: 'right-start',
    offsetConfig: calculateOffset,
    flipConfig: { padding: 36 },
  });

  const { t } = useTranslation();

  const { refs } = floatingLayout;

  const [inputType, setInputType] = useState(type);

  useEffect(() => {
    setInputType(type);
  }, [type]);

  const isInputTypePassword = inputType === 'password';

  useEffect(() => {
    const { validationDetail, isValid } = getPasswordValidityDetail(value, passwordConfig);

    onValidationChange?.({ validationDetail, isValid });

    setValidationDetail((prevState) => ({ ...prevState, ...validationDetail }));
  }, [value, passwordConfig]);

  const onToggleInputType = (evt) => {
    // prevent form submit
    evt?.preventDefault?.();

    setInputType(isInputTypePassword ? 'text' : 'password');
  };

  const onCopyPassword = (evt) => {
    // prevent form submit
    evt?.preventDefault?.();
  };

  const suffixSection = (
    <InputAction position="right">
      {showInputTypeToggle && value && (
        <Button
          type="tertiary"
          containerClass="no-p-l no-p-r content-width"
          onClick={onToggleInputType}
        >
          <FontAwesomeIcon icon={isInputTypePassword ? faEye : faEyeSlash} className="icon left" />
        </Button>
      )}

      {canCopy && value && (
        <ClipboardCopy
          text={value}
          showCopied={showCopiedPassword}
          placeholder={t(props.label) || 'Password'}
        >
          <Button
            type="tertiary"
            containerClass="no-p-l no-p-r content-width"
            onClick={onCopyPassword}
          >
            <FontAwesomeIcon icon={faCopy} className="icon left" />
          </Button>
        </ClipboardCopy>
      )}
    </InputAction>
  );

  const inputSection = () => {
    let inputProps = {
      value: value,
      type: inputType,
      suffixSection: suffixSection,
      ...props,
    };

    if (showPasswordValidation) {
      inputProps = {
        ...inputProps,
        onFocus: () => setIsOpen(true),
        onBlur: () => setIsOpen(false),
        ref: refs.setReference,
      };
    }

    return <Input {...inputProps} />;
  };

  return (
    <>
      {inputSection()}

      <FloatingContainer containerClass="requirements" show={isOpen} hasArrow {...floatingLayout}>
        <Card containerClass="validation-container">
          <ValidationInfo validationDetail={validationDetail} keyName="minLength" />
          <ValidationInfo validationDetail={validationDetail} keyName="minLowerCase" />
          <ValidationInfo validationDetail={validationDetail} keyName="minUpperCase" />
          <ValidationInfo validationDetail={validationDetail} keyName="minNumeric" />
          <ValidationInfo validationDetail={validationDetail} keyName="minSpecialChar" />
        </Card>
      </FloatingContainer>
    </>
  );
};

PasswordInput.propTypes = {
  value: PropTypes.string,
  type: PropTypes.string,
  canCopy: PropTypes.bool,
  showInputTypeToggle: PropTypes.bool,
  showPasswordValidation: PropTypes.bool,
  showCopiedPassword: PropTypes.bool,
  onValidationChange: PropTypes.func,
  passwordConfig: PropTypes.object,
};

export default PasswordInput;
