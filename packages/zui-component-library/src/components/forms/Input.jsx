import { forwardRef, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { isFinite, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import Field from './Field';
import { getInfoDetail } from './helper';

const DEFAULT_PROPS = {
  label: '',
  name: '',
  containerStyle: {},
  containerClass: '',
  inputClassName: '',
  placeholder: 'ENTER_TEXT',
  info: { type: '', message: '', context: '' },
  tooltip: {},
  showInfoOnChange: false,
  type: 'text',
  onChange: noop,
  onBlur: noop,
  value: '',
  prefixSection: null,
  suffixSection: null,
  containerRef: {},
  inputRef: null,
};

const Input = ({
  label = DEFAULT_PROPS.label,
  name = DEFAULT_PROPS.name,
  containerStyle = DEFAULT_PROPS.containerStyle,
  containerClass = DEFAULT_PROPS.containerClass,
  inputClassName = DEFAULT_PROPS.inputClassName,
  placeholder = DEFAULT_PROPS.placeholder,
  info = DEFAULT_PROPS.info,
  tooltip = DEFAULT_PROPS.tooltip,
  showInfoOnChange = DEFAULT_PROPS.showInfoOnChange,
  type = DEFAULT_PROPS.type,
  onChange = DEFAULT_PROPS.onChange,
  onBlur = DEFAULT_PROPS.onBlur,
  value = DEFAULT_PROPS.value,
  prefixSection = DEFAULT_PROPS.prefixSection,
  suffixSection = DEFAULT_PROPS.suffixSection,
  containerRef = DEFAULT_PROPS.containerRef,
  inputRef = DEFAULT_PROPS.inputRef,
  ...props
}) => {
  const { t } = useTranslation();

  const inputContainerRef = useRef();

  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    if (inputContainerRef.current && (prefixSection || suffixSection)) {
      const inputElement = inputContainerRef.current.getElementsByClassName('input')[0];
      const prefixElement = inputContainerRef.current.getElementsByClassName(
        'input-action-container left',
      )[0];
      const suffixElement = inputContainerRef.current.getElementsByClassName(
        'input-action-container right',
      )[0];

      if (inputElement && prefixElement && isFinite(prefixElement.offsetWidth)) {
        inputElement.style.paddingLeft = `${prefixElement.offsetWidth}px`;
      }

      if (inputElement && suffixElement && isFinite(suffixElement.offsetWidth)) {
        inputElement.style.paddingRight = `${suffixElement.offsetWidth + 7}px`;
      }
    }
  }, [inputContainerRef.current, prefixSection, suffixSection]);

  const onChangeEvt = (evt) => {
    if (showInfoOnChange) {
      if (!isDirty) {
        setIsDirty(true);
      }
    } else {
      if (isDirty) {
        setIsDirty(false);
      }
    }

    onChange(evt);
  };

  const onBlurEvt = (evt) => {
    setIsDirty(true);

    onBlur(evt);
  };

  const infoDetail = getInfoDetail(info, name);

  return (
    <Field
      ref={containerRef}
      label={label}
      htmlFor={name}
      containerClass={containerClass}
      containerStyle={containerStyle}
      info={info}
      tooltip={tooltip}
    >
      <>
        <div ref={inputContainerRef} className="input-container">
          {prefixSection}
          <input
            ref={inputRef}
            type={type}
            name={name}
            className={getFormattedClassName(
              `input ${infoDetail.type ? infoDetail.type : ''} ${inputClassName}`,
            )}
            placeholder={t(placeholder)}
            onChange={onChangeEvt}
            value={value}
            autoComplete={`${name}-${type}`}
            {...props}
            onBlur={onBlurEvt}
          />
          {suffixSection}
        </div>
      </>
    </Field>
  );
};

Input.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string,
  containerStyle: PropTypes.object,
  containerClass: PropTypes.string,
  inputClassName: PropTypes.string,
  placeholder: PropTypes.string,
  showInfoOnChange: PropTypes.bool,
  info: PropTypes.shape({
    type: PropTypes.oneOf(['', 'info', 'warning', 'error']),
    message: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
    context: PropTypes.string,
  }),
  tooltip: PropTypes.object,
  type: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  value: PropTypes.string,
  prefixSection: PropTypes.any,
  suffixSection: PropTypes.any,
  containerRef: PropTypes.any,
  inputRef: PropTypes.any,
};

const InputForwardRef = (props, ref) => <Input {...props} containerRef={ref} />;

export default forwardRef(InputForwardRef);
