import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Field from './Field';

const DEFAULT_PROPS = {
  label: '',
  name: '',
  containerClass: '',
  containerStyle: {},
  inputClassName: '',
  placeholder: '',
  info: { type: '', message: '', context: '' },
  tooltip: {},
  type: 'text',
  onChange: noop,
  value: '',
};

const TextArea = ({
  label = DEFAULT_PROPS.label,
  name = DEFAULT_PROPS.name,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  inputClassName = DEFAULT_PROPS.inputClassName,
  placeholder = DEFAULT_PROPS.placeholder,
  info = DEFAULT_PROPS.info,
  tooltip = DEFAULT_PROPS.tooltip,
  type = DEFAULT_PROPS.type,
  onChange = DEFAULT_PROPS.onChange,
  value = DEFAULT_PROPS.value,
  ...props
}) => {
  const { t } = useTranslation();

  return (
    <Field
      label={label}
      htmlFor={name}
      containerClass={containerClass}
      containerStyle={containerStyle}
      info={info}
      tooltip={tooltip}
    >
      <textarea
        type={type}
        name={name}
        className={`textarea ${inputClassName}`}
        placeholder={t(placeholder)}
        onChange={onChange}
        value={value}
        {...props}
      />
    </Field>
  );
};

TextArea.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  inputClassName: PropTypes.string,
  placeholder: PropTypes.string,
  info: PropTypes.shape({
    type: PropTypes.oneOf(['', 'info', 'warning', 'error']),
    message: PropTypes.string,
    context: PropTypes.string,
  }),
  tooltip: PropTypes.object,
  type: PropTypes.string,
  onChange: PropTypes.func,
  value: PropTypes.string,
  children: PropTypes.any,
};

export default TextArea;
