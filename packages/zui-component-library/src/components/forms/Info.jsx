import { useTranslation } from 'react-i18next';

import {
  faCheck,
  faExclamationCircle,
  faExclamationTriangle,
  faQuestion,
} from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const getIcon = (type) => {
  let icon = faQuestion;

  if (type === 'success') {
    icon = faCheck;
  }

  if (type === 'warning') {
    icon = faExclamationTriangle;
  }

  if (type === 'error') {
    icon = faExclamationCircle;
  }

  return icon;
};

const DEFAULT_PROPS = {
  show: true,
  showIcon: true,
  type: '',
  message: '',
  containerClass: '',
  containerStyle: {},
};

const Info = ({
  show = DEFAULT_PROPS.show,
  showIcon = DEFAULT_PROPS.showIcon,
  type = DEFAULT_PROPS.type,
  message = DEFAULT_PROPS.message,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  const renderMessage = () => {
    if (typeof message === 'string') {
      return t(message);
    }

    if (typeof message === 'function') {
      return message?.({ type, message, show });
    }

    return message;
  };

  if (show) {
    return (
      <div
        className={getFormattedClassName(`info-detail-container ${type} ${containerClass}`)}
        style={containerStyle}
      >
        {showIcon && <FontAwesomeIcon icon={getIcon(type)} className="icon left" />}

        {renderMessage()}
      </div>
    );
  }

  return null;
};

Info.propTypes = {
  show: PropTypes.bool,
  showIcon: PropTypes.bool,
  type: PropTypes.oneOf(['', 'info', 'warning', 'error']),
  message: PropTypes.any,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default Info;
