import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  position: 'right',
  children: null,
};

const InputAction = ({ position = DEFAULT_PROPS.position, children = DEFAULT_PROPS.children }) => {
  return <div className={`is-flex has-ai-c input-action-container ${position}`}>{children}</div>;
};

InputAction.propTypes = {
  position: PropTypes.oneOf(['left', 'right']),
  children: PropTypes.any,
};

export default InputAction;
