import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  children: null,
};

const FieldGroup = ({
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  return (
    <div className={getFormattedClassName(`field-group ${containerClass}`)} style={containerStyle}>
      {children}
    </div>
  );
};

FieldGroup.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default FieldGroup;
