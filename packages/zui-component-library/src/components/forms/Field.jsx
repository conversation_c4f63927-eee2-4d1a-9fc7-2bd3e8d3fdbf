import { Children, cloneElement, forwardRef, isValidElement } from 'react';

import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import Label from './Label';
import { getInfoDetail } from './helper';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  label: '',
  htmlFor: '',
  labelHtmlContent: '',
  labelProps: {},
  info: { type: '' },
  tooltip: {},
  isStacked: false,
  containerRef: {},
  children: null,
};

const Field = ({
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  label = DEFAULT_PROPS.label,
  htmlFor = DEFAULT_PROPS.htmlFor,
  labelHtmlContent = DEFAULT_PROPS.labelHtmlContent,
  labelProps = DEFAULT_PROPS.labelProps,
  info = DEFAULT_PROPS.info,
  tooltip = DEFAULT_PROPS.tooltip,
  isStacked = DEFAULT_PROPS.isStacked,
  containerRef = DEFAULT_PROPS.containerRef,
  children = DEFAULT_PROPS.children,
}) => {
  const infoDetail = getInfoDetail(info, htmlFor);

  const hasTooltip = tooltip?.content || infoDetail?.type;

  const childrenWithInfo = Children.map(children, (child) => {
    const propsToAdd = {};

    if (infoDetail.type) {
      propsToAdd.info = infoDetail;
    }

    if (hasTooltip) {
      propsToAdd.hasTooltip = hasTooltip;
    }

    if (isValidElement(child) && Object.keys(propsToAdd).length > 0) {
      return cloneElement(child, propsToAdd);
    }

    return child;
  });

  const formattedClassName = getFormattedClassName(
    `field ${isStacked ? 'field-stacked' : ''} ${
      hasTooltip ? 'has-tooltip' : ''
    }  ${containerClass}`,
  );

  return (
    <div ref={containerRef} className={formattedClassName} style={containerStyle}>
      <Label
        text={label}
        htmlFor={htmlFor}
        htmlContent={labelHtmlContent}
        info={infoDetail}
        tooltip={tooltip}
        hasTooltip={!!hasTooltip}
        {...labelProps}
      />
      {childrenWithInfo}
    </div>
  );
};

Field.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  label: PropTypes.string,
  htmlFor: PropTypes.string,
  labelHtmlContent: PropTypes.string,
  labelProps: PropTypes.object,
  info: PropTypes.object,
  tooltip: PropTypes.object,
  isStacked: PropTypes.bool,
  containerRef: PropTypes.any,
  children: PropTypes.any,
};

const FieldForwardRef = (props, ref) => <Field {...props} containerRef={ref} />;

export default forwardRef(FieldForwardRef);
