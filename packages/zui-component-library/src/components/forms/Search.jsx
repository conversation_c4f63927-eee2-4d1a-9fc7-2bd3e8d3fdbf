import { useEffect, useState } from 'react';

import { faSearch, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';
import Input from './Input';
import InputAction from './InputAction';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  iconPosition: 'right',
  inputClassName: '',
  placeholder: 'SEARCH_PLACEHOLDER',
  term: '',
  onSearch: noop,
  disabled: false,
  searchOnKeyPress: false,
  onBlur: noop,
  searchIconVisible: true,
};

const Search = ({
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  iconPosition = DEFAULT_PROPS.iconPosition,
  inputClassName = DEFAULT_PROPS.inputClassName,
  placeholder = DEFAULT_PROPS.placeholder,
  term = DEFAULT_PROPS.term,
  onSearch = DEFAULT_PROPS.onSearch,
  disabled = DEFAULT_PROPS.disabled,
  searchOnKeyPress = DEFAULT_PROPS.searchOnKeyPress,
  onBlur = DEFAULT_PROPS.onBlur,
  searchIconVisible,
  ...props
}) => {
  const [value, setValue] = useState(term);

  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    setValue(term);
  }, [term]);

  const onChange = (evt) => {
    const { value } = evt.target;

    if (searchOnKeyPress) {
      onClick(value);
    }

    if (!isDirty) {
      setIsDirty(true);
    }

    setValue(value);
  };

  const onClick = (term) => {
    if (!disabled && term) {
      onSearch((term + '').trim());
    }
  };

  const onClearClick = (evt) => {
    evt.preventDefault();

    setIsDirty(false);
    setValue('');
    onSearch('');
  };

  const onKeyPress = (evt) => {
    const { key } = evt;

    if (key === 'Enter') {
      onClick(value);
    }
  };

  const onBlurEvt = (evt) => {
    onBlur(evt);

    if (!value && isDirty) {
      setIsDirty(false);
      onSearch('');
    }
  };

  const prefixSection =
    iconPosition === 'left' ? (
      <InputAction position="left">
        <Button
          type="tertiary"
          containerClass="no-p-l no-p-r content-width"
          onClick={() => {
            onClick(value);
          }}
          disabled={disabled}
        >
          <FontAwesomeIcon className="icon" icon={faSearch} />
        </Button>
      </InputAction>
    ) : null;

  const suffixSection = (
    <InputAction position="right">
      {(value || isDirty) && (
        <Button type="tertiary" containerClass="no-p-l no-p-r content-width" onClick={onClearClick}>
          <FontAwesomeIcon icon={faTimes} className="icon left" />
        </Button>
      )}

      {iconPosition === 'right' && value && searchIconVisible && (
        <Button
          type="tertiary"
          containerClass="no-p-l no-p-r content-width"
          onClick={() => {
            onClick(value);
          }}
          disabled={disabled}
        >
          <FontAwesomeIcon className="icon" icon={faSearch} />
        </Button>
      )}
    </InputAction>
  );

  return (
    <Input
      inputClassName={inputClassName}
      placeholder={placeholder}
      onChange={onChange}
      value={value}
      onKeyPress={onKeyPress}
      disabled={disabled}
      onBlur={onBlurEvt}
      prefixSection={prefixSection}
      suffixSection={suffixSection}
      containerClass={`search-container ${iconPosition} ${containerClass}`}
      containerStyle={containerStyle}
      maxLength="128"
      {...props}
    />
  );
};

Search.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  iconPosition: PropTypes.oneOf(['left', 'right']),
  inputClassName: PropTypes.string,
  placeholder: PropTypes.string,
  term: PropTypes.string,
  onSearch: PropTypes.func,
  disabled: PropTypes.bool,
  searchOnKeyPress: PropTypes.bool,
  onBlur: PropTypes.func,
  searchIconVisible: PropTypes.bool,
};

export default Search;
