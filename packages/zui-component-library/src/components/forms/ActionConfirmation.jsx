import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { mergeFormValues } from '../../utils/dom';

import Card from '../cards/Card';
import Input from './Input';

const DEFAULT_PROPS = {
  message: '',
  deleteText: 'Type “YES” to confirm:',
  confirmText: 'YES',
  showConfirmation: true,
  onValidityChange: noop,
};

const ActionConfirmation = ({
  message = DEFAULT_PROPS.message,
  deleteText = DEFAULT_PROPS.deleteText,
  confirmText = DEFAULT_PROPS.confirmText,
  showConfirmation = DEFAULT_PROPS.showConfirmation,
  onValidityChange = DEFAULT_PROPS.onValidityChange,
}) => {
  const { t } = useTranslation();

  useEffect(() => {
    if (!showConfirmation) {
      onValidityChange(true);
    }
  }, [showConfirmation]);

  const [formValues, setFormValues] = useState({
    delete: '',
  });

  const onFormFieldChange = (evt) => {
    const { value } = evt.target;

    setFormValues(mergeFormValues(evt));

    onValidityChange(value === confirmText);
  };

  return (
    <Card containerClass="action-confirmation-container">
      <span className="text-normal">{t(message)}</span>
      {showConfirmation && (
        <>
          <div>
            <span className="text-normal">{t(deleteText)}</span>
          </div>
          <Input
            label=""
            containerClass="delete-input"
            name="delete"
            placeholder={t(confirmText)}
            onChange={onFormFieldChange}
            value={formValues.delete}
          />
        </>
      )}
    </Card>
  );
};

ActionConfirmation.propTypes = {
  message: PropTypes.string,
  deleteText: PropTypes.string,
  confirmText: PropTypes.string,
  showConfirmation: PropTypes.bool,
  onValidityChange: PropTypes.func,
};

export default ActionConfirmation;
