import { faSquare } from '@fortawesome/pro-regular-svg-icons';
import { faCheckSquare } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  onChange: noop,
  name: '',
  text: '',
  right: false,
  checked: false,
  checkedColor: 'var(--semantic-color-content-interactive-primary-default, #2160e1)',
  disabled: false,
  disabledColor: 'var(--semantic-color-content-interactive-primary-disabled, #8590a6)',
  isLarge: true,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const Checkbox = ({
  onChange = DEFAULT_PROPS.onChange,
  name = DEFAULT_PROPS.name,
  text = DEFAULT_PROPS.text,
  right = DEFAULT_PROPS.right,
  checked = DEFAULT_PROPS.checked,
  checkedColor = DEFAULT_PROPS.checkedColor,
  disabled = DEFAULT_PROPS.disabled,
  disabledColor = DEFAULT_PROPS.disabledColor,
  isLarge = DEFAULT_PROPS.isLarge,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
  ...props
}) => {
  const checkboxState = `${disabled ? 'disabled' : 'active'}`;

  const FontIcon = (
    <FontAwesomeIcon
      className={checkboxState}
      icon={checked ? faCheckSquare : faSquare}
      color={disabled ? disabledColor : checkedColor}
    />
  );

  const formattedClassName = getFormattedClassName(
    `label checkbox ${checkboxState} ${isLarge ? 'large' : ''} ${containerClass}`,
  );

  const content = children ? children : text;

  return (
    <>
      <label className={formattedClassName} htmlFor={name} style={containerStyle}>
        {!right && FontIcon}
        {content && <div className="content-container"> {content} </div>}
        {right && FontIcon}

        {/**
         * default checkbox won't show as there is a global css for input to unset
         * all the browser default see input { all : unset }
         */}
        <input
          type="checkbox"
          id={name}
          name={name}
          onChange={onChange}
          checked={checked}
          disabled={disabled}
          {...props}
        />
      </label>
    </>
  );
};

Checkbox.propTypes = {
  onChange: PropTypes.func,
  name: PropTypes.string,
  text: PropTypes.string,
  right: PropTypes.bool,
  checked: PropTypes.bool,
  checkedColor: PropTypes.string,
  disabled: PropTypes.bool,
  disabledColor: PropTypes.string,
  isLarge: PropTypes.bool,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default Checkbox;
