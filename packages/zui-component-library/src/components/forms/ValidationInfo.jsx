import { useTranslation } from 'react-i18next';

import { faCheckCircle, faExclamationCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { isUndefined } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  validationDetail: {},
  keyName: '',
  hideIfUnset: true,
};

const ValidationInfo = ({
  validationDetail = DEFAULT_PROPS.validationDetail,
  keyName = DEFAULT_PROPS.keyName,
  hideIfUnset = DEFAULT_PROPS.hideIfUnset,
}) => {
  const { t } = useTranslation();

  const { isValid, message, config = {} } = validationDetail?.[keyName] || {};

  const value = isUndefined(config?.[keyName]) ? '' : config?.[keyName];

  if (hideIfUnset && !config?.[keyName]) {
    return null;
  }

  return (
    <div className="is-flex has-ai-c validation-info">
      <FontAwesomeIcon
        icon={isValid ? faCheckCircle : faExclamationCircle}
        className={`icon left ${isValid ? 'has-color-success' : 'has-color-error'}`}
      />

      <span className="text">{t(message, { value })}</span>
    </div>
  );
};

ValidationInfo.propTypes = {
  validationDetail: PropTypes.object,
  keyName: PropTypes.string,
  hideIfUnset: PropTypes.bool,
};

export default ValidationInfo;
