import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import LabelTooltip from './LabelTooltip';

const DEFAULT_PROPS = {
  containerClass: '',
  containerStyle: {},
  htmlFor: '',
  text: '',
  tooltip: {},
  info: {},
  hasTooltip: false,
  children: null,
};

const Label = ({
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  htmlFor = DEFAULT_PROPS.htmlFor,
  text = DEFAULT_PROPS.text,
  tooltip = DEFAULT_PROPS.tooltip,
  info = DEFAULT_PROPS.info,
  hasTooltip = DEFAULT_PROPS.hasTooltip,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const labelRef = useRef();

  const [labelRefProps, setLabelRefProps] = useState({});

  const formattedClass = getFormattedClassName(
    `label ${info?.type ? info?.type : ''} ${hasTooltip ? 'has-tooltip' : ''} ${containerClass}`,
  );

  const labelProps = {
    className: formattedClass,
    htmlFor: htmlFor,
    style: containerStyle,
  };

  const renderTooltipSection = () => {
    return (
      <LabelTooltip
        labelRef={labelRef}
        updateReferenceElementsProps={setLabelRefProps}
        name={htmlFor}
        info={info}
        {...tooltip}
      />
    );
  };

  if (children) {
    return (
      <>
        <label ref={labelRef} {...labelProps}>
          {children}
          {renderTooltipSection()}
        </label>
      </>
    );
  }

  if (text) {
    return (
      <>
        <label ref={labelRef} {...labelRefProps} {...labelProps}>
          {t(text)}
          {renderTooltipSection()}
        </label>
      </>
    );
  }

  return null;
};

Label.propTypes = {
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  htmlFor: PropTypes.string,
  text: PropTypes.string,
  tooltip: PropTypes.object,
  info: PropTypes.object,
  hasTooltip: PropTypes.bool,
  children: PropTypes.any,
};

export default Label;
