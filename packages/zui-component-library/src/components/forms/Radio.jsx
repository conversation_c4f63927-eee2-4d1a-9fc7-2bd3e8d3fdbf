import { faCircle, faCircleDot } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

const DEFAULT_PROPS = {
  onChange: noop,
  name: '',
  id: '',
  text: '',
  right: false,
  checked: false,
  disabled: false,
  isLarge: true,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const Radio = ({
  onChange = DEFAULT_PROPS.onChange,
  name = DEFAULT_PROPS.name,
  id = DEFAULT_PROPS.id,
  text = DEFAULT_PROPS.text,
  right = DEFAULT_PROPS.right,
  checked = DEFAULT_PROPS.checked,
  disabled = DEFAULT_PROPS.disabled,
  isLarge = DEFAULT_PROPS.isLarge,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
  ...props
}) => {
  const radioState = `${disabled ? 'disabled' : 'active'}`;

  const FontIcon = (
    <FontAwesomeIcon
      className={radioState}
      icon={checked ? faCircleDot : faCircle}
      color={`${disabled ? '#cacbcc' : '#005f99'}`}
    />
  );

  const formattedClassName = getFormattedClassName(
    `label radio ${radioState} ${isLarge ? 'large' : ''} ${containerClass}`,
  );

  const content = children ? children : text;

  return (
    <>
      <label className={formattedClassName} htmlFor={id} style={containerStyle}>
        {!right && FontIcon}
        {content && <div className="content-container"> {content} </div>}
        {right && FontIcon}

        {/**
         * default radio won't show as there is a global css for input to unset
         * all the browser default see input { all : unset }
         */}
        <input
          type="radio"
          id={id}
          name={name}
          onChange={onChange}
          checked={checked}
          disabled={disabled}
          {...props}
        />
      </label>
    </>
  );
};

Radio.propTypes = {
  onChange: PropTypes.func,
  name: PropTypes.string,
  id: PropTypes.string,
  text: PropTypes.string,
  right: PropTypes.bool,
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  isLarge: PropTypes.bool,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default Radio;
