import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Card from '../cards/Card';
import Tooltip from '../tooltip/Tooltip';
import Info from './Info';

const calculateOffset = () => {
  return { mainAxis: 5 };
};

const DEFAULT_PROPS = {
  labelRef: { current: '' },
  hasCard: true,
  name: '',
  updateReferenceElementsProps: noop,
  content: null,
  containerClass: '',
  containerStyle: {},
  info: {},
};

const LabelTooltip = ({
  labelRef = DEFAULT_PROPS.labelRef,
  hasCard = DEFAULT_PROPS.hasCard,
  updateReferenceElementsProps = DEFAULT_PROPS.updateReferenceElementsProps,
  content = DEFAULT_PROPS.content,
  containerClass = DEFAULT_PROPS,
  containerStyle = DEFAULT_PROPS.containerStyle,
  info = DEFAULT_PROPS.info,
  ...rest
}) => {
  const CustomTag = hasCard ? Card : 'div';

  const renderValidationDetailSection = () => {
    return <Info show={!!info?.type} type={info?.type} showIcon={false} message={info?.message} />;
  };

  if (content || info?.type) {
    return (
      <Tooltip
        placement="top-start"
        offset={calculateOffset}
        updateReferenceElementsProps={updateReferenceElementsProps}
        elementRef={labelRef}
        {...rest}
      >
        <CustomTag
          containerClass={`label-tooltip-container ${containerClass}`}
          containerStyle={containerStyle}
        >
          <Info show={!!content} showIcon={false} message={content} />
          {renderValidationDetailSection()}
        </CustomTag>
      </Tooltip>
    );
  }

  return null;
};

LabelTooltip.propTypes = {
  labelRef: PropTypes.object,
  hasCard: PropTypes.bool,
  name: PropTypes.string,
  updateReferenceElementsProps: PropTypes.func,
  content: PropTypes.any,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  info: PropTypes.object,
};

export default LabelTooltip;
