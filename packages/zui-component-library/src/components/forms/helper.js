import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

export const getInfoDetail = (info, name) => {
  if (info[name]) {
    return info[name];
  }

  if (info.context === name) {
    return info;
  }

  return { type: '' };
};

export const defaultValidationDetail = {
  isValid: true,
  context: 'generic',
  type: '',
  message: '',
};

export const defaultFormProps = {
  mode: 'add',
  onDetailChange: noop,
  validationDetail: defaultValidationDetail,
  setValidationDetail: noop,
  detail: {},
};

export const defaultFormPropTypes = {
  mode: PropTypes.oneOf(['read', 'add', 'edit']),
  onDetailChange: PropTypes.func,
  detail: PropTypes.object,
  validationDetail: PropTypes.shape({
    isValid: PropTypes.bool,
    type: PropTypes.oneOf(['', 'info', 'warning', 'error']),
    message: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
    context: PropTypes.string,
  }),
  setValidationDetail: PropTypes.func,
};
