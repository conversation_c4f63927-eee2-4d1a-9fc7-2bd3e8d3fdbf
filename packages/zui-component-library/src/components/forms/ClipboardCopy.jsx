import { useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import Card from '../cards/Card';
import FloatingContainer from '../floating/FloatingContainer';

const DEFAULT_PROPS = {
  text: '',
  showCopied: true,
  placeholder: '',
  onCopy: noop,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const ClipboardCopy = ({
  text = DEFAULT_PROPS.text,
  showCopied = DEFAULT_PROPS.showCopied,
  placeholder = DEFAULT_PROPS.placeholder,
  onCopy = DEFAULT_PROPS.onCopy,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const [isCopied, setIsCopied] = useState(false);

  const {
    open: isOpen,
    setOpen: setIsOpen,
    ...floatingLayout
  } = useFloatingLayout({
    placement: 'right',
    showOnHover: false,
    showOnClick: true,
    flipConfig: { padding: 36 },
    sizeConfig: {
      padding: 36,
    },
    inlineConfig: true,
  });

  const { refs } = floatingLayout;

  const onCopyClick = (_, result) => {
    setIsCopied(result);

    setIsOpen(true);

    setTimeout(() => {
      setIsOpen(false);
    }, 2 * 1000);

    onCopy?.(result);
  };

  return (
    <>
      <CopyToClipboard text={text} onCopy={onCopyClick}>
        <div ref={refs.setReference} className={containerClass} style={containerStyle}>
          {children}
        </div>
      </CopyToClipboard>

      <FloatingContainer show={isOpen} hasArrow {...floatingLayout}>
        <Card>
          <div className="clipboard-copy-message-container">
            {isCopied
              ? t(`${showCopied ? text : placeholder} is copied successfully!`)
              : `Not able to copy!`}
          </div>
        </Card>
      </FloatingContainer>
    </>
  );
};

ClipboardCopy.propTypes = {
  text: PropTypes.string,
  showCopied: PropTypes.bool,
  placeholder: PropTypes.string,
  onCopy: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.any,
};

export default ClipboardCopy;
