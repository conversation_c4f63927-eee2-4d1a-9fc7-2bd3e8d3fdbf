import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Button } from '../buttons';
import { Label } from '../forms';
import TextWithTooltip from '../tooltip/TextWithTooltip';

const DEFAULT_PROPS = {
  onChange: noop,
  buttonType: 'secondary',
  buttonLabel: 'BROWSE_FILE',
  hasLeftIcon: false,
  leftIcon: '',
  fileInfoLabel: 'NO_FILE_CHOSEN',
  fileInfoPosition: 'right',
  buttonContainerClass: '',
  buttonContainerStyle: {},
  isMultipleFiles: false,
};

const InputFile = ({
  onChange = DEFAULT_PROPS.onChange,
  buttonType = DEFAULT_PROPS.buttonType,
  buttonLabel = DEFAULT_PROPS.buttonLabel,
  hasLeftIcon = DEFAULT_PROPS.hasLeftIcon,
  leftIcon = DEFAULT_PROPS.leftIcon,
  fileInfoLabel = DEFAULT_PROPS.fileInfoLabel,
  fileInfoPosition = DEFAULT_PROPS.fileInfoPosition,
  buttonContainerClass = DEFAULT_PROPS.buttonContainerClass,
  buttonContainerStyle = DEFAULT_PROPS.buttonContainerStyle,
  isMultipleFiles = DEFAULT_PROPS.isMultipleFiles,
}) => {
  const { t } = useTranslation();

  const [fileDetail, setFileDetail] = useState([]);
  const [fileInfo, setFileInfo] = useState(fileInfoLabel);

  useEffect(() => {
    setFileInfo(fileInfoLabel || 'NO_FILE_CHOSEN');
  }, [fileInfoLabel]);

  const onFileChange = (evt) => {
    const { files } = evt.target;

    const filesLength = files.length;

    if (filesLength > 0) {
      const info = filesLength === 1 ? files[0].name : `${filesLength} files`;

      setFileInfo(info);

      setFileDetail(() => {
        onChange(files);
        return files;
      });
    } else {
      setFileDetail(() => {
        onChange(fileDetail);
        return fileDetail;
      });
    }
  };

  return (
    <div className="is-flex has-ai-c file-browser-container">
      {fileInfoPosition === 'left' && (
        <div className="file-detail-container left">{t(fileInfo)}</div>
      )}

      <Label htmlFor="file-browser" containerClass="file-browser-label">
        <Button
          containerClass={`file-browser-button ${buttonContainerClass}`}
          type={buttonType}
          style={buttonContainerStyle}
        >
          {hasLeftIcon && <FontAwesomeIcon icon={leftIcon} className="icon left" />}
          {t(buttonLabel)}
        </Button>
      </Label>

      {fileInfoPosition === 'right' && (
        <TextWithTooltip containerClass={'file-detail-container right'}>
          <span> {t(fileInfo)} </span>
        </TextWithTooltip>
      )}

      <input
        id="file-browser"
        className="file"
        name="browse files"
        onChange={onFileChange}
        type="file"
        multiple={isMultipleFiles}
      />
    </div>
  );
};

InputFile.propTypes = {
  onChange: PropTypes.func,
  hasLeftIcon: PropTypes.bool,
  leftIcon: PropTypes.any,
  buttonType: PropTypes.oneOf(['', 'primary', 'secondary', 'tertiary']),
  buttonLabel: PropTypes.string,
  fileInfoLabel: PropTypes.string,
  fileInfoPosition: PropTypes.oneOf(['left', 'right']),
  buttonContainerClass: PropTypes.string,
  buttonContainerStyle: PropTypes.object,
  isMultipleFiles: PropTypes.bool,
  tooltip: PropTypes.object,
};

export default InputFile;
