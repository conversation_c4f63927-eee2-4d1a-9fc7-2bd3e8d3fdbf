import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import Card from '../cards/Card';
import { Field, FieldGroup } from '../forms';
import { Toast } from '../toast';

const defaultResultValue = {
  status: 'COMPLETE',
  totalRecordsAdded: 0,
  totalRecordsDeleted: 0,
  totalRecordsUpdated: 0,
  failedRecords: [],
  processedRecords: 0,
  totalRecordsInImport: 0,
  errors: [],
};

const DEFAULT_PROPS = {
  title: 'IMPORT_RESULTS',
  titleContainerClass: '',
  titleContainerStyle: {},
  result: {},
  showErrorNotification: true,
  containerClass: '',
  containerStyle: {},
};

const FileImportResult = ({
  title = DEFAULT_PROPS.title,
  titleContainerClass = DEFAULT_PROPS.titleContainerClass,
  titleContainerStyle = DEFAULT_PROPS.titleContainerStyle,
  result = DEFAULT_PROPS.result,
  showErrorNotification = DEFAULT_PROPS.showErrorNotification,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  const {
    status,
    totalRecordsAdded,
    totalRecordsDeleted,
    totalRecordsUpdated,
    failedRecords,
    errors,
    processedRecords,
    totalRecordsInImport,
  } = { ...defaultResultValue, ...result };

  const [isNotificationClosed, setIsNotificationClosed] = useState(false);

  const renderFailedRecordSection = () => {
    if (failedRecords.length > 0) {
      return (
        <FieldGroup>
          <Field label="FAILED_RECORDS">
            <div className="failed-records-container">
              {failedRecords.map(({ errorCode = '', name = '', description = '' }, idx) => (
                <p key={idx} className="record text-gray-darker">
                  {`${t(errorCode)} - ${description ? description : name}`}
                </p>
              ))}
            </div>
          </Field>
        </FieldGroup>
      );
    }

    return null;
  };

  const onNotificationClose = () => {
    setIsNotificationClosed(true);
  };

  const renderErrorNotificationSection = () => {
    if (showErrorNotification && !isNotificationClosed && errors.length > 0) {
      const hasSingleData = errors.length === 1;

      const errorMessage = errors.map(({ errorCode = '', description = '' }) => (
        <p
          key={errorCode}
          className={getFormattedClassName(`text-content ${hasSingleData ? '' : 'has-as-s'}`)}
        >
          {`${t(errorCode)} - ${description}`}
        </p>
      ));

      return (
        <div className="notification-container">
          <Toast type="error" onClose={onNotificationClose}>
            {errorMessage}
          </Toast>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`file-import-result-container ${containerClass}`} style={containerStyle}>
      <section className={`text-upper-large ${titleContainerClass}`} style={titleContainerStyle}>
        {t(title)}
      </section>

      {renderErrorNotificationSection()}

      <Card containerClass="is-flex has-fd-c file-browser-form-container">
        <FieldGroup>
          <Field label="STATUS">
            <span className="text-gray-darker">{t(status)}</span>
          </Field>
          <Field label="PROCESSED_RECORD">
            <span className="text-gray-darker">{processedRecords}</span>
          </Field>
        </FieldGroup>
        <FieldGroup>
          <Field label="TOTAL_RECORDS_ADDED">
            <span className="text-gray-darker">{totalRecordsAdded}</span>
          </Field>
          <Field label="TOTAL_RECORDS_DELETED">
            <span className="text-gray-darker">{totalRecordsDeleted}</span>
          </Field>
        </FieldGroup>
        <FieldGroup>
          <Field label="TOTAL_RECORDS_IN_IMPORT">
            <span className="text-gray-darker">{totalRecordsInImport}</span>
          </Field>
          <Field label="TOTAL_RECORDS_UPDATED">
            <span className="text-gray-darker">{totalRecordsUpdated}</span>
          </Field>
        </FieldGroup>
        {renderFailedRecordSection()}
      </Card>
    </div>
  );
};

FileImportResult.propTypes = {
  title: PropTypes.string,
  titleContainerClass: PropTypes.string,
  titleContainerStyle: PropTypes.object,
  result: PropTypes.object,
  showErrorNotification: PropTypes.bool,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default FileImportResult;
