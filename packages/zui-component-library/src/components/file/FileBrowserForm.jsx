import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getOnChangeValue } from '../../utils/dom';

import { Card } from '../cards';
import { Checkbox, Field } from '../forms';
import DownloadFile from './DownloadFile';
import FileImportResult from './FileImportResult';
import InputFile from './InputFile';

const DEFAULT_PROPS = {
  overrideLabel: 'OVERRIDE_EXISTING_ENTRIES',
  hideOverrideLabel: false,
  overrideProps: {},
  onDetailChange: noop,
  setValidationDetail: noop,
  renderDownloadSection: (props) => <DownloadFile {...props} />,
  result: null,
};

const FileBrowserForm = ({
  overrideLabel = DEFAULT_PROPS.overrideLabel,
  hideOverrideLabel = DEFAULT_PROPS.hideOverrideLabel,
  overrideProps = DEFAULT_PROPS.overrideProps,
  onDetailChange = DEFAULT_PROPS.onDetailChange,
  setValidationDetail = DEFAULT_PROPS.setValidationDetail,
  renderDownloadSection = DEFAULT_PROPS.renderDownloadSection,
  result = DEFAULT_PROPS.result,
  ...props
}) => {
  const { t } = useTranslation();

  const [override, setOverride] = useState(false);
  const [filesDetail, setFilesDetail] = useState([]);

  useEffect(() => {
    const validationDetail = { isValid: filesDetail.length > 0, message: '' };
    setValidationDetail(validationDetail);

    onDetailChange({ override, filesDetail });
  }, [override, filesDetail]);

  const onOverrideChange = (evt) => {
    const changedValue = getOnChangeValue(evt.target);

    setOverride(changedValue.override);
  };

  const onFileChange = (files) => {
    setFilesDetail(files);
  };

  if (result && Object.keys(result).length > 0) {
    return <FileImportResult result={result} />;
  }

  return (
    <>
      <section className="text-upper-large">{t('INFORMATION')}</section>

      <Card containerClass="is-flex file-browser-form-container">
        {!hideOverrideLabel && (
          <div className="is-flex override-section">
            <Field label={overrideLabel} {...overrideProps}>
              <Checkbox
                name="override"
                containerClass="large"
                checked={override}
                onChange={onOverrideChange}
              />
            </Field>
          </div>
        )}
        <div className="is-flex has-fd-c file-section">
          {renderDownloadSection({ ...props })}
          <InputFile onChange={onFileChange} />
        </div>
      </Card>
    </>
  );
};

FileBrowserForm.propTypes = {
  hideOverrideLabel: PropTypes.bool,
  overrideLabel: PropTypes.string,
  overrideProps: PropTypes.object,
  onDetailChange: PropTypes.func,
  setValidationDetail: PropTypes.func,
  renderDownloadSection: PropTypes.func,
  result: PropTypes.object,
};

export default FileBrowserForm;
