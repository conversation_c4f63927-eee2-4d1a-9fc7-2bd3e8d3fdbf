import { useTranslation } from 'react-i18next';

import { faDownload } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getFormattedClassName } from '../../utils/dom';

import { Button } from '../buttons';
import { Label } from '../forms';

const DEFAULT_PROPS = {
  label: 'CSV_FILE',
  subLabel: 'SAMPLE_CSV_DOWNLOAD',
  labelTooltip: {},
  variantType: 'labelWithDescriptionLink',
  iconPosition: 'left',
  icon: faDownload,
  onDownloadClick: noop,
  containerClass: '',
  containerStyle: {},
  children: null,
};

const DownLoadFile = ({
  label = DEFAULT_PROPS.label,
  subLabel = DEFAULT_PROPS.subLabel,
  tooltip = DEFAULT_PROPS.tooltip,
  variantType = DEFAULT_PROPS.variantType,
  iconPosition = DEFAULT_PROPS.iconPosition,
  icon = DEFAULT_PROPS.icon,
  onDownloadClick = DEFAULT_PROPS.onDownloadClick,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  buttonContainerClass = DEFAULT_PROPS.buttonContainerClass,
  buttonContainerStyle = DEFAULT_PROPS.buttonContainerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  const hasIconLeft = iconPosition === 'left';

  const onDownloadFile = async (evt) => {
    // evt.stopPropogation()
    evt.preventDefault();

    const detail = await onDownloadClick();

    if (detail) {
      const { data, fileName } = detail;

      const blob = new Blob([data], { type: 'text/plain' });
      const fileDownloadUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');

      link.href = fileDownloadUrl;
      link.setAttribute('download', fileName);
      link.setAttribute('target', '_blank');
      link.setAttribute('rel', 'noopener noreferrer');
      link.click();
    }
  };

  const formattedClassName = getFormattedClassName(`download-file-container ${containerClass}`);

  const formattedButtonClassName = getFormattedClassName(`no-p-l no-p-r ${buttonContainerClass}`);

  const hasTooltip = tooltip?.content;

  const renderVariantType = () => {
    if (variantType === 'labelWithDescriptionLink') {
      return (
        <Label
          tooltip={tooltip}
          hasTooltip={hasTooltip}
          containerClass={formattedClassName}
          containerStyle={containerStyle}
        >
          <Button
            type="tertiary"
            onClick={onDownloadFile}
            containerClass={formattedButtonClassName}
            containerStyle={buttonContainerStyle}
          >
            <span className="text-normal">{t(label)}</span> &nbsp; (
            <span className="">{t(subLabel)}</span>)
          </Button>
        </Label>
      );
    }

    if (variantType === 'iconWithText') {
      return (
        <Label
          tooltip={tooltip}
          hasTooltip={hasTooltip}
          containerClass={formattedClassName}
          containerStyle={containerStyle}
        >
          <Button
            type="tertiary"
            onClick={onDownloadFile}
            containerClass={formattedButtonClassName}
            containerStyle={buttonContainerStyle}
          >
            {hasIconLeft && <FontAwesomeIcon icon={icon} className="icon left" />}
            {t(label)}
            {!hasIconLeft && <FontAwesomeIcon icon={icon} className="icon right" />}
          </Button>
        </Label>
      );
    }

    return children;
  };

  return renderVariantType();
};

DownLoadFile.propTypes = {
  label: PropTypes.string,
  subLabel: PropTypes.string,
  variantType: PropTypes.oneOf(['', 'labelWithDescriptionLink', 'iconWithText']),
  iconPosition: PropTypes.oneOf(['left', 'right']),
  icon: PropTypes.any,
  onDownloadClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node,
};

export default DownLoadFile;
