import { faLoader } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { PropTypes } from 'prop-types';

const DEFAULT_PROPS = {
  isSpinnerPlain: false,
  spinnerClass: '',
  spinnerStyle: '',
  containerClass: '',
  containerStyle: {},
};

const Spinner = ({
  isSpinnerPlain = DEFAULT_PROPS.isSpinnerPlain,
  spinnerClass = DEFAULT_PROPS.spinnerClass,
  spinnerStyle = DEFAULT_PROPS.spinnerStyle,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children,
}) => {
  const renderSpinnerContent = () => {
    if (children) {
      return children;
    }

    if (isSpinnerPlain) {
      return <span className={`spinner ${spinnerClass}`} style={spinnerStyle} />;
    }

    return (
      <FontAwesomeIcon
        icon={faLoader}
        spin
        className={`spinner ${spinnerClass}`}
        size="2xl"
        style={spinnerStyle}
      />
    );
  };

  return (
    <div className={`spinner-container ${containerClass}`} style={containerStyle}>
      {renderSpinnerContent()}
    </div>
  );
};

Spinner.propTypes = {
  isSpinnerPlain: PropTypes.bool,
  spinnerClass: PropTypes.string,
  spinnerStyle: PropTypes.object,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node,
};

export default Spinner;
