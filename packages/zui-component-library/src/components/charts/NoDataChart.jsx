import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  message: 'NO_DATA_AVAILABLE',
  containerClass: '',
  containerStyle: {},
  children: null,
};

const NoDataChart = ({
  message = DEFAULT_PROPS.message,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  return (
    <div
      className={`is-flex has-jc-c has-ai-c no-data-available ${containerClass}`}
      style={containerStyle}
    >
      {children ? children : t(message)}
    </div>
  );
};

NoDataChart.propTypes = {
  message: PropTypes.string,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node,
};

export default NoDataChart;
