export const CHART_COLORS = [
  '#194CBB',
  '#25BAE2',
  '#9F46D7',
  '#389685',
  '#D7E152',
  '#732FE4',
  '#F36185',
  '#0076BE',
  '#1F77B4',
  '#FF7F0E',
  '#2CA02C',
  '#D62728',
  '#9467BD',
  '#8C564B',
  '#E377C2',
  '#7F7F7F',
];

export const CHART_THEME = {
  //   background: '#ffffff',
  text: {
    fontSize: 11,
    fill: '#333333',
    outlineWidth: 0,
    outlineColor: 'transparent',
  },
  axis: {
    domain: {
      line: {
        stroke: '#E9E9E9',
        strokeWidth: 1,
      },
    },
    legend: {
      text: {
        fontSize: 12,
        fill: '#333333',
        outlineWidth: 0,
        outlineColor: 'transparent',
      },
    },
    ticks: {
      line: {
        stroke: '#E9E9E9',
        strokeWidth: 1,
      },
      text: {
        fontSize: 11,
        fill: '#333333',
        outlineWidth: 0,
        outlineColor: 'transparent',
      },
    },
  },
  grid: {
    line: {
      stroke: '#E9E9E9',
      strokeWidth: 1,
    },
  },
  legends: {
    title: {
      text: {
        fontSize: 11,
        fill: '#333333',
        outlineWidth: 0,
        outlineColor: 'transparent',
      },
    },
    text: {
      fontSize: 11,
      fill: '#333333',
      outlineWidth: 0,
      outlineColor: 'transparent',
    },
    ticks: {
      line: {},
      text: {
        fontSize: 10,
        fill: '#333333',
        outlineWidth: 0,
        outlineColor: 'transparent',
      },
    },
  },
  annotations: {
    text: {
      fontSize: 13,
      fill: '#333333',
      outlineWidth: 2,
      outlineColor: '#ffffff',
      outlineOpacity: 1,
    },
    link: {
      stroke: '#000000',
      strokeWidth: 1,
      outlineWidth: 2,
      outlineColor: '#ffffff',
      outlineOpacity: 1,
    },
    outline: {
      stroke: '#000000',
      strokeWidth: 2,
      outlineWidth: 2,
      outlineColor: '#ffffff',
      outlineOpacity: 1,
    },
    symbol: {
      fill: '#000000',
      outlineWidth: 2,
      outlineColor: '#ffffff',
      outlineOpacity: 1,
    },
  },
  tooltip: {
    container: {
      background: '#ffffff',
      fontSize: 12,
    },
    basic: {},
    chip: {},
    table: {},
    tableCell: {},
    tableCellValue: {},
  },
};

export const CHART_CONFIG = {
  LINE: {
    padding: 0.7,
    margin: { top: 50, right: 30, bottom: 60, left: 70 },
    theme: CHART_THEME,
    crosshairType: 'x',
    pointSize: 10,
    colors: CHART_COLORS,
    colorBy: 'index',
    pointColor: { theme: 'background' },
    pointBorderWidth: 2,
    pointLabelYOffset: -120,
    useMesh: false,
    enableSlices: 'x',
    axisLeft: {
      tickSize: 0,
      tickPadding: 10,
      legend: 'Event Count',
      legendPosition: 'middle',
      legendOffset: -50,
    },
    enableGridX: false,
    xScale: {
      type: 'time',
    },
    axisBottom: {
      tickRotation: -45,
      tickSize: 0,
      tickPadding: 10,
      tickValues: 25,
    },
  },
  BAR: {
    indexBy: 'name',
    keys: ['value'],
    padding: 0.7,
    enableLabel: false,
    margin: { top: 50, right: 0, bottom: 50, left: 70 },
    theme: CHART_THEME,
    colors: ['#2160E1'],
    borderRadius: '8px',
    axisLeft: {
      tickSize: 0,
      tickPadding: 10,
      legend: 'SSO Count',
      legendPosition: 'middle',
      legendOffset: -50,
    },
    axisBottom: {
      tickSize: 0,
      tickPadding: 10,
    },
  },
};
