import { useEffect, useState } from 'react';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getDateRange } from '../../utils/dateHelper';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import Items from '../dropdowns/Items';
import FloatingContainer from '../floating/FloatingContainer';
import DateRangePicker from './DateRangePicker';
import { DEFAULT_CALENDAR_CONFIG, getDateRangeDetails } from './helper';

const DEFAULT_PROPS = {
  config: {
    ...DEFAULT_CALENDAR_CONFIG,
  },
  selectedList: [],
  itemDetail: {},
  setIsActive: noop,
  onClick: noop,
};

const CustomDatePicker = ({
  config = DEFAULT_PROPS.config,
  selectedList = DEFAULT_PROPS.selectedList,
  onClick = DEFAULT_PROPS.onClick,
  itemDetail = DEFAULT_PROPS.itemDetail,
  setIsActive = DEFAULT_PROPS.setIsActive,
  ...props
}) => {
  const isCustomSelected = selectedList?.[0]?.value === 'CUSTOM';

  const calendarConfig = {
    ...DEFAULT_CALENDAR_CONFIG,
    ...config,
  };

  const {
    open: showPicker,
    setOpen: setShowPicker,
    ...floatingLayout
  } = useFloatingLayout({
    placement: 'right',
    showOnHover: false,
    showOnClick: true,
    arrowConfig: null,
    offsetConfig: { mainAxis: -1, crossAxis: -26 },
    // hide: false,
    flipConfig: 15,
  });

  const { refs } = floatingLayout;

  const [customRange, setCustomRange] = useState(
    getDateRangeDetails({
      payload: {},
      config: calendarConfig,
    }),
  );

  useEffect(() => {
    if (isCustomSelected) {
      const customData = selectedList?.[0] || {};

      const { startDate, endDate, minDate, maxDate } = getDateRangeDetails({
        payload: { ...(customData.range || {}) },
        config: calendarConfig,
      });

      setCustomRange(
        {
          ...customData.range,
          startDate,
          endDate,
          minDate,
          maxDate,
        } || {},
      );

      setShowPicker(true);
    }
  }, []);

  const onStartDateChange = (newStartDate) => {
    const { startDate, endDate } = getDateRangeDetails({
      payload: {
        startDate: newStartDate,
        endDate: customRange.endDate,
      },
      config: calendarConfig,
    });

    setCustomRange((prevState) => ({ ...prevState, startDate, endDate }));
  };

  const onEndDateChange = (newEndDate) => {
    const { startDate, endDate } = getDateRangeDetails({
      payload: {
        startDate: customRange.startDate,
        endDate: newEndDate,
      },
      config: calendarConfig,
    });

    setCustomRange((prevState) => ({ ...prevState, startDate, endDate }));
  };

  const onCustomApplyRange = (range) => {
    const rangePayload = {
      name: 'CUSTOM',
      formatPattern: 'M/D/YYYY h:m:ss A',
    };

    if (rangePayload.name === 'CUSTOM') {
      const { startDate, endDate } = range;
      rangePayload.startDate = startDate;
      rangePayload.endDate = endDate;
    }

    const dateDetails = getDateRange(rangePayload);

    const rangeDetails = getDateRangeDetails({
      payload: {
        startDate: range.startDate,
        endDate: range.endDate,
      },
      config: calendarConfig,
    });

    onClick([
      {
        ...itemDetail,
        ...dateDetails,
        range,
        ...rangeDetails,
      },
    ]);

    setCustomRange(range);
  };

  const onCustomRangeClose = () => {
    setIsActive(false);
  };

  const onCustomClick = () => {
    setShowPicker(true);

    if (!isCustomSelected) {
      onClick(itemDetail);
    }
  };

  return (
    <div ref={refs.setReference}>
      <Items itemDetail={itemDetail} onClick={onCustomClick} {...props} />

      {showPicker && (
        <FloatingContainer show={showPicker} {...floatingLayout}>
          <DateRangePicker
            applyRange={onCustomApplyRange}
            onClose={onCustomRangeClose}
            onEndDateChange={onEndDateChange}
            onStartDateChange={onStartDateChange}
            startDate={customRange.startDate}
            endDate={customRange.endDate}
            minDate={customRange.minDate}
            maxDate={customRange.maxDate}
            message={calendarConfig.message}
            showHourMinuteOption={calendarConfig.showHourMinuteOption}
            showHourOption={calendarConfig.showHourOption}
            showMinuteOption={calendarConfig.showMinuteOption}
            showSecondsOption={calendarConfig.showSecondsOption}
          />
        </FloatingContainer>
      )}
    </div>
  );
};

CustomDatePicker.propTypes = {
  config: PropTypes.object,
  selectedList: PropTypes.array,
  itemDetail: PropTypes.object,
  setIsActive: PropTypes.func,
  onClick: PropTypes.func,
};

export default CustomDatePicker;
