import { useEffect, useState } from 'react';
import ReactDatePicker from 'react-datepicker';
import { useTranslation } from 'react-i18next';

import dayjs from 'dayjs';
import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import Button from '../buttons/Button';
import TimePicker from './TimePicker';
import { dayClassName, formatWeekDay } from './helper';

const DEFAULT_PROPS = {
  minDate: new Date(),
  maxDate: new Date(),
  startDate: new Date(),
  onStartDateChange: noop,
  endDate: new Date(),
  onEndDateChange: noop,
  message: {},
  messageType: 'info',
  showHourMinuteOption: true,
  showHourOption: false,
  showMinuteOption: false,
  showSecondsOption: false,
  applyRange: noop,
  onClose: noop,
};

const DateRangePicker = ({
  minDate = DEFAULT_PROPS.minDate,
  maxDate = DEFAULT_PROPS.maxDate,
  startDate = DEFAULT_PROPS.startDate,
  onStartDateChange = DEFAULT_PROPS.onStartDateChange,
  endDate = DEFAULT_PROPS.endDate,
  onEndDateChange = DEFAULT_PROPS.onEndDateChange,
  message = DEFAULT_PROPS.message,
  messageType = DEFAULT_PROPS.messageType,
  showHourMinuteOption = DEFAULT_PROPS.showHourMinuteOption,
  showHourOption = DEFAULT_PROPS.showHourOption,
  showMinuteOption = DEFAULT_PROPS.showMinuteOption,
  showSecondsOption = DEFAULT_PROPS.showSecondsOption,
  applyRange = DEFAULT_PROPS.applyRange,
  onClose = DEFAULT_PROPS.onClose,
}) => {
  const { t } = useTranslation();

  const [selectedStartDate, setSelectedStartDate] = useState(startDate);
  const [selectedEndDate, setSelectedEndDate] = useState(endDate);

  useEffect(() => {
    setSelectedStartDate(startDate);
    setSelectedEndDate(endDate);
  }, [startDate, endDate]);

  const onSelection = (date, isStart, isDate) => {
    const oldDate = dayjs(isStart ? selectedStartDate : selectedEndDate);
    let newDate = dayjs(date);

    if (!isDate) {
      newDate = newDate
        .set('day', oldDate.get('day'))
        .set('month', oldDate.get('month'))
        .set('year', oldDate.get('year'));
    } else {
      newDate = newDate
        .set('hours', oldDate.get('hours'))
        .set('minutes', oldDate.get('minutes'))
        .set('seconds', oldDate.get('seconds'));
    }

    newDate = newDate.toDate();

    if (isStart) {
      onStartDateChange(newDate);
      setSelectedStartDate(newDate);
    } else {
      onEndDateChange(newDate);
      setSelectedEndDate(newDate);
    }
  };

  const onApplyRangeClick = () => {
    applyRange({
      startDate: selectedStartDate,
      endDate: selectedEndDate,
      minDate,
      maxDate,
    });
  };

  return (
    <article className="date-range-picker-container">
      <section className="picker-container">
        <section className="left">
          <p className="title">{t('START_TIME')}</p>

          <ReactDatePicker
            dayClassName={dayClassName}
            fixedHeight
            formatWeekDay={formatWeekDay}
            inline
            onChange={(date) => {
              onSelection(date, true, true);
            }}
            selected={selectedStartDate}
            selectsStart
            startDate={selectedStartDate}
            endDate={selectedEndDate}
            minDate={minDate}
            maxDate={maxDate}
          />

          <TimePicker
            onSelection={(date) => {
              onSelection(date, true, false);
            }}
            showHourMinuteOption={showHourMinuteOption}
            showHourOption={showHourOption}
            showMinuteOption={showMinuteOption}
            showSecondsOption={showSecondsOption}
            selectedDate={selectedStartDate}
          />
        </section>

        <section className="right">
          <p className="title">{t('END_TIME')}</p>

          <ReactDatePicker
            dayClassName={dayClassName}
            fixedHeight
            formatWeekDay={formatWeekDay}
            inline
            onChange={(date) => {
              onSelection(date, false, true);
            }}
            selected={selectedEndDate}
            selectsEnd
            startDate={selectedStartDate}
            endDate={selectedEndDate}
            minDate={selectedStartDate}
            maxDate={maxDate}
          />

          <TimePicker
            onSelection={(date) => {
              onSelection(date, false, false);
            }}
            showHourMinuteOption={showHourMinuteOption}
            showHourOption={showHourOption}
            showMinuteOption={showMinuteOption}
            showSecondsOption={showSecondsOption}
            selectedDate={selectedEndDate}
          />
        </section>
      </section>

      <section className="message-container">
        <p className={`message ${messageType}`}>
          {message.useTranslation ? t(message.text, message.transalationPayload) : message.text}
        </p>
      </section>

      <section className="footer is-flex">
        <Button type="primary" onClick={onApplyRangeClick}>
          {t('OK')}
        </Button>

        <Button type="tertiary" onClick={onClose}>
          {t('CANCEL')}
        </Button>
      </section>
    </article>
  );
};

DateRangePicker.propTypes = {
  minDate: PropTypes.objectOf(Date),
  maxDate: PropTypes.objectOf(Date),
  startDate: PropTypes.objectOf(Date),
  onStartDateChange: PropTypes.func,
  endDate: PropTypes.objectOf(Date),
  onEndDateChange: PropTypes.func,
  message: PropTypes.object,
  messageType: PropTypes.string,
  showHourMinuteOption: PropTypes.bool,
  showHourOption: PropTypes.bool,
  showMinuteOption: PropTypes.bool,
  showSecondsOption: PropTypes.bool,
  applyRange: PropTypes.func,
  onClose: PropTypes.func,
};

export default DateRangePicker;
