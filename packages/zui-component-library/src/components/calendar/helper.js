import dayjs from 'dayjs';

import { getDateRange, getTimeRange } from '../../utils/dateHelper';

export const DEFAULT_CALENDAR_CONFIG = {
  showHourMinuteOption: false,
  showHourOption: true,
  showMinuteOption: true,
  showSecondsOption: false,
  hourInterval: 1,
  roundHourTo: 1,
  minuteInterval: 1,
  roundMinutesTo: 1,
  secondsInterval: 1,
  roundSecondsTo: 1,
  defaultIntervalFromEndDate: 60 * 60 * 26,
  maxInterval: 60 * 60 * 24 * 92,
  message: { text: 'End date can be up to 92 days after start date' },
};

export const dayClassName = () => 'each-day';

export const formatWeekDay = (day) => day.substring(0, 3);

export const getCalendarDDList = ({ level = 'REPORT', formatPattern = 'M/D/YYYY hh:mm:ss A' }) => {
  const data = getTimeRange(level).map((timeRange) => ({
    label: timeRange,
    value: timeRange,
    ...getDateRange({ name: timeRange, formatPattern }),
  }));

  return data;
};

export const getDateRangeDetails = ({ payload, config }) => {
  let startDate = payload.startDate
    ? new Date(payload.startDate)
    : dayjs().subtract(config.defaultIntervalFromEndDate, 'seconds').toDate();
  let endDate = payload.endDate
    ? new Date(payload.endDate)
    : dayjs().add(config.maxInterval, 'seconds').toDate();

  const minDate = config.minDate
    ? dayjs(config.minDate).toDate()
    : dayjs().subtract(11, 'years').startOf('day').toDate();
  const maxDate = config.maxDate ? dayjs(config.maxDate).toDate() : dayjs().endOf('day').toDate();

  let maxDateFromStartDate = dayjs(startDate).add(config.maxInterval, 'seconds').toDate();

  if (endDate > maxDateFromStartDate) {
    endDate = new Date(maxDateFromStartDate);
  }

  if (maxDateFromStartDate > maxDate) {
    maxDateFromStartDate = new Date(maxDate);
  }

  if (endDate > maxDateFromStartDate) {
    endDate = new Date(maxDateFromStartDate);
  }

  if (endDate < startDate) {
    endDate = startDate;
  }

  if (!payload.startDate) {
    if (config.roundMinutesTo !== 1) {
      startDate = dayjs(startDate)
        .set('minutes', config.roundMinutesTo)
        .set('seconds', config.roundMinutesTo)
        .toDate();
    }
  }

  if (!payload.endDate) {
    if (config.roundMinutesTo !== 1) {
      const minutes = dayjs(endDate).get('minutes');

      if (minutes >= config.roundMinutesTo) {
        endDate = dayjs(endDate).set('minutes', config.roundMinutesTo);
      } else {
        endDate = dayjs(endDate).subtract(1, 'hours').set('minutes', config.roundMinutesTo);
      }

      endDate = dayjs(endDate).set('seconds', 0).toDate();
    }
  }

  return {
    startDate,
    endDate,
    minDate,
    maxDate: maxDateFromStartDate,
  };
};
