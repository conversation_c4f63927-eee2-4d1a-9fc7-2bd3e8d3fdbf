import { forwardRef, useEffect, useState } from 'react';
import ReactDatePicker from 'react-datepicker';
import { useTranslation } from 'react-i18next';

import { faTimesCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import dayjs from 'dayjs';
import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import useFloatingLayout from '../../hooks/floating/useFloatingLayout';
import Button from '../buttons/Button';
import FloatingContainer from '../floating/FloatingContainer';
import Field from '../forms/Field';
import TimePicker from './TimePicker';
import { dayClassName, formatWeekDay } from './helper';

const DEFAULT_DATE_FORMAT = 'MM/DD/YYYY';

const getFormattedDate = ({ selectedDate, format = DEFAULT_DATE_FORMAT, defaultValue = '' }) => {
  const date = dayjs(selectedDate);

  return date.isValid() ? date.format(format) : defaultValue;
};

const DEFAULT_PROPS = {
  format: DEFAULT_DATE_FORMAT,
  type: 'secondary',
  elementType: 'input',
  label: '',
  name: '',
  placeholder: 'SELECT_DATE',
  skipTranslation: false,
  selectedDate: '',
  elementProps: {},
  minDate: '',
  maxDate: '',
  hideOnSelection: true,
  showTimePicker: false,
  showHourMinuteOption: true,
  showHourOption: false,
  showMinuteOption: false,
  showSecondsOption: false,
  showClearSelectedDate: true,
  onClick: noop,
  onSelection: noop,
  onChange: noop,
  onClose: noop,
  containerClass: '',
  containerStyle: {},
};

const InlineDatePicker = ({
  format = DEFAULT_PROPS.format,
  type = DEFAULT_PROPS.type,
  onClick = DEFAULT_PROPS.onClick,
  label = DEFAULT_PROPS.label,
  name = DEFAULT_PROPS.name,
  placeholder = DEFAULT_PROPS.placeholder,
  skipTranslation = DEFAULT_PROPS.skipTranslation,
  selectedDate = DEFAULT_PROPS.selectedDate,
  onSelection = DEFAULT_PROPS.onSelection,
  onChange = DEFAULT_PROPS.onChange,
  elementProps = DEFAULT_PROPS.elementProps,
  minDate = DEFAULT_PROPS.minDate,
  maxDate = DEFAULT_PROPS.maxDate,
  hideOnSelection = DEFAULT_PROPS.hideOnSelection,
  showTimePicker = DEFAULT_PROPS.showTimePicker,
  showHourMinuteOption = DEFAULT_PROPS.showHourMinuteOption,
  showHourOption = DEFAULT_PROPS.showHourOption,
  showMinuteOption = DEFAULT_PROPS.showMinuteOption,
  showSecondsOption = DEFAULT_PROPS.showSecondsOption,
  showClearSelectedDate = DEFAULT_PROPS.showClearSelectedDate,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  ...rest
}) => {
  const { t } = useTranslation();

  const [formattedDate, setFormattedDate] = useState(getFormattedDate({ selectedDate, format }));

  useEffect(() => {
    setFormattedDate(getFormattedDate({ selectedDate, format }));
  }, [selectedDate]);

  const {
    open: isOpen,
    setOpen: setIsOpen,
    ...floatingLayout
  } = useFloatingLayout({
    showOnHover: false,
    showOnClick: true,
    placement: 'bottom',
  });

  const { refs } = floatingLayout;

  const onToggleClick = (evt) => {
    evt?.preventDefault();

    setIsOpen((prevState) => !prevState);

    onClick();
  };

  let validatedSelectedDate = selectedDate;

  if (selectedDate) {
    const isDateValid = dayjs(selectedDate).isValid();

    if (isDateValid) {
      validatedSelectedDate = dayjs(validatedSelectedDate).toDate();
    } else {
      validatedSelectedDate = '';
    }
  } else {
    validatedSelectedDate = '';
  }

  const getButtonText = () => {
    if (formattedDate) {
      return formattedDate;
    } else {
      return skipTranslation ? placeholder : t(placeholder);
    }
  };

  const onClearDate = (e) => {
    e?.stopPropagation?.();

    onSelection({ selectedDate: '', formattedDate: '' });

    const evt = {
      target: {
        name,
        value: '',
      },
    };

    onChange(evt);

    setIsOpen(false);
  };

  const renderInputSection = () => {
    const isDateValid = dayjs(selectedDate).isValid();

    return (
      <Button
        type={type}
        onClick={onToggleClick}
        containerClass="full-width"
        containerStyle={{ height: '32px' }}
        {...elementProps}
      >
        {getButtonText()}

        {isDateValid && showClearSelectedDate && (
          <FontAwesomeIcon icon={faTimesCircle} className="icon right" onClick={onClearDate} />
        )}
      </Button>
    );
  };

  const onDateSelection = (date, isDate) => {
    const oldDate = dayjs(validatedSelectedDate).isValid() ? dayjs(validatedSelectedDate) : dayjs();
    let newDate = dayjs(date);

    if (!isDate) {
      newDate = newDate
        .set('day', oldDate.get('day'))
        .set('month', oldDate.get('month'))
        .set('year', oldDate.get('year'));
    } else {
      newDate = newDate
        .set('hours', oldDate.get('hours'))
        .set('minutes', oldDate.get('minutes'))
        .set('seconds', oldDate.get('seconds'));
    }

    onSelection({ selectedDate: newDate.toDate(), formattedDate: newDate.format(format) });

    const evt = {
      target: {
        name,
        value: newDate.toDate(),
      },
    };

    onChange(evt);

    if (hideOnSelection || isDate) {
      setIsOpen(false);
    }
  };

  const renderDatePickerSection = () => {
    if (isOpen) {
      return (
        <FloatingContainer show={isOpen} hasArrow {...floatingLayout}>
          <div className={`inline-date-picker-container`}>
            <ReactDatePicker
              dayClassName={dayClassName}
              fixedHeight
              formatWeekDay={formatWeekDay}
              onChange={(date) => {
                onDateSelection(date, true);
              }}
              inline
              selected={validatedSelectedDate}
              minDate={minDate}
              maxDate={maxDate}
              {...rest}
            />

            {showTimePicker && (
              <TimePicker
                onSelection={(date) => {
                  onDateSelection(date, false);
                }}
                showHourMinuteOption={showHourMinuteOption}
                showHourOption={showHourOption}
                showMinuteOption={showMinuteOption}
                showSecondsOption={showSecondsOption}
                selectedDate={validatedSelectedDate}
              />
            )}
          </div>
        </FloatingContainer>
      );
    }
  };

  return (
    <Field
      ref={refs.setReference}
      label={label}
      htmlFor={name}
      containerClass={`date-picker-custom-input-container ${containerClass}`}
      containerStyle={containerStyle}
      {...rest}
    >
      {renderInputSection()}
      {renderDatePickerSection()}
    </Field>
  );
};

InlineDatePicker.propTypes = {
  format: PropTypes.string,
  type: PropTypes.string,
  elementType: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  placeholder: PropTypes.string,
  skipTranslation: PropTypes.bool,
  selectedDate: PropTypes.objectOf(Date),
  onSelection: PropTypes.func,
  onChange: PropTypes.func,
  onClick: PropTypes.func,
  elementProps: PropTypes.object,
  containerRef: PropTypes.object,
  minDate: PropTypes.objectOf(Date),
  maxDate: PropTypes.objectOf(Date),
  hideOnSelection: PropTypes.bool,
  showTimePicker: PropTypes.bool,
  showHourMinuteOption: PropTypes.bool,
  showHourOption: PropTypes.bool,
  showMinuteOption: PropTypes.bool,
  showSecondsOption: PropTypes.bool,
  showClearSelectedDate: PropTypes.bool,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

const InlineDatePickerForwardRef = (props, ref) => (
  <InlineDatePicker {...props} containerRef={ref} />
);

export default forwardRef(InlineDatePickerForwardRef);
