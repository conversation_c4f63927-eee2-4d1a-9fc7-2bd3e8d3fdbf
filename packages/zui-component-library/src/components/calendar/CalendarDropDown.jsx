import { useEffect, useState } from 'react';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getDateRange } from '../../utils/dateHelper';
import { getFormattedClassName } from '../../utils/dom';

import DropDown from '../dropdowns/DropDown';
import Items from '../dropdowns/Items';
import CustomDatePicker from './CustomDatePicker';
import { DEFAULT_CALENDAR_CONFIG, getCalendarDDList } from './helper';

const DEFAULT_PROPS = {
  level: 'REPORT',
  formatPattern: 'M/D/YYYY hh:mm:ss A',
  config: {
    ...DEFAULT_CALENDAR_CONFIG,
  },
  selectedList: [],
  setSelectedList: noop,
};

const CalendarDropDown = ({
  level = DEFAULT_PROPS.level,
  formatPattern = DEFAULT_PROPS.formatPattern,
  config = DEFAULT_PROPS.config,
  selectedList = DEFAULT_PROPS.selectedList,
  setSelectedList = DEFAULT_PROPS.setSelectedList,
  ...rest
}) => {
  const [list, setList] = useState(selectedList);
  const [timeRange, setTimeRange] = useState([]);

  const calendarConfig = {
    ...DEFAULT_CALENDAR_CONFIG,
    ...config,
  };

  useEffect(() => {
    setList(selectedList);
  }, [selectedList]);

  useEffect(() => {
    const newList = getCalendarDDList({ level, formatPattern });

    setTimeRange(newList);
  }, [level, formatPattern]);

  const renderItem = ({ itemDetail, valueKey, ...rest }) => {
    const itemValue = itemDetail[valueKey];

    const props = { itemDetail, valueKey, ...rest };

    if (itemValue == 'CUSTOM') {
      return <CustomDatePicker config={calendarConfig} selectedList={selectedList} {...props} />;
    }

    return <Items {...props} />;
  };

  const onSelection = (detail) => {
    let canHide = true;
    let canUpdate = false;

    let { value, startTime, endTime } = detail?.[0] || {};

    if (value === 'CUSTOM') {
      canHide = false;
      setList(detail);
    } else {
      detail = [{ ...(detail?.[0] || {}), ...getDateRange({ name: value, formatPattern }) }];
      startTime = detail?.[0].startTime || '';
      endTime = detail?.[0].endTime || '';
    }

    // order of canHide matter here
    if (value && ((startTime && endTime) || value === 'ALL')) {
      canUpdate = true;
      canHide = true;
    }

    if (canUpdate) {
      setSelectedList(detail);
    }

    return canHide;
  };

  const selectedItemsTextMapperFunc = ({ item, labelKey, t }) => {
    return getFormattedClassName(
      `${t(item?.[labelKey])}${item?.displayText ? `: (${item?.displayText})` : ''}`,
    );
  };

  return (
    <DropDown
      list={timeRange}
      selectedList={list}
      onSelection={onSelection}
      renderItem={renderItem}
      selectedItemsTextMapperFunc={selectedItemsTextMapperFunc}
      {...rest}
    />
  );
};

CalendarDropDown.propTypes = {
  level: PropTypes.oneOf(['ZIDENTITY_DASHBOARD', 'REPORT', 'TOKEN', 'EXPIRY']),
  formatPattern: PropTypes.string,
  config: PropTypes.object,
  selectedList: PropTypes.array,
  setSelectedList: PropTypes.func,
};

export default CalendarDropDown;
