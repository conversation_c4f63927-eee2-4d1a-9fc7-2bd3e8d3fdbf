import { useEffect, useState } from 'react';

import dayjs from 'dayjs';
import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getDatesInRange } from '../../utils/dateHelper';

import DropDown from '../dropdowns/DropDown';

const today = dayjs().startOf('day');

const midDayToday = today.add(12, 'hours');

const startOfHourMinuteOption = today.add(1, 'hours').add(30, 'minutes');

const hourMinuteFormat = 'h:m';

const eachHourMinuteTillMidDay = [...Array(12).keys()]
  .map((index) => startOfHourMinuteOption.add(index, 'hours').toDate())
  .map((hourMinuteDate) => ({
    value: dayjs(hourMinuteDate).get('hours') + ':' + dayjs(hourMinuteDate).get('minutes'),
    label: dayjs(hourMinuteDate).format(hourMinuteFormat),
  }));

const hourMinuteOptionsList = [...eachHourMinuteTillMidDay];

const hourFormat = 'h';

const eachHourTillMidDay = getDatesInRange({
  startDate: dayjs(today),
  endDate: midDayToday,
  amount: 1,
  unit: 'hours',
}).map((hourDate) => ({
  value: dayjs(hourDate).get('hours'),
  label: dayjs(hourDate).format(hourFormat),
}));

const hourOptionsList = [...eachHourTillMidDay];

const minuteFormat = 'mm';

const eachMinutesInHour = [...Array(60).keys()].map((index) => {
  const minute = dayjs(today).add(index, 'minutes');

  return {
    value: dayjs(minute).get('minutes'),
    label: dayjs(minute).format(minuteFormat),
  };
});

const secondsFormat = 'ss';

const eachSecondsInMinute = [...Array(60).keys()].map((index) => {
  const seconds = dayjs(today).add(index, 'seconds');

  return {
    value: dayjs(seconds).get('seconds'),
    label: dayjs(seconds).format(secondsFormat),
  };
});

const meridiemFormat = 'A';

const meridiemOptionsList = [
  { value: 'AM', label: 'AM' },
  { value: 'PM', label: 'TIME_PM' },
];

const floatingConfig = {
  middleware: {
    hide: true,
  },
};

const DEFAULT_PROPS = {
  selectedDate: new Date(),
  onSelection: noop,
  showHourMinuteOption: true,
  hourMinuteList: hourMinuteOptionsList,
  showHourOption: false,
  hourList: hourOptionsList,
  showMinuteOption: false,
  minuteList: eachMinutesInHour,
  showSecondsOption: false,
  secondsList: eachSecondsInMinute,
  showMeridiemOption: true,
  meridiemList: meridiemOptionsList,
};

const TimePicker = ({
  selectedDate = DEFAULT_PROPS.selectedDate,
  onSelection = DEFAULT_PROPS.onSelection,
  showHourMinuteOption = DEFAULT_PROPS.showHourMinuteOption,
  hourMinuteList = DEFAULT_PROPS.hourMinuteList,
  showHourOption = DEFAULT_PROPS.showHourOption,
  hourList = DEFAULT_PROPS.hourList,
  showMinuteOption = DEFAULT_PROPS.showMinuteOption,
  minuteList = DEFAULT_PROPS.minuteList,
  showSecondsOption = DEFAULT_PROPS.showSecondsOption,
  secondsList = DEFAULT_PROPS.secondsList,
  showMeridiemOption = DEFAULT_PROPS.showMeridiemOption,
  meridiemList = DEFAULT_PROPS.meridiemList,
}) => {
  const [selectedHourMinuteList, setSelectedHourMinuteList] = useState([]);
  const [selectedHourList, setSelectedHourList] = useState([]);
  const [selectedMinuteList, setSelectedMinuteList] = useState([]);
  const [selectedSecondsList, setSelectedSecondsList] = useState([]);
  const [selectedMeridiemList, setSelectedMeridiemList] = useState([]);

  const [isUpdatingDate, setIsUpdatingDate] = useState(false);

  const updateDate = () => {
    let hour = 0;
    let minutes = 0;
    let seconds = 0;
    let meridiem = 'AM';

    if (showHourMinuteOption) {
      const [{ value } = {}] = selectedHourMinuteList;

      if (value) {
        const [hourSplit, minuteSplit] = value.split(':');

        hour = +hourSplit;
        minutes = +minuteSplit;
      }
    } else {
      if (showHourOption) {
        const [{ value = 1 } = {}] = selectedHourList;

        if (value) {
          hour = value;
        }
      }

      if (showMinuteOption) {
        const [{ value = 0 } = {}] = selectedMinuteList;

        if (value) {
          minutes = value;
        }
      }
    }

    if (showSecondsOption) {
      const [{ value = 0 } = {}] = selectedSecondsList;

      if (value) {
        seconds = value;
      }
    }

    if (showMeridiemOption) {
      const [{ value = 'AM' } = {}] = selectedMeridiemList;

      if (value) {
        meridiem = value;
      }
    }

    let parsedDate = new Date(selectedDate);

    parsedDate = dayjs(parsedDate)
      .set('hours', meridiem === 'AM' ? hour : hour + 12)
      .toDate();
    parsedDate = dayjs(parsedDate).set('minutes', minutes).toDate();
    parsedDate = dayjs(parsedDate).set('seconds', seconds).toDate();

    setIsUpdatingDate(false);
    onSelection(parsedDate);
  };

  useEffect(() => {
    if (isUpdatingDate) updateDate();
  }, [isUpdatingDate]);

  const onHourMinuteSelection = (data) => {
    setSelectedHourMinuteList(data);
    setIsUpdatingDate(true);
  };

  const onHourSelection = (data) => {
    setSelectedHourList(data);
    setIsUpdatingDate(true);
  };

  const onMinuteSelection = (data) => {
    setSelectedMinuteList(data);
    setIsUpdatingDate(true);
  };

  const onSecondsSelection = (data) => {
    setSelectedSecondsList(data);
    setIsUpdatingDate(true);
  };

  const onMeridiemSelection = (data) => {
    setSelectedMeridiemList(data);
    setIsUpdatingDate(true);
  };

  useEffect(() => {
    const selectedHour = dayjs(selectedDate).get('hours') % 12 || 12;
    const selectedMinute = dayjs(selectedDate).get('minutes');
    const selectedSeconds = dayjs(selectedDate).get('seconds');

    const selectedHourMinute = selectedHour + ':' + selectedMinute;

    const hourMinuteData = hourMinuteList.find(
      (hourMinute) => hourMinute.value === selectedHourMinute,
    );

    if (hourMinuteData) setSelectedHourMinuteList([hourMinuteData]);

    const hourData = hourList.find((hour) => hour.value === selectedHour);

    if (hourData) setSelectedHourList([hourData]);

    const minuteData = minuteList.find((minute) => minute.value === selectedMinute);

    if (minuteData) setSelectedMinuteList([minuteData]);

    const secondsData = secondsList.find((seconds) => seconds.value === selectedSeconds);

    if (secondsData) setSelectedSecondsList([secondsData]);

    const selectedMeridiem = dayjs(selectedDate).format(meridiemFormat);

    const meridiemData = meridiemList.find((meridiem) => meridiem.value === selectedMeridiem);

    if (meridiemData) setSelectedMeridiemList([meridiemData]);
  }, [selectedDate]);

  return (
    <article className="time-picker-container is-flex has-jc-c">
      {showHourMinuteOption && (
        <DropDown
          list={hourMinuteList}
          selectedList={selectedHourMinuteList}
          onSelection={onHourMinuteSelection}
          hasSearch={false}
          floatingConfig={floatingConfig}
        />
      )}

      {!showHourMinuteOption && showHourOption && (
        <DropDown onSelection={onHourSelection} selectedList={selectedHourList} list={hourList} />
      )}

      {!showHourMinuteOption && showMinuteOption && (
        <DropDown
          onSelection={onMinuteSelection}
          selectedList={selectedMinuteList}
          list={minuteList}
          hasSearch={false}
          floatingConfig={floatingConfig}
        />
      )}

      {showSecondsOption && (
        <DropDown
          onSelection={onSecondsSelection}
          selectedList={selectedSecondsList}
          list={secondsList}
          hasSearch={false}
          floatingConfig={floatingConfig}
        />
      )}

      {showMeridiemOption && (
        <DropDown
          onSelection={onMeridiemSelection}
          selectedList={selectedMeridiemList}
          list={meridiemList}
          hasSearch={false}
          floatingConfig={floatingConfig}
        />
      )}
    </article>
  );
};

TimePicker.propTypes = {
  selectedDate: PropTypes.objectOf(Date),
  onSelection: PropTypes.func,
  showHourMinuteOption: PropTypes.bool,
  hourMinuteList: PropTypes.arrayOf(Object),
  showHourOption: PropTypes.bool,
  hourList: PropTypes.arrayOf(Object),
  showMinuteOption: PropTypes.bool,
  minuteList: PropTypes.arrayOf(Object),
  showSecondsOption: PropTypes.bool,
  secondsList: PropTypes.arrayOf(Object),
  showMeridiemOption: PropTypes.bool,
  meridiemList: PropTypes.arrayOf(Object),
};

export default TimePicker;
