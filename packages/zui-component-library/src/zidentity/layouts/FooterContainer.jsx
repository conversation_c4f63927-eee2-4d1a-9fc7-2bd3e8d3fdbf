import { useRef } from 'react';

import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import dayjs from 'dayjs';
import { isString } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../components/cards';
import { Tooltip } from '../../components/tooltip';

const DATE_FORMAT = 'MMMM DD, YYYY - hh:mm A';

const DEFAULT_PROPS = {
  buildVersion: '',
  buildTime: '',
  dateFormat: DATE_FORMAT,
  dataPolicyLink: '',
};

const isLinkValid = (dataPolicyLink) => {
  return dataPolicyLink && isString(dataPolicyLink);
};

const FooterContainer = ({
  buildVersion = DEFAULT_PROPS.buildVersion,
  buildTime = DEFAULT_PROPS.buildTime,
  dateFormat = DEFAULT_PROPS.dateFormat,
  dataPolicyLink = DEFAULT_PROPS.dataPolicyLink,
}) => {
  const buildVersionRef = useRef();

  const uiBuildTimestamp = dayjs.unix(+buildTime);

  const isDateValid = buildTime && uiBuildTimestamp.isValid();

  const isDataPolicyLinkValid = isLinkValid(dataPolicyLink);

  return (
    <footer className="footer-container">
      <div className="left-section">
        <div className="copyright-section">
          Copyright©2007-{new Date().getFullYear()} Zscaler Inc. All rights reserved.
        </div>

        <div className="build-version">
          Version {buildVersion}
          {isDateValid && (
            <FontAwesomeIcon ref={buildVersionRef} icon={faInfoCircle} className="icon right" />
          )}
        </div>

        {isDateValid && (
          <Tooltip elementRef={buildVersionRef}>
            <Card>
              <p> {uiBuildTimestamp?.format?.(dateFormat)} </p>
            </Card>
          </Tooltip>
        )}

        {isDataPolicyLinkValid && (
          <a
            className="data-policy"
            href={dataPolicyLink}
            target="_blank"
            rel="noopener noreferrer"
          >
            Data Policy
          </a>
        )}
      </div>
    </footer>
  );
};

FooterContainer.propTypes = {
  buildVersion: PropTypes.string,
  buildTime: PropTypes.string,
  dateFormat: PropTypes.string,
  dataPolicyLink: PropTypes.string,
};

export default FooterContainer;
