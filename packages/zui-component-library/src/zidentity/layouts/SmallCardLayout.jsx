import PropTypes from 'prop-types';

const SmallCardLayout = ({
  showFooter = true,
  supportLink = 'https://www.zscaler.com/privacy/faq',
  containerClass = '',
  containerStyle = {},
  children,
}) => {
  const renderFooterSection = () => {
    if (!showFooter) {
      return null;
    }

    return (
      <footer className="footer-container has-jc-e">
        <div className="copyright-section typography-paragraph2">
          Copyright©2007-{new Date().getFullYear()} Zscaler Inc. All rights reserved. | &nbsp;
          <a
            href={supportLink}
            className="typography-paragraph2 text-white"
            target={'_blank'}
            rel="noreferrer"
            style={{ textDecoration: 'none' }}
          >
            Zscaler Support
          </a>
        </div>
      </footer>
    );
  };

  return (
    <div className={`small-card-layout-container ${containerClass}`} style={containerStyle}>
      <div className="small-card-content-container">{children}</div>

      {renderFooterSection()}
    </div>
  );
};

SmallCardLayout.propTypes = {
  showFooter: PropTypes.bool,
  supportLink: PropTypes.string,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  children: PropTypes.node.isRequired,
};

export default SmallCardLayout;
