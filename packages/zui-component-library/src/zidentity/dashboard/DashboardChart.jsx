import PropTypes from 'prop-types';

import { BarChart, LineChart } from '../../components/charts';
import NoData<PERSON>hart from '../../components/charts/NoDataChart';

const DEFAULT_PROPS = {
  chartType: 'BAR',
  data: [],
  renderNoDataChart: (props) => <NoDataChart {...props} />,
  noDataProps: {},
  containerClass: '',
  containerStyle: {},
};

const DashboardChart = ({
  chartType = DEFAULT_PROPS.chartType,
  data = DEFAULT_PROPS.data,
  renderNoDataChart = DEFAULT_PROPS.renderNoDataChart,
  noDataProps = DEFAULT_PROPS.noDataProps,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  ...config
}) => {
  const renderChartType = () => {
    if (data?.length === 0) {
      return renderNoDataChart(noDataProps);
    }

    if (chartType === 'LINE') {
      return <LineChart data={data} {...config} />;
    }

    return <BarChart data={data} {...config} />;
  };

  return (
    <div className={`dashboard-chart ${containerClass}`} style={containerStyle}>
      {renderChartType()}
    </div>
  );
};

DashboardChart.propTypes = {
  data: PropTypes.array,
  chartType: PropTypes.oneOf(['BAR', 'LINE']),
  renderNoDataChart: PropTypes.func,
  noDataProps: PropTypes.object,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default DashboardChart;
