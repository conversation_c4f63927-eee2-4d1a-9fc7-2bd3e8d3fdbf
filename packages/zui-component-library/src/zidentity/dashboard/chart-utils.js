import dayjs from 'dayjs';

import { CHART_COLORS } from '../../components/charts/helper';

export const getEntriesDetail = ({ entries = [] }) => {
  const formattedEntries = [];
  const legends = [];

  entries.forEach((entry, idx) => {
    const { name, total, trend = {} } = entry || {};

    legends.push({ id: name, name, isSelected: true, color: CHART_COLORS[idx] });

    const formattedEntry = {
      id: name,
      color: CHART_COLORS[idx],
      origin: entry,
    };

    if (trend?.trendInterval) {
      formattedEntry.data = [];

      const { trendInterval, trendStartTime, trendValues = [] } = trend || {};

      trendValues.map((value, idx) => {
        const newData = {
          x: dayjs.unix(trendStartTime + idx * trendInterval).toDate(),
          y: value,
        };

        formattedEntry.data.push(newData);
      });
    } else {
      formattedEntry.name = name;
      formattedEntry.value = total;
    }

    formattedEntries.push(formattedEntry);
  });

  return { formattedEntries, legends };
};

export const CHART_MAX_DATA_POINTS = {
  FIFTY: 50,
  HUNDRED: 100,
};

const TREND_BASIC_UNIT = 864;

export const getTimePayloadDetail = ({
  selectedTimeRange = {},
  maxEntries = CHART_MAX_DATA_POINTS.FIFTY,
}) => {
  const { startTime, endTime, value } = selectedTimeRange;

  let payload = {
    startTime: startTime,
    endTime: endTime,
    trendInterval: TREND_BASIC_UNIT,
    format: 'MMM DD YYYY',
  };

  if (value === 'ONE_DAY') {
    payload.format = 'MMM DD HH:mm';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2;
    }
  }

  if (value === 'TWO_DAYS') {
    payload.format = 'MMM DD HH:mm';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2 * 2;
    } else {
      payload.trendInterval = TREND_BASIC_UNIT * 2;
    }
  }

  if (value === 'SEVEN_DAYS') {
    payload.format = 'MMM DD HH:mm';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2 * 7;
    } else {
      payload.trendInterval = TREND_BASIC_UNIT * 7;
    }
  }

  if (value === 'FOURTEEN_DAYS') {
    payload.format = 'MMM DD HH:mm';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2 * 14;
    } else {
      payload.trendInterval = TREND_BASIC_UNIT * 14;
    }
  }

  if (value === 'THIRTY_DAYS') {
    payload.format = 'MMM DD YYYY';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2 * 30;
    } else {
      payload.trendInterval = TREND_BASIC_UNIT * 30;
    }
  }

  if (value === 'SIXTY_DAYS') {
    payload.format = 'MMM DD YYYY';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2 * 60;
    } else {
      payload.trendInterval = TREND_BASIC_UNIT * 60;
    }
  }

  if (value === 'NINTY_DAYS') {
    payload.format = 'MMM DD YYYY';

    if (maxEntries === CHART_MAX_DATA_POINTS.FIFTY) {
      payload.trendInterval = TREND_BASIC_UNIT * 2 * 90;
    } else {
      payload.trendInterval = TREND_BASIC_UNIT * 90;
    }
  }

  return payload;
};
