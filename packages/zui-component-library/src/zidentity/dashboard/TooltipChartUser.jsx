import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

import { Card } from '../../components/cards';

const DEFAULT_PROPS = {
  xAxisSliceDetail: {},
};

const TooltipChartUser = ({ xAxisSliceDetail = DEFAULT_PROPS.xAxisSliceDetail }) => {
  const { t } = useTranslation();

  const { data } = xAxisSliceDetail;

  const { name, value } = data || {};

  const renderData = () => {
    return (
      <div className="is-flex has-fd-c" style={{ gap: '6px' }}>
        <div>{t(name)}</div>

        <div>{value} Users</div>
      </div>
    );
  };

  return <Card containerStyle={{ gap: '6px' }}>{renderData()}</Card>;
};

TooltipChartUser.propTypes = {
  xAxisSliceDetail: PropTypes.object,
  format: PropTypes.string,
};

export default TooltipChartUser;
