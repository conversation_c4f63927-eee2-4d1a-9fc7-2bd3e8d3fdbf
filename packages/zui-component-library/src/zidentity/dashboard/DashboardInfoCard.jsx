import { useTranslation } from 'react-i18next';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  icon: null,
  iconIsImage: true,
  iconProps: {},
  label: '',
  list: [],
  containerClass: '',
  containerStyle: {},
};

const DashboardInfoCard = ({
  icon = DEFAULT_PROPS.icon,
  iconIsImage = DEFAULT_PROPS.iconIsImage,
  iconProps = DEFAULT_PROPS.iconProps,
  label = DEFAULT_PROPS.label,
  list = DEFAULT_PROPS.list,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
}) => {
  const { t } = useTranslation();

  const getFormattedNumber = (count = 0) => {
    return new Intl.NumberFormat().format(count);
  };

  return (
    <div className={`dashboard-card ${containerClass}`} style={containerStyle}>
      <div className="icon-containers">
        {iconIsImage ? (
          <img src={icon} style={{ height: '100%' }} alt={label} {...iconProps} />
        ) : (
          <FontAwesomeIcon icon={icon} {...iconProps} />
        )}
      </div>

      <div className="label-container">{t(label)}</div>

      <div className="list-container">
        {list.map(({ id, name, count }) => (
          <span key={id || name} className="detail-container">
            <div className="value-container"> {getFormattedNumber(count)}</div>
            <div className="name-container">{name} </div>
          </span>
        ))}
      </div>
    </div>
  );
};

DashboardInfoCard.propTypes = {
  icon: PropTypes.any,
  iconIsImage: PropTypes.bool,
  iconProps: PropTypes.object,
  label: PropTypes.string,
  list: PropTypes.array,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default DashboardInfoCard;
