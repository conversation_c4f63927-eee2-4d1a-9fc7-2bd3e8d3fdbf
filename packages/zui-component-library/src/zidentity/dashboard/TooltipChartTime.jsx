import { useTranslation } from 'react-i18next';

import { faCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import dayjs from 'dayjs';
import { find } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../components/cards';

const DEFAULT_PROPS = {
  xAxisSliceDetail: {},
  format: 'MMM DD YYYY hh:mm:ss A',
  legends: [],
};

const TooltipChartTime = ({
  xAxisSliceDetail = DEFAULT_PROPS.xAxisSliceDetail,
  format = DEFAULT_PROPS.format,
  legends = DEFAULT_PROPS.legends,
}) => {
  const { t } = useTranslation();

  const { slice } = xAxisSliceDetail;

  const { points = [] } = slice || {};

  const chartTime = points[0]?.data || {};

  const formattedDate = dayjs(chartTime.x).format(format);

  const renderPoint = ({ serieColor, serieId, data: { y } = {} }) => {
    const { color } = find(legends, { id: serieId }) || { color: serieColor };

    return (
      <div key={serieId} className="is-flex " style={{ gap: '6px' }}>
        <FontAwesomeIcon icon={faCircle} style={{ color }} />
        <span style={{ color }}>{t(serieId)}</span>
        <span> {y} </span>
      </div>
    );
  };

  const renderPoints = () => {
    return (
      <div className="is-flex has-fd-c" style={{ gap: '6px' }}>
        {points.map((data) => renderPoint(data))}
      </div>
    );
  };

  return (
    <Card containerStyle={{ gap: '6px' }}>
      <div> {formattedDate} </div>

      {renderPoints()}
    </Card>
  );
};

TooltipChartTime.propTypes = {
  xAxisSliceDetail: PropTypes.object,
  format: PropTypes.string,
  legends: PropTypes.array,
};

export default TooltipChartTime;
