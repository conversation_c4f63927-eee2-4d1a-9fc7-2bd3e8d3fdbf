import { useTranslation } from 'react-i18next';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { Card } from '../../components/cards';

const DEFAULT_PROPS = {
  title: '',
  containerClass: '',
  containerStyle: {},
  renderTabsSection: noop,
  renderDropdownSection: noop,
  renderLegendsSection: noop,
};

const DashboardChartContainer = ({
  title = DEFAULT_PROPS.title,
  renderTabsSection = DEFAULT_PROPS.renderTabsSection,
  renderDropdownSection = DEFAULT_PROPS.renderDropdownSection,
  renderLegendsSection = DEFAULT_PROPS.renderLegendsSection,
  containerClass = DEFAULT_PROPS.containerClass,
  containerStyle = DEFAULT_PROPS.containerStyle,
  children,
}) => {
  const { t } = useTranslation();

  return (
    <Card
      containerClass={`dashboard-chart-container ${containerClass}`}
      containerStyle={containerStyle}
    >
      <div className="is-flex has-ai-c" style={{ gap: '10%' }}>
        {title && <div className="label-container">{t(title)}</div>}
        {renderTabsSection?.()}
        {renderDropdownSection?.()}
      </div>

      <div className="is-flex full-width has-ai-c" style={{ gap: '12px' }}>
        {children}
        <div className="legends-container">{renderLegendsSection?.()}</div>
      </div>
    </Card>
  );
};

DashboardChartContainer.propTypes = {
  title: PropTypes.string,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
  renderTabsSection: PropTypes.func,
  renderDropdownSection: PropTypes.func,
  renderLegendsSection: PropTypes.func,
  children: PropTypes.element,
};

export default DashboardChartContainer;
