import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

import AppLogo from './components/AppLogo';
import ExpandedNavMenu from './components/ExpandedNavMenu';
import InputSearch from './components/InputSearch';
import NavigationBarSelect from './components/NavigationBarSelect';
import SearchResult from './components/SearchResult';
import ToggleNavBar from './components/ToggleNavBar';

import { checkRelatedRoutes, recursiveSearch } from './utils';

const NavigationBar = ({
  NAVIGATION_MENU = [],
  appLogo = '',
  appName = '',
  onNavigate = () => {},
  onDispatch = () => {},
}) => {
  const { t } = useTranslation();
  const availableNavigationItems = NAVIGATION_MENU;

  const [menuStatus, setMenuStatus] = useState(() => {
    const newState = {
      minimize: JSON.parse(localStorage.getItem('leftMenuMinimize')),
    };

    return newState;
  });
  const [mainLoading, setMainLoading] = useState(null);
  const [navItemLoading, setNavItemLoading] = useState(null);
  const [searchedMenu, setSearchedMenu] = useState({
    resetKey: 0,
    data: null,
    searchedKey: '',
  });
  const [openedMenu, setOpenedMenu] = useState(() => {
    const openItems = availableNavigationItems.filter(checkRelatedRoutes);
    return openItems.length > 0 ? openItems[0].name : '';
  });

  const { hovering, minimize } = menuStatus;

  const mergeMenuStatus = (status) => {
    // eslint-disable-next-line no-prototype-builtins
    if (status.hasOwnProperty('minimize')) {
      localStorage.setItem('leftMenuMinimize', status.minimize);
    }
    setMenuStatus((prevState) => {
      const newState = {
        ...prevState,
        ...status,
      };

      return newState;
    });
  };

  const handleSearchOnChange = (searchKey) => {
    setSearchedMenu((prev) => ({
      ...prev,
      searchedKey: searchKey || '',
      data: searchKey ? recursiveSearch(availableNavigationItems, searchKey) : null,
    }));
  };

  const handleClick = (e, item) => {
    e.stopPropagation();
    if (item.subMenu) {
      setOpenedMenu((prevState) => {
        if (prevState === item.name) {
          // If the clicked menu is already open, close it
          return '';
        }
        // Otherwise, open the clicked menu
        return item.name;
      });
    } else if (item.navigationURL) {
      onNavigate(item.navigationURL);
    }
    if (typeof item.onClick === 'function') {
      return item.onClick({
        navigate: onNavigate,
        dispatch: onDispatch,
        setLoading: (status) => setNavItemLoading(status && item.name),
        setMainLoading: (status) => setMainLoading(status),
      });
    }
  };
  return (
    <>
      <div
        className={`
        navigation-bar
        ${hovering ? ' hovering' : ''}
        ${minimize ? ' minimize' : ''}
      `
          .trim()
          .split(/[\s,\t,\n]+/)
          .join(' ')}
        onMouseEnter={() => minimize && mergeMenuStatus({ hovering: true })}
        onMouseLeave={() => mergeMenuStatus({ hovering: false })}
      >
        <AppLogo appLogo={appLogo} />
        <NavigationBarSelect appName={appName} />
        <div className="input-search">
          <InputSearch
            inputProps={{
              placeholder: t('SEARCH'),
              onChange: handleSearchOnChange,
            }}
          />
          {searchedMenu?.searchedKey && (
            <SearchResult
              handleItemClick={handleClick}
              searchedMenu={searchedMenu}
              setSearchedMenu={setSearchedMenu}
            />
          )}
        </div>
        <ExpandedNavMenu
          openedMenu={openedMenu}
          handleItemClick={handleClick}
          navItemLoading={navItemLoading}
          navigationMenu={availableNavigationItems}
          onNavigate={onNavigate}
          onDispatch={onDispatch}
        />
        <ToggleNavBar
          minimizeMenu={!!minimize}
          setMinimizeMenu={(value) => mergeMenuStatus({ minimize: value, hovering: false })}
        />
        {mainLoading && (
          <div className="left-navigation-loader">
            <>...Loading </>
          </div>
        )}
      </div>
      {hovering && <div style={{ width: '5rem' }} />}
    </>
  );
};

NavigationBar.propTypes = {
  appLogo: PropTypes.any,
  appName: PropTypes.string,
  NAVIGATION_MENU: PropTypes.array,
  onNavigate: PropTypes.func,
  onDispatch: PropTypes.func,
};

export default NavigationBar;
