import { useEffect } from 'react';

import { faChevronRight } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import HighlightedText from '../highlightedText';
import NoDataMessageWrapper from '../noDataMessageWrapper';

const SearchResult = ({
  handleItemClick = () => null,
  searchedMenu = {},
  setSearchedMenu = () => null,
}) => {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' || e.keyCode === 27) {
        setSearchedMenu({
          resetKey: null,
          data: null,
          searchedKey: '',
        });
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, []);

  const handleClick = (e, itemIndex, itemsList) => {
    // eslint-disable-next-line no-plusplus,no-shadow
    for (let i = 0, len = itemsList.length; i < len; ++i) {
      if (i > itemIndex) return;
      handleItemClick(e, itemsList[i], true);
    }

    setSearchedMenu((prev) => ({
      resetKey: prev.resetKey + 1 || 0,
      data: null,
      searchedKey: '',
    }));
  };

  return (
    <div className="search-result">
      <NoDataMessageWrapper data={searchedMenu.data}>
        {searchedMenu.data.map((items, index) => {
          /* eslint-disable react/no-array-index-key */
          const isDeep = items.length > 2;
          const [firstElem, ...rest] = items;
          const itemList = isDeep ? rest : items;
          return (
            <div key={index} className={`search-result-item${!isDeep ? ' shallow' : ''}`}>
              {isDeep && (
                <p className="top-level-item">
                  <HighlightedText text={firstElem.name} searchString={searchedMenu.searchedKey} />
                </p>
              )}
              <p className="sub-level-items">
                {itemList.map((child, i) => (
                  <span key={child.name}>
                    <span
                      className={!isDeep && !i ? 'top-level-item' : 'sub-level-item'}
                      onClick={(e) => handleClick(e, i, itemList)}
                      onKeyDown={noop}
                    >
                      <HighlightedText text={child.name} searchString={searchedMenu.searchedKey} />
                    </span>
                    {(isDeep ? rest : items).length - 1 !== i && (
                      <span className="sub-level-arrow-icon">
                        <FontAwesomeIcon icon={faChevronRight} />
                      </span>
                    )}
                  </span>
                ))}
              </p>
            </div>
          );
        })}
      </NoDataMessageWrapper>
    </div>
  );
};

SearchResult.propTypes = {
  handleItemClick: PropTypes.func,
  setSearchedMenu: PropTypes.func,
  searchedMenu: PropTypes.shape({
    data: PropTypes.arrayOf(PropTypes.shape({})),
    searchedKey: PropTypes.string,
  }),
};

SearchResult.defaultProps = {
  handleItemClick: () => null,
  setSearchedMenu: () => null,
  searchedMenu: {
    data: [],
    searchedKey: '',
  },
};

export default SearchResult;
