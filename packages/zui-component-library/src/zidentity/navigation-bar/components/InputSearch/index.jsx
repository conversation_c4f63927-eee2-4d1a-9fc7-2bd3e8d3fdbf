import { useEffect, useState } from 'react';

import PropTypes from 'prop-types';

import { Search } from '../../../../components/forms';

import useDebounce from '../../utils/customHooks/useDebounce';

const DEFAULT_PROPS = {
  defaultValue: '',
  debounceTime: 500,
  inputProps: {
    onChange: (e) => e,
  },
  startSearchLength: 0,
};

const InputSearch = ({
  inputProps = DEFAULT_PROPS.inputProps,
  debounceTime = DEFAULT_PROPS.debounceTime,
  startSearchLength = DEFAULT_PROPS.startSearchLength,
  defaultValue = DEFAULT_PROPS.defaultValue,
}) => {
  const [value, setSearchValue] = useState(defaultValue || null);
  const debouncedSearch = useDebounce(inputProps.onChange, { wait: debounceTime }, []);

  useEffect(() => {
    if (value === null) {
      return;
    }
    if (value && value.length < startSearchLength) {
      return;
    }
    debouncedSearch(value);
  }, [value]);

  const handleOnChange = ({ target: { value: val } }) => {
    setSearchValue(val);
  };

  return (
    <Search
      searchIconVisible={false}
      onSearch={(term) => {
        setSearchValue(term);
      }}
      onChange={handleOnChange}
      term={value}
    />
  );
};

InputSearch.propTypes = {
  defaultValue: PropTypes.string,
  debounceTime: PropTypes.number,
  inputProps: PropTypes.any,
  startSearchLength: PropTypes.number,
  themeColor: PropTypes.string,
  validation: PropTypes.PropTypes.func,
  data: PropTypes.number,
};

export default InputSearch;
