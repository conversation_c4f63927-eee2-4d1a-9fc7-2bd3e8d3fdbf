import { faChevronLeft, faChevronRight } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import PropTypes from 'prop-types';

import { Button } from '../../../../components/buttons';

const DEFAULT_PROPS = {
  minimizeMenu: false,
  setMinimizeMenu: (bool) => bool,
};

const ToggleNavBar = ({
  minimizeMenu = DEFAULT_PROPS.minimizeMenu,
  setMinimizeMenu = DEFAULT_PROPS.setMinimizeMenu,
}) => (
  <div className="toggle-nav-bar">
    <Button
      type="secondary"
      onClick={(event) => {
        event.stopPropagation();
        return setMinimizeMenu(!minimizeMenu);
      }}
    >
      <FontAwesomeIcon icon={minimizeMenu ? faChevronRight : faChevronLeft} />
    </Button>
  </div>
);

ToggleNavBar.propTypes = {
  minimizeMenu: PropTypes.bool,
  setMinimizeMenu: PropTypes.func,
};

export default ToggleNavBar;
