import { Component } from 'react';
import uniqueId from 'react-html-id';

import PropTypes from 'prop-types';

export class HighlightedText extends Component {
  constructor() {
    super();
    uniqueId.enableUniqueIds(this);
  }

  render() {
    const { searchString, text } = this.props;
    if (!searchString) return text;
    // Split on higlight term and include term into parts, ignore case
    // eslint-disable-next-line no-useless-escape
    const invalid = /[°"§%()\[\]{}=\\?´`'#<>|,;.:+_-]+/g;
    const updatedSearchString = searchString.replace(invalid, '');
    const parts = text.split(new RegExp(`(${updatedSearchString})`, 'gi'));
    return (
      <span>
        {parts.map((part) => (
          <span
            key={this.nextUniqueId()}
            style={part.toLowerCase() === searchString.toLowerCase() ? { fontWeight: 'bold' } : {}}
          >
            {part}
          </span>
        ))}
      </span>
    );
  }
}

HighlightedText.propTypes = {
  text: PropTypes.string,
  searchString: PropTypes.string,
};

HighlightedText.defaultProps = {
  text: '',
  searchString: '',
};

export default HighlightedText;
