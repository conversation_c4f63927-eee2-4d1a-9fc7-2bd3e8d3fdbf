import { useTranslation } from 'react-i18next';

import { isEmpty } from 'lodash-es';
import PropTypes from 'prop-types';

const DEFAULT_PROPS = {
  data: null,
  message: 'NO_ITEMS_FOUND',
  messageProps: {},
  children: null,
};

const NoDataMessageWrapper = ({
  data = DEFAULT_PROPS.data,
  message = DEFAULT_PROPS.message,
  messageProps = DEFAULT_PROPS.messageProps,
  children = DEFAULT_PROPS.children,
}) => {
  const { t } = useTranslation();

  return data && !isEmpty(data) ? (
    children
  ) : (
    <div className="message-wrapper">
      <p>{t(message, { ...messageProps })}</p>
    </div>
  );
};

NoDataMessageWrapper.propTypes = {
  data: PropTypes.instanceOf(Object),
  children: PropTypes.instanceOf(Object),
  message: PropTypes.string,
  messageProps: PropTypes.shape({}),
};

export default NoDataMessageWrapper;
