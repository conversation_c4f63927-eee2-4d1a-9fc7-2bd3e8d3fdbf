import { useState } from 'react';
import { withTranslation } from 'react-i18next';

import { faAngleRight } from '@fortawesome/free-solid-svg-icons';
import { faSortDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { checkRelatedRoutes } from '../../utils';
import HoveringPanel from '../HoveringPanel';

const ExpandedNavMenu = ({
  openedMenu = [],
  navigationMenu = [],
  navItemLoading = null,
  handleItemClick = () => null,
  t = (str) => str,
  onNavigate = () => {},
  onDispatch = () => {},
}) => {
  const [hoveringMenuItem, setHoveringMenuItem] = useState(null);

  const buildNavMenu = (navMenu = [], level = 0) =>
    navMenu.map((item) => {
      const { name, subMenu, iconProps, onHoverMenu, separateLine, OnHoverComponent, isNew } = item;
      const isOpen = openedMenu?.includes(name);
      const isSelected = checkRelatedRoutes(item);
      const isLoading = navItemLoading === name;
      const itemClassName = `
      navigation-item
      ${isOpen ? ' open' : ''}
      ${separateLine ? ' line' : ''}
      ${isLoading ? ' loading' : ''}
      ${isSelected ? ' selected' : ''}
      ${level === 0 ? ' top-level' : ' sub-level'}
      ${hoveringMenuItem?.name === name ? ' hovering' : ''}
      ${iconProps?.icon ? `has-icon ${iconProps?.position || ''}` : ''}
    `
        .trim()
        .split(/[\s,\t,\n]+/)
        .join(' ');

      // change icon based on selected state
      let icon = iconProps?.icon;
      if (isSelected && iconProps?.activeicon) {
        icon = iconProps.activeicon;
      }

      return (
        <div
          key={name}
          className={itemClassName}
          onClick={(e) => handleItemClick(e, item)}
          onKeyDown={noop}
          onMouseEnter={() => onHoverMenu && setHoveringMenuItem(item)}
          onMouseLeave={() => onHoverMenu && setHoveringMenuItem(null)}
          style={{ position: OnHoverComponent ? 'initial' : 'relative' }}
        >
          <div className="name-icon-wrapper">
            {((iconProps?.icon && iconProps?.position !== 'right') || isLoading) && (
              <span className={`icon-wrapper${isLoading ? ' loading' : ''}`}>
                {isLoading ? <>...Loading</> : <FontAwesomeIcon {...iconProps} icon={icon} />}
              </span>
            )}
            <span className="name">
              {name}
              {isNew && <span className="new-badge">{t('NEW').toUpperCase()}</span>}
            </span>
            {iconProps?.icon && iconProps?.position === 'right' && (
              <span className="right-icon-wrapper">
                <FontAwesomeIcon {...iconProps} icon={icon} />
              </span>
            )}
            {onHoverMenu && subMenu?.length ? (
              <span className="arrow-icon-wrapper-hover-menu">
                <FontAwesomeIcon icon={faAngleRight} />
              </span>
            ) : null}
            {!onHoverMenu && subMenu?.length ? (
              <span className="arrow-icon-wrapper">
                <FontAwesomeIcon icon={faSortDown} />
              </span>
            ) : null}
          </div>
          {!onHoverMenu && isOpen && buildNavMenu(subMenu, level + 1)}
          {onHoverMenu && hoveringMenuItem?.name === name && (
            <HoveringPanel
              menu={hoveringMenuItem}
              onNavigate={onNavigate}
              onDispatch={onDispatch}
            />
          )}
        </div>
      );
    });

  return <div className="navigation-items">{buildNavMenu(navigationMenu, 0)}</div>;
};

ExpandedNavMenu.propTypes = {
  handleItemClick: PropTypes.func,
  navItemLoading: PropTypes.string,
  openedMenu: PropTypes.oneOfType([PropTypes.array, PropTypes.string]),
  navigationMenu: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
  onNavigate: PropTypes.func,
  onDispatch: PropTypes.func,
};

export default withTranslation()(ExpandedNavMenu);
