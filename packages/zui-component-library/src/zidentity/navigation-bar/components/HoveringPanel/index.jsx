import { useLocation } from 'react-router-dom';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const HoveringPanel = ({ menu = {}, onNavigate = () => {}, onDispatch = () => {} }) => {
  const { subMenu, OnHoverComponent } = menu;
  const location = useLocation();

  if (OnHoverComponent) return <OnHoverComponent />;

  // eslint-disable-next-line consistent-return
  const handleClick = (item) => {
    if (item.navigationURL) {
      onNavigate(item.navigationURL);
    }
    if (typeof item.onClick === 'function') return item.onClick(onDispatch);
  };

  return (
    <div className="hovering-panel-wrapper">
      {subMenu?.map((menuItem) => {
        const isActive = location.pathname.includes(menuItem.navigationURL);
        const itemClassName = 'hovering-panel-item' + (isActive ? ' active' : '');
        return (
          <div key={menuItem.name} className={itemClassName}>
            {menuItem.title && (
              <div className="hovering-panel-item-title">
                <span>{menuItem.title}</span>
              </div>
            )}
            <div
              className="hovering-panel-item-name"
              onClick={() => handleClick(menuItem)}
              onKeyDown={noop}
            >
              <span>{menuItem.name}</span>
            </div>
            {/* {menuItem?.subMenu?.length ? <HoveringPanel menu={menu.subMenu} /> : null} */}
          </div>
        );
      })}
    </div>
  );
};

HoveringPanel.propTypes = {
  menu: PropTypes.shape({}),
  onNavigate: PropTypes.func,
  onDispatch: PropTypes.func,
};

HoveringPanel.defaultProps = {
  menu: {},
  onNavigate: () => {},
  onDispatch: () => {},
};

export default HoveringPanel;
