import { useTranslation } from 'react-i18next';

import PropTypes from 'prop-types';

const NavigationBarSelect = ({ appName }) => {
  const { t } = useTranslation();
  return (
    <div className="navigation-bar-select-wrapper">
      <span>{appName ? t(appName) : 'ZIdentity'}</span>
    </div>
  );
};

NavigationBarSelect.propTypes = {
  appName: PropTypes.string,
};

export default NavigationBarSelect;
