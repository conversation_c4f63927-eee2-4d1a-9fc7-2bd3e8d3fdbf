/* eslint-disable max-len */
// NAV MENU ITEM STRUCTURE EXAMPLE
// {
//   name: i18n.t('Test'), [showing item's name]
//   iconProps: { icon: testIcon, className: 'testClassName' }, [Attrs or Props related to the icon]
//   relatedRouteKeys: ['/test'], [URL segments, in the presence of which the given item should be active]
//   navigationURL: '/admin/test', [the route providing navigate]
//   onClick: ({ history, dispatch, setLoading, setMainLoading }) => functionBody, [if you don't need to navigate, but to perform some action]
//   permissionKey: 'TEST_PRIVILEGE', [the permission key, in which case the item should be visible if it is not NONE]
//   accessPrivilege: 'bi-admin-management', [the accessPrivilege, in which case the item should be visible if it is not NONE]
//   accessRole: 'bi-admin', [the accessRole, in which case the item should be visible if accessRole is equal to data.accessRole]
//   onHoverMenu: true, [the subMenus will not be show vertical, but will be opened from the right side during on hover]
//   OnHoverComponent: require('components/Test').default, [if you want to display another component instead of subMenu, but onHoverMenu property must be true]
//   separateLine: true, [if you need to separate the items with a line]
//   subMenu: [{ ... }], [here can be the same objects mentioned above, it can be recursive render]
// }
