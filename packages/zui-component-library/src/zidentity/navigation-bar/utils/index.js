export const checkRelatedRoutes = (item) => {
  const { pathname } = window.location;
  return (
    item.relatedRouteKeys?.some((routeKey) => {
      return pathname.includes(routeKey);
    }) || pathname.includes(item.navigationURL)
  );
};

export const recursiveSearch = (arr, searchKey) => {
  const lowerCaseSearchKey = searchKey.toLowerCase();
  const result = [];

  const searchSubMenu = (menu, parentMenu, index) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of menu) {
      const { subMenu = [] } = item;
      const lowerCaseName = item.name.toLowerCase();
      const matchingItems = index && lowerCaseName.includes(lowerCaseSearchKey);
      if (matchingItems) {
        result.push([...parentMenu, item]);
      }
      if (subMenu.length) {
        searchSubMenu(subMenu, [...parentMenu, item], index + 1);
      }
    }
  };

  searchSubMenu(arr, [], 0);

  return result;
};

export const toggleArray = (arr = [], value = '') => {
  const array = [...arr];
  if (arr.includes(value)) {
    return array.filter((item) => item !== value);
  }
  return [...array, value];
};

export const recursiveFilter = (
  arr = [],
  subKey = '',
  filterFn = () => null,
  ignoreParent = false,
  isConfigurationEnabled,
) => {
  const copy = (o) => ({ ...o });
  // eslint-disable-next-line no-param-reassign,no-unused-expressions,no-return-assign
  return arr.map(copy).filter((item) => {
    // eslint-disable-next-line no-return-assign,no-shadow,no-param-reassign
    const deepChecking = (item) => {
      if (item[subKey] && Array.isArray(item[subKey])) {
        item[subKey] = item[subKey].filter((subItem) => {
          // Checking if Configuration Feature Flag is not enabled, filtering out configuration nav item
          if (!isConfigurationEnabled) {
            return !subItem?.navigationURL?.includes('configuration');
          }
          return subItem;
        });
        return item[subKey].length;
      }
      return null;
    };
    //const deepChecking = item => (item[subKey] = item?.[subKey]?.map(copy)?.filter(f))?.length;
    if (ignoreParent) {
      switch (true) {
        case !!filterFn(item) && !item[subKey]:
          return true;
        case !!filterFn(item) && !!item[subKey]:
          deepChecking(item);
          return true;
        case !filterFn(item) && !item[subKey]:
          return false;
        case !filterFn(item) && !!item[subKey]:
          deepChecking(item);
          return false;
        default:
          // eslint-disable-next-line no-param-reassign,no-return-assign
          return deepChecking(item);
      }
    } else {
      if (filterFn(item)) return true;
      return deepChecking(item);
    }
  });
};
