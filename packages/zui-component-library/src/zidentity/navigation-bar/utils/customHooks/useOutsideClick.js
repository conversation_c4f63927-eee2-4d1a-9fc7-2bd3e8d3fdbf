import { useEffect } from 'react';

/**
 * @typedef {Object} Ref
 * @property {Object} current
 */

/**
 * A hook that calls the callback when clicking outside of the element(s)
 * @param {Ref|Ref[]} refs element(s)
 * @param {Function} callback
 * @param {Object} ignoreOutsideCallbacks
 */
const useOutsideClick = (refs, callback = () => null, ignoreOutsideCallbacks = {}) => {
  useEffect(() => {
    // eslint-disable-next-line consistent-return
    function handleClickOutside(event) {
      if (Object.keys(ignoreOutsideCallbacks).length) {
        const ignoreKeys = [];
        // eslint-disable-next-line no-restricted-syntax
        for (const ignoreKey of Object.keys(ignoreOutsideCallbacks)) {
          const ignoreElement = event.target.classList.contains(ignoreKey);
          if (ignoreElement) {
            ignoreOutsideCallbacks[ignoreKey]();
            ignoreKeys.push(ignoreKey);
          }
        }
        if (ignoreKeys.length) return;
      }

      if (refs instanceof Array) {
        if (
          refs
            .filter((ref) => ref)
            .every((ref) => ref.current && !ref.current?.contains?.(event.target))
        ) {
          callback(false);
        }
      } else if (refs.current && !refs.current?.contains?.(event.target)) {
        callback(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [refs, callback]);
};

export default useOutsideClick;
