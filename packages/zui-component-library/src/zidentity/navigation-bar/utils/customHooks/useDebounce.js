import { useEffect, useMemo, useRef } from 'react';

/**
 * Debounce hook
 * Debounces a function
 *
 * @param {Function} callback The callback to debounce
 * @param {Object=} options The options to debounce
 * @param {number=} options.wait The duration to debounce
 * @param {boolean=} options.immediate The immediate to debounce
 * @param {number} dependency The dependency to callback
 * @returns {Function} The debounced callback
 */
const useDebounce = (
  callback,
  { wait = 500, immediate = false } = { wait: 500, immediate: false },
  dependency,
) => {
  const timeoutRef = useRef(null);

  useEffect(() => () => clearTimeout(timeoutRef.current), []);

  return useMemo(
    () => {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
      return function _debouncedCallback(...args) {
        const context = this;

        function later() {
          timeoutRef.current = null;
          if (!immediate) callback.apply(context, args);
        }

        const callNow = immediate && !timeoutRef.current;

        clearTimeout(timeoutRef.current);

        timeoutRef.current = setTimeout(later, wait);
        if (callNow) callback.apply(context, args);
      };
    },
    // eslint-disable-next-line
    dependency ? [wait, immediate, ...dependency] : undefined,
  );
};

export default useDebounce;
