import { useEffect, useState } from 'react';

import { createDOMElement, isDOMElementPresent } from '../utils/dom';

const useGetDOMElementBy = ({
  autoCreate = true,
  tagName = 'div',
  parentElement = document.body,
  attributes = {},
}) => {
  const { isPresent, element } = isDOMElementPresent({ ...attributes });

  const [domElement, setDomElement] = useState(element);

  const createElement = () => {
    createDOMElement({ tagName, parentElement, attributes });
  };

  useEffect(() => {
    if (!isPresent && autoCreate) {
      createElement();
    }

    const { element } = isDOMElementPresent({ ...attributes });

    setDomElement(element);
  }, [isPresent, element]);

  return domElement;
};

export default useGetDOMElementBy;
