import { useLayoutEffect, useState } from 'react';

import { noop } from 'lodash-es';

const useIsTextOverflow = ({
  elementRef = { current: {} },
  checkHorizontalOverflow = true,
  onOverflow = noop,
}) => {
  const [isOverflow, setIsOverflow] = useState(false);

  const checkforOverflow = ({ element = {} }) => {
    const { clientWidth, scrollWidth, clientHeight, scrollHeight } = element;

    const hasOverflow = checkHorizontalOverflow
      ? scrollWidth > clientWidth
      : scrollHeight > clientHeight;

    if (!(isOverflow === hasOverflow)) {
      setIsOverflow(hasOverflow);

      onOverflow?.(hasOverflow);
    }
  };

  useLayoutEffect(() => {
    const { current } = elementRef;

    if (current) {
      checkforOverflow({ element: current });
    }
  }, [
    onOverflow,
    elementRef?.current?.scrollWidth,
    elementRef?.current?.scrollHeight,
    checkHorizontalOverflow,
  ]);

  return [isOverflow];
};

export default useIsTextOverflow;
