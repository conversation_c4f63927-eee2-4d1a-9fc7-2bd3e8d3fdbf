import { useCallback, useEffect, useMemo, useState } from 'react';

import { noop } from 'lodash-es';

const defaultConfig = {
  pageSize: 100,
  pageOffset: 0,
  totalRecord: -1,
  list: [],
  initialPage: 1,
  onPageChange: noop,
};

const usePagination = (options = {}) => {
  const { pageSize, pageOffset, totalRecord, list, initialPage, onPageChange } = {
    ...defaultConfig,
    ...options,
  };

  const getTotalPageCount = () => {
    if (totalRecord > 0) {
      return Math.ceil(totalRecord / pageSize);
    }

    if (list.length > 0) {
      return Math.ceil(list.length / pageSize);
    }

    return 1;
  };

  const [currentPage, setCurrentPage] = useState(initialPage);

  useEffect(() => {
    setCurrentPage(currentPage);
  }, [initialPage]);

  const getPageLastIndex = () => {
    return currentPage * pageSize;
  };

  const getPageFirstIndex = () => {
    return getPageLastIndex() - pageSize;
  };

  const [pageLastIndex, setPageLastIndex] = useState(getPageLastIndex());
  const [pageFirstIndex, setPageFirstIndex] = useState(getPageFirstIndex());

  const isCurrentPageFirstPage = () => {
    if (currentPage === 1) {
      return true;
    } else {
      return false;
    }
  };

  const isCurrentPageLastPage = () => {
    const length = getTotalPageCount();

    if (currentPage === length) {
      return true;
    } else {
      return false;
    }
  };

  const [isFistPage, setIsFistPage] = useState(isCurrentPageFirstPage());
  const [isLastPage, setIsLastPage] = useState(isCurrentPageLastPage());

  const getPageRange = () => {
    const length = getTotalPageCount();

    return Array.from({ length }, (_, idx) => idx + 1);
  };

  const getCurrentRecord = useCallback(() => {
    return list.slice(pageFirstIndex, pageLastIndex);
  }, [list, pageFirstIndex, pageLastIndex]);

  const currentRecord = useMemo(() => getCurrentRecord(), [list, pageFirstIndex, pageLastIndex]);

  const [pageRange, setPageRange] = useState(getPageRange());

  useEffect(() => {
    setPageLastIndex(getPageLastIndex());
    setPageFirstIndex(getPageFirstIndex());

    // setCurrentRecord(getCurrentRecord());
    setPageRange(getPageRange());

    setIsFistPage(isCurrentPageFirstPage());
    setIsLastPage(isCurrentPageLastPage());
  }, [pageSize, pageOffset, totalRecord, list, currentPage]);

  const goNext = async () => {
    const newPage = currentPage + 1;

    await onPageChange?.({ pageNumber: newPage });

    setCurrentPage(newPage);
  };

  const goPrev = async () => {
    const newPage = currentPage - 1;

    await onPageChange?.({ pageNumber: newPage });

    setCurrentPage(newPage);
  };

  const goTo = async (newPageIndex) => {
    await onPageChange?.({ pageNumber: newPageIndex });

    setCurrentPage(newPageIndex);
  };

  return {
    currentRecord,
    // setCurrentRecord,
    currentPage,
    setCurrentPage,
    pageFirstIndex,
    pageLastIndex,
    isFistPage,
    isLastPage,
    pageRange,
    setPageRange,
    pageSize,
    pageOffset,
    totalRecord,
    list,
    goNext,
    goPrev,
    goTo,
  };
};

export default usePagination;
