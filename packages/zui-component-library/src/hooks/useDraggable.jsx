import { useDrag, useDrop } from 'react-dnd';

import { noop } from 'lodash-es';

const getDefaultCollect = (monitor) => ({
  isDragging: !!monitor.isDragging(),
});

const useDraggable = ({
  type = 'dragNDrop',
  dragCollect = noop,
  accept = '',
  dropCollect = noop,
  onDrop = noop,
  item = {},
}) => {
  const [dragState = {}, dragRef, previewRef] = useDrag({
    collect: (monitor) => ({
      ...getDefaultCollect(monitor),
      ...(dragCollect?.(monitor) || {}),
    }),
    item: () => item,
    type,
  });

  const [dropState = {}, dropRef] = useDrop({
    accept: accept || type,
    drop: onDrop,
    collect: (monitor) => ({
      ...(dropCollect?.(monitor) || {}),
    }),
  });

  return { dropRef, previewRef, dragRef, dragState, dropState, ...dragState, ...dropState };
};

export default useDraggable;
