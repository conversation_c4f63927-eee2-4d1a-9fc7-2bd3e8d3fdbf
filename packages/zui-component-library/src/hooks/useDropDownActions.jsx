import { useState } from 'react';

import { noop } from 'lodash-es';

import useApiCall from './useApiCall';

const defaultDetail = {
  pageSize: 100,
  pageOffset: 0,
};

const useDropDownActions = ({
  detail,
  defaultOnOpenApiPayload = {},
  fetchOnce = false,
  apiCallFunc = noop,
}) => {
  const { apiCall } = useApiCall({ hasLoader: false });

  const [fetchedOnce, setFetchedOnce] = useState(false);
  const [isDropDownLoading, setIsDropDownLoading] = useState(true);
  const [onOpenApiPayload, setOnOpenApiPayload] = useState({ ...defaultOnOpenApiPayload });
  const [loadMoreApiPayload, setLoadMoreApiPayload] = useState({});

  const { pageSize, pageOffset } = { ...defaultDetail, ...detail };

  const onDropDownOpen = ({ apiPayload = {} } = {}) => {
    if (!(fetchOnce && fetchedOnce)) {
      setIsDropDownLoading(true);

      apiCall(apiCallFunc({ ...onOpenApiPayload, ...apiPayload }))
        .catch(noop)
        .finally(() => {
          setIsDropDownLoading(false);

          if (!fetchedOnce) {
            setFetchedOnce(true);
          }
        });
    }
  };

  const onLoadMoreClick = ({ term, fromStart, ...loadMorePayload } = {}) => {
    setIsDropDownLoading(true);

    if (fromStart) {
      apiCall(
        apiCallFunc({
          name: term,
          requireTotal: true,
          ...loadMoreApiPayload,
          ...loadMorePayload,
        }),
      )
        .catch(noop)
        .finally(() => {
          setIsDropDownLoading(false);
        });
    } else {
      apiCall(
        apiCallFunc({
          name: term,
          requireTotal: false,
          pageOffset: pageOffset + pageSize,
          pageSize,
          ...loadMoreApiPayload,
          ...loadMorePayload,
        }),
      )
        .catch(noop)
        .finally(() => {
          setIsDropDownLoading(false);
        });
    }
  };

  return {
    isDropDownLoading,
    setIsDropDownLoading,
    setOnOpenApiPayload,
    setLoadMoreApiPayload,
    onDropDownOpen,
    onLoadMoreClick,
  };
};

export default useDropDownActions;
