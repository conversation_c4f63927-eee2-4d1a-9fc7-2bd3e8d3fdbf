import { useEffect } from 'react';

const useEventHandler = ({
  eventListenerList = [],
  elementRef = {},
  isActive = true,
  disabled = false,
  hasElementFallback = true,
}) => {
  const updateEventListener = (add = true) => {
    if (eventListenerList) {
      eventListenerList.forEach(({ event, listener, elementRef: overrideRef = {} }) => {
        let element = overrideRef?.current || elementRef?.current;

        if (!element && hasElementFallback) {
          element = document;
        }

        if (element) {
          if (add) {
            element?.addEventListener?.(event, listener);
          } else {
            element?.removeEventListener?.(event, listener);
          }
        }
      });
    }
  };

  useEffect(() => {
    if (isActive && !disabled) {
      updateEventListener(true);
    }

    return () => {
      updateEventListener(false);
    };
  }, [elementRef, isActive, disabled, eventListenerList]);

  useEffect(() => {
    return () => {
      updateEventListener(false);
    };
  }, []);
};

export default useEventHandler;
