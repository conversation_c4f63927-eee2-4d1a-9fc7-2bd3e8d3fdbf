import { useCallback, useEffect, useState } from 'react';

const useClickHandler = ({
  containerRef,
  onOutsideClick,
  onInsideClick,
  onException,
  isActive,
  disabled,
}) => {
  const [evtDetail, setEvtDetail] = useState({ evt: {}, isClickedOutside: false });

  const onAnywhereClickHandler = useCallback(
    (evt) => {
      try {
        if (containerRef) {
          const path = evt.path || (evt.composedPath && evt.composedPath());

          if (
            !path.some((element) => element !== window && containerRef?.current?.contains(element))
          ) {
            setEvtDetail({ evt, isClickedOutside: true });
            onOutsideClick?.(evt);
          } else {
            setEvtDetail({ evt, isClickedOutside: false });
            onInsideClick?.(evt);
          }
        }
      } catch (ex) {
        setEvtDetail({ evt, isClickedOutside: false });
        onException?.(evt);
      }
    },
    [containerRef],
  );

  useEffect(() => {
    if (isActive && !disabled) {
      document.addEventListener('click', onAnywhereClickHandler);
    }

    return () => {
      document.removeEventListener('click', onAnywhereClickHandler);
    };
  }, [isActive, disabled]);

  return evtDetail;
};

export default useClickHandler;
