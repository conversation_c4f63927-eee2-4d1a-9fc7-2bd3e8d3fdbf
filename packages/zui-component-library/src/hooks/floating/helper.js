import { safePolygon } from '@floating-ui/react';

import { cloneDeep } from 'lodash-es';

export const FLOATING_CONFIG_NAME = {
  HOVER: 'hoverConfig',
  CLICK: 'clickConfig',
  FOCUS: 'focusConfig',
  DISMISS: 'dismissConfig',
  ROLE: 'roleConfig',
  OFFSET: 'offsetConfig',
  SHIFT: 'shiftConfig',
  FLIP: 'flipConfig',
  ARROW: 'arrowConfig',
  SIZE: 'sizeConfig',
  AUTO_PLACEMENT: 'autoPlacementConfig',
  HIDE: 'hideConfig',
  INLINE: 'inlineConfig',
};

const defaultHoverConfig = {
  move: false,
  delay: 250,
  handleClose: safePolygon({ blockPointerEvents: false }),
};

const defaultClickConfig = {};

const defaultFocusConfig = {};

const defaultDismissConfig = { ancestorScroll: true };

const defaultRoleConfig = {};

const defaultOffsetConfig = {};

const defaultShiftConfig = {};

const defaultFlipConfig = {};

const defaultArrowConfig = {};

const defaultSizeConfig = {
  padding: 36,
  apply({ availableWidth, availableHeight, elements }) {
    Object.assign(elements.floating.style, {
      maxWidth: `${availableWidth}px`,
      maxHeight: `${availableHeight}px`,
    });
  },
};

const defaultAutoPlacementConfig = {};

const defaultHideConfig = {};

const defaultInlineConfig = {};

const defaultFloatingConfig = {
  [FLOATING_CONFIG_NAME.HOVER]: defaultHoverConfig,
  [FLOATING_CONFIG_NAME.CLICK]: defaultClickConfig,
  [FLOATING_CONFIG_NAME.FOCUS]: defaultFocusConfig,
  [FLOATING_CONFIG_NAME.DISMISS]: defaultDismissConfig,
  [FLOATING_CONFIG_NAME.ROLE]: defaultRoleConfig,
  [FLOATING_CONFIG_NAME.OFFSET]: defaultOffsetConfig,
  [FLOATING_CONFIG_NAME.SHIFT]: defaultShiftConfig,
  [FLOATING_CONFIG_NAME.FLIP]: defaultFlipConfig,
  [FLOATING_CONFIG_NAME.ARROW]: defaultArrowConfig,
  [FLOATING_CONFIG_NAME.SIZE]: defaultSizeConfig,
  [FLOATING_CONFIG_NAME.AUTO_PLACEMENT]: defaultAutoPlacementConfig,
  [FLOATING_CONFIG_NAME.HIDE]: defaultHideConfig,
  [FLOATING_CONFIG_NAME.INLINE]: defaultInlineConfig,
};

const activeFloatingConfig = cloneDeep(defaultFloatingConfig);

export const getDefaultFloatingConfig = ({ configName } = {}) => {
  const config = cloneDeep(defaultFloatingConfig);

  if (configName) {
    return config[configName] || {};
  }

  return config;
};

export const getActiveFloatingConfig = ({ configName } = {}) => {
  const config = cloneDeep(activeFloatingConfig);

  if (configName) {
    config[configName] || {};
  }

  return config || {};
};

// use this to set as a global config for the app
export const setActiveFloatingConfig = ({ newConfig = {}, extendConfig = {} } = {}) => {
  Object.keys(newConfig).forEach((configName) => {
    const isConfigValid = FLOATING_CONFIG_NAME[configName];

    if (isConfigValid) {
      const oldConfig = cloneDeep(activeFloatingConfig[configName]);

      const config = newConfig[configName];

      let shouldExtendConfig = false;

      if (typeof extendConfig === 'boolean' && extendConfig) {
        shouldExtendConfig = true;
      }

      if (extendConfig[configName]) {
        shouldExtendConfig = true;
      }

      if (shouldExtendConfig) {
        activeFloatingConfig[configName] = { ...oldConfig, ...config };
      } else {
        activeFloatingConfig[configName] = { ...config };
      }
    }
  });
};
