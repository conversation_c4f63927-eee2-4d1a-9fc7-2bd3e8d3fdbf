import { useCallback, useMemo, useRef, useState } from 'react';

import {
  arrow,
  autoPlacement,
  autoUpdate,
  flip,
  hide,
  inline,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useFocus,
  useHover,
  useInteractions,
  useRole,
} from '@floating-ui/react';

import { isUndefined, noop } from 'lodash-es';

import { getActiveFloatingConfig } from './helper';

const getMiddlewareConfig = (
  {
    offsetConfig,
    inlineConfig,
    shiftConfig,
    flipConfig,
    arrowConfig,
    sizeConfig,
    autoPlacementConfig,
    hideConfig,
  } = {},
  { arrowRef } = {},
) => {
  let middleware = [];

  // order of middleware matters
  if (!isUndefined(offsetConfig)) {
    middleware.push(offset(offsetConfig));
  }

  if (!isUndefined(inlineConfig)) {
    middleware.push(inline(inlineConfig));
  }

  if (!isUndefined(flipConfig)) {
    middleware.push(flip(flipConfig));
  }

  if (!isUndefined(shiftConfig)) {
    middleware.push(shift(shiftConfig));
  }

  if (!isUndefined(sizeConfig)) {
    middleware.push(size(sizeConfig));
  }

  if (isUndefined(flipConfig) && !isUndefined(autoPlacementConfig)) {
    middleware.push(autoPlacement(autoPlacementConfig));
  }

  if (!isUndefined(arrowConfig)) {
    middleware.push(arrow({ element: arrowRef, ...arrowConfig }));
  }

  if (!isUndefined(hideConfig)) {
    middleware.push(hide(hideConfig));
  }

  return middleware;
};

const DEFAULT_PROPS = {
  placement: 'bottom',
  strategy: 'fixed',
  initialOpen: false,
  showOnHover: true,
  showOnClick: false,
  onOpenChange: noop,
  ...getActiveFloatingConfig(),
};

const useFloatingLayout = (config = {}) => {
  const arrowRef = useRef();

  const {
    placement,
    strategy,
    initialOpen,
    showOnHover,
    showOnClick,
    onOpenChange,
    hoverConfig,
    clickConfig,
    focusConfig,
    dismissConfig,
    roleConfig,
    ...middleware
  } = {
    ...DEFAULT_PROPS,
    ...config,
  };

  const [open, setOpen] = useState(initialOpen);

  const computedMiddleware = getMiddlewareConfig(middleware, { arrowRef });

  const handleOnOpenChange = useCallback(
    (newState) => {
      setOpen(newState);
      onOpenChange(newState);
    },
    [onOpenChange],
  );

  const data = useFloating({
    placement,
    strategy,
    open,
    onOpenChange: handleOnOpenChange,
    whileElementsMounted: autoUpdate,
    middleware: computedMiddleware,
  });

  const context = data.context;

  const hover = useHover(context, { enabled: showOnHover, ...hoverConfig });
  const click = useClick(context, { enabled: showOnClick, ...clickConfig });
  const focus = useFocus(context, { ...focusConfig });
  const dismiss = useDismiss(context, { ...dismissConfig });
  const role = useRole(context, { ...roleConfig });

  const interactions = useInteractions([hover, click, focus, dismiss, role].filter(Boolean));

  return useMemo(
    () => ({
      open,
      setOpen,
      arrowRef,
      ...interactions,
      ...data,
    }),
    [open, setOpen, interactions, data, arrowRef],
  );
};

export default useFloatingLayout;
