import { useCallback, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';

import { noop } from 'lodash-es';

const defaultApiSuccessNotifier = () => (response) => Promise.resolve(response);

// TODO: better design system
// lastUpdated : to get latest callback if registry is changed multiple time
const functionsRegistry = {
  showLoader: () => noop,
  hideLoader: () => noop,
  hideNotification: () => noop,
  apiErrorNotifier: () => noop,
  apiSuccessNotifier: defaultApiSuccessNotifier,
  lastUpdated: new Date().toString(),
};

const defaultConfig = {
  hasLoader: true,
  hasNotification: true,
  clearNotification: true,
  clearNotificationOnNextCall: true,
  clearLoader: true,
  errorNotificationPayload: {},
  successNotificationPayload: {},
};

export const updateUseApiCallFunctionsRegistry = ({
  showLoader,
  hideLoader,
  hideNotification,
  apiErrorNotifier,
  apiSuccessNotifier,
}) => {
  if (showLoader) {
    functionsRegistry.showLoader = showLoader;
  }

  if (hideLoader) {
    functionsRegistry.hideLoader = hideLoader;
  }

  if (hideNotification) {
    functionsRegistry.hideNotification = hideNotification;
  }

  if (apiErrorNotifier) {
    functionsRegistry.apiErrorNotifier = apiErrorNotifier;
  }

  if (apiSuccessNotifier) {
    functionsRegistry.apiSuccessNotifier = apiSuccessNotifier;
  }

  functionsRegistry.lastUpdated = new Date().toString();
};

const useApiCall = (options = {}) => {
  const notificationIdsRef = useRef([]);

  const {
    dispatch = useDispatch(),
    hasLoader,
    hasNotification,
    clearNotification,
    clearNotificationOnNextCall,
    clearLoader,
    errorNotificationPayload,
    successNotificationPayload,
  } = {
    ...defaultConfig,
    ...options,
  };

  const cleanupLoader = () => {
    if (clearLoader) {
      dispatch?.(functionsRegistry.hideLoader?.());
    }
  };

  const cleanupNotification = (id) => {
    if (id) {
      dispatch?.(functionsRegistry?.hideNotification?.(id));

      return;
    }

    if (clearNotification) {
      notificationIdsRef.current.forEach((id) => {
        dispatch?.(functionsRegistry?.hideNotification?.(id));
      });
    }
  };

  useEffect(() => {
    return () => {
      cleanupLoader();
      cleanupNotification();
    };
  }, []);

  const apiCallerFunc = useCallback(
    (apiCall, config = {}) => {
      const settings = {
        hasLoader,
        hasNotification,
        clearNotification,
        clearNotificationOnNextCall,
        clearLoader,
        errorNotificationPayload,
        successNotificationPayload,
        ...config,
      };

      if (settings.hasLoader) {
        dispatch?.(functionsRegistry?.showLoader?.());
      }

      if (settings.clearNotificationOnNextCall) {
        cleanupNotification();
      }

      return dispatch?.(apiCall)
        ?.then?.(
          functionsRegistry.apiSuccessNotifier?.(
            dispatch,
            settings.hasNotification,
            settings.successNotificationPayload,
          ),
        )
        ?.then?.((payload) => {
          const { notificationId } = payload || {};

          if (notificationId && settings.clearNotification) {
            const ids = notificationIdsRef.current;

            ids.push(notificationId);

            notificationIdsRef.current = [...ids];
          }

          return Promise.resolve(payload);
        })
        ?.catch?.(
          functionsRegistry.apiErrorNotifier?.(
            dispatch,
            settings.hasNotification,
            settings.errorNotificationPayload,
          ),
        )
        ?.catch?.((error = {}) => {
          const { notificationId } = error;

          if (notificationId && settings.clearNotification) {
            const ids = notificationIdsRef.current;

            ids.push(notificationId);

            notificationIdsRef.current = [...ids];
          }

          return Promise.reject(error);
        })
        ?.finally?.(() => {
          if (settings.hasLoader) {
            dispatch?.(functionsRegistry?.hideLoader?.());
          }
        });
    },
    [hasLoader, hasNotification, clearNotification, clearLoader, functionsRegistry.lastUpdated],
  );

  return { apiCall: apiCallerFunc, cleanupNotification, cleanupLoader };
};

export default useApiCall;
