// -----------------------------------------------------------------------------
// This file contains all application-wide Sass mixins.
// -----------------------------------------------------------------------------

/// Event wrapper
/// <AUTHOR>
/// @param {Bool} $self [false] - Whether or not to include current selector
/// @link https://twitter.com/csswizardry/status/478938530342006784 Original tweet from <PERSON>
@mixin on-event($self: false) {
  @if $self {
    &,
    &:hover,
    &:active,
    &:focus {
      @content;
    }
  } @else {
    &:hover,
    &:active,
    &:focus {
      @content;
    }
  }
}

/// Make a context based selector a little more friendly
/// <AUTHOR>
/// @param {String} $context
@mixin when-inside($context) {
  #{$context} & {
    @content;
  }
}

@mixin inline-block($vertical-align: top) {
  display: inline-block;
  vertical-align: $vertical-align;
}

@mixin floating-arrow-border {
  &[class*='top'] {
    border-left-color: transparent;
    border-top-color: transparent;
  }

  &[class*='right'] {
    border-top-color: transparent;
    border-right-color: transparent;
  }

  &[class*='bottom'] {
    border-right-color: transparent;
    border-bottom-color: transparent;
  }

  &[class*='left'] {
    border-bottom-color: transparent;
    border-left-color: transparent;
  }
}

@mixin has-ellipses() {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
