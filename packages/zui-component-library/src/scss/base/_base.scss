// -----------------------------------------------------------------------------
// This file contains very basic styles.
// -----------------------------------------------------------------------------

/**
 * Set up a decent box model on the root element
 */
html {
  box-sizing: border-box;

  font-size: var(--semantic-typography-size-base, 1rem); /* 1 rem = 10 px */

  line-height: var(--semantic-typography-lineHeight-base, 1.75rem); // 3
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/**
 * Make all elements from the DOM inherit from the parent box-sizing
 * Since `*` has a specificity of 0, it does not override the `html` value
 * making all elements inheriting from the root box-sizing value
 * See: https://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/
 */
*,
*::before,
*::after {
  box-sizing: inherit;
}

/**
 * Generic styles body
 */
body {
  margin: 0;

  color: var(--semantic-color-content-base-primary, #131a2e);
  background-color: var(--semantic-color-background-primary, #fff);

  font-family: var(
    --semantic-fontFamily-default,
    'Inter',
    ui-sans-serif,
    system-ui,
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji'
  );

  display: flex;
  flex-direction: column;
}
