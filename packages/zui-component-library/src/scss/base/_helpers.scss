// -----------------------------------------------------------------------------
// This file contains CSS helper classes.
// -----------------------------------------------------------------------------

/**
*  flex
*/
.is-flex {
  display: flex;
}

.has-fd-c {
  flex-direction: column;
}

.has-jc-sb {
  justify-content: space-between;
}

.has-jc-c {
  justify-content: center;
}

.has-jc-s {
  justify-content: flex-start;
}

.has-jc-e {
  justify-content: flex-end;
}

.has-ai-b {
  align-items: baseline;
}

.has-ai-c {
  align-items: center;
}

.has-ai-s {
  align-items: flex-start;
}

.has-ai-e {
  align-items: flex-end;
}

.has-as-s {
  align-self: flex-start;
}

.has-as-c {
  align-self: center;
}

.has-background-primary {
  background-color: var(--semantic-color-content-base-brand, #2160e1);
}

.has-background-light {
  background-color: var(--semantic-color-surface-base-primary, #fff);
}

.has-background-success {
  background-color: var(--semantic-color-surface-status-success-active, #cbeec5);
}

.has-color-primary {
  color: var(--semantic-color-content-interactive-primary-default, #7ba5fe);
}

.has-color-success {
  color: var(--semantic-color-content-status-success-primary, #b6e1af);
}

.has-color-success-secondary {
  color: var(--semantic-color-content-status-success-secondary, #569b4b);
}

.has-color-warning {
  color: var(--semantic-color-content-status-warning-primary, #fbad7f);
}

.has-color-error {
  color: var(--semantic-color-content-status-danger-primary, #8e201c);
}

.has-color-error-secondary {
  color: var(--semantic-color-content-status-danger-secondary, #dc362e);
}

.text-white {
  color: var(--semantic-color-content-inverted-base-primary, #fff);
}

.text-gray {
  color: var(--semantic-color-content-base-subdued, #677289);
}

.text-gray-darker {
  color: var(--semantic-color-content-base-secondary, #4a5468);
}

.text-gray-darkest {
  color: var(--semantic-color-surface-alpha-overlay, #191919a3);
}

.logo-container {
  width: 164px;
  height: 34px;
}

.icon {
  &.left {
    margin-right: 8px;
  }

  &.right {
    margin-left: 8px;
  }

  & + .text {
    flex-grow: 1;
    text-align: left;
  }
}

.full-width {
  width: 100%;
}

/**
 * Clear inner floats
 */
.clearfix::after {
  clear: both;
  content: '';
  display: table;
}

/**
 * Main content containers
 * 1. Make the container full-width with a maximum width
 * 2. Center it in the viewport
 * 3. Leave some space on the edges, especially valuable on small screens
 */
.container {
  // max-width: $max-width; /* 1 */
  margin-left: auto; /* 2 */
  margin-right: auto; /* 2 */
  padding-left: 20px; /* 3 */
  padding-right: 20px; /* 3 */
  width: 100%; /* 1 */
}

/**
 * Hide text while making it readable for screen readers
 * 1. Needed in WebKit-based browsers because of an implementation bug;
 *    See: https://code.google.com/p/chromium/issues/detail?id=457146
 */
.hide-text {
  overflow: hidden;
  padding: 0; /* 1 */
  text-indent: 101%;
  white-space: nowrap;
}

/**
 * Hide element while making it readable for screen readers
 * Shamelessly borrowed from HTML5Boilerplate:
 * https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css#L119-L133
 */
.visually-hidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.text-center {
  text-align: center;
}

.pointer {
  cursor: pointer;
}

.no-p {
  padding: 0;
}

.no-p-t {
  padding-top: 0;
}

.no-p-r {
  padding-right: 0;
}

.no-p-b {
  padding-bottom: 0;
}

.no-p-l {
  padding-left: 0;
}

.no-m {
  margin: 0;
}

.no-m-t {
  margin-top: 0;
}

.no-m-r {
  margin-right: 0;
}

.no-m-b {
  margin-bottom: 0;
}

.no-m-l {
  margin-left: 0;
}

.hide {
  display: none;
}
