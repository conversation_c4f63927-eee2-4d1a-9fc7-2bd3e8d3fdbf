.table-container {
  border-radius: 0;

  border: none;

  position: relative;

  color: var(--semantic-color-content-base-primary, #131a2e);
  @extend .typography-paragraph1;

  &.has-footer {
    margin-bottom: 32px;
  }
}

.table {
  display: inline-block;

  width: 100%;
  max-width: inherit;
  height: 100%;
  max-height: inherit;

  overflow: auto;
}

.header-container {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 1;

  border-top: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  background-color: var(--semantic-color-surface-table-header-default, #f7f8fa);

  @extend .typography-paragraph1-strong;

  &.has-overflow {
    display: inline-block;
  }
}

.row-container {
  display: flex;
  width: 100%;

  background-color: inherit;
}

.column-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  flex-shrink: 0;
  flex-grow: 1;

  position: relative;

  height: 44px;

  padding: 0 8px;

  &.no-grow {
    flex-grow: 0;
  }

  &:last-of-type {
    border-right: none;
  }
}

.sort-section {
  cursor: pointer;

  .icon {
    &.disabled {
      color: var(--semantic-color-content-base-tertiary, #677289);
    }

    &.active {
      color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
    }
  }
}

.body-container {
  height: calc(100% - 45px);

  background-color: var(--semantic-color-surface-table-row-default, #fff);
}

.cell-container {
  display: flex;
  align-items: center;

  flex-shrink: 0;
  flex-grow: 1;

  @include has-ellipses();

  width: 100%;

  height: 52px;
  line-height: 52px;

  padding: 0 8px;

  background-color: inherit;
  border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

  &.no-grow {
    flex-grow: 0;
  }

  .actions-container {
    justify-content: center;
    width: 100%;
  }
}

.table-action-btn {
  padding-left: 5px;
  padding-right: 5px;
  margin-right: 8px;
}

.row-number {
  text-align: center;
}

.selector {
  text-align: center;
  justify-content: center;

  &.checkbox {
    .icon {
      color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
    }
  }
}

.status {
  display: inline-flex;
  align-items: center;

  // max-width: 76px;
  height: 32px;

  padding: 6px 8px;

  border-radius: 4px;
}

.inline-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-list-container {
  border-radius: var(--semantic-cornerRadius-80, 8px);

  background-color: var(--semantic-color-surface-base-primary, #fff);

  @extend .shadow-large;

  .action {
    padding: var(--semantic-spacing-px-80, 8px);

    min-width: 108px;
    height: 36px;

    line-height: 20px;

    color: var(--semantic-color-interactive-primary-default, #2160e1);
    background-color: var(--semantic--color-surface-interactive-secondary-default, #fff);

    &:hover {
      cursor: pointer;
      color: var(--semantic-color-surface-interactive-primary-hover, #0e3896);
      background-color: var(--semantic-color-surface-menu-hover, #e9eef3);
    }
  }
}

.column-config-container {
  top: 0;
  right: 0;
  position: absolute;

  z-index: 2;
  height: 45px;

  .show-list-icon {
    height: 100%;

    font-size: var(--semantic-fontSize-xxl, 2rem);
    background-color: var(--semantic-color-surface-base-tertiary, #e9eef3);
  }

  &:hover {
    .configure-column-container {
      display: block;
    }
  }
}

.configure-column-container {
  display: none;

  position: absolute;
  right: 0;
  top: 45px;

  padding: 1rem;

  border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

  background-color: var(--semantic-color-surface-base-primary, #fff);
}

.configure-column-list-container {
  max-height: 350px;
  overflow: scroll;
}

.drag-and-drop-item-container {
  padding: 8px 0;

  :hover {
    cursor: move;
  }

  .label {
    margin-bottom: 0;
    overflow: hidden;
  }

  .dnd-icon {
    padding-top: 0;
    padding-bottom: 0;
  }
}

// later need to change this
.drag-and-drop-item-container > div > label > svg {
  color: var(--semantic-color-content-interactive-primary-active, #194cbb);
}

.resizer {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 5px;
  cursor: col-resize;
  user-select: none;
  touch-action: none;
}

.resizer.isResizing {
  background: var(--semantic-color-surface-interactive-primary-active, #194cbb);
  opacity: 1;
}

.no-data-container {
  width: 100%;
}

.table-footer-container {
  position: absolute;

  bottom: -24px;
  left: 0;
}
