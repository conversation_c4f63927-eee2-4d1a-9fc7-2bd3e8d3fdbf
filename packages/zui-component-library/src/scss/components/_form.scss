.field-group {
  display: flex;

  width: 100%;

  &.has-width-auto {
    width: auto;
  }

  .field {
    margin-right: 40px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.field {
  margin: 20px 0;
  width: 100%;
  // width: 24rem;
  position: relative;

  display: flex;
  flex-direction: column;
  flex-wrap: wrap;

  flex-grow: 1;
  // flex-basis: 0;
  min-width: 0;

  &.field-stacked {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: baseline;
  }
}

.label {
  @include has-ellipses();
  @extend .typography-paragraph1;

  display: inline-block;
  align-self: flex-start;
  color: var(--semantic-color-content-base-secondary, #4a5468);
  max-width: 100%;

  font-weight: 500;
  margin-bottom: 8px;

  & .label-tooltip-container {
    &.card {
      padding: 12px 16px;
    }
  }

  &.has-tooltip {
    &:hover {
      cursor: help;
    }

    .info-detail-container:not(:last-of-type) {
      margin-bottom: 16px;
    }

    .arrow-container {
      border-color: var(--semantic-color-surface-elevated-low10, #fff);

      @include floating-arrow-border();
    }
  }

  &:disabled {
    color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
  }
}

.label-tooltip-container {
  border-radius: var(--semantic-cornerRadius-rounded, 4px);
  background: var(--semantic-color-surface-elevated-low10, #fff);

  @extend .shadow-small;

  .tooltip-bold {
    font-size: 14px;
  }
}

.input-container {
  position: relative;

  width: 100%;
}

.input {
  @extend .typography-paragraph1;

  border-radius: var(--semantic-cornerRadius-60, 6px);
  border: 1px solid var(--semantic-color-border-interactive-secondary-default, #cad0dc);
  background: var(--semantic-color-surface-fields-default, #fff);
  padding: 5px 6px;

  color: var(--semantic-color-content-base-primary, #131a2e);

  width: 100%;

  &::placeholder {
    color: var(--semantic-color-content-base-tertiary, #677289);
  }

  &:read-only {
    background: var(--semantic-color-surface-fields-disabled, #f7f8fa);
  }

  &:disabled {
    border: 1px solid var(--semantic-color-border-interactive-secondary-disabled, #e9eef3);
    background: var(--semantic-color-surface-fields-disabled, #f7f8fa);
    color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
  }

  &.no-padding {
    padding: 0;
  }

  & ~ .error-detail {
    color: var(--semantic-color-content-base-tertiary, #677289);
  }

  &:hover {
    border: 1px solid var(--semantic-color-border-interactive-secondary-hover, #a3acbd);
    background: var(--semantic-color-surface-fields-hover, #f7f8fa);
  }

  &:focus,
  :active {
    border: 1px solid var(--semantic-color-content-interactive-primary-default, #2160e1);
    background: var(--semantic-color-surface-fields-active, #fff);
  }

  &.error {
    border: 1px solid var(--semantic-color-border-status-danger-active, #dc362e);
    background: var(--semantic-color-surface-status-danger-default, #ffedee);
    color: var(--semantic-color-content-base-primary, #131a2e);
  }
}

.input-action-container {
  position: absolute;

  bottom: 0;
  height: 100%;

  &.left {
    left: 0;
    padding-left: 8px;
  }

  &.right {
    right: 0;
    padding-right: 8px;
  }
}

.info-detail-container {
  @extend .typography-paragraph1;

  padding-top: 4px;

  color: var(--semantic-color-content-base-primary, #131a2e);

  text-align: left;

  white-space: break-spaces;

  &.error {
    color: var(--semantic-color-content-status-danger-primary, #8e201c);
  }

  &.warning {
    color: var(--semantic-color-content-status-warning-primary, #8b3f17);
  }
}

.checkbox,
.radio {
  display: flex;
  align-items: center;

  cursor: pointer;

  &.large {
    font-size: 2rem;
  }

  &.disabled {
    color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);

    cursor: default;
  }

  & :not(:disabled):hover {
    box-shadow: 0 0 1px var(--semantic-color-border-interactive-primary-hover, #0e3896);
  }

  .content-container {
    padding-left: 8px;
  }

  input {
    display: none;
  }
}

.textarea {
  @extend .typography-paragraph1;

  padding: 5px 6px;

  color: var(--semantic-color-content-base-primary, #131a2e);

  border-radius: var(--semantic-cornerRadius-60, 6px);
  border: 1px solid var(--semantic-color-border-interactive-secondary-default, #cad0dc);
  background: var(--semantic-color-surface-fields-default, #fff);

  min-width: 250px;

  width: 100%;

  &::placeholder {
    color: var(--semantic-color-content-base-tertiary, #677289);
  }

  &:read-only {
    background: var(--semantic-color-surface-fields-disabled, #f7f8fa);
    color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);

    &.error {
      color: var(--semantic-color-content-interactive-primary-disabledOnSecondary, #586378);
      border: 1px solid var(--semantic-color-border-status-danger-default, #fda2a1);
      background: var(--semantic-color-surface-status-danger-default, #ffedee);
    }
  }

  &.error {
    border: 1px solid var(--semantic-color-border-status-danger-default, #fda2a1);
    background: var(--semantic-color-surface-status-danger-default, #ffedee);
  }

  &.no-padding {
    padding: 0;
  }
}

.validation-container {
  @extend .typography-paragraph1;
  @extend .shadow-small;

  background-color: var(--semantic-color-background-primary, #fff);

  .validation-info {
    margin-bottom: 0.5rem;

    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.action-confirmation-container {
  .delete-input {
    width: 90px;
    margin-bottom: 0;
  }
}
