.buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;

  margin-bottom: 20px;
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--semantic-spacing-px-40, 4px);

  cursor: pointer;

  padding: var(--semantic-spacing-px-60, 6px) 12px;
  min-width: 84px;

  white-space: nowrap;

  @extend .typography-paragraph1;

  text-align: center;
  line-height: var(--semantic-typography-lineHeight-base, 1.75rem);

  color: var(--semantic-color-content-base-primary, #131a2e);
  background-color: var(--semantic-color-surface-base-primary, #fff);
  border: 1px solid transparent;
  border-radius: var(--semantic-cornerRadius-60, 6px);

  &.primary {
    color: var(--semantic-color-content-inverted-base-primary, #fff);
    background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);

    &:disabled {
      color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
      border-color: var(--semantic-color-border-interactive-primary-disabled, #cad0dc);
      background-color: var(--semantic-color-surface-interactive-primary-disabled, #e9eef3);

      cursor: default;

      &.error {
        border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):hover {
      background-color: var(--semantic-color-surface-interactive-primary-hover, #0e3896);
    }

    &:not(:disabled):active {
      background-color: var(--semantic-color-surface-interactive-primary-active, #194cbb);
    }

    &:not(:disabled):focus {
      background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
    }
  }

  &.secondary {
    color: var(--semantic-color-content-interactive-primary-default, #2160e1);
    border-color: var(--semantic-color-border-interactive-primary-default, #2160e1);
    background-color: var(--semantic-color-surface-base-secondary, #f7f8fa);

    &:disabled {
      color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
      border-color: var(--semantic-color-border-interactive-secondary-disabled, #e9eef3);
      background-color: var(--semantic-color-surface-interactive-secondary-disabled, #f7f8fa);

      cursor: default;

      &.error {
        border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):hover {
      color: var(--semantic-color-content-interactive-primary-hover, #d4e2ff);
      border-color: var(--semantic-color-border-interactive-primary-hover, #d4e2ff);
      background-color: var(--semantic-color-surface-interactive-secondary-hover, #242c41);

      &.error {
        border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):active {
      color: var(--semantic-color-content-interactive-primary-active, #194cbb);
      border-color: var(--semantic-color-border-interactive-primary-active, #194cbb);
      background-color: var(--semantic-color-surface-interactive-secondary-active, #c9dbff);

      &.error {
        border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):focus {
      color: var(--semantic-color-content-interactive-primary-focus, #0e3896);
      border-color: var(--semantic-color-border-interactive-primary-default, #2160e1);
      background-color: var(--semantic-color-surface-interactive-secondary-focus, #e3ecff);

      &.error {
        border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &.error {
      border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
    }
  }

  &.tertiary {
    background-color: inherit;
    color: var(--semantic-color-content-interactive-primary-default, #2160e1);

    &:disabled {
      color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
      cursor: default;

      &.error {
        color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):hover {
      color: var(--semantic-color-content-interactive-primary-hover, #0e3896);
      background-color: var(--semantic-color-surface-interactive-secondary-hover, #f2f7ff);

      &.error {
        color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):active {
      color: var(--semantic-color-content-interactive-primary-active, #194cbb);
      background-color: var(--semantic-color-surface-interactive-secondary-active, #c9dbff);

      &.error {
        color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &:not(:disabled):focus {
      background: var(--semantic-color-surface-interactive-secondary-focus, #e3ecff);

      &.error {
        color: var(--semantic-color-border-interactive-danger-default, #dc362e);
      }
    }

    &.error {
      color: var(--semantic-color-border-interactive-danger-default, #dc362e);
    }

    &.no-p-l {
      padding-left: 0;
    }

    &.no-p-r {
      padding-right: 0;
    }
  }

  &.link {
    padding: 0;
    justify-content: flex-start;
    text-align: left;
    min-width: 0;
    text-decoration: none;
  }

  &.is-small {
    padding: 6px 12px;

    line-height: 8px;
    font-size: 13px;
  }

  &.is-large {
    padding: 8px 24px;

    line-height: 24px;
    font-size: 16px;
  }

  &.full-width {
    width: 100%;
  }

  &.content-width {
    min-width: 0px;
  }
}

.link {
  padding: 0;
  justify-content: flex-start;
  text-align: left;
  min-width: 0;
  text-decoration: none;
}

.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;

  // background-color: var(--semantic-color-background-primary, #fff);
}

.radio-buttons-container {
  display: flex;

  .button {
    &:not(.primary) {
      border-color: var(--semantic-color-surface-base-tertiary, #e9eef3);
    }
  }
}

.toggle-button-container {
  display: flex;
  align-items: center;
  gap: var(--semantic-spacing-px-80, 8px);

  &:not(.disabled):hover {
    cursor: pointer;
  }

  .icon-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--semantic-spacing-px-80, 8px);

    padding: var(--semantic-spacing-px-40, 4px);
    border-radius: var(--semantic-cornerRadius-240, 24px);

    border: 1px solid transparent;
  }

  &.on {
    .icon-container {
      color: var(--semantic-color-content-inverted-interactive-highlight, #f7f8fa);
      background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);

      &:hover {
        background-color: var(--semantic-color-surface-interactive-primary-hover, #0e3896);
      }
    }
  }

  &.off {
    .icon-container {
      flex-direction: row-reverse;

      color: var(--semantic-color-content-interactive-primary-default, #2160e1);
      background-color: var(--semantic-color-surface-base-secondary, #f7f8fa);
      border-color: var(--semantic-color-border-base-secondary, #bac2cf);

      &:hover {
        background-color: var(--semantic-color-surface-interactive-secondary-hover, #f2f7ff);
      }
    }
  }

  &.on,
  &.off {
    &.disabled {
      .icon-container {
        border-color: var(--semantic-color-border-interactive-primary-disabled, #cad0dc);
        background-color: var(--semantic-color-surface-interactive-primary-disabled, #e9eef3);

        color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
      }
    }
  }
}
