.help-container {
  position: fixed;
  z-index: 25;

  &.bottom-right {
    bottom: 0;
    right: 0;
  }
}

.help-content-container {
  position: relative;
}

.help-button {
  position: absolute;
  right: 12px;
  bottom: 12px;

  width: auto;
  height: 44px;
  padding: 10px;

  opacity: 0.7;
  text-align: center;

  background: var(--semantic-color-surface-interactive-primary-default, #2160e1);
  border-radius: var(--semantic-cornerRadius-240, 24px);

  &:hover {
    cursor: pointer;
    opacity: 1;
  }
}

.help-browser-container {
  position: absolute;
  right: 12px;
  bottom: 12px;

  width: 340px;
  height: 480px;
  min-width: 340px;
  min-height: 480px;

  border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  background: var(--semantic-color-surface-fields-default, #fff);
  @extend .shadow-medium;
}

.help-browser-header-container {
  position: relative;
  @extend .typography-paragraph1;

  padding: 8px 16px;
  color: var(--semantic-color-content-base-secondary, #4a5468);
  background: var(--semantic-color-surface-fields-default, #fff);

  cursor: move;

  transition: transform 0.23s; /* Sticking effect */
  // pointer-events: none; /* Allow clicking trough the div */
}

.help-browser-body-container {
  height: calc(100% - 55px);
  background: var(--semantic-color-surface-fields-default, #fff);
  border-top: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  position: relative;
}

.help-browser-iframe-container {
  width: 100%;
  height: 100%;
}
