.list-builder-container {
  max-width: calc(100% - 1px);

  .left-section {
    max-width: calc(100% - 80px);
  }

  .list-builder-text-area-container {
    margin: 0;
  }

  .list-builder-text-area {
    height: 36px;

    &.expanded {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      height: auto;
    }
  }

  .builder-list-container {
    gap: 4px;

    border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
    border-top: none;

    border-radius: 0 0 var(--semantic-cornerRadius-60, 6px) var(--semantic-cornerRadius-60, 6px);
  }

  .builder-list-search-container {
    padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-120, 12px);
  }

  .builder-item-container {
    max-height: 260px;
    overflow-y: scroll;
  }

  .builder-item {
    padding: var(--semantic-spacing-px-40, 4px) var(--semantic-spacing-px-120, 12px);

    .detail-section {
      padding: var(--semantic-spacing-px-60, 6px) 0;
    }

    &:hover {
      background-color: var(--semantic-color-surface-menu-hover, #e9eef3);
    }
  }

  .selected-items {
    background-color: transparent;
    border-color: transparent;
  }

  .right-section {
    margin-left: var(--semantic-spacing-px-60, 6px);
  }

  .no-records-section {
    padding: var(--semantic-spacing-px-120, 12px);
    text-align: center;
    color: var(--semantic-color-content-interactive-primary-default, #2160e1);
  }
}
