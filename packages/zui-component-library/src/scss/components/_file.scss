.file-browser-label {
  cursor: pointer;
}

.file-browser-button {
  pointer-events: none;
}

.file-detail-container {
  &.left {
    margin-right: var(--semantic-spacing-px-120, 12px);
  }
  &.right {
    margin-left: var(--semantic-spacing-px-120, 12px);
  }
}

.file {
  opacity: 0;
  height: 0;
  width: 0;
}

.file-browser-form-container {
  flex-direction: row;
}

.download-file-container,
.file-browser-label {
  &.label {
    margin-bottom: 0;
  }
}

.failed-records-container {
  margin-top: var(--semantic-spacing-px-120, 12px);

  .record {
    padding-bottom: var(--semantic-spacing-px-80, 8px);
  }
}

.file-browser-container {
  width: 100%;

  .file-browser-label {
    flex-shrink: 0;
  }
}
