.tabs-container {
  display: flex;

  border-bottom: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

  margin-bottom: 12px;
}

.tab {
  padding: 6px 12px;
  @extend .typography-paragraph1;

  color: var(--semantic-color-content-base-primary, #131a2e);
  border-bottom: 2px solid transparent;

  &:focus {
    color: var(--semantic-color-content-interactive-primary-hover, #0e3896);
    border: 2px solid var(--semantic-color-border-focus-default, #236bf5);
  }

  &:hover {
    cursor: pointer;
    border-bottom: 1px solid var(--semantic-color-border-interactive-primary-hover, #0e3896);
  }

  &:disabled {
    color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
  }

  &.first-layer {
    &.active {
      color: var(--semantic-color-content-interactive-primary-default, #2160e1);
      border-bottom: 2px solid var(--semantic-color-border-interactive-primary-default, #2160e1);
    }
  }
}
