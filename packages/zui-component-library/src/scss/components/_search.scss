.search-container {
  &::placeholder {
    color: var(--semantic-color-content-base-tertiary, #677289);
  }

  color: var(--semantic-color-content-base-primary, #131a2e);

  @extend .typography-paragraph1;

  &.light {
    background: var(--semantic-color-surface-fields-default, #fff);
  }

  & .icon {
    color: var(--semantic-color-content-base-primary, #131a2e);
  }

  &.field {
    margin: 0;
  }

  .input-container,
  .input {
    height: 38px;
  }
}

.dropdown-with-search {
  display: flex;
  align-items: center;

  .dropdown-container {
    .selected-items {
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      height: 38px;
      align-items: center;
    }
  }

  .search-container {
    .input {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      border-left-color: transparent;
    }
  }
}
