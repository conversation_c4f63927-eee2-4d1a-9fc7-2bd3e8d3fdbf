.modal-container {
  display: flex;
  flex-flow: column wrap;
  justify-content: center;
  align-items: center;
  align-content: center;

  position: fixed;

  top: 0;
  left: 0;

  z-index: 15;

  &.right {
    right: 0;
    left: unset;
    align-content: end;
  }

  &.is-blocking {
    width: 100%;
    height: 100%;

    background-color: #19191a80;
  }
}

.modal-content-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--semantic-spacing-px-160, 16px);

  width: 100%;

  padding: var(--semantic-spacing-px-240, 24px);

  @extend .shadow-large;

  border-radius: var(--semantic-cornerRadius-40, 4px);
  background: var(--semantic-color-surface-elevated-low20, #fff);
}

.modal-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: center;

  width: 100%;

  color: var(--semantic-color-content-base-primary, #131a2e);

  @extend .typography-header5;
}

.modal-body-container {
  display: flex;
  flex-flow: column nowrap;

  width: 100%;
  max-height: 100%;

  overflow-y: auto;
}

.modal-footer-container {
  display: flex;

  width: 100%;

  padding-top: var(--semantic-spacing-px-160, 16px);

  border-top: 1px solid transparent;
}

.crud-modal {
  &.right {
    .modal-content-container {
      min-width: 835px;
      max-width: 1000px;
      height: 100%;
    }

    .modal-body-container {
      height: calc(100vh - 156px);
    }

    .modal-footer-container {
      border-top: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
    }
  }

  &.center {
    .modal-content-container {
      width: 640px;
    }

    .modal-body-container {
      max-height: calc(100vh - 220px);
    }
  }

  .card {
    padding-left: 0;

    &:not(.action-confirmation-container) {
      padding-top: 0px;
    }

    & .text-tooltip-content-container {
      padding-top: 20px;
    }
  }
}
