.date-range-picker-container,
.inline-date-picker-container {
  display: flex;
  flex-direction: column;

  background-color: var(--semantic-color-surface-base-primary, #fff);
  border-radius: var(--semantic-cornerRadius-80, 8px);

  @extend .shadow-large;

  p {
    margin: 0;
  }

  & .picker-container {
    display: flex;

    padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-160, 16px);

    .left,
    .right {
      display: flex;
      flex-grow: 1;

      flex-direction: column;

      & .title {
        color: var(--semantic-color-content-base-tertiary, #677289);
        margin-bottom: 8px;

        @extend .typography-paragraph1;
        @extend .typography-paragraph1-uppercase;
      }
    }

    .left {
      margin-right: 16px;
    }
  }

  & .message-container {
    padding: 0 16px 8px 16px;
    flex-grow: 1;

    text-align: center;
    font-size: 12px;

    & .message {
      margin: 0;

      &.info {
        color: var(--semantic-color-content-interactive-primary-default, #2160e1);
      }
    }
  }

  & .footer {
    padding: 8px 16px;
    text-align: left;

    border-top: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
  }

  .react-datepicker {
    border: none;
    width: 100%;

    background-color: var(--semantic-color-surface-base-primary, #fff);

    & .react-datepicker__header {
      padding-top: 0px;
      border-radius: 0px;

      border: none;

      background-color: inherit;
      color: inherit;

      & .react-datepicker__current-month {
        color: var(--semantic-color-content-interactive-primary-default, #2160e1);

        @extend .typography-paragraph1;

        text-transform: uppercase;

        padding: 8px;
      }

      & .react-datepicker__day-names {
        padding: 5px;

        & .react-datepicker__day-name {
          display: inline-block;
          text-align: center;
          vertical-align: top;
          min-width: 30px;
          width: calc(100% / 7);

          text-align: center;
          opacity: 0.9;
          margin: 0;

          color: var(--semantic-color-content-base-primary, #131a2e);

          @extend .typography-paragraph2;

          text-transform: uppercase;
        }
      }
    }

    & .react-datepicker__month-container {
      width: 100%;

      & .react-datepicker__month {
        border-top: 0px;
        min-height: 186px;
        margin: 0;
        margin-bottom: 12px;
      }
    }

    & .react-datepicker__week {
      display: flex;
      justify-content: center;
      align-items: center;

      padding: var(--semantic-spacing-px-50, 5px);
      width: 100%;

      & .react-datepicker__day {
        display: flex;
        justify-content: center;
        align-items: center;

        flex-grow: 1;

        text-align: center;
        vertical-align: top;

        min-width: 0;
        margin: 0;

        width: 36px;
        height: 36px;

        cursor: pointer;

        background-color: var(--semantic-color-surface-base-primary, #fff);
        color: var(--semantic-color-content-base-primary, #131a2e);

        border: 1px solid transparent;

        @extend .typography-paragraph1;
        @extend .typography-paragraph1-strong;

        &:hover {
          background-color: var(--semantic-color-surface-table-row-hover, #f2f7ff);
        }

        &.react-datepicker__day--in-selecting-range {
          background-color: inherit;

          &:hover {
            border-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
            border-radius: 50%;
          }
        }

        &.react-datepicker__day--in-range {
          font-weight: bold;
          background-color: var(--semantic-color-surface-table-row-hover, #f2f7ff);
        }

        &.react-datepicker__day--selected {
          background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
          color: var(--semantic-color-content-inverted-base-primary, #fff);

          border-radius: 50%;

          @extend .typography-paragraph2;

          &:hover {
            background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
            color: var(--content-inverted-base-primary, #fff);
          }
        }

        &.react-datepicker__day--disabled {
          cursor: default;
          opacity: 0.5;

          color: var(--semantic-color-content-base-tertiary, #949eb2);

          &:hover {
            color: var(--semantic-color-content-base-tertiary, #949eb2);
          }
        }

        &.react-datepicker__day--outside-month {
          background-color: inherit;
          color: transparent;
          pointer-events: none;
        }
      }
    }
  }
}

.inline-date-picker-container {
  padding: 8px;
}
