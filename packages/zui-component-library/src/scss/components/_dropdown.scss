// .dd-selected-items-color-default {
//   &:not(.disabled):hover:active {
//     color: var(--semantic-color-content-base-primary, #131a2e);
//   }
// }

// .dd-selected-items-bkg-default {
//   background-color: $color-lighter-gray-background;
//   border-color: $color-lighter-gray-background;
// }

// .dd-selected-items-color-primary {
//   color: $color-white-background;
// }

// .dd-selected-items-bkg-primary {
//   background-color: $color-standard-blue-text;
//   border-color: $color-standard-blue-text;

//   &:not(.disabled):hover {
//     background-color: $color-medium-blue-hover;
//     border-color: $color-medium-blue-hover;
//   }

//   &:not(.disabled):active {
//     background-color: $color-dark-blue-active;
//     border-color: $color-dark-blue-active;
//   }
// }

// .dd-selected-items-color-secondary {
//   color: $color-standard-blue-text;
//   border-color: $color-standard-blue-text;

//   &.disabled {
//     color: $color-medium-gray-disabled;
//     border-color: $color-medium-gray-disabled;
//   }

//   &:not(.disabled):hover {
//     color: $color-medium-blue-hover;
//     border-color: $color-medium-blue-hover;
//   }

//   &:not(.disabled):active {
//     color: $color-dark-blue-active;
//     border-color: $color-dark-blue-active;
//   }
// }

// .dd-selected-items-bkg-secondary {
//   &.disabled {
//     background-color: $color-lightest-gray-background;
//     border-color: $color-lightest-gray-background;
//   }
// }

// .dd-selected-items-color-tertiary {
//   color: $color-standard-blue-text;

//   &:not(.disabled):hover {
//     color: $color-medium-blue-hover;
//   }

//   &:not(.disabled):active {
//     color: $color-dark-blue-active;
//   }
// }

// .dd-selected-items-bkg-tertiary {
//   background-color: $color-lightest-blue-background;
//   border-color: $color-lightest-blue-background;
// }

.dd-selected-items-error {
  border-color: var(--semantic-color-border-interactive-danger-default, #dc362e);
}

.dropdown-container {
  position: relative;

  .selected-items {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    gap: 4px;

    padding: var(--semantic-spacing-px-60, 4px) var(--semantic-spacing-px-60, 6px);

    border-radius: var(--semantic-cornerRadius-60, 6px);

    cursor: pointer;

    color: var(--semantic-color-content-base-tertiary, #677289);
    border: 1px solid var(--semantic-color-border-interactive-secondary-default, #cad0dc);
    background-color: var(--semantic-color-surface-fields-default, #fff);

    &.disabled {
      // color: $color-medium-gray-disabled;
      color: var(--semantic-color-content-interactive-primary-disabled, #8590a6);
      border-color: 1px solid var(--semantic-color-border-interactive-secondary-disabled, #e9eef3);
      background-color: var(--semantic-color-surface-fields-disabled, #f7f8fa);

      cursor: not-allowed;
    }

    &:not(.disabled):hover {
      color: var(--semantic-color-content-base-primary, #131a2e);
    }

    &:not(.disabled).active {
      color: var(--semantic-color-content-base-primary, #131a2e);
      border-color: var(--semantic-color-border-interactive-secondary-active, #236bf5);
    }

    .left-content {
      @extend .typography-paragraph1;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      display: flex;
      align-items: center;
    }

    &.error {
      border: 1px solid var(--semantic-color-border-status-danger-default, #fda2a1);
      background-color: var(--semantic-color-surface-status-danger-default, #ffedee);
    }
  }
}

.items-selection-container {
  z-index: 10;

  & > .items-selection {
    border-radius: var(--semantic-cornerRadius-80, 8px);
    background-color: var(--semantic-color-surface-elevated-low10, #fff);

    /* shadow/elevation/medium */
    @extend .shadow-medium;
  }

  .items-selection {
    display: flex;
    flex-flow: column nowrap;
    // align-items: flex-start;
    gap: var(--semantic-spacing-px-0, 0px);

    padding: var(--semantic-spacing-px-80, 8px);

    position: relative;

    height: inherit;
    max-height: inherit;

    .search-section {
      padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-120, 12px);
    }

    .no-item-section {
      @extend .typography-paragraph1;

      padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-120, 12px);
    }

    .items {
      width: 100%;
      height: 100%;

      position: relative;

      overflow-y: scroll;

      & .load-more-container {
        border-top: 1px solid var(--semantic-color-border-interactive-primary-disabled, #8590a6);
      }

      &.loading {
        min-height: 100px;
      }
    }

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--semantic-spacing-px-100, 10px);

      @extend .typography-paragraph1;

      padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-120, 12px);

      border-radius: var(--semantic-cornerRadius-60, 6px);
      background-color: var(--semantic-color-surface-interactive-secondary-default, #fff);

      color: var(--semantic-color-content-base-primary, #131a2e);

      .section {
        display: flex;
        align-items: center;
      }

      .with-text {
        max-width: calc(100% - 4px);
      }

      .has-right-section {
        max-width: calc(100% - 12px);
      }

      .text {
        word-break: break-all;
      }

      &:hover {
        cursor: pointer;
        background-color: var(--semantic-color-surface-menu-hover, #e9eef3);
      }

      &.selected {
        background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);
        color: var(--semantic-color-content-inverted-base-primary, #fff);

        &:hover {
          background: var(--semantic-color-surface-interactive-primary-default, #2160e1);
          color: var(--semantic-color-content-inverted-base-primary, #fff);
        }
      }
    }
  }
}

.multi-selection-container {
  display: flex;
  flex-flow: column nowrap;

  position: relative;

  z-index: 10;

  width: 560px;
  height: inherit;
  max-height: inherit;

  border-radius: var(--semantic-cornerRadius-80, 8px);
  background-color: var(--semantic-color-surface-elevated-low10, #fff);

  /* shadow/elevation/medium */
  @extend .shadow-medium;

  .selections-options {
    display: flex;
    flex-wrap: nowrap;

    height: inherit;
    max-height: inherit;

    .option {
      width: 50%;

      &:first-of-type {
        border-right: 1px solid var(--semantic-color-border-interactive-primary-disabled, #8590a6);
      }

      .items-selection {
        min-height: 200px;
        height: calc(100% - 70px);
        max-height: calc(100% - 70px);

        .items {
          padding-bottom: var(--semantic-spacing-px-400, 40px);
        }
      }

      .head-section {
        display: flex;
        align-items: center;

        padding: var(--semantic-spacing-px-120, 12px);
        padding-bottom: var(--semantic-spacing-px-0, 0px);
      }
    }
  }

  .actions-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    position: absolute;

    left: 0;
    bottom: 0;
    width: 100%;

    padding: var(--semantic-spacing-px-0, 0px) var(--semantic-spacing-px-80, 8px)
      var(--semantic-spacing-px-80, 8px);
    background-color: var(--semantic-color-surface-elevated-low10, #fff);

    .section {
      display: flex;
      align-items: center;
    }
  }

  .has-border-right {
    border-right: 1px solid var(--semantic-color-border-interactive-primary-disabled, #8590a6);
  }
}
