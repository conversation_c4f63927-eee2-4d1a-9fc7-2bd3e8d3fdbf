.toast-container {
  display: flex;
  flex-flow: column wrap;
  justify-content: flex-start;
  align-items: center;
  align-content: center;
  gap: var(--semantic-spacing-px-120, 12px);

  position: fixed;

  top: 39px;

  left: 50%;
  transform: translate(-50%);

  z-index: 20;
}

.toast {
  gap: var(--semantic-spacing-px-160, 16px);

  min-width: 480px;
  max-width: 750px;

  margin-bottom: var(--semantic-spacing-px-80, 8px);

  text-align: center;

  border-radius: var(--semantic-cornerRadius-80, 8px);

  @extend .shadow-small;

  @extend .typography-paragraph1-strong;

  color: var(--semantic-color-content-base-primary, #131a2e);

  padding: var(--semantic-spacing-px-100, 10px) var(--semantic-spacing-px-120, 12px);

  & .action-container {
    &:hover {
      cursor: pointer;
    }
  }

  & .text-container {
    text-align: left;
    width: calc(100% - 80px);
    justify-content: center;

    .text-content {
      word-wrap: break-word;
      width: 100%;
    }
  }

  background: var(--semantic-color-surface-status-neutral-default, #4a5468);

  & .icon-container,
  & .action-container {
    color: var(--semantic-color-content-status-neutral-secondary, #8590a6);
  }

  &.help {
    background: var(--semantic-color-surface-status-info-default, #f2f7ff);

    & .icon-container,
    & .action-container {
      color: var(--semantic-color-content-status-info-secondary, #2160e1);
    }
  }

  &.success {
    background: var(--semantic-color-surface-status-success-default, #f0ffee);

    & .icon-container,
    & .action-container {
      color: var(--semantic-color-content-status-success-secondary, #569b4b);
    }
  }

  &.warning {
    background: var(--semantic-color-surface-status-warning-default, #fff4ee);

    & .icon-container,
    & .action-container {
      color: var(--semantic-color-content-status-warning-secondary, #eb6e27);
    }
  }

  &.error {
    background-color: var(--semantic-color-surface-status-danger-default, #ffedee);

    & .icon-container,
    & .action-container {
      color: var(--semantic-color-content-status-danger-secondary, #dc362e);
    }
  }
}
