.dashboard-card {
  display: flex;

  flex-direction: column;
  align-items: flex-start;
  gap: var(--semantic-spacing-px-80, 8px);

  padding: var(--semantic-spacing-px-160, 16px);

  border-radius: var(--semantic-cornerRadius-80, 8px);

  border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

  width: 25%;

  .icon-container {
    // background-color: #f2f7ff;
    color: var(--semantic-color-content-interactive-primary-active, #194cbb);
  }

  .label-container {
    @extend .typography-paragraph1-strong;
  }

  .list-container {
    display: flex;

    align-items: flex-start;
    gap: var(--semantic-spacing-px-160, 16px);

    .detail-container {
      display: flex;

      flex-direction: column;
      align-items: flex-start;
      gap: var(--semantic-spacing-px-40, 4px);
    }

    .value-container {
      @extend .typography-paragraph1-strong;

      font-size: var(--semantic-fontSize-large, 1.5rem);
    }

    .name-container {
      color: var(--semantic-color-content-base-tertiary, #677289);
      font-feature-settings:
        'clig' off,
        'liga' off;

      @extend .typography-paragraph2;
    }
  }
}

.dashboard-chart-container {
  width: 100%;

  border-radius: var(--semantic-cornerRadius-80, 8px);
  border: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);

  .label-container {
    @extend .typography-paragraph1-strong;

    flex-grow: 0;
  }

  .dashboard-chart {
    width: 85%;
    max-width: calc(100% - 65px);
    height: 377px;
  }

  .no-data-available {
    height: 100%;
  }
}
