.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-120, 12px);

  border: 1px solid transparent;
  border-top-color: var(--semantic-color-border-base-primary, #dde3ea);
  border-radius: 0 0 var(--semantic-cornerRadius-60, 6px) var(--semantic-cornerRadius-60, 6px);

  background-color: var(--semantic-color-surface-fields-default, #fff);
}

.page-navigator-container {
  display: flex;

  .navigator-button {
    padding: 0 1rem;
  }
}

.goto-page-container {
  display: flex;
  align-items: center;
}

.goto-page-container-input {
  width: 32px;
  margin: 0;

  .input {
    background-color: var(--semantic-color-surface-fields-default, #fff);
  }
}

.range-container {
  padding-left: 1rem;
}
