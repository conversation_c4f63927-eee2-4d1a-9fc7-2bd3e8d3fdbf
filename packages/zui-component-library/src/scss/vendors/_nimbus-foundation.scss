:root {
  --semantic-color-background-primary: #fff;
  --semantic-color-background-secondary: #f7f8fa;
}
[data-mode='dark'] {
  --semantic-color-background-primary: #0d1322;
  --semantic-color-background-secondary: #374054;
}
:root {
  --semantic-color-border-base-subdued: #e9eef3;
  --semantic-color-border-base-primary: #dde3ea;
  --semantic-color-border-base-secondary: #bac2cf;
  --semantic-color-border-base-tertiary: #949eb2;
  --semantic-color-border-inverted-primary: #fff;
  --semantic-color-border-interactive-primary-default: #2160e1;
  --semantic-color-border-interactive-primary-hover: #0e3896;
  --semantic-color-border-interactive-primary-active: #194cbb;
  --semantic-color-border-interactive-primary-disabled: #cad0dc;
  --semantic-color-border-interactive-primary-focus: #0e3896;
  --semantic-color-border-interactive-secondary-default: #cad0dc;
  --semantic-color-border-interactive-secondary-hover: #a3acbd;
  --semantic-color-border-interactive-secondary-active: #0e3896;
  --semantic-color-border-interactive-secondary-disabled: #cad0dc;
  --semantic-color-border-interactive-secondary-focus: #0e3896;
  --semantic-color-border-interactive-danger-default: #dc362e;
  --semantic-color-border-interactive-danger-hover: #8e201c;
  --semantic-color-border-interactive-danger-active: #b1322a;
  --semantic-color-border-interactive-danger-disabled: #cad0dc;
  --semantic-color-border-interactive-danger-focus: #8e201c;
  --semantic-color-border-severity-lowest-default: #bde5de;
  --semantic-color-border-severity-lowest-active: #3da592;
  --semantic-color-border-severity-low-default: #fcde62;
  --semantic-color-border-severity-low-active: #e3b80d;
  --semantic-color-border-severity-medium-default: #fecfa2;
  --semantic-color-border-severity-medium-active: #f19325;
  --semantic-color-border-severity-high-default: #fda2a1;
  --semantic-color-border-severity-high-active: #f16f69;
  --semantic-color-border-severity-critical-default: #fb99b2;
  --semantic-color-border-severity-critical-active: #f36185;
  --semantic-color-border-focus: #236bf5;
  --semantic-color-border-status-info-default: #a2c1ff;
  --semantic-color-border-status-info-active: #5588fb;
  --semantic-color-border-status-success-default: #a1d399;
  --semantic-color-border-status-success-active: #77b86d;
  --semantic-color-border-status-warning-default: #fbad7f;
  --semantic-color-border-status-warning-active: #f18445;
  --semantic-color-border-status-danger-default: #fda2a1;
  --semantic-color-border-status-danger-active: #dc362e;
  --semantic-color-border-status-neutral-default: #bac2cf;
  --semantic-color-border-status-neutral-active: #949eb2;
  --semantic-color-border-accent-pink-default: #fbc5dd;
  --semantic-color-border-accent-pink-active: #f36185;
  --semantic-color-border-accent-purple-default: #f2c8f9;
  --semantic-color-border-accent-purple-active: #ca5ce0;
  --semantic-color-border-accent-deepPurple-default: #d7c4fc;
  --semantic-color-border-accent-deepPurple-active: #9e72f1;
  --semantic-color-border-accent-blue-default: #a2c1ff;
  --semantic-color-border-accent-blue-active: #236bf5;
  --semantic-color-border-accent-lightBlue-default: #cce9fc;
  --semantic-color-border-accent-lightBlue-active: #59b6f3;
  --semantic-color-border-accent-teal-default: #bde5de;
  --semantic-color-border-accent-teal-active: #5ab2a1;
  --semantic-color-border-accent-green-default: #cbeec5;
  --semantic-color-border-accent-green-active: #77b86d;
  --semantic-color-border-accent-yellow-default: #feea99;
  --semantic-color-border-accent-yellow-active: #ffd118;
  --semantic-color-border-accent-amber-default: #fbe8b9;
  --semantic-color-border-accent-amber-active: #f4c351;
  --semantic-color-border-accent-orange-default: #fecfa2;
  --semantic-color-border-accent-orange-active: #f6a248;
  --semantic-color-border-accent-deepOrange-default: #fbad7f;
  --semantic-color-border-accent-deepOrange-active: #f18445;
  --semantic-color-border-accent-red-default: #fda2a1;
  --semantic-color-border-accent-red-active: #e8544c;
  --semantic-color-border-accent-brown-default: #dcbdad;
  --semantic-color-border-accent-brown-active: #b08775;
  --semantic-color-border-accent-blueGrey-default: #bac2cf;
  --semantic-color-border-accent-blueGrey-active: #949eb2;
}
[data-mode='dark'] {
  --semantic-color-border-base-subdued: #242c41;
  --semantic-color-border-base-primary: #2e364a;
  --semantic-color-border-base-secondary: #404a5e;
  --semantic-color-border-base-tertiary: #586378;
  --semantic-color-border-inverted-primary: #131a2e;
  --semantic-color-border-interactive-primary-default: #7ba5fe;
  --semantic-color-border-interactive-primary-hover: #d4e2ff;
  --semantic-color-border-interactive-primary-active: #a2c1ff;
  --semantic-color-border-interactive-primary-disabled: #4a5468;
  --semantic-color-border-interactive-primary-focus: #d4e2ff;
  --semantic-color-border-interactive-secondary-default: #2e364a;
  --semantic-color-border-interactive-secondary-hover: #404a5e;
  --semantic-color-border-interactive-secondary-active: #d4e2ff;
  --semantic-color-border-interactive-secondary-disabled: #4a5468;
  --semantic-color-border-interactive-secondary-focus: #d4e2ff;
  --semantic-color-border-interactive-danger-default: #e8544c;
  --semantic-color-border-interactive-danger-hover: #fda2a1;
  --semantic-color-border-interactive-danger-active: #f16f69;
  --semantic-color-border-interactive-danger-disabled: #4a5468;
  --semantic-color-border-interactive-danger-focus: #fda2a1;
  --semantic-color-border-severity-lowest-default: #2e7a6d;
  --semantic-color-border-severity-lowest-active: #3da592;
  --semantic-color-border-severity-low-default: #795f0c;
  --semantic-color-border-severity-low-active: #b9960e;
  --semantic-color-border-severity-medium-default: #8d5513;
  --semantic-color-border-severity-medium-active: #be731c;
  --semantic-color-border-severity-high-default: #a32b24;
  --semantic-color-border-severity-high-active: #dc362e;
  --semantic-color-border-severity-critical-default: #a22255;
  --semantic-color-border-severity-critical-active: #e34c75;
  --semantic-color-border-focus: #7ba5fe;
  --semantic-color-border-status-info-default: #194cbb;
  --semantic-color-border-status-info-active: #2160e1;
  --semantic-color-border-status-success-default: #346c2b;
  --semantic-color-border-status-success-active: #4b8b40;
  --semantic-color-border-status-warning-default: #8b3f17;
  --semantic-color-border-status-warning-active: #ba561f;
  --semantic-color-border-status-danger-default: #8e201c;
  --semantic-color-border-status-danger-active: #bf392f;
  --semantic-color-border-status-neutral-default: #4a5468;
  --semantic-color-border-status-neutral-active: #677289;
  --semantic-color-border-accent-pink-default: #a22255;
  --semantic-color-border-accent-pink-active: #d33466;
  --semantic-color-border-accent-purple-default: #88269b;
  --semantic-color-border-accent-purple-active: #ac33c4;
  --semantic-color-border-accent-deepPurple-default: #6022c7;
  --semantic-color-border-accent-deepPurple-active: #732fe4;
  --semantic-color-border-accent-blue-default: #1442a8;
  --semantic-color-border-accent-blue-active: #2160e1;
  --semantic-color-border-accent-lightBlue-default: #1c79ae;
  --semantic-color-border-accent-lightBlue-active: #28a9f1;
  --semantic-color-border-accent-teal-default: #2e7a6d;
  --semantic-color-border-accent-teal-active: #3da592;
  --semantic-color-border-accent-green-default: #3f7b35;
  --semantic-color-border-accent-green-active: #62ab57;
  --semantic-color-border-accent-yellow-default: #795f0c;
  --semantic-color-border-accent-yellow-active: #b9960e;
  --semantic-color-border-accent-amber-default: #a5701b;
  --semantic-color-border-accent-amber-active: #b88121;
  --semantic-color-border-accent-orange-default: #8d5513;
  --semantic-color-border-accent-orange-active: #be731c;
  --semantic-color-border-accent-deepOrange-default: #a24b1b;
  --semantic-color-border-accent-deepOrange-active: #d26223;
  --semantic-color-border-accent-red-default: #b1322a;
  --semantic-color-border-accent-red-active: #dc362e;
  --semantic-color-border-accent-brown-default: #906a5a;
  --semantic-color-border-accent-brown-active: #b08775;
  --semantic-color-border-accent-blueGrey-default: #586378;
  --semantic-color-border-accent-blueGrey-active: #76829a;
}
:root {
  --semantic-color-brand-white: #fff;
  --semantic-color-brand-pale04: #f2f7ff;
  --semantic-color-brand-pale03: #e3ecff;
  --semantic-color-brand-pale02: #d4e2ff;
  --semantic-color-brand-pale01: #7ba5fe;
  --semantic-color-brand-subdued: #5588fb;
  --semantic-color-brand-default: #2160e1;
  --semantic-color-brand-strong01: #194cbb;
  --semantic-color-brand-strong02: #0e3896;
  --semantic-color-brand-strong03: #002260;
}
[data-mode='dark'] {
  --semantic-color-brand-white: #fff;
  --semantic-color-brand-pale04: #05193b;
  --semantic-color-brand-pale03: #002260;
  --semantic-color-brand-pale02: #0e3896;
  --semantic-color-brand-pale01: #2160e1;
  --semantic-color-brand-subdued: #5588fb;
  --semantic-color-brand-default: #7ba5fe;
  --semantic-color-brand-strong01: #a2c1ff;
  --semantic-color-brand-strong02: #d4e2ff;
  --semantic-color-brand-strong03: #e3ecff;
}
:root {
  --semantic-color-content-base-primary: #131a2e;
  --semantic-color-content-base-secondary: #4a5468;
  --semantic-color-content-base-tertiary: #677289;
  --semantic-color-content-base-brand: #2160e1;
  --semantic-color-content-base-subdued: #677289;
  --semantic-color-content-interactive-primary-default: #2160e1;
  --semantic-color-content-interactive-primary-hover: #0e3896;
  --semantic-color-content-interactive-primary-active: #194cbb;
  --semantic-color-content-interactive-primary-disabled: #8590a6;
  --semantic-color-content-interactive-primary-disabledOnSecondary: #586378;
  --semantic-color-content-interactive-primary-focus: #0e3896;
  --semantic-color-content-inverted-base-primary: #fff;
  --semantic-color-content-inverted-base-secondary: #e9eef3;
  --semantic-color-content-inverted-base-tertiary: #cad0dc;
  --semantic-color-content-inverted-interactive-disabled: #cad0dc;
  --semantic-color-content-inverted-interactive-highlight: #f7f8fa;
  --semantic-color-content-immutable-white: #fff;
  --semantic-color-content-immutable-lightGrey: #677289;
  --semantic-color-content-immutable-grey: #4a5468;
  --semantic-color-content-immutable-black: #131a2e;
  --semantic-color-content-immutable-disabled: #8590a6;
  --semantic-color-content-severity-lowest-primary: #296d60;
  --semantic-color-content-severity-lowest-secondary: #389685;
  --semantic-color-content-severity-low-primary: #654e0a;
  --semantic-color-content-severity-low-secondary: #e3b80d;
  --semantic-color-content-severity-medium-primary: #76460f;
  --semantic-color-content-severity-medium-secondary: #f19325;
  --semantic-color-content-severity-high-primary: #8e201c;
  --semantic-color-content-severity-high-secondary: #e8544c;
  --semantic-color-content-severity-critical-primary: #8b1a4b;
  --semantic-color-content-severity-critical-secondary: #e34c75;
  --semantic-color-content-status-info-primary: #0e3896;
  --semantic-color-content-status-info-secondary: #2160e1;
  --semantic-color-content-status-success-primary: #1e4e16;
  --semantic-color-content-status-success-secondary: #569b4b;
  --semantic-color-content-status-warning-primary: #8b3f17;
  --semantic-color-content-status-warning-secondary: #eb6e27;
  --semantic-color-content-status-danger-primary: #8e201c;
  --semantic-color-content-status-danger-secondary: #dc362e;
  --semantic-color-content-status-neutral-primary: #374054;
  --semantic-color-content-status-neutral-secondary: #8590a6;
  --semantic-color-content-accent-pink-default: #8b1a4b;
  --semantic-color-content-accent-pink-secondary: #e34c75;
  --semantic-color-content-accent-purple-default: #771f88;
  --semantic-color-content-accent-purple-secondary: #bf3ad9;
  --semantic-color-content-accent-deepPurple-default: #50229e;
  --semantic-color-content-accent-deepPurple-secondary: #732fe4;
  --semantic-color-content-accent-blue-default: #0e3896;
  --semantic-color-content-accent-blue-secondary: #2160e1;
  --semantic-color-content-accent-lightBlue-default: #186a98;
  --semantic-color-content-accent-lightBlue-secondary: #28a9f1;
  --semantic-color-content-accent-teal-default: #296d60;
  --semantic-color-content-accent-teal-secondary: #389685;
  --semantic-color-content-accent-green-default: #346c2b;
  --semantic-color-content-accent-green-secondary: #569b4b;
  --semantic-color-content-accent-yellow-default: #795f0c;
  --semantic-color-content-accent-yellow-secondary: #e3b80d;
  --semantic-color-content-accent-amber-default: #7e4f10;
  --semantic-color-content-accent-amber-secondary: #f2b933;
  --semantic-color-content-accent-orange-default: #76460f;
  --semantic-color-content-accent-orange-secondary: #f19325;
  --semantic-color-content-accent-deepOrange-default: #8b3f17;
  --semantic-color-content-accent-deepOrange-secondary: #eb6e27;
  --semantic-color-content-accent-red-default: #8e201c;
  --semantic-color-content-accent-red-secondary: #dc362e;
  --semantic-color-content-accent-brown-default: #5f463c;
  --semantic-color-content-accent-brown-secondary: #a17664;
  --semantic-color-content-accent-blueGrey-default: #4a5468;
  --semantic-color-content-accent-blueGrey-secondary: #8590a6;
}
[data-mode='dark'] {
  --semantic-color-content-base-primary: #e9eef3;
  --semantic-color-content-base-secondary: #bac2cf;
  --semantic-color-content-base-tertiary: #949eb2;
  --semantic-color-content-base-brand: #7ba5fe;
  --semantic-color-content-base-subdued: #76829a;
  --semantic-color-content-interactive-primary-default: #7ba5fe;
  --semantic-color-content-interactive-primary-hover: #d4e2ff;
  --semantic-color-content-interactive-primary-active: #a2c1ff;
  --semantic-color-content-interactive-primary-disabled: #949eb2;
  --semantic-color-content-interactive-primary-disabledOnSecondary: #a3acbd;
  --semantic-color-content-interactive-primary-focus: #d4e2ff;
  --semantic-color-content-inverted-base-primary: #131a2e;
  --semantic-color-content-inverted-base-secondary: #404a5e;
  --semantic-color-content-inverted-base-tertiary: #76829a;
  --semantic-color-content-inverted-interactive-disabled: #949eb2;
  --semantic-color-content-inverted-interactive-highlight: #2160e1;
  --semantic-color-content-immutable-white: #fff;
  --semantic-color-content-immutable-lightGrey: #677289;
  --semantic-color-content-immutable-grey: #4a5468;
  --semantic-color-content-immutable-black: #131a2e;
  --semantic-color-content-immutable-disabled: #8590a6;
  --semantic-color-content-severity-lowest-primary: #bde5de;
  --semantic-color-content-severity-lowest-secondary: #5ab2a1;
  --semantic-color-content-severity-low-primary: #feea99;
  --semantic-color-content-severity-low-secondary: #e3b80d;
  --semantic-color-content-severity-medium-primary: #fecfa2;
  --semantic-color-content-severity-medium-secondary: #f6a248;
  --semantic-color-content-severity-high-primary: #fda2a1;
  --semantic-color-content-severity-high-secondary: #f16f69;
  --semantic-color-content-severity-critical-primary: #fcafc9;
  --semantic-color-content-severity-critical-secondary: #f36185;
  --semantic-color-content-status-info-primary: #d4e2ff;
  --semantic-color-content-status-info-secondary: #5588fb;
  --semantic-color-content-status-success-primary: #b6e1af;
  --semantic-color-content-status-success-secondary: #77b86d;
  --semantic-color-content-status-warning-primary: #fbad7f;
  --semantic-color-content-status-warning-secondary: #f18445;
  --semantic-color-content-status-danger-primary: #fda2a1;
  --semantic-color-content-status-danger-secondary: #e8544c;
  --semantic-color-content-status-neutral-primary: #cad0dc;
  --semantic-color-content-status-neutral-secondary: #949eb2;
  --semantic-color-content-accent-pink-default: #fbdaed;
  --semantic-color-content-accent-pink-secondary: #f36185;
  --semantic-color-content-accent-purple-default: #fbe2ff;
  --semantic-color-content-accent-purple-secondary: #ca5ce0;
  --semantic-color-content-accent-deepPurple-default: #e7dcfe;
  --semantic-color-content-accent-deepPurple-secondary: #8953eb;
  --semantic-color-content-accent-blue-default: #e3ecff;
  --semantic-color-content-accent-blue-secondary: #236bf5;
  --semantic-color-content-accent-lightBlue-default: #e6f5ff;
  --semantic-color-content-accent-lightBlue-secondary: #59b6f3;
  --semantic-color-content-accent-teal-default: #d5f2ed;
  --semantic-color-content-accent-teal-secondary: #5ab2a1;
  --semantic-color-content-accent-green-default: #e0fcdc;
  --semantic-color-content-accent-green-secondary: #77b86d;
  --semantic-color-content-accent-yellow-default: #fff5cc;
  --semantic-color-content-accent-yellow-secondary: #e3b80d;
  --semantic-color-content-accent-amber-default: #fdf1d2;
  --semantic-color-content-accent-amber-secondary: #dfa62d;
  --semantic-color-content-accent-orange-default: #fecfa2;
  --semantic-color-content-accent-orange-secondary: #f6a248;
  --semantic-color-content-accent-deepOrange-default: #fbad7f;
  --semantic-color-content-accent-deepOrange-secondary: #f18445;
  --semantic-color-content-accent-red-default: #ffd4d5;
  --semantic-color-content-accent-red-secondary: #e8544c;
  --semantic-color-content-accent-brown-default: #fae2d4;
  --semantic-color-content-accent-brown-secondary: #be9987;
  --semantic-color-content-accent-blueGrey-default: #e9eef3;
  --semantic-color-content-accent-blueGrey-secondary: #949eb2;
}
:root {
  --semantic-color-surface-base-primary: #fff;
  --semantic-color-surface-base-secondary: #f7f8fa;
  --semantic-color-surface-base-tertiary: #e9eef3;
  --semantic-color-surface-base-brand: #2160e1;
  --semantic-color-surface-interactive-primary-default: #2160e1;
  --semantic-color-surface-interactive-primary-hover: #0e3896;
  --semantic-color-surface-interactive-primary-active: #194cbb;
  --semantic-color-surface-interactive-primary-disabled: #e9eef3;
  --semantic-color-surface-interactive-primary-focus: #0e3896;
  --semantic-color-surface-interactive-secondary-default: #fff;
  --semantic-color-surface-interactive-secondary-hover: #f2f7ff;
  --semantic-color-surface-interactive-secondary-active: #e3ecff;
  --semantic-color-surface-interactive-secondary-disabled: #f7f8fa;
  --semantic-color-surface-interactive-secondary-focus: #e3ecff;
  --semantic-color-surface-interactive-danger-default: #dc362e;
  --semantic-color-surface-interactive-danger-hover: #8e201c;
  --semantic-color-surface-interactive-danger-active: #b1322a;
  --semantic-color-surface-interactive-danger-disabled: #e9eef3;
  --semantic-color-surface-interactive-danger-focus: #8e201c;
  --semantic-color-surface-immutable-darkGrey: #2e364a;
  --semantic-color-surface-elevated-low10: #fff;
  --semantic-color-surface-elevated-low20: #fff;
  --semantic-color-surface-elevated-medium30: #fff;
  --semantic-color-surface-elevated-high40: #fff;
  --semantic-color-surface-severity-lowest-default: #edfffc;
  --semantic-color-surface-severity-lowest-active: #bde5de;
  --semantic-color-surface-severity-low-default: #fefce3;
  --semantic-color-surface-severity-low-active: #feefb3;
  --semantic-color-surface-severity-medium-default: #fff6eb;
  --semantic-color-surface-severity-medium-active: #fedec0;
  --semantic-color-surface-severity-high-default: #ffedee;
  --semantic-color-surface-severity-high-active: #ffbbbc;
  --semantic-color-surface-severity-critical-default: #fdeff9;
  --semantic-color-surface-severity-critical-active: #fbc5dd;
  --semantic-color-surface-status-info-default: #f2f7ff;
  --semantic-color-surface-status-info-active: #d4e2ff;
  --semantic-color-surface-status-success-default: #f0ffee;
  --semantic-color-surface-status-success-active: #cbeec5;
  --semantic-color-surface-status-warning-default: #fff4ee;
  --semantic-color-surface-status-warning-active: #ffd5bc;
  --semantic-color-surface-status-danger-default: #ffedee;
  --semantic-color-surface-status-danger-active: #ffbbbc;
  --semantic-color-surface-status-neutral-default: #f7f8fa;
  --semantic-color-surface-status-neutral-active: #dde3ea;
  --semantic-color-surface-accent-pink-default: #fdeff9;
  --semantic-color-surface-accent-pink-active: #fbc5dd;
  --semantic-color-surface-accent-purple-default: #fdf3ff;
  --semantic-color-surface-accent-purple-active: #f2c8f9;
  --semantic-color-surface-accent-deepPurple-default: #f7f3ff;
  --semantic-color-surface-accent-deepPurple-active: #d7c4fc;
  --semantic-color-surface-accent-blue-default: #f2f7ff;
  --semantic-color-surface-accent-blue-active: #d4e2ff;
  --semantic-color-surface-accent-lightBlue-default: #f2faff;
  --semantic-color-surface-accent-lightBlue-active: #cce9fc;
  --semantic-color-surface-accent-teal-default: #edfffc;
  --semantic-color-surface-accent-teal-active: #bde5de;
  --semantic-color-surface-accent-green-default: #f0ffee;
  --semantic-color-surface-accent-green-active: #cbeec5;
  --semantic-color-surface-accent-yellow-default: #fefce3;
  --semantic-color-surface-accent-yellow-active: #feefb3;
  --semantic-color-surface-accent-amber-default: #fffae6;
  --semantic-color-surface-accent-amber-active: #fbe8b9;
  --semantic-color-surface-accent-orange-default: #fff6eb;
  --semantic-color-surface-accent-orange-active: #fedec0;
  --semantic-color-surface-accent-deepOrange-default: #fff4ee;
  --semantic-color-surface-accent-deepOrange-active: #ffd5bc;
  --semantic-color-surface-accent-red-default: #ffedee;
  --semantic-color-surface-accent-red-active: #ffbbbc;
  --semantic-color-surface-accent-brown-default: #f9f4f1;
  --semantic-color-surface-accent-brown-active: #ebcfc0;
  --semantic-color-surface-accent-blueGrey-default: #f7f8fa;
  --semantic-color-surface-accent-blueGrey-active: #dde3ea;
  --semantic-color-surface-alpha-overlay: #191919a3;
  --semantic-color-surface-alpha-active: #2160e114;
  --semantic-color-surface-nav-topBar-default: #2160e1;
  --semantic-color-surface-nav-topBar-hover: #194cbb;
  --semantic-color-surface-nav-topBar-active: #0e3896;
  --semantic-color-surface-nav-topBar-gradStart: #194cbb;
  --semantic-color-surface-nav-topBar-gradEnd: #2160e1;
  --semantic-color-surface-nav-sideBar-default: #f7f8fa;
  --semantic-color-surface-nav-sideBar-hover: #e9eef3;
  --semantic-color-surface-nav-sideBar-active: #e3ecff;
  --semantic-color-surface-fields-default: #fff;
  --semantic-color-surface-fields-hover: #f7f8fa;
  --semantic-color-surface-fields-active: #f2f7ff;
  --semantic-color-surface-fields-disabled: #e9eef3;
  --semantic-color-surface-menu-default: #fff;
  --semantic-color-surface-menu-hover: #e9eef3;
  --semantic-color-surface-menu-focus: #e9eef3;
  --semantic-color-surface-menu-selected: #e3ecff;
  --semantic-color-surface-menu-selectedHover: #d4e2ff;
  --semantic-color-surface-table-header-default: #f7f8fa;
  --semantic-color-surface-table-row-default: #fff;
  --semantic-color-surface-table-row-hover: #f2f7ff;
  --semantic-color-surface-table-row-active: #e3ecff;
  --semantic-color-surface-skeleton-default-linearStart: #e9eef3;
  --semantic-color-surface-skeleton-default-linearEnd: #dde3ea;
  --semantic-color-surface-skeleton-onBrand-linearStart: #d4e2ff;
  --semantic-color-surface-skeleton-onBrand-linearEnd: #a2c1ff;
}
[data-mode='dark'] {
  --semantic-color-surface-base-primary: #131a2e;
  --semantic-color-surface-base-secondary: #1c2337;
  --semantic-color-surface-base-tertiary: #242c41;
  --semantic-color-surface-base-brand: #5588fb;
  --semantic-color-surface-interactive-primary-default: #7ba5fe;
  --semantic-color-surface-interactive-primary-hover: #a2c1ff;
  --semantic-color-surface-interactive-primary-active: #d4e2ff;
  --semantic-color-surface-interactive-primary-disabled: #374054;
  --semantic-color-surface-interactive-primary-focus: #d4e2ff;
  --semantic-color-surface-interactive-secondary-default: #1c2337;
  --semantic-color-surface-interactive-secondary-hover: #242c41;
  --semantic-color-surface-interactive-secondary-active: #2e364a;
  --semantic-color-surface-interactive-secondary-disabled: #2e364a;
  --semantic-color-surface-interactive-secondary-focus: #242c41;
  --semantic-color-surface-interactive-danger-default: #e8544c;
  --semantic-color-surface-interactive-danger-hover: #fda2a1;
  --semantic-color-surface-interactive-danger-active: #f16f69;
  --semantic-color-surface-interactive-danger-disabled: #374054;
  --semantic-color-surface-interactive-danger-focus: #fda2a1;
  --semantic-color-surface-immutable-darkGrey: #2e364a;
  --semantic-color-surface-elevated-low10: #1c2337;
  --semantic-color-surface-elevated-low20: #242c41;
  --semantic-color-surface-elevated-medium30: #2e364a;
  --semantic-color-surface-elevated-high40: #374054;
  --semantic-color-surface-severity-lowest-default: #1f5249;
  --semantic-color-surface-severity-lowest-active: #245f55;
  --semantic-color-surface-severity-low-default: #513e08;
  --semantic-color-surface-severity-low-active: #654e0a;
  --semantic-color-surface-severity-medium-default: #5f380b;
  --semantic-color-surface-severity-medium-active: #76460f;
  --semantic-color-surface-severity-high-default: #701611;
  --semantic-color-surface-severity-high-active: #7a1614;
  --semantic-color-surface-severity-critical-default: #5e0b36;
  --semantic-color-surface-severity-critical-active: #741341;
  --semantic-color-surface-status-info-default: #001e4e;
  --semantic-color-surface-status-info-active: #002260;
  --semantic-color-surface-status-success-default: #0c3107;
  --semantic-color-surface-status-success-active: #10380c;
  --semantic-color-surface-status-warning-default: #401c06;
  --semantic-color-surface-status-warning-active: #471f09;
  --semantic-color-surface-status-danger-default: #410f10;
  --semantic-color-surface-status-danger-active: #541517;
  --semantic-color-surface-status-neutral-default: #1c2337;
  --semantic-color-surface-status-neutral-active: #242c41;
  --semantic-color-surface-accent-pink-default: #5e0b36;
  --semantic-color-surface-accent-pink-active: #741341;
  --semantic-color-surface-accent-purple-default: #551362;
  --semantic-color-surface-accent-purple-active: #661975;
  --semantic-color-surface-accent-deepPurple-default: #401f77;
  --semantic-color-surface-accent-deepPurple-active: #48218a;
  --semantic-color-surface-accent-blue-default: #012673;
  --semantic-color-surface-accent-blue-active: #0e3896;
  --semantic-color-surface-accent-lightBlue-default: #104c6f;
  --semantic-color-surface-accent-lightBlue-active: #145b83;
  --semantic-color-surface-accent-teal-default: #1f5249;
  --semantic-color-surface-accent-teal-active: #296d60;
  --semantic-color-surface-accent-green-default: #1e4e16;
  --semantic-color-surface-accent-green-active: #295d20;
  --semantic-color-surface-accent-yellow-default: #513e08;
  --semantic-color-surface-accent-yellow-active: #654e0a;
  --semantic-color-surface-accent-amber-default: #5d3c0d;
  --semantic-color-surface-accent-amber-active: #6d450e;
  --semantic-color-surface-accent-orange-default: #5f380b;
  --semantic-color-surface-accent-orange-active: #76460f;
  --semantic-color-surface-accent-deepOrange-default: #5f2a0f;
  --semantic-color-surface-accent-deepOrange-active: #753413;
  --semantic-color-surface-accent-red-default: #701611;
  --semantic-color-surface-accent-red-active: #7a1614;
  --semantic-color-surface-accent-brown-default: #413029;
  --semantic-color-surface-accent-brown-active: #503b32;
  --semantic-color-surface-accent-blueGrey-default: #374054;
  --semantic-color-surface-accent-blueGrey-active: #404a5e;
  --semantic-color-surface-alpha-overlay: #191919a3;
  --semantic-color-surface-alpha-active: #7ba5fe1a;
  --semantic-color-surface-nav-topBar-default: #2160e1;
  --semantic-color-surface-nav-topBar-hover: #002260;
  --semantic-color-surface-nav-topBar-active: #05193b;
  --semantic-color-surface-nav-topBar-gradStart: #2160e1;
  --semantic-color-surface-nav-topBar-gradEnd: #0e3896;
  --semantic-color-surface-nav-sideBar-default: #131a2e;
  --semantic-color-surface-nav-sideBar-hover: #242c41;
  --semantic-color-surface-nav-sideBar-active: #2e364a;
  --semantic-color-surface-fields-default: #131a2e;
  --semantic-color-surface-fields-hover: #242c41;
  --semantic-color-surface-fields-active: #1c2337;
  --semantic-color-surface-fields-disabled: #374054;
  --semantic-color-surface-menu-default: #1c2337;
  --semantic-color-surface-menu-hover: #2e364a;
  --semantic-color-surface-menu-focus: #2e364a;
  --semantic-color-surface-menu-selected: #404a5e;
  --semantic-color-surface-menu-selectedHover: #4a5468;
  --semantic-color-surface-table-header-default: #242c41;
  --semantic-color-surface-table-row-default: #131a2e;
  --semantic-color-surface-table-row-hover: #242c41;
  --semantic-color-surface-table-row-active: #2e364a;
  --semantic-color-surface-skeleton-default-linearStart: #242c41;
  --semantic-color-surface-skeleton-default-linearEnd: #404a5e;
  --semantic-color-surface-skeleton-onBrand-linearStart: #5588fb;
  --semantic-color-surface-skeleton-onBrand-linearEnd: #2160e1;
}
@font-face {
  font-display: swap;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  src:
    url(fonts/Inter/Inter-Regular.woff2?v=3.15) format('woff2'),
    url(fonts/Inter/Inter-Regular.woff?v=3.15) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  src:
    url(fonts/Inter/Inter-Medium.woff2?v=3.15) format('woff2'),
    url(fonts/Inter/Inter-Medium.woff?v=3.15) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  src:
    url(fonts/Inter/Inter-Light.woff2?v=3.15) format('woff2'),
    url(fonts/Inter/Inter-Light.woff?v=3.15) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  src:
    url(fonts/Inter/Inter-Bold.woff2?v=3.15) format('woff2'),
    url(fonts/Inter/Inter-Bold.woff?v=3.15) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Inter;
  font-style: normal;
  font-weight: 100;
  src:
    url(fonts/Inter/Inter-Thin.woff2?v=3.15) format('woff2'),
    url(fonts/Inter/Inter-Thin.woff?v=3.15) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Inter;
  font-style: italic, oblique;
  font-weight: 100;
  src:
    url(fonts/Inter/Inter-Italic.woff2?v=3.15) format('woff2'),
    url(fonts/Inter/Inter-Italic.woff?v=3.15) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  src:
    url(fonts/Source/SourceCodePro-Regular.ttf.woff2?v=2.042) format('woff2'),
    url(fonts/Source/SourceCodePro-Regular.ttf?v=2.042) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  src:
    url(fonts/Source/SourceCodePro-MediumIt.ttf.woff2?v=1.062) format('woff2'),
    url(fonts/Source/SourceCodePro-MediumIt.ttf?v=1.062) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  src:
    url(fonts/Source/SourceCodePro-Light.ttf.woff2?v=2.042) format('woff2'),
    url(fonts/Source/SourceCodePro-Light.ttf?v=2.042) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  src:
    url(fonts/Source/SourceCodePro-Bold.ttf.woff2?v=2.042) format('woff2'),
    url(fonts/Source/SourceCodePro-Bold.ttf?v=2.042) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 100;
  src:
    url(fonts/Source/SourceCodePro-ExtraLight.ttf.woff2?v=2.042) format('woff2'),
    url(fonts/Source/SourceCodePro-ExtraLight.ttf?v=2.042) format('woff');
}
@font-face {
  font-display: swap;
  font-family: Source Code Pro;
  font-style: italic, oblique;
  font-weight: 100;
  src: url(fonts/Source/SourceCodeVF-Italic.ttf.woff2?v=1.026) format('woff2');
}
:root {
  --semantic-color-shadow-ambient: rgba(0, 0, 0, 0.12);
  --semantic-shadow-ambient-large-x: 0;
  --semantic-shadow-ambient-large-y: 0;
  --semantic-shadow-ambient-large-blur: 8px;
  --semantic-shadow-ambient-large-spread: 0;
  --semantic-shadow-ambient-medium-x: 0;
  --semantic-shadow-ambient-medium-y: 0;
  --semantic-shadow-ambient-medium-blur: 6px;
  --semantic-shadow-ambient-medium-spread: 0;
  --semantic-shadow-ambient-small-x: 0;
  --semantic-shadow-ambient-small-y: 0;
  --semantic-shadow-ambient-small-blur: 4px;
  --semantic-shadow-ambient-small-spread: 0;
  --semantic-shadow-ambient-xs-x: 0;
  --semantic-shadow-ambient-xs-y: 0;
  --semantic-shadow-ambient-xs-blur: 1px;
  --semantic-shadow-ambient-xs-spread: 0;
  --semantic-color-shadow-definition: rgba(0, 0, 0, 0.14);
  --semantic-shadow-definition-large-x: 0;
  --semantic-shadow-definition-large-y: 8px;
  --semantic-shadow-definition-large-blur: 24px;
  --semantic-shadow-definition-large-spread: 0;
  --semantic-shadow-definition-medium-x: 0;
  --semantic-shadow-definition-medium-y: 4px;
  --semantic-shadow-definition-medium-blur: 12px;
  --semantic-shadow-definition-medium-spread: 0;
  --semantic-shadow-definition-small-x: 0;
  --semantic-shadow-definition-small-y: 2px;
  --semantic-shadow-definition-small-blur: 4px;
  --semantic-shadow-definition-small-spread: 0;
  --semantic-shadow-definition-xs-x: 0;
  --semantic-shadow-definition-xs-y: 1px;
  --semantic-shadow-definition-xs-blur: 2px;
  --semantic-shadow-definition-xs-spread: 0;
}
[data-mode='dark'] {
  --semantic-color-shadow-ambient: rgba(0, 0, 0, 0.24);
  --semantic-color-shadow-definition: rgba(0, 0, 0, 0.42);
  --semantic-shadow-definition-large-y: 16px;
  --semantic-shadow-definition-medium-y: 8px;
  --semantic-shadow-definition-small-y: 4px;
  --semantic-shadow-definition-xs-y: 2px;
}
.shadow-large {
  box-shadow:
    var(--semantic-shadow-definition-large-x) var(--semantic-shadow-definition-large-y)
      var(--semantic-shadow-definition-large-blur) var(--semantic-shadow-definition-large-spread)
      var(--semantic-color-shadow-definition),
    var(--semantic-shadow-ambient-large-x) var(--semantic-shadow-ambient-large-y)
      var(--semantic-shadow-ambient-large-blur) var(--semantic-shadow-ambient-large-spread)
      var(--semantic-color-shadow-ambient);
}
.shadow-medium {
  box-shadow:
    var(--semantic-shadow-definition-medium-x) var(--semantic-shadow-definition-medium-y)
      var(--semantic-shadow-definition-medium-blur) var(--semantic-shadow-definition-medium-spread)
      var(--semantic-color-shadow-definition),
    var(--semantic-shadow-ambient-medium-x) var(--semantic-shadow-ambient-medium-y)
      var(--semantic-shadow-ambient-medium-blur) var(--semantic-shadow-ambient-medium-spread)
      var(--semantic-color-shadow-ambient);
}
.shadow-small {
  box-shadow:
    var(--semantic-shadow-definition-small-x) var(--semantic-shadow-definition-small-y)
      var(--semantic-shadow-definition-small-blur) var(--semantic-shadow-definition-small-spread)
      var(--semantic-color-shadow-definition),
    var(--semantic-shadow-ambient-small-x) var(--semantic-shadow-ambient-small-y)
      var(--semantic-shadow-ambient-small-blur) var(--semantic-shadow-ambient-small-spread)
      var(--semantic-color-shadow-ambient);
}
.shadow-xs {
  box-shadow:
    var(--semantic-shadow-definition-xs-x) var(--semantic-shadow-definition-xs-y)
      var(--semantic-shadow-definition-xs-blur) var(--semantic-shadow-definition-xs-spread)
      var(--semantic-color-shadow-definition),
    var(--semantic-shadow-ambient-xs-x) var(--semantic-shadow-ambient-xs-y)
      var(--semantic-shadow-ambient-xs-blur) var(--semantic-shadow-ambient-xs-spread)
      var(--semantic-color-shadow-ambient);
}
:root {
  --semantic-spacing-px-0: 0;
  --semantic-spacing-px-10: 1px;
  --semantic-spacing-px-20: 2px;
  --semantic-spacing-px-40: 4px;
  --semantic-spacing-px-60: 6px;
  --semantic-spacing-px-80: 8px;
  --semantic-spacing-px-100: 10px;
  --semantic-spacing-px-120: 12px;
  --semantic-spacing-px-140: 14px;
  --semantic-spacing-px-160: 16px;
  --semantic-spacing-px-200: 20px;
  --semantic-spacing-px-240: 24px;
  --semantic-spacing-px-280: 28px;
  --semantic-spacing-px-320: 32px;
  --semantic-spacing-px-360: 36px;
  --semantic-spacing-px-400: 40px;
  --semantic-spacing-px-440: 44px;
  --semantic-spacing-px-480: 48px;
  --semantic-spacing-px-520: 52px;
  --semantic-spacing-px-560: 56px;
  --semantic-spacing-px-640: 64px;
  --semantic-spacing-px-800: 80px;
  --semantic-spacing-px-960: 96px;
  --semantic-spacing-px-1120: 112px;
  --semantic-spacing-px-1280: 128px;
  --semantic-spacing-rem-0: 0;
  --semantic-spacing-rem-10: 0.0625rem;
  --semantic-spacing-rem-20: 0.125rem;
  --semantic-spacing-rem-40: 0.25rem;
  --semantic-spacing-rem-60: 0.375rem;
  --semantic-spacing-rem-80: 0.5rem;
  --semantic-spacing-rem-100: 0.625rem;
  --semantic-spacing-rem-120: 0.75rem;
  --semantic-spacing-rem-140: 0.875rem;
  --semantic-spacing-rem-160: 1rem;
  --semantic-spacing-rem-200: 1.25rem;
  --semantic-spacing-rem-240: 1.5rem;
  --semantic-spacing-rem-280: 1.75rem;
  --semantic-spacing-rem-320: 2rem;
  --semantic-spacing-rem-360: 2.25rem;
  --semantic-spacing-rem-400: 2.5rem;
  --semantic-spacing-rem-440: 2.75rem;
  --semantic-spacing-rem-480: 3rem;
  --semantic-spacing-rem-520: 3.25rem;
  --semantic-spacing-rem-560: 3.5rem;
  --semantic-spacing-rem-640: 4rem;
  --semantic-spacing-rem-800: 5rem;
  --semantic-spacing-rem-960: 6rem;
  --semantic-spacing-rem-1120: 7rem;
  --semantic-spacing-rem-1280: 8rem;
  --semantic-cornerRadius-0: 0;
  --semantic-cornerRadius-20: 2px;
  --semantic-cornerRadius-40: 4px;
  --semantic-cornerRadius-60: 6px;
  --semantic-cornerRadius-80: 8px;
  --semantic-cornerRadius-120: 12px;
  --semantic-cornerRadius-160: 16px;
  --semantic-cornerRadius-200: 20px;
  --semantic-cornerRadius-240: 24px;
  --semantic-cornerRadius-1000: 50%;
  --semantic-fontSize-xxl: 2rem;
  --semantic-fontSize-xl: 1.75rem;
  --semantic-fontSize-large: 1.5rem;
  --semantic-fontSize-med: 1.25rem;
  --semantic-fontSize-base: 1rem;
  --semantic-fontSize-small: 0.875rem;
  --semantic-fontSize-xs: 0.75rem;
  --semantic-fontSize-xxs: 0.625rem;
  --semantic-fontWeight-medium: 500;
  --semantic-fontWeight-regular: 400;
  --semantic-letterSpacing-uppercase: 0.06em;
}
.typography-header1 {
  font-size: var(--semantic-fontSize-xxl);
}
.typography-header1,
.typography-header2 {
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-header2 {
  font-size: var(--semantic-fontSize-xl);
}
.typography-header3 {
  font-size: var(--semantic-fontSize-large);
}
.typography-header3,
.typography-header4 {
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-header4 {
  font-size: var(--semantic-fontSize-med);
}
.typography-header5 {
  font-size: var(--semantic-fontSize-base);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-paragraph1,
.typography-paragraph1-condensed {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-paragraph1-strong,
.typography-paragraph1-uppercase {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-paragraph1-uppercase {
  letter-spacing: var(--semantic-letterSpacing-uppercase);
}
.typography-paragraph2 {
  font-size: var(--semantic-fontSize-xs);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-paragraph2-strong,
.typography-paragraph2-uppercase {
  font-size: var(--semantic-fontSize-xs);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-paragraph2-uppercase {
  letter-spacing: var(--semantic-letterSpacing-uppercase);
}
.typography-paragraph3 {
  font-size: var(--semantic-fontSize-xxs);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-paragraph3-strong {
  font-size: var(--semantic-fontSize-xxs);
  font-weight: var(--semantic-fontWeight-medium);
}
.typography-monospace {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-regular);
}
.typography-monospace-strong {
  font-size: var(--semantic-fontSize-small);
  font-weight: var(--semantic-fontWeight-medium);
}
:root {
  --semantic-spacing-default-0: 0;
  --semantic-spacing-default-1: 0.25rem;
  --semantic-spacing-default-2: 0.5rem;
  --semantic-spacing-default-3: 0.75rem;
  --semantic-spacing-default-4: 1rem;
  --semantic-spacing-default-5: 1.25rem;
  --semantic-spacing-default-6: 1.5rem;
  --semantic-spacing-default-8: 2rem;
  --semantic-spacing-default-10: 2.5rem;
  --semantic-spacing-default-12: 3rem;
  --semantic-spacing-default-14: 3.5rem;
  --semantic-spacing-default-16: 4rem;
  --semantic-spacing-default-18: 4.5rem;
  --semantic-spacing-default-20: 5rem;
  --semantic-spacing-default-px: 0.0625rem;
  --semantic-spacing-default-0_5: 0.125rem;
  --semantic-spacing-default-1_5: 0.375rem;
  --semantic-spacing-default-2_5: 0.625rem;
  --semantic-spacing-default-3_5: 0.875rem;
  --semantic-spacing-shrink-small-1: 0.125rem;
  --semantic-spacing-shrink-small-2: 0.375rem;
  --semantic-spacing-shrink-small-3: 0.625rem;
  --semantic-spacing-shrink-small-px: 0;
  --semantic-spacing-shrink-small-0_5: 0.0625rem;
  --semantic-spacing-shrink-small-1_5: 0.25rem;
  --semantic-spacing-shrink-small-2_5: 0.5rem;
  --semantic-spacing-shrink-medium-1: 0;
  --semantic-spacing-shrink-medium-2: 0.25rem;
  --semantic-spacing-shrink-medium-3: 0.5rem;
  --semantic-spacing-shrink-medium-4: 0.75rem;
  --semantic-spacing-shrink-medium-5: 1rem;
  --semantic-spacing-shrink-medium-6: 1.25rem;
  --semantic-spacing-shrink-medium-8: 1.75rem;
  --semantic-spacing-shrink-medium-10: 2.25rem;
  --semantic-spacing-shrink-medium-12: 2.75rem;
  --semantic-spacing-shrink-medium-14: 3.25rem;
  --semantic-spacing-shrink-medium-16: 3.75rem;
  --semantic-spacing-shrink-medium-18: 4.25rem;
  --semantic-spacing-shrink-medium-20: 4.75rem;
  --semantic-spacing-shrink-medium-1_5: 0.125rem;
  --semantic-spacing-shrink-medium-2_5: 0.375rem;
  --semantic-spacing-grow-small-0: 0.125rem;
  --semantic-spacing-grow-small-1: 0.375rem;
  --semantic-spacing-grow-small-2: 0.625rem;
  --semantic-spacing-grow-small-3: 0.875rem;
  --semantic-spacing-grow-small-0_5: 0.25rem;
  --semantic-spacing-grow-small-1_5: 0.5rem;
  --semantic-spacing-grow-small-2_5: 0.75rem;
  --semantic-spacing-grow-medium-0: 0.25rem;
  --semantic-spacing-grow-medium-1: 0.5rem;
  --semantic-spacing-grow-medium-2: 0.75rem;
  --semantic-spacing-grow-medium-3: 1rem;
  --semantic-spacing-grow-medium-4: 1.25rem;
  --semantic-spacing-grow-medium-5: 1.5rem;
  --semantic-spacing-grow-medium-6: 1.75rem;
  --semantic-spacing-grow-medium-8: 2.25rem;
  --semantic-spacing-grow-medium-10: 2.75rem;
  --semantic-spacing-grow-medium-12: 3.5rem;
  --semantic-spacing-grow-medium-14: 3.75rem;
  --semantic-spacing-grow-medium-16: 4.25rem;
  --semantic-spacing-grow-medium-18: 4.75rem;
  --semantic-spacing-grow-medium-19: 5rem;
  --semantic-spacing-grow-medium-20: 5.25rem;
  --semantic-spacing-grow-medium-0_5: 0.375rem;
  --semantic-spacing-grow-medium-1_5: 0.625rem;
  --semantic-spacing-grow-medium-2_5: 0.875rem;
}
@media (min-width: 1024px) {
  :root {
    --semantic-spacing-default-0: 0;
    --semantic-spacing-default-1: 0.25rem;
    --semantic-spacing-default-2: 0.5rem;
    --semantic-spacing-default-3: 0.75rem;
    --semantic-spacing-default-4: 1rem;
    --semantic-spacing-default-5: 1.25rem;
    --semantic-spacing-default-6: 1.5rem;
    --semantic-spacing-default-8: 2rem;
    --semantic-spacing-default-10: 2.5rem;
    --semantic-spacing-default-12: 3rem;
    --semantic-spacing-default-14: 3.5rem;
    --semantic-spacing-default-16: 4rem;
    --semantic-spacing-default-18: 4.5rem;
    --semantic-spacing-default-20: 5rem;
    --semantic-spacing-default-px: 0.0625rem;
    --semantic-spacing-default-0_5: 0.125rem;
    --semantic-spacing-default-1_5: 0.375rem;
    --semantic-spacing-default-2_5: 0.625rem;
    --semantic-spacing-default-3_5: 0.875rem;
    --semantic-spacing-shrink-small-1: 0.25rem;
    --semantic-spacing-shrink-small-2: 0.5rem;
    --semantic-spacing-shrink-small-3: 0.75rem;
    --semantic-spacing-shrink-small-px: 0.0625rem;
    --semantic-spacing-shrink-small-0_5: 0.125rem;
    --semantic-spacing-shrink-small-1_5: 0.375rem;
    --semantic-spacing-shrink-small-2_5: 0.625rem;
    --semantic-spacing-shrink-medium-1: 0.25rem;
    --semantic-spacing-shrink-medium-2: 0.5rem;
    --semantic-spacing-shrink-medium-3: 0.75rem;
    --semantic-spacing-shrink-medium-4: 1rem;
    --semantic-spacing-shrink-medium-5: 1.25rem;
    --semantic-spacing-shrink-medium-6: 1.5rem;
    --semantic-spacing-shrink-medium-8: 2rem;
    --semantic-spacing-shrink-medium-10: 2.5rem;
    --semantic-spacing-shrink-medium-12: 3rem;
    --semantic-spacing-shrink-medium-14: 3.5rem;
    --semantic-spacing-shrink-medium-16: 4rem;
    --semantic-spacing-shrink-medium-18: 4.5rem;
    --semantic-spacing-shrink-medium-20: 5rem;
    --semantic-spacing-shrink-medium-1_5: 0.375rem;
    --semantic-spacing-shrink-medium-2_5: 0.625rem;
    --semantic-spacing-grow-small-0: 0;
    --semantic-spacing-grow-small-1: 0.25rem;
    --semantic-spacing-grow-small-2: 0.5rem;
    --semantic-spacing-grow-small-3: 0.75rem;
    --semantic-spacing-grow-small-0_5: 0.125rem;
    --semantic-spacing-grow-small-1_5: 0.375rem;
    --semantic-spacing-grow-small-2_5: 0.625rem;
    --semantic-spacing-grow-medium-0: 0;
    --semantic-spacing-grow-medium-1: 0.25rem;
    --semantic-spacing-grow-medium-2: 0.5rem;
    --semantic-spacing-grow-medium-3: 0.75rem;
    --semantic-spacing-grow-medium-4: 1rem;
    --semantic-spacing-grow-medium-5: 1.25rem;
    --semantic-spacing-grow-medium-6: 1.5rem;
    --semantic-spacing-grow-medium-8: 2rem;
    --semantic-spacing-grow-medium-10: 2.5rem;
    --semantic-spacing-grow-medium-12: 3rem;
    --semantic-spacing-grow-medium-14: 3.5rem;
    --semantic-spacing-grow-medium-16: 4rem;
    --semantic-spacing-grow-medium-18: 4.5rem;
    --semantic-spacing-grow-medium-19: 4.75rem;
    --semantic-spacing-grow-medium-20: 5rem;
    --semantic-spacing-grow-medium-0_5: 0.125rem;
    --semantic-spacing-grow-medium-1_5: 0.375rem;
    --semantic-spacing-grow-medium-2_5: 0.625rem;
  }
}
:root {
  --semantic-sizes-element-xs: 1.25rem;
  --semantic-sizes-element-default: 1.5rem;
  --semantic-sizes-element-base: 1.75rem;
  --semantic-sizes-element-medium: 2rem;
  --semantic-sizes-element-large: 2.25rem;
  --semantic-sizes-element-xl: 2.5rem;
  --semantic-sizes-element-2xl: 2.75rem;
  --semantic-sizes-screenSize-horizontal-small: 22.5rem;
  --semantic-sizes-screenSize-horizontal-default: 27.5rem;
  --semantic-sizes-screenSize-horizontal-large: 48rem;
  --semantic-sizes-screenSize-vertical-small: 48.75rem;
  --semantic-sizes-screenSize-vertical-default: 59.75rem;
  --semantic-sizes-screenSize-vertical-large: 64rem;
}
@media (min-width: 1024px) {
  :root {
    --semantic-sizes-element-xs: 1rem;
    --semantic-sizes-element-default: 1.25rem;
    --semantic-sizes-element-base: 1.5rem;
    --semantic-sizes-element-medium: 1.75rem;
    --semantic-sizes-element-large: 2rem;
    --semantic-sizes-element-xl: 2.25rem;
    --semantic-sizes-element-2xl: 2.5rem;
    --semantic-sizes-screenSize-horizontal-small: 64rem;
    --semantic-sizes-screenSize-horizontal-default: 90rem;
    --semantic-sizes-screenSize-horizontal-large: 120rem;
    --semantic-sizes-screenSize-vertical-small: 48rem;
    --semantic-sizes-screenSize-vertical-default: 64rem;
    --semantic-sizes-screenSize-vertical-large: 67.5rem;
  }
}
:root {
  --semantic-cornerRadius-none: 0;
  --semantic-cornerRadius-small: 0.375rem;
  --semantic-cornerRadius-medium: 0.5rem;
  --semantic-cornerRadius-default: 0.75rem;
  --semantic-cornerRadius-large: 1rem;
  --semantic-cornerRadius-full: 50%;
}
@media (min-width: 1024px) {
  :root {
    --semantic-cornerRadius-none: 0;
    --semantic-cornerRadius-small: 0.25rem;
    --semantic-cornerRadius-medium: 0.375rem;
    --semantic-cornerRadius-default: 0.5rem;
    --semantic-cornerRadius-large: 0.75rem;
    --semantic-cornerRadius-full: 50%;
  }
}
:root {
  --semantic-typography-size-2xs: 0.75rem;
  --semantic-typography-size-xs: 0.875rem;
  --semantic-typography-size-default: 1rem;
  --semantic-typography-size-base: 1.25rem;
  --semantic-typography-size-medium: 1.5rem;
  --semantic-typography-size-large: 1.75rem;
  --semantic-typography-size-xl: 2rem;
  --semantic-typography-size-2xl: 2.25rem;
  --semantic-typography-lineHeight-2xs: 1rem;
  --semantic-typography-lineHeight-xs: 1.25rem;
  --semantic-typography-lineHeight-default: 1.5rem;
  --semantic-typography-lineHeight-defaultCondensed: 1.375rem;
  --semantic-typography-lineHeight-base: 1.75rem;
  --semantic-typography-lineHeight-medium: 2rem;
  --semantic-typography-lineHeight-large: 2.25rem;
  --semantic-typography-lineHeight-xl: 2.5rem;
  --semantic-typography-lineHeight-2xl: 2.75rem;
  --semantic-typography-letterSpacing-uppercaseXs: 0.16;
  --semantic-typography-letterSpacing-uppercaseDefault: 0.36;
  --semantic-typography-weight-font-regular: 400;
  --semantic-typography-weight-font-strong: 600;
  --semantic-typography-weight-icon-light: light;
  --semantic-typography-weight-icon-regular: regular;
  --semantic-typography-weight-icon-solid: solid;
}
@media (min-width: 1024px) {
  :root {
    --semantic-typography-size-2xs: 0.625rem;
    --semantic-typography-size-xs: 0.75rem;
    --semantic-typography-size-default: 0.875rem;
    --semantic-typography-size-base: 1rem;
    --semantic-typography-size-medium: 1.25rem;
    --semantic-typography-size-large: 1.5rem;
    --semantic-typography-size-xl: 1.75rem;
    --semantic-typography-size-2xl: 2rem;
    --semantic-typography-lineHeight-2xs: 0.875rem;
    --semantic-typography-lineHeight-xs: 1rem;
    --semantic-typography-lineHeight-default: 1.25rem;
    --semantic-typography-lineHeight-defaultCondensed: 1.125rem;
    --semantic-typography-lineHeight-base: 1.5rem;
    --semantic-typography-lineHeight-medium: 1.75rem;
    --semantic-typography-lineHeight-large: 2rem;
    --semantic-typography-lineHeight-xl: 2.25rem;
    --semantic-typography-lineHeight-2xl: 2.5rem;
    --semantic-typography-letterSpacing-uppercaseXs: 0.06;
    --semantic-typography-letterSpacing-uppercaseDefault: 0.16;
    --semantic-typography-weight-font-regular: 400;
    --semantic-typography-weight-font-strong: 500;
    --semantic-typography-weight-icon-light: light;
    --semantic-typography-weight-icon-regular: regular;
    --semantic-typography-weight-icon-solid: solid;
  }
}
:root {
  --semantic-fontFamily-default:
    'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  --semantic-fontFamily-monospace: 'Source Code Pro', monospace;
}
.typography-header1 {
  font-size: var(--semantic-typography-size-2xl);
  line-height: 1.25;
}
.typography-header1,
.typography-header2 {
  font-family: var(--semantic-fontFamily-default);
  font-weight: var(--semantic-typography-weight-font-strong);
}
.typography-header2 {
  font-size: var(--semantic-typography-size-xl);
  line-height: 1.3;
}
.typography-header3 {
  font-size: var(--semantic-typography-size-large);
  line-height: 1.32;
}
.typography-header3,
.typography-header4 {
  font-family: var(--semantic-fontFamily-default);
  font-weight: var(--semantic-typography-weight-font-strong);
}
.typography-header4 {
  font-size: var(--semantic-typography-size-medium);
  line-height: 1.2;
}
.typography-header5 {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-base);
  font-weight: var(--semantic-typography-weight-font-strong);
  line-height: 1.25;
}
.typography-paragraph1 {
  line-height: 1.4;
}
.typography-paragraph1,
.typography-paragraph1-condensed {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-default);
  font-weight: var(--semantic-typography-weight-font-regular);
}
.typography-paragraph1-condensed {
  line-height: 1.28;
}
.typography-paragraph1-strong,
.typography-paragraph1-uppercase {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-default);
  font-weight: var(--semantic-typography-weight-font-strong);
  line-height: 1.4;
}
.typography-paragraph1-uppercase {
  letter-spacing: var(--semantic-typography-letterSpacing-uppercaseDefault);
  text-transform: uppercase;
}
.typography-paragraph2 {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-xs);
  font-weight: var(--semantic-typography-weight-font-regular);
  line-height: 1.32;
}
.typography-paragraph2-strong,
.typography-paragraph2-uppercase {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-xs);
  font-weight: var(--semantic-typography-weight-font-strong);
  line-height: 1.32;
}
.typography-paragraph2-uppercase {
  letter-spacing: var(--semantic-typography-letterSpacing-uppercaseXs);
  text-transform: uppercase;
}
.typography-paragraph3 {
  font-weight: var(--semantic-typography-weight-font-regular);
}
.typography-paragraph3,
.typography-paragraph3-strong {
  font-family: var(--semantic-fontFamily-default);
  font-size: var(--semantic-typography-size-2xs);
  line-height: 1.36;
}
.typography-paragraph3-strong {
  font-weight: var(--semantic-typography-weight-font-strong);
}
.typography-monospace {
  font-weight: var(--semantic-typography-weight-font-regular);
}
.typography-monospace,
.typography-monospace-strong {
  font-family: var(--semantic-fontFamily-monospace);
  font-size: var(--semantic-typography-size-default);
  line-height: 1.4;
}
.typography-monospace-strong {
  font-weight: var(--semantic-typography-weight-font-strong);
}
