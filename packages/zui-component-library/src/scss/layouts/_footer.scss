.footer-container {
  display: flex;

  position: fixed;
  left: 0;
  bottom: 0;

  background-color: var(--semantic-color-surface-interactive-primary-default, #2160e1);

  color: var(--semantic-color-content-inverted-base-primary, #fff);

  height: 32px;

  padding: var(--semantic-spacing-px-80, 8px) var(--semantic-spacing-px-120, 12px);

  width: 100%;

  @extend .typography-paragraph1;

  .left-section,
  .copyright-section {
    display: flex;
  }

  .copyright-section {
    flex-shrink: 0;
  }

  .build-version {
    margin: 0 var(--semantic-spacing-px-60, 6px);
  }

  .build-version::before {
    content: '|';
    margin-right: 4px;
  }

  .data-policy {
    color: var(--semantic-color-content-inverted-base-primary, #fff);
    text-decoration: none;
  }

  .data-policy::before {
    content: '|';
    margin-right: 4px;
  }
}
