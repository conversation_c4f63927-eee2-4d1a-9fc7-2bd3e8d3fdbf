.navigation-bar-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0;

  img {
    width: 44px;
    height: auto;
  }
}

.navigation-items {
  height: calc(100vh - 18.5rem);
  overflow-y: auto;

  .navigation-item {
    cursor: pointer;
    position: relative;

    .name-icon-wrapper {
      padding: 12px 16px;
      display: flex;
      align-items: center;

      .icon-wrapper {
        color: var(--semantic-color-content-base-tertiary, #677289);
        width: 24px;
        text-align: center;
      }

      .name {
        @extend .typography-paragraph1;
        margin-left: 16px;
        color: var(--semantic-color-content-base-secondary, #4a5468);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        max-width: 180px; /* Limit width to prevent overflow */
      }

      .arrow-icon-wrapper,
      .arrow-icon-wrapper-hover-menu,
      .right-icon-wrapper {
        margin-left: auto;
        color: var(--semantic-color-content-base-tertiary, #677289);
      }

      .arrow-icon-wrapper svg {
        transform: rotate(-90deg);
      }

      &:hover {
        background-color: var(--semantic-color-surface-nav-sideBar-hover, #e9eef3);
        color: var(--semantic-color-content-base-primary, #131a2e);
      }
    }

    &.open > .name-icon-wrapper .arrow-icon-wrapper svg {
      transform: rotate(0deg);
    }

    &.hovering .name-icon-wrapper {
      background-color: var(--semantic-color-surface-nav-sideBar-hover, #e9eef3);
      color: var(--semantic-color-content-base-primary, #131a2e);
    }

    &.selected > .name-icon-wrapper {
      color: var(--semantic-color-content-interactive-primary-default, #2160e1);

      .icon-wrapper,
      .name,
      .arrow-icon-wrapper,
      .arrow-icon-wrapper-hover-menu,
      .right-icon-wrapper {
        color: var(--semantic-color-content-interactive-primary-default, #2160e1);
      }
    }

    &.selected.top-level > .name-icon-wrapper {
      background-color: var(--semantic-color-surface-nav-sideBar-active, #e3ecff);
    }

    &.sub-level:not(.loading):not(.has-icon),
    &.sub-level.has-icon.right:not(.loading) {
      .name {
        margin-left: 40px;
      }
    }

    &.line {
      border-bottom: 1px solid var(--semantic-color-border-base-subdued, #e9eef3);
    }

    .new-badge {
      display: inline-block;
      margin-left: 8px;
      padding: 2px 6px;
      background-color: var(--semantic-color-surface-status-info-default, #f2f7ff);
      color: var(--semantic-color-content-interactive-primary-default, #2160e1);
      border-radius: var(--semantic-cornerRadius-40, 4px);
      font-size: var(--semantic-fontSize-xs, 0.75rem);
      font-weight: var(--semantic-fontWeight-medium, 500);
    }
  }
}

.hovering-panel-wrapper {
  position: absolute;
  top: 0;
  left: 100%;
  min-width: 220px;
  background-color: var(--semantic-color-surface-menu-default, #fff);
  border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  border-radius: var(--semantic-cornerRadius-60, 6px);
  @extend .shadow-small;
  z-index: 9999;
  margin-left: 2px;

  .hovering-panel-item {
    .hovering-panel-item-title {
      @extend .typography-paragraph1-strong;
      color: var(--semantic-color-content-base-secondary, #4a5468);
      margin: 8px 16px;
    }

    .hovering-panel-item-name {
      @extend .typography-paragraph1;
      color: var(--semantic-color-content-base-tertiary, #677289);
      padding: 12px 16px;
      cursor: pointer;

      &:hover {
        background-color: var(--semantic-color-surface-menu-hover, #e9eef3);
        color: var(--semantic-color-content-base-primary, #131a2e);
      }
    }

    &.active {
      .hovering-panel-item-name {
        color: var(--semantic-color-content-interactive-primary-default, #2160e1);
        background-color: var(--semantic-color-surface-menu-selected, #e3ecff);
      }
    }
  }
}

.input-search {
  padding: 8px;
  width: 100%;

  .input-search-container {
    position: relative;
    display: flex;
    align-items: center;
    height: 2.5rem;
    background-color: var(--semantic-color-surface-fields-default, #fff);
    border: 1px solid var(--semantic-color-border-interactive-secondary-default, #cad0dc);
    border-radius: var(--semantic-cornerRadius-60, 6px);
    width: 100%;

    &:hover {
      border-color: var(--semantic-color-border-interactive-secondary-hover, #a3acbd);
      background-color: var(--semantic-color-surface-fields-hover, #f7f8fa);
    }

    &:focus-within {
      border-color: var(--semantic-color-content-interactive-primary-default, #2160e1);
      background-color: var(--semantic-color-surface-fields-active, #f2f7ff);
    }

    &.valid-false {
      border-color: var(--semantic-color-border-status-danger-active, #dc362e);
      background-color: var(--semantic-color-surface-status-danger-default, #ffedee);
    }

    input {
      @extend .typography-paragraph1;
      height: 100%;
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      padding: 0 8px;
      margin-left: 24px;
      color: var(--semantic-color-content-base-primary, #131a2e);

      &::placeholder {
        color: var(--semantic-color-content-base-tertiary, #677289);
      }
    }

    .search-icon {
      position: absolute;
      left: 8px;
      color: var(--semantic-color-content-base-tertiary, #677289);
    }

    .clear-button {
      border: none;
      background: transparent;
      color: var(--semantic-color-content-base-tertiary, #677289);
      cursor: pointer;
      padding: 0 8px;
      position: absolute;
      right: 0;
    }
  }

  .validation-message {
    @extend .typography-paragraph2;
    margin-top: 4px;
    color: var(--semantic-color-content-status-danger-primary, #8e201c);
  }
}

.navigation-bar-select-wrapper {
  @extend .typography-paragraph1-strong;
  color: var(--semantic-color-content-base-secondary, #4a5468);
  text-align: center;
  padding: 4px 0;
}

.message-wrapper {
  align-items: center;
  justify-content: center;
  height: inherit;
  min-height: inherit;
  text-align: center;
  width: 100%;
  p {
    font-size: var(--semantic-fontSize-xxl);
    color: var(--semantic-color-content-base-tertiary, #677289);
    text-align: center;
  }
}

.search-result {
  position: absolute;
  top: 10rem;
  left: 2rem;
  z-index: 100;
  min-width: 260px;
  max-width: 320px;
  padding: 8px 0;
  border-radius: var(--semantic-cornerRadius-80, 8px);
  background-color: var(--semantic-color-surface-menu-default, #fff);
  border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);
  @extend .shadow-small;

  .search-result-item {
    padding: 8px 16px;
    margin: 4px 0;
    height: 5.6rem;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &:hover {
      background-color: var(--semantic-color-surface-menu-hover, #e9eef3);
    }

    .top-level-item {
      @extend .typography-paragraph1-strong;
      color: var(--semantic-color-content-base-secondary, #4a5468);
      padding: 4px;
    }

    .sub-level-items {
      padding: 4px;
      display: flex;
      align-items: center;

      .sub-level-item {
        @extend .typography-paragraph1;
        color: var(--semantic-color-content-base-tertiary, #677289);
        cursor: pointer;
      }

      .sub-level-arrow-icon {
        padding: 0 8px;
        color: var(--semantic-color-content-base-tertiary, #677289);
      }
    }

    &.shallow {
      padding: 12px 16px;
    }
  }

  .message-wrapper {
    padding: 16px;
    text-align: center;

    p {
      @extend .typography-paragraph1;
      color: var(--semantic-color-content-base-secondary, #4a5468);
    }
  }
}

.toggle-nav-bar {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 2.5rem;
  height: 2.5rem;
  button {
    width: 100%;
    height: 100%;
    border-radius: var(--semantic-cornerRadius-1000, 50%);
    padding: 0;
    min-width: unset;
  }
}

.navigation-bar,
.navigation-items,
.navigation-item {
  overflow: visible;
}

.navigation-bar {
  min-width: 80px;
  width: 260px;
  background: var(--semantic-color-surface-nav-sideBar-default, #f7f8fa);
  z-index: 15;
  position: relative;
  border: 1px solid var(--semantic-color-border-base-primary, #dde3ea);

  .left-navigation-loader {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--semantic-color-surface-alpha-overlay, rgba(25, 25, 25, 0.64));
    @extend .typography-paragraph1;
    color: var(--semantic-color-content-base-primary, #131a2e);
  }

  &.minimize:not(.hovering) {
    width: 80px;

    .navigation-bar-select-wrapper span,
    .input-search input,
    .navigation-item.sub-level,
    .name-icon-wrapper .name,
    .name-icon-wrapper .arrow-icon-wrapper,
    .name-icon-wrapper .right-icon-wrapper {
      display: none;
    }
  }

  &.hovering {
    height: 100%;
    width: 260px;
    position: absolute;
    @extend .shadow-small;
  }
}
