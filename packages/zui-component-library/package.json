{"name": "@zscaler/zui-component-library", "version": "3.2.2", "description": "ZUI Reusable Component Library", "main": "dist/index.js", "module": "dist/index.es.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "rollup -c -w", "build": "rollup -c", "sass-watch": "sass --no-source-map -w src/scss/main.scss:dist/index.css", "update": "ncu -u && pnpm i", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "https://bitbucket.corp.zscaler.com/scm/oneidentity/zui-component-library.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "files": ["dist/"], "dependencies": {"@floating-ui/react": "^0.27.8", "@nivo/bar": "^0.96.0", "@nivo/core": "^0.96.0", "@nivo/line": "^0.96.0", "@tanstack/react-table": "^8.21.3", "ip-address": "^10.0.1", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^8.3.0", "@zs-nimbus/foundations": "^1.4.1", "react-diff-viewer": "^3.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-terser": "^0.4.4", "@swc/core": "^1.11.24", "rollup": "^4.40.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-scss": "^4.0.1", "rollup-plugin-swc3": "^0.12.1", "rollup-plugin-visualizer": "^5.14.0"}, "peerDependencies": {"@fortawesome/fontawesome-pro": "^6.4.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/pro-light-svg-icons": "^6.4.0", "@fortawesome/pro-regular-svg-icons": "^6.4.0", "@fortawesome/pro-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@nivo/bar": "^0.84.0", "@nivo/core": "^0.84.0", "@nivo/line": "^0.84.0", "dayjs": "^1.11.10", "i18next": "^23.6.0", "lodash-es": "^4.17.21", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.3.1", "react-html-id": "^0.1.5", "react-redux": "^8.1.3", "react-router-dom": "^7.5.0"}, "lint-staged": {"*.{js,jsx}": ["eslint --cache --fix", "prettier --ignore-unknown --plugin-search-dir=. --write"], "*.{css,scss,md,json}": "prettier --ignore-unknown --plugin-search-dir=. --write"}}