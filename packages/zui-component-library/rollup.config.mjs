import commonjs from '@rollup/plugin-commonjs';
import resolve from '@rollup/plugin-node-resolve';
import terser from '@rollup/plugin-terser';

import copy from 'rollup-plugin-copy';
import del from 'rollup-plugin-delete';
import external from 'rollup-plugin-peer-deps-external';
import scss from 'rollup-plugin-scss';
import { swc } from 'rollup-plugin-swc3';
import { visualizer } from 'rollup-plugin-visualizer';

const production = !process.env.ROLLUP_WATCH;

export default [
  {
    input: './src/index.js',
    output: [
      {
        file: 'dist/index.js',
        format: 'es',
        sourcemap: !production ? true : false,
      },
    ],
    plugins: [
      production &&
        del({
          targets: 'dist/*',
        }),
      scss(),
      external(),
      resolve({ extensions: ['.mjs', '.js', '.jsx', '.json', '.node'] }),
      swc(),
      commonjs(),
      production && terser(),
      copy({
        targets: [{ src: 'src/scss', dest: 'dist' }],
      }),
      production && visualizer(),
    ],
  },
];
