{"name": "zui-components", "private": true, "scripts": {"build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "update": "ncu -u && pnpm i"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.26.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "eslint": "^9.26.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.1.0", "prettier": "^3.5.3", "turbo": "2.5.3"}, "packageManager": "pnpm@10.11.0"}