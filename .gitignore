# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
.idea

.eslintcache

.husky

*.sh
*.gz
*.tgz

# testing
coverage

# production
build
dist

# misc
.env.local
.env.test.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*


.cache
*.log

.turbo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db