# zui-components starter

This is an official starter of zui-components using [Turborepo](https://turbo.build/repo/docs).

## What's inside?

This zui-components includes the following packages/apps:

### Apps and Packages

- `docs`: a portal to showcase component library app
- `ui`: a stub React component library shared by `docs` applications
- `eslint-config-custom`: `eslint` configurations (includes `eslint-config-next` and `eslint-config-prettier`)
- `tsconfig`: `tsconfig.json`s used throughout the monorepo

Each package/app will be 100% [TypeScript](https://www.typescriptlang.org/).

### Utilities

This zui-components has some additional tools already setup for you:

- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Prettier](https://prettier.io) for code formatting

### Build

To build all apps and packages, run the following command:

```
cd zui-components
pnpm build
```

### Develop

To develop all apps and packages, run the following command:

```
cd zui-components
pnpm dev
```

## Useful Links

Learn more about the power of zui-components:

- [Tasks](https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks)
- [Caching](https://turbo.build/repo/docs/core-concepts/caching)
- [Remote Caching](https://turbo.build/repo/docs/core-concepts/remote-caching)
- [Filtering](https://turbo.build/repo/docs/core-concepts/monorepos/filtering)
- [Configuration Options](https://turbo.build/repo/docs/reference/configuration)
- [CLI Usage](https://turbo.build/repo/docs/reference/command-line-reference)
